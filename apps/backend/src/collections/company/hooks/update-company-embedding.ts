import { CollectionBeforeChangeHook, parseCookies } from 'payload';
import { Manufacturer } from '@/payload-types';
import { AIService } from '@/services/AIService';

export const updateCompanyEmbedding: CollectionBeforeChangeHook<Manufacturer> = async ({ data, originalDoc, req }) => {
    const embeddingTextChanged = data.embeddingText && data.embeddingText !== originalDoc?.embeddingText;
    const embeddingText = data.embeddingText ?? originalDoc?.embeddingText;
    const missingEmbedding = !originalDoc?.embedding;

    if (embeddingTextChanged || (missingEmbedding && embeddingText)) {
        try {
            const jwt = parseCookies(req.headers).get('dcide-jwt');
            if (!jwt) {
                throw new Error('No JWT found');
            }

            data.embedding = await AIService.getEmbedding(embeddingText!);
        } catch (error: any) {
            console.error('Error calculating embedding', error.message);
        }
    }
};
