import { useState } from 'react';

const useAction = <T extends Array<any>, U>(
    handler: (...args: T) => Promise<U>,
): [(...args: T) => Promise<U>, boolean] => {
    const [running, setRunning] = useState(false);

    const wrapped = async (...args: T) => {
        setRunning(true);
        const result = await handler(...args);

        setRunning(false);

        return result;
    };

    return [wrapped, running];
};

export { useAction };
