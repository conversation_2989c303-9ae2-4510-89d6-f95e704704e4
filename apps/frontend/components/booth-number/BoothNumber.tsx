import React, { FC } from 'react';

import { Avatar, AvatarProps, Group, MantineSize, Stack, Tooltip } from '@mantine/core';

import { CompanyProfile, Event } from '@repo/dcide-component-models';

import { useLocalEvent } from 'hooks/use-local-event';

import { getId } from 'helpers/getId';

import cx from './BoothNumber.module.css';

type BoothNumberProps = {
    name: string;
    booth: string;
    event?: string;
    inline?: boolean;
    inlineMultiline?: boolean;
    matchHeight?: boolean;
    size: MantineSize;
} & AvatarProps;

const WrappedBoothNumber: FC<
    {
        event?: Event;
        company: Pick<CompanyProfile, 'name' | 'id'>;
        size?: 'xs' | 'sm' | 'md';
        showEventName?: boolean;
        inline?: boolean;
    } & AvatarProps
> = ({ event, company, size = 'sm', showEventName, inline, ...rest }) => {
    const { localEvent } = useLocalEvent();
    const activeEvent = event || localEvent;

    if (!activeEvent) {
        return null;
    }

    const booth = activeEvent.companies.find((c) => getId(c.company) === company.id)?.booth;

    if (!booth) {
        return null;
    }

    return (
        <BoothNumber
            name={company.name}
            booth={booth}
            event={showEventName ? activeEvent.name : ''}
            size={size}
            inline={inline}
            {...rest}
        />
    );
};

const BoothNumber: FC<BoothNumberProps> = ({
    name,
    booth,
    event,
    inline,
    inlineMultiline,
    matchHeight,
    size,
    ...rest
}) => {
    const Body = (
        <Avatar
            component="div"
            classNames={cx}
            variant="gradient"
            data-size={size}
            data-inline={inline}
            data-match-height={matchHeight}
            radius="sm"
            {...rest}
        >
            {event && <span className={cx.prefix}>{event}</span>}
            <span className={cx.prefix}>BOOTH</span>
            {booth}
        </Avatar>
    );

    if (inline && inlineMultiline) {
        return (
            <Stack gap={4} fw={500} c="dimmed">
                <span>Find us at</span>
                {Body}
            </Stack>
        );
    }

    if (inline) {
        return (
            <Group gap={4} fw={500} c="dimmed">
                <span>Find us at</span>
                {Body}
            </Group>
        );
    }

    return <Tooltip label={`Find ${name} at booth ${booth}`}>{Body}</Tooltip>;
};

export { WrappedBoothNumber as BoothNumber };
export { BoothNumber as BoothNumberUnwrapped };
