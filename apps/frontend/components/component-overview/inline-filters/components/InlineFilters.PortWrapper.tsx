import React, { FC } from 'react';

import { Group } from '@mantine/core';

import cx from './InlineFilters.PortWrapper.module.css';

const InlineFiltersPortWrapper: FC<{ active?: boolean; children: React.ReactNode }> = ({ active, children }) => {
    return (
        <Group gap={1} className={cx.root} data-active={active}>
            {children}
        </Group>
    );
};

export { InlineFiltersPortWrapper };
