dateModified: 1750266966
elementSources:
  craft\elements\Entry:
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: true
      key: '*'
      tableAttributes:
        - status
        - section
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      heading: Content
      type: heading
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'single:2f74b7e2-1cd6-4066-8ac8-48b9731b5b37' # Homepage
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      key: 'section:3b52fd6c-9ab2-468f-addf-dac444492786' # Job Categories
      type: native
    -
      key: 'section:f4caa8b3-8f76-4daf-b9b9-e64adec06343' # Jobs
      type: native
    -
      key: 'section:1be68fba-f2b8-4606-b1fc-012399bddcf6' # News Categories
      type: native
    -
      key: 'section:c947061b-185f-4d72-a4d5-75653e7ae376' # News
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:65fd33c2-b449-49d2-be10-f59790984699' # Page
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      key: 'section:1ebe34f6-fb3e-431d-8088-7336e945d649' # Teasers
      type: native
    -
      heading: Elements
      type: heading
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:e69270f5-1926-4710-92f9-453f25ef85bd' # Image Transforms
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:140b74fb-9bf9-418a-819e-e345b48a3de0' # SVGs
      tableAttributes: '-'
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:983e4658-ef41-46e4-9a1a-538b7eed6410' # Swooms
      tableAttributes: '-'
      type: native
    -
      heading: Styles
      type: heading
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:105a519c-7bf4-457b-8d9f-8a8973be192d' # Button Styles
      tableAttributes: '-'
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:f320dfe4-16f5-4ff9-805b-8b3077564de5' # Card Styles
      tableAttributes: '-'
      type: native
    -
      key: 'section:5b5bc007-d075-40b1-b789-5bc99e44728b' # Embed Styles
      type: native
    -
      key: 'section:40c196d5-5717-4b27-b060-e14a1110ce48' # Grid Styles
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:4d03de75-aa66-4abe-b1fa-d64a63180f56' # Header Styles
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:4dbc2f01-427e-448b-9ab9-c984e7fed203' # Image Styles
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:14c62909-a941-483e-bf99-ec024c5c00c6' # Navigation Style
      tableAttributes: '-'
      type: native
    -
      key: 'section:ad842226-225a-4314-ba7c-fde9f62d5a92' # Section Styles
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:3aac8114-a678-494a-921e-2c12265df747' # Teaser Styles
      tableAttributes: '-'
      type: native
    -
      key: 'section:2a440a88-6b29-407d-8fff-2f08d3a63766' # Text Styles
      type: native
    -
      heading: Variables
      type: heading
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:7fc8fbd1-484c-4554-87b7-c8c9a1fa5735' # Borders
      tableAttributes:
        - 'fieldInstance:a0c7176c-454d-4266-b455-dc9025e917b3'
        - 'fieldInstance:40e65524-1131-4aba-b115-a7e373583236'
        - 'field:0c2ab25c-afd4-4827-ad03-2acbe67a0fe5' # Color
        - 'fieldInstance:3eca7100-c89d-4667-b2da-9d282a960363'
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:41525efe-c6b3-445d-8503-8b911ffd53f4' # Box Shadows
      tableAttributes:
        - 'fieldInstance:038a3a1a-08c6-4eb3-b592-53e4429739f0'
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:d1abc506-ac19-4af8-8f15-78375f5c2146' # Colors
      tableAttributes:
        - 'fieldInstance:9a08b1ec-b540-4305-8582-15d79e1f684c'
        - 'fieldInstance:833d356a-7ccd-49f1-a36c-165b1bdfc53c'
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:21a6a9f1-a326-49b6-8167-dd528d0ae5c7' # Fonts
      tableAttributes:
        - 'fieldInstance:fb4e59c6-0663-4193-a956-c6b83bf23cf2'
        - 'fieldInstance:c49698a8-af24-47cf-9a91-f07ef18a6925'
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:b75fb7db-feaf-41e2-bd1b-1ed8f10c448d' # Font Sizes
      tableAttributes: '-'
      type: native
    -
      defaultSort:
        - structure
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:fcd0994a-3cc1-414a-aeee-a5f096c2af7a' # Gradients
      tableAttributes:
        - 'fieldInstance:5ec45897-9f9f-43c2-98e0-91e90b46ea59'
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:35944086-a010-4294-acf3-ea975a7a90e9' # Heights
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:c26163fd-ba8f-461b-91c0-adfa0cea4002' # Paddings
      tableAttributes: '-'
      type: native
    -
      defaultSort:
        - postDate
        - desc
      defaultViewMode: ''
      disabled: false
      key: 'section:3f1f3aee-c7af-4c72-9f51-92b1c1265b2b' # Spacers
      tableAttributes:
        - status
        - postDate
        - expiryDate
        - authors
        - link
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:95952cf5-089a-4707-8019-8d1affcc8ebd' # Text Shadows
      tableAttributes:
        - 'fieldInstance:ee3f1fd7-16ca-4f13-97d4-9dd7d301b226'
      type: native
    -
      key: 'section:3c1b74a5-0e52-4420-ac8b-541d393c9267' # Transforms
      type: native
    -
      defaultSort:
        - id
        - asc
      defaultViewMode: ''
      disabled: false
      key: 'section:19d9c017-e51c-4628-adf8-0d7e960d4945' # Widths
      tableAttributes: '-'
      type: native
email:
  fromEmail: $SITE_EMAIL_ADDRESS
  fromName: $SITE_EMAIL_NAME
  replyToEmail: null
  template: ''
  transportSettings:
    apiKey: $MAILGUN_API_KEY
    domain: $MAILGUN_DOMAIN
    endpoint: $MAILGUN_ENDPOINT
  transportType: craft\mailgun\MailgunAdapter
fs:
  files:
    hasUrls: true
    name: Files
    settings:
      bucket: webosaurus
      bucketSelectionMode: choose
      expires: ''
      keyFileContents: ''
      projectId: $GOOGLE_APPLICATION_PROJECT_ID
      subfolder: '@files'
    type: craft\googlecloud\Fs
    url: 'https://storage.googleapis.com/webosaurus'
  images:
    hasUrls: true
    name: Images
    settings:
      bucket: webosaurus
      bucketSelectionMode: choose
      expires: ''
      keyFileContents: ''
      projectId: $GOOGLE_APPLICATION_PROJECT_ID
      subfolder: '@images'
    type: craft\googlecloud\Fs
    url: 'https://webosaurus.imgix.net'
  videos:
    hasUrls: true
    name: Videos
    settings:
      bucket: webosaurus
      bucketSelectionMode: choose
      expires: ''
      keyFileContents: ''
      projectId: $GOOGLE_APPLICATION_PROJECT_ID
      subfolder: '@videos'
    type: craft\googlecloud\Fs
    url: 'https://storage.googleapis.com/webosaurus'
meta:
  __names__:
    0b8fd983-4666-4a6b-bd5b-9769e8885b99: Image # Image
    0c2ab25c-afd4-4827-ad03-2acbe67a0fe5: Color # Color
    0e59312a-2706-4aa5-99c7-8846da9148c1: 'Public Schema' # Public Schema
    1be68fba-f2b8-4606-b1fc-012399bddcf6: 'News Categories' # News Categories
    1cabd79f-a996-4532-83ec-cac89a4ea9be: Teasers # Teasers
    1ebe34f6-fb3e-431d-8088-7336e945d649: Teasers # Teasers
    2a440a88-6b29-407d-8fff-2f08d3a63766: 'Text Styles' # Text Styles
    2b6cd1a9-b9dc-4ed4-9b20-c1e3d38e72e6: Main # Main
    2c715fe0-c849-4464-8db8-37416df25d64: Teaser # Teaser
    2f74b7e2-1cd6-4066-8ac8-48b9731b5b37: Homepage # Homepage
    3aac8114-a678-494a-921e-2c12265df747: 'Teaser Styles' # Teaser Styles
    3b52fd6c-9ab2-468f-addf-dac444492786: 'Job Categories' # Job Categories
    3c1b74a5-0e52-4420-ac8b-541d393c9267: Transforms # Transforms
    3ca2760a-0d5e-4f4c-a95e-4de0423802cc: 'Teaser Style' # Teaser Style
    3cd1fa32-a677-4c85-bcfe-4b2bce971190: Default # Default
    03e54a6a-6f2a-453c-b8e4-994ae89bad83: Font # Font
    3f1f3aee-c7af-4c72-9f51-92b1c1265b2b: Spacers # Spacers
    3ff230e1-65f7-4f29-aaa5-921906f16037: Videos # Videos
    4ae5640b-0279-4ec6-8483-4cbc2e70162f: French # French
    4bea2fe6-6d98-43ed-ad07-2093144466c2: 'Image Style' # Image Style
    4ccd212f-f187-4104-9778-6be34062a2ee: 'CSS Value' # CSS Value
    4d03de75-aa66-4abe-b1fa-d64a63180f56: 'Header Styles' # Header Styles
    4dbc2f01-427e-448b-9ab9-c984e7fed203: 'Image Styles' # Image Styles
    4df2c05e-5f9b-489a-8b50-44f46a5808d4: Section # Section
    5b5bc007-d075-40b1-b789-5bc99e44728b: 'Embed Styles' # Embed Styles
    5cf5d097-d2db-4940-a4ff-fbe67d3faa9c: Vizy # Vizy
    05e8d0cd-133d-46ea-9f26-78565c2fbfd2: 'CSS Percentage' # CSS Percentage
    7b829450-f844-46f2-b87e-40726264b3ac: 'Size with Maximum' # Size with Maximum
    7ce7f3c5-4dee-44fa-8fed-763727951fc7: 'Image Transforms' # Image Transforms
    7d3ca87b-1d61-475b-b456-829caaf24af6: 'SEO Description' # SEO Description
    7d269788-0013-491f-852e-59020092f1dc: 'Box Shadow' # Box Shadow
    7f0ee02b-7a89-4f20-8f27-857fea26d029: Embed # Embed
    7f9617f0-906b-48a6-bc00-2fb4428e867d: 'Text Style' # Text Style
    7fc8fbd1-484c-4554-87b7-c8c9a1fa5735: Borders # Borders
    8ab575be-24db-4bc4-94e0-16c154fef751: 'Teaser Subtitle' # Teaser Subtitle
    8b024b3a-a251-4d42-ba35-60c420dae139: Parallax # Parallax
    08b4f32a-6338-4ba3-ac83-953d14d0b1a5: 'Teaser Description' # Teaser Description
    8c245525-b1bc-410b-87d7-4edb980bb03e: SVG # SVG
    9c67a732-11a4-4295-889b-8bda08e11229: 'Button Style' # Button Style
    9c985e1d-0cb3-4323-a766-83e0e80bfa2f: 'Image Transform' # Image Transform
    9c500934-248b-481a-94db-81f3d115ee78: 'Teaser Type' # Teaser Type
    11b4ce4e-226a-4417-875e-c1836f08d249: Code # Code
    012edf8e-cf41-468c-a31e-cf6c448664d1: Sizes # Sizes
    14c62909-a941-483e-bf99-ec024c5c00c6: 'Navigation Style' # Navigation Style
    18abf74b-579b-47cc-824b-3e0e81fffc29: Border # Border
    19d9c017-e51c-4628-adf8-0d7e960d4945: Widths # Widths
    21a6a9f1-a326-49b6-8167-dd528d0ae5c7: Fonts # Fonts
    30cd0bd7-34bf-4b44-a015-1e89685fc346: Job # Job
    37c38255-bde8-4488-a0b3-2091f66a2e34: 'Card Style' # Card Style
    39f69766-88e8-42fb-8bd2-ab9762ea27d1: 'Text Shadow' # Text Shadow
    40c196d5-5717-4b27-b060-e14a1110ce48: 'Grid Styles' # Grid Styles
    46b6d367-22f2-4846-87c6-40dd4f5e4817: 'Navigation Style' # Navigation Style
    46bd87d1-ef89-47dd-979f-14814250fc43: Header # Header
    46ca365d-2e11-483c-aecb-3b8ef4225c20: Color # Color
    057cab16-58fe-44f1-ae88-926a5c4bb71d: Sizes # Sizes
    62e3d07a-eb53-413a-9004-c167c3c25331: 'Breakout Container' # Breakout Container
    65fd33c2-b449-49d2-be10-f59790984699: Page # Page
    71c911ce-7c34-4a67-b005-ee0a2abb1534: Buttons # Buttons
    71efa591-925f-4ee6-8c7c-56021846946b: 'Box Shadow' # Box Shadow
    76a3687b-2448-4f8a-8145-3a34a87a0d0d: Transform # Transform
    80a18959-aaf1-432e-b851-f4dd87013245: Spacer # Spacer
    83d4af74-427e-4a83-9b85-2877648e6335: 'Font Size' # Font Size
    84a79a1f-4663-4c63-93bd-d0b4a778bd8b: 'Blocks Two' # Blocks Two
    91a274df-c45a-4c5c-a80f-f5e0f37ea220: English # English
    94c778c9-b25c-415d-b804-6cca2227e769: Favicon # Favicon
    94d2db3d-1e0a-48e5-883a-8f323343109a: Quote # Quote
    99ae9b6b-3677-4c63-92e5-1ceae5f22b34: 'Grid Style' # Grid Style
    105a519c-7bf4-457b-8d9f-8a8973be192d: 'Button Styles' # Button Styles
    132deb32-285e-4bf9-bf57-fd37fb131d52: 'SEO Title' # SEO Title
    140b74fb-9bf9-418a-819e-e345b48a3de0: SVGs # SVGs
    251f107a-ad70-4689-927b-08d009a6eefe: 'Card Style' # Card Style
    288d53a6-2777-4e7b-b03d-a47e303918b0: Homepage # Homepage
    448d9439-b58e-40f7-bef2-bb31ec581b39: 'Teaser Title' # Teaser Title
    724bc72c-650f-481d-9519-4663b2cdcdc5: 'SEO Image' # SEO Image
    845fc630-074a-4d11-a947-2ff826592dbb: Scripts # Scripts
    983e4658-ef41-46e4-9a1a-538b7eed6410: Swooms # Swooms
    2263b110-cdaa-4f56-8069-61167defbf77: 'Job Category' # Job Category
    2824b1de-d2ec-479a-9960-4ecab26f0e10: Description # Description
    2924db5d-4a97-4f0d-928c-aadaa4b9a6ae: Border # Border
    8433c4f1-2fc5-4f46-9085-b0a3880075bf: 'Button Style' # Button Style
    23731b03-d214-4d55-b32e-d84410fd2c9a: Columns # Columns
    41525efe-c6b3-445d-8503-8b911ffd53f4: 'Box Shadows' # Box Shadows
    75555ad2-2588-45fc-bf45-ff4ac33d9223: 'Navigation Style' # Navigation Style
    95952cf5-089a-4707-8019-8d1affcc8ebd: 'Text Shadows' # Text Shadows
    114751db-927d-45b6-885a-7fb1dfb7dd42: SVG # SVG
    478974c9-29d7-4fdd-947b-182cc66e71e1: Columns # Columns
    644288ca-0897-4768-9143-599523114151: Image # Image
    725099a4-75b3-4fab-b231-506e287763b6: 'Header Style' # Header Style
    781229a2-72da-48ac-9c43-c021a8c9f905: Buttons # Buttons
    964547e1-7d37-4532-ac4b-1515d1f7ab25: 'Media Style' # Media Style
    16142051-cb6e-47de-910b-0cfab8d65ab9: Files # Files
    35944086-a010-4294-acf3-ea975a7a90e9: Heights # Heights
    67010891-ffdd-4731-be8d-b37430702274: 'Image Carousel' # Image Carousel
    95374682-d92f-4ede-a03e-2ae0f789de96: Sections # Sections
    98779659-20c3-43dc-8554-bcaf11b34121: Width # Width
    a1b07457-3993-4b88-8580-91bab21ae94d: 'Teaser Image' # Teaser Image
    a5abc42f-67d3-4699-8d9f-9390b1581fec: 'Single Line Text' # Single Line Text
    a7a1f507-e722-48b3-b267-dc2cefdbf687: Fallbacks # Fallbacks
    a8b0609e-50f6-4851-bb99-c90effa6a281: Size # Size
    a66f376e-928d-4aa9-8947-c44a937f0061: Images # Images
    aa5bf992-8106-48a8-b695-99d4865766dc: Column # Column
    acc74012-a823-4e7b-a287-c25a6d84e4d2: 'Section Style' # Section Style
    ad842226-225a-4314-ba7c-fde9f62d5a92: 'Section Styles' # Section Styles
    add35fac-196b-4e98-bbcf-6e51624635dd: Font # Font
    b4f6fcd1-afe1-43a5-9796-b4c14a8e1e8c: Hyper # Hyper
    b16a22a8-d025-4f95-8f78-65ae8fb43915: 'Teaser Grid' # Teaser Grid
    b36da1d7-c8f2-4fc1-8169-64f0259c3ff8: Blocks # Blocks
    b58f71db-4d20-42f0-b575-499d43f198e4: Images # Images
    b75fb7db-feaf-41e2-bd1b-1ed8f10c448d: 'Font Sizes' # Font Sizes
    b532db96-1b6d-46ac-ad3a-fcc04f03508d: 'Image Grid' # Image Grid
    b786b486-877d-4664-97b8-67cf1e61fd3f: Subtitle # Subtitle
    bb1f3c19-1b8e-42f6-b475-618288cc4cf0: Align # Align
    bc4c95ee-ea1d-49b1-b560-e552bb125d3c: 'Text Header' # Text Header
    bd71c99b-cab3-433b-b441-769f02e33489: Vizy2 # Vizy2
    be106aa1-239b-4c1f-8a70-1c3f8953a059: Padding # Padding
    c0f024f3-5f2e-4375-9bc3-4ddf00672698: 'Color or Gradient' # Color or Gradient
    c3db44e8-6258-414c-a54e-c7e7757af681: Spacing # Spacing
    c4e53567-37a4-4775-9533-a7c3fed0616b: Height # Height
    c5b0c0b4-eebd-40ce-9c23-d83314bde22b: 'Header Style' # Header Style
    c7d4a26c-9c7c-479f-83ee-43bb794e7f09: 'Teaser Style' # Teaser Style
    c596aab2-0aa4-43b8-90d0-b737638e574a: 'Text Style' # Text Style
    c26163fd-ba8f-461b-91c0-adfa0cea4002: Paddings # Paddings
    c916885d-52fe-4bf4-bb8d-51f2dc20fd72: Align # Align
    c947061b-185f-4d72-a4d5-75653e7ae376: News # News
    cf562ba0-a46c-4d60-9b25-973615abb5f3: Buttons # Buttons
    d1abc506-ac19-4af8-8f15-78375f5c2146: Colors # Colors
    d2a1ad88-43c3-467b-9c9f-9feffc297bdc: 'CSS Color' # CSS Color
    d4e3415e-4d5d-4ed9-9a51-b4dde8c1b234: Button # Button
    d7b56266-4f04-4253-9d25-518e44c21fe5: Users # Users
    d9bf798a-38e4-4392-8043-76f1f788f6cd: Width # Width
    d13f96ff-676f-4142-a858-0fdb7e8284b0: General # General
    d94c7961-bef3-4664-b613-35ca535b8616: Text # Text
    d266f9e0-936a-4eb5-ab27-b3095a2aa438: Page # Page
    d2104d73-5906-4d3b-ae8b-8b2da611ba67: 'Embed Style' # Embed Style
    dcaa3d21-0e8e-48b2-a6cd-6b54c7a93a6b: News # News
    dd420bf9-89a8-4dc8-9c69-1a6807f15c24: SEO # SEO
    de82a6f1-fb0d-4217-be76-9725190726c9: Socials # Socials
    de82119c-a2c2-4375-83c7-3dbc7a32e3f4: Gradient # Gradient
    e53cb2c7-8e97-48df-93ed-a828e0cf7205: Anchor # Anchor
    e79c8b71-9307-4b7d-9912-1f3b5798f777: Swoom # Swoom
    e89a2125-2b8b-4ed1-a1d2-ae65ede47a55: 'Blocks One' # Blocks One
    e69270f5-1926-4710-92f9-453f25ef85bd: 'Image Transforms' # Image Transforms
    ecd562f1-7b01-41c4-a81c-a9f51feecb63: 'Section Style' # Section Style
    ee8b0f5d-8009-4198-a0dd-e04dc285829e: Dutch # Dutch
    f3bc6c07-6292-443a-b9e4-1e850d8242d5: Account # Account
    f4caa8b3-8f76-4daf-b9b9-e64adec06343: Jobs # Jobs
    f320dfe4-16f5-4ff9-805b-8b3077564de5: 'Card Styles' # Card Styles
    f335d354-3103-458d-a5f4-f54b334ae58a: Grid # Grid
    fbb85c82-e797-4335-8964-684a851c7df4: 'News Category' # News Category
    fcd0994a-3cc1-414a-aeee-a5f096c2af7a: Gradients # Gradients
    fcf7bb35-82b3-4a74-b96e-55636339ede0: Transform # Transform
plugins:
  blitz:
    edition: standard
    enabled: true
    licenseKey: 7VIAXLNFJ6INL8JW971OAZL8
    schemaVersion: 5.11.0
    settings:
      apiKey: ''
      cacheControlHeader: 'public, s-maxage=********, max-age=0'
      cacheDuration: 0
      cacheElementQueries: true
      cacheElements: true
      cacheGeneratorSettings:
        __assoc__:
          -
            - concurrency
            - '5'
      cacheGeneratorType: putyourlightson\blitz\drivers\generators\HttpGenerator
      cachePurgerType: putyourlightson\blitz\drivers\purgers\DummyPurger
      cacheStorageSettings:
        __assoc__:
          -
            - folderPath
            - '@webroot/cache/blitz'
          -
            - createGzipFiles
            - '1'
      cacheStorageType: putyourlightson\blitz\drivers\storage\FileStorage
      cachingEnabled: ''
      customSiteUris: ''
      debug: false
      deployerType: putyourlightson\blitz\drivers\deployers\DummyDeployer
      driverJobPriority: 100
      excludedQueryStringParams:
        -
          enabled: true
          queryStringParam: gclid
          siteId: ''
        -
          enabled: true
          queryStringParam: fbclid
          siteId: ''
      excludedUriPatterns: ''
      includedUriPatterns:
        -
          enabled: true
          siteId: ''
          uriPattern: '.*'
      injectScriptEvent: DOMContentLoaded
      integrations:
        - putyourlightson\blitz\drivers\integrations\CommerceIntegration
        - putyourlightson\blitz\drivers\integrations\FeedMeIntegration
        - putyourlightson\blitz\drivers\integrations\SeomaticIntegration
      maxUriLength: 2048
      mutexTimeout: 1
      outputComments: false
      queryStringCaching: '0'
      refreshCacheAutomaticallyForGlobals: true
      refreshCacheJobPriority: 10
      refreshCacheWhenElementSavedNotLive: false
      refreshCacheWhenElementSavedUnchanged: false
      refreshMode: 3
      sendPoweredByHeader: true
  element-api:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  expanded-singles:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
    settings:
      expandSingles: '1'
      redirectToEntry: '1'
  google-cloud:
    edition: standard
    enabled: true
    schemaVersion: '2.0'
  hyper:
    edition: standard
    enabled: true
    licenseKey: 5Q5L7LTH530CJZQUL18H3R34
    schemaVersion: 1.0.0
  mailgun:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  navigation:
    edition: standard
    enabled: true
    licenseKey: 9TIADACG6ELB2Q3B4C6NXB1W
    schemaVersion: 2.1.0
  prefetch:
    edition: standard
    enabled: true
    schemaVersion: 1.0.0
  seo:
    edition: standard
    enabled: true
    schemaVersion: 3.2.0
    settings:
      description: ''
      facebookAppId: ''
      metaTemplate: layout/seo.html
      removeAlternateUrls: ''
      robots:
        - ''
        - ''
        - ''
        - ''
        - ''
        - ''
      robotsTxt: "{# Sitemap URL #}\r\nSitemap: {{ url(seo.sitemapName ~ '.xml') }}\r\n\r\n{# Disallows #}\r\n{% if craft.app.config.env != 'production' %}\r\n\r\n{# Disallow access to everything when NOT in production #}\r\nUser-agent: *\r\nDisallow: /\r\n\r\n{% else %}\r\n\r\n{# Disallow access to cpresources/ when live #}\r\nUser-agent: *\r\nDisallow: /cpresources/\r\n\r\n{% endif %}"
      sitemapLimit: '1000'
      sitemapName: sitemap
      socialImage: ''
      title:
        -
          __assoc__:
            -
              - key
              - 33b
            -
              - locked
              - '1'
            -
              - template
              - "{{ getenv('SITE_NAME') }}"
      titleSuffix: null
      twitterHandle: ''
  vizy:
    edition: standard
    enabled: true
    licenseKey: P9M9GI4YTO9L3QRHXTEFNB7K
    schemaVersion: 0.9.0
system:
  edition: pro
  live: true
  name: $SITE_NAME
  schemaVersion: 5.8.0.1
  timeZone: Europe/Brussels
