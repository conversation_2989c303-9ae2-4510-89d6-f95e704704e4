import React, { FC } from 'react';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Title, Tabs } from '@mantine/core';

import { DiagramSidebar } from 'components/diagram';

import { GenericComponentCategoryGrid } from './GenericComponentCategoryGrid';
import { TeamComponents } from './TeamComponents';
import { ProductCatalog } from './ProductCatalog';
import { useCurrentUser } from 'hooks/use-current-user';
import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';
import { SidebarType, state as sidebarState } from 'components/diagram/state/sidebar';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { FeatureKey, useFeatureAccess } from 'hooks/use-feature-access';

const AddComponentInstance: FC = () => {
    const user = useCurrentUser();
    const privateLibraryEnabled = useFeatureAccess(FeatureKey.TEAM_COMPONENT_LIBRARY);

    const { type: currentSidebarType } = useSnapshot(sidebarState);

    const renderContent = () => {
        switch (currentSidebarType) {
            case SidebarType.ADD_GENERIC:
                return <GenericComponentCategoryGrid />;
            case SidebarType.ADD_TEAM:
                return !privateLibraryEnabled ? (
                    <SubscriptionOnboardingMessage>
                        Want to save components for reuse?
                    </SubscriptionOnboardingMessage>
                ) : (
                    <TeamComponents />
                );
            case SidebarType.ADD_CATALOG:
                return <ProductCatalog />;
            default:
                return <GenericComponentCategoryGrid />;
        }
    };

    return (
        <>
            <DiagramSidebar.Header>
                <Title>Add New Component</Title>
            </DiagramSidebar.Header>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section>
                {renderContent()}
            </DiagramSidebar.Section>
        </>
    );
};

export { AddComponentInstance };
