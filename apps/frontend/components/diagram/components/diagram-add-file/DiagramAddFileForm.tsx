import React, { FC, SetStateAction, Dispatch } from 'react';

import { Box, Button, Stack } from '@mantine/core';
import { Dropzone, IMAGE_MIME_TYPE, PDF_MIME_TYPE } from '@mantine/dropzone';
import { IoDocumentOutline } from 'react-icons/io5';

import { ComponentFileTypes } from '@repo/dcide-component-models';

import { Form } from 'components/forms/Form';
import { SelectField } from 'components/forms/fields/SelectField';
import { TextField } from 'components/forms/fields/TextField';

import { z } from 'zod';

import { FileService as GlobalFileService } from 'services/FileService';

import cx from './DiagramAddFileModal.module.css';

export type DiagramAddFileFormValues = {
    metadata: {
        name: string;
        type: string;
    }[];
};

const DiagramAddFileForm = ({
    files,
    onSubmit,
    setFiles,
    loading,
    onCancel,
}: {
    files: File[];
    onSubmit: (values: DiagramAddFileFormValues) => void;
    setFiles: Dispatch<SetStateAction<File[]>>;
    loading: boolean;
    onCancel?: () => void;
}) => {
    const defaultValues: DiagramAddFileFormValues = {
        metadata: files.map((file) => {
            const { name } = GlobalFileService.getNameAndExtension(file);

            return {
                name: name,
                type: 'other',
            };
        }),
    };

    return (
        <Form<DiagramAddFileFormValues>
            defaultValues={defaultValues}
            onSubmit={onSubmit}
            zodSchema={z.object({
                metadata: z
                    .object({
                        name: z.string().min(5, {
                            message: 'Name must contain at least 5 characters',
                        }),
                        type: z.string(),
                    })
                    .array(),
            })}
        >
            {files.length ? (
                <Box>
                    {files.map((file, index) => (
                        <FormFile index={index} key={file.name} />
                    ))}
                </Box>
            ) : (
                <Dropzone
                    className={cx.dropzone}
                    onDrop={setFiles}
                    accept={[...IMAGE_MIME_TYPE, ...PDF_MIME_TYPE]}
                    multiple={false}
                >
                    <Box className={cx.dropzoneContent}>
                        <IoDocumentOutline />
                        Drop your files
                    </Box>
                </Dropzone>
            )}
            <Box className={cx.actions}>
                {files.length > 0 && (
                    <Button size="xs" type="submit" loading={loading}>
                        Upload
                    </Button>
                )}
                {onCancel && (
                    <Button size="xs" variant="subtle" onClick={onCancel}>
                        Cancel
                    </Button>
                )}
            </Box>
        </Form>
    );
};

const FormFile: FC<{ index: number }> = ({ index }) => {
    return (
        <Stack gap="xs">
            <TextField name={`metadata.${index}.name`} label="Name" required />
            <SelectField name={`metadata.${index}.type`} label="Type" data={ComponentFileTypes.options} required />
        </Stack>
    );
};

export { DiagramAddFileForm };
