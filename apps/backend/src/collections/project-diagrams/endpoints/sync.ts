import type { Endpoint } from 'payload';

import { after } from 'next/server';
import { z } from 'zod';

import { Pointer, SyncEngineServer, Transaction } from '@repo/dcide-sync-engine';
import { ProjectDesignDiagram } from '@/payload-types';
import { getId, getRequestUser, handleEndpointError } from '../../../helpers';
import { getTransactionsForDiagram } from '../../../helpers/get-transactions-for-diagram';
import { performDatabaseTransaction } from '../../../helpers/perform-database-transaction';

import { BadRequestError, ForbiddenError } from '../../../responses/errors';

import { applyDiagramTransaction } from '@/collections/project-diagrams/helpers/apply-diagram-transaction';
import { TransactionService } from '@/services/TransactionService';

type State = Pick<ProjectDesignDiagram, 'connections' | 'componentInstances' | 'groups' | 'images'>;

export const sync: Endpoint = {
    path: '/:diagramId/sync',
    method: 'post',
    handler: async (request) => {
        try {
            const user = await getRequestUser(request);

            if (!user) {
                throw new ForbiddenError();
            }

            const start = Date.now();

            const { routeParams, payload } = request;

            const { diagramId } = z
                .object({
                    diagramId: z.string(),
                })
                .parse(routeParams);

            const { transactions, pointer } = (await request.json!()) as {
                transactions: Transaction[];
                pointer: Pointer;
            };

            if (!transactionsHaveUniqueDate(transactions)) {
                throw new BadRequestError('Transactions must have unique dates!');
            }

            const diagram = await payload.findByID({
                collection: 'projectDesignDiagrams',
                id: diagramId,
                depth: 0,
            });

            console.log('Sync', diagramId);
            console.log('Get Diagram', Date.now() - start, 'ms');

            if (!diagram.id) {
                throw new Error(`Diagram ${diagramId} not found`);
            }

            const server = new SyncEngineServer<State>({
                getState: () => ({
                    connections: diagram.connections,
                    componentInstances: diagram.componentInstances,
                    groups: diagram.groups,
                    images: diagram.images,
                    textareas: diagram.textareas,
                    voltageClasses: diagram.voltageClasses,
                }),
                setState: async (_, transactions) => {
                    console.time('Sync: Set State');
                    await performDatabaseTransaction(async () => {
                        for (const transaction of transactions) {
                            await applyDiagramTransaction(payload, diagramId, transaction, true, user);
                        }
                    });
                    console.timeEnd('Sync: Set State');
                },
                getTransactions: async (pointer) => {
                    const transactions = await getTransactionsForDiagram(request, diagramId, 'ascending');

                    const indexOfPointer = transactions.findIndex((transaction) => transaction.id === pointer?.id);

                    if (indexOfPointer === -1 || indexOfPointer === transactions.length - 1) {
                        return {
                            transactions: [],
                            filtered: true,
                        };
                    }

                    const transactionsFromPointer = transactions.slice(indexOfPointer + 1);

                    const enrichedTransactions: Transaction[] = transactionsFromPointer.map((transaction) => {
                        return {
                            ...transaction,
                            status: 'validatedByServer',
                        } as Transaction;
                    });

                    return {
                        transactions: enrichedTransactions,
                        filtered: true,
                    };
                },
            });

            const { transactionsToReturn, transactionsToBroadcast } = await server.pull(transactions, pointer);

            if (transactionsToBroadcast.length > 0) {
                after(async () => {
                    await TransactionService.broadcast(diagramId, transactionsToBroadcast);
                });
            }

            const projectId = getId(diagram.project);
            if (projectId) {
                after(
                    payload.db.collections.projects.updateOne(
                        { _id: projectId },
                        {
                            $set: {
                                updatedAt: new Date().toISOString(),
                                updatedBy: user.id,
                            },
                        },
                    ),
                );
            }

            console.log('Return response', Date.now() - start, 'ms');
            return Response.json({
                success: true,
                transactions: transactionsToReturn,
            });
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

const transactionsHaveUniqueDate = (transactions: Transaction[]): boolean => {
    const dates: string[] = [];

    for (const transaction of transactions) {
        if (dates.includes(transaction.date)) return false;

        dates.push(transaction.date);
    }

    return true;
};
