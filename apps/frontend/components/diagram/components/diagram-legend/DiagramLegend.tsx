import React, { FC } from 'react';
import { Diagram, VoltageType } from '@repo/dcide-component-models';

import { Box, UnstyledButton } from '@mantine/core';
import { TbX } from 'react-icons/tb';

import { ColorSwatchWithColorPicker } from 'components/color/ColorSwatchWithColorPicker';

import { useVoltageClasses } from 'components/diagram/hooks/use-voltage-classes';

import { diagram } from 'components/diagram/state/diagram';

import cx from './DiagramLegend.module.css';

const DiagramLegend: FC<{
    onRequestClose: () => void;
}> = ({ onRequestClose }) => {
    const voltageClasses = useVoltageClasses();

    return (
        <Box className={cx.legend}>
            <UnstyledButton className={cx.close} onClick={onRequestClose}>
                <TbX />
            </UnstyledButton>
            <Box className={cx.items}>
                <Box className={cx.title}>DC</Box>
                {voltageClasses.DC.map((voltageClass, index) => (
                    <VoltageClass voltageType="DC" voltageClass={voltageClass} index={index} key={voltageClass.label} />
                ))}
            </Box>
            <Box className={cx.items}>
                <Box className={cx.title}>AC</Box>
                {voltageClasses.AC.map((voltageClass, index) => (
                    <VoltageClass voltageType="AC" voltageClass={voltageClass} index={index} key={voltageClass.label} />
                ))}
            </Box>
        </Box>
    );
};

const VoltageClass: FC<{
    voltageType: VoltageType;
    index: number;
    voltageClass: Diagram['voltageClasses'][VoltageType][number];
}> = ({ voltageType, index, voltageClass }) => {
    const updateColor = (color: string) => {
        diagram.voltageClasses[voltageType][index].color = color;
    };

    return (
        <Box className={cx.item}>
            <ColorSwatchWithColorPicker color={voltageClass.color} onChange={updateColor} />
            <Box>{voltageClass.label}</Box>
        </Box>
    );
};

export { DiagramLegend };
