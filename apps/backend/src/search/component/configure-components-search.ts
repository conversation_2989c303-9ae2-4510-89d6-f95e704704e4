import type { Payload } from 'payload';
import { config } from '@/config';
import { ComponentSearchState } from '@/search/types';
import { addQueryFilters } from './query-filters';
import { addPermissionsFilters } from './permissions-filters';
import { SearchIndex } from '@repo/dcide-component-models';
import { addSortStages } from './add-sort-stages';
import { addPostSortStages } from './post-sort-operations';
import { getId } from '@/helpers/get-id';
import {
    getSynonymsStages,
    addTextSearch,
    addVectorSearch,
    setTextSearchFlags,
    getTextStages,
} from '@/search/helpers/text-and-vector-search-stages';
import { addPortsStages } from './port-stages/add-ports-stages';

export const configureComponentsSearch = async (search: ComponentSearchState, payload: Payload) => {
    // only use exact match /w components when there is a type filter
    if ('type' in search.query) {
        search.exactMatch = true;
    }

    search.query.manufacturersForActiveEvents = search.query.manufacturer
        ? undefined
        : await getManufacturersForActiveEvents(payload);

    setTextSearchFlags(search);

    await addSearchStages(search);
    await addQueryFilters(payload, search);
    addPermissionsFilters(search);
    addPortsStages(search, search.query);

    addSortStages(search);
    addPostSortStages(search);
};

const getManufacturersForActiveEvents = async (payload: Payload) => {
    const activeEvents = await payload.find({ collection: 'events', where: { isActive: { equals: true } } });

    const manufacturerIds = activeEvents.docs.flatMap(
        (event) => event.companies?.map(({ company }) => getId(company)) ?? [],
    );

    return manufacturerIds.length > 0 ? manufacturerIds : undefined;
};

const addSearchStages = async (search: ComponentSearchState) => {
    if (search.hasVectorSearch) {
        await addVectorSearch(search, SearchIndex.COMPONENT_INDEX);
    }

    if (search.hasTextSearch) {
        const textQuery = search.query.search!;
        addTextSearch(SearchIndex.FULL_TEXT_INDEX, search, [
            ...getTextStages(textQuery, 'name', config.search.textSearch.nameBoost),
            ...getTextStages(textQuery, 'productIdentifier', config.search.textSearch.productIdentifierBoost),
            ...getTextStages(textQuery, 'productSeries', config.search.textSearch.productSeriesBoost),
            ...getSynonymsStages(textQuery, 'embeddingText', config.search.textSearch.embeddingTextBoost),
        ]);
    }
};
