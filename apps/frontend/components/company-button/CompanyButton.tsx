import { Box, Flex, Text, Tooltip, UnstyledButton } from '@mantine/core';

import Link from 'next/link';

import { CompanyProfile } from '@repo/dcide-component-models';

import { CompanyLogo } from 'components/company-logo';

import cx from './CompanyButton.module.css';

const CompanyButton = ({
    label,
    href,
    icon,
    company,
    tooltip,
    onClick,
    selected,
}: {
    label?: string;
    href?: string;
    icon?: React.ReactNode;
    company?: CompanyProfile;
    tooltip?: string;
    onClick?: () => void;
    selected?: boolean;
}) => {
    const content = (
        <>
            {icon}
            {label && <Text className={cx.label}>{label}</Text>}
            {company && (
                <Flex align="center" className={cx.logo}>
                    <CompanyLogo logos={company.logos} fallback={<Box className={cx.fallback}>{company.name}</Box>} />
                </Flex>
            )}
        </>
    );

    if (href) {
        return (
            <Tooltip label={tooltip} disabled={!tooltip} position="bottom">
                <Box className={cx.button} component={Link} href={href} data-selected={selected}>
                    {content}
                </Box>
            </Tooltip>
        );
    }

    return (
        <Tooltip label={tooltip} disabled={!tooltip} position="bottom">
            <UnstyledButton className={cx.button} onClick={onClick} data-selected={selected}>
                {content}
            </UnstyledButton>
        </Tooltip>
    );
};

export { CompanyButton };
