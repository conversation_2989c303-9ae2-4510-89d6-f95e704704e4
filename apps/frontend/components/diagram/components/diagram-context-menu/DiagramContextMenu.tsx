import React, { <PERSON> } from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box, Menu } from '@mantine/core';

import { TbBackspace, TbClipboardList, TbCopy, TbCut, TbSearch } from 'react-icons/tb';
import { MdRedo, MdUndo } from 'react-icons/md';
import { IoTrashOutline } from 'react-icons/io5';

import { CopyPasteService } from 'components/diagram/services/CopyPasteService';
import { ContextMenuService } from 'components/diagram/services/ContextMenuService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { ProductSearchService } from 'components/diagram/services/ProductSearchService';
import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';

import { useSelection } from 'components/diagram/hooks/selection/use-selection';
import { useDeleteSelection } from 'components/diagram/hooks/selection/use-delete-selection';

import { historyState } from 'components/diagram/state/history';
import { DiagramContextMenuType, state as contextMenuState } from 'components/diagram/state/context-menu';

import cx from 'components/context-menu/ContextMenu.module.css';

const DiagramContextMenu = () => {
    const { position } = useSnapshot(contextMenuState);

    return position ? <DiagramContextMenuConditional position={position} /> : null;
};

const DiagramContextMenuConditional: FC<{
    position: typeof contextMenuState.position;
}> = ({ position }) => {
    const { selection } = useSelection();
    const deleteSelection = useDeleteSelection();

    const { undoCount, redoCount } = useSnapshot(historyState);

    const { type, componentInstance } = useSnapshot(contextMenuState);

    let singleSelectedComponentInstance = null;

    if (selection.length === 1 && selection[0].type === 'component-instance') {
        singleSelectedComponentInstance = selection[0].id;
    }

    return (
        <Menu
            opened={true}
            onChange={() => {
                ContextMenuService.closeContextMenu();
            }}
            classNames={{
                dropdown: cx.dropdown,
                item: cx.item,
                itemSection: cx.itemSection,
            }}
            position="right-start"
            offset={0}
        >
            <Menu.Target>
                <Box
                    style={{
                        position: 'fixed',

                        left: position!.x + 15,
                        top: position!.y - 15,

                        width: 1,
                        height: 1,
                    }}
                />
            </Menu.Target>
            <Menu.Dropdown>
                {type === DiagramContextMenuType.COMPONENT_INSTANCE && componentInstance && (
                    <React.Fragment>
                        <Menu.Item
                            onClick={() => {
                                ProductSearchService.openProductSearchFor(componentInstance);
                            }}
                            leftSection={<TbSearch />}
                        >
                            Search products
                        </Menu.Item>
                        <Menu.Divider />
                    </React.Fragment>
                )}
                <Menu.Item
                    onClick={() => {
                        CopyPasteService.cut();
                        DiagramSyncService.save();
                    }}
                    disabled={!selection.length}
                    leftSection={<TbCut />}
                    rightSection={<div>⌘X</div>}
                >
                    Cut
                </Menu.Item>
                <Menu.Item
                    onClick={() => {
                        CopyPasteService.copy();
                    }}
                    disabled={!selection.length}
                    leftSection={<TbCopy />}
                    rightSection={<div>⌘C</div>}
                >
                    Copy
                </Menu.Item>
                <Menu.Item
                    onClick={() => {
                        if (singleSelectedComponentInstance) {
                            ComponentInstanceService.copySpecifications(singleSelectedComponentInstance);
                        }
                    }}
                    disabled={!singleSelectedComponentInstance}
                    leftSection={<TbCopy />}
                >
                    Copy specifications
                </Menu.Item>
                <Menu.Item
                    onClick={() => {
                        CopyPasteService.activatePastingMode(position ?? undefined);
                    }}
                    leftSection={<TbClipboardList />}
                    rightSection={<div>⌘V</div>}
                >
                    Paste
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item
                    onClick={() => {
                        DiagramSyncService.undo();
                    }}
                    leftSection={<MdUndo style={{ strokeWidth: 0 }} />}
                    rightSection={<div>⌘Z</div>}
                    disabled={undoCount === 0}
                >
                    Undo
                </Menu.Item>
                <Menu.Item
                    onClick={() => {
                        DiagramSyncService.redo();
                    }}
                    leftSection={<MdRedo style={{ strokeWidth: 0 }} />}
                    rightSection={<div>⌘Y</div>}
                    disabled={redoCount === 0}
                >
                    Redo
                </Menu.Item>
                <Menu.Divider />
                <Menu.Item
                    onClick={deleteSelection}
                    disabled={selection.length === 0}
                    leftSection={<IoTrashOutline />}
                    rightSection={<TbBackspace />}
                >
                    Delete
                </Menu.Item>
            </Menu.Dropdown>
        </Menu>
    );
};

export { DiagramContextMenu };
