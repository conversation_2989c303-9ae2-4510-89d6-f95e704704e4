import React, { FC } from 'react';

import { Box, Stack } from '@mantine/core';
import { IoChevronBack } from 'react-icons/io5';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { DiagramCommentActions } from 'components/diagram/components/diagram-comments/DiagramCommentActions';
import { DiagramCommentReply } from 'components/diagram/components/diagram-comments/DiagramComment.Reply';
import { DiagramCommentThread } from 'components/diagram/components/diagram-comments/DiagramComment.Thread';

import { CommentService } from 'components/diagram/services/CommentService';

import { useActiveCanvasComment } from 'components/diagram/hooks/use-active-canvas-comment';
import { useDiagramComments } from 'components/diagram/hooks/use-diagram-comments';

import classes from './CommentsSidebar.module.css';

const CommentsSidebarComment: FC = () => {
    const comments = useDiagramComments();
    const activeCommentId = useActiveCanvasComment();
    const comment = comments.find((comment) => comment.id === activeCommentId);

    return comment ? (
        <React.Fragment>
            <DiagramSidebar.SimpleHeader
                leftSection={
                    <DiagramSidebar.Button
                        onClick={() => {
                            CommentService.deactivateCanvasComment();
                            CommentService.deactivateSidebarComment();
                        }}
                    >
                        <IoChevronBack />
                        <span>All comments</span>
                    </DiagramSidebar.Button>
                }
                rightSection={
                    <DiagramSidebar.ButtonGroup>
                        <DiagramCommentActions.Delete comment={comment}>
                            {({ Icon, handle }) => (
                                <DiagramSidebar.Button onClick={handle}>
                                    <Icon />
                                </DiagramSidebar.Button>
                            )}
                        </DiagramCommentActions.Delete>
                        <DiagramCommentActions.Resolve comment={comment}>
                            {({ Icon, handle }) => (
                                <DiagramSidebar.Button onClick={handle}>
                                    <Icon />
                                </DiagramSidebar.Button>
                            )}
                        </DiagramCommentActions.Resolve>
                    </DiagramSidebar.ButtonGroup>
                }
            ></DiagramSidebar.SimpleHeader>
            <Box className={classes.content}>
                <Stack gap="md">
                    <DiagramCommentThread comment={comment} />
                    <DiagramCommentReply comment={comment} />
                </Stack>
            </Box>
        </React.Fragment>
    ) : null;
};

export { CommentsSidebarComment };
