import React, { FC } from 'react';

import Link from 'next/link';

import { Box, BoxProps, Tooltip, Transition, UnstyledButton } from '@mantine/core';
import { IoCheckmark } from 'react-icons/io5';

import cx from './ChecklistItem.module.css';

export enum ChecklistItemIconPosition {
    N = 'n',
    S = 's',
    E = 'e',
    W = 'w',
    NE = 'ne',
    NW = 'nw',
    SE = 'se',
    SW = 'sw',
}

export type ChecklistItemType = {
    value: string;
    label: string;
    href?: string;
    onClick?: () => void;
    icon?: React.ReactNode;
    iconPosition?: ChecklistItemIconPosition;
};

type Props = {
    completed?: boolean;
    handleComplete?: (value: any) => void;
} & ChecklistItemType;

const ChecklistItem: FC<Props> & {
    Finish: typeof ChecklistItemFinish;
    Wrapper: typeof ChecklistItemWrapper;
} = ({ value, label, href, onClick, icon, iconPosition, completed, handleComplete }) => {
    const buttonProps = {
        'data-button': true,
        'data-type': value,
        'data-completed': completed,
        'data-icon-position': iconPosition,
        'className': cx.item,
    };

    const content = (
        <>
            <span className={cx.icon}>{icon}</span>
            <span className={cx.label}> {label}</span>
            {handleComplete ? (
                <Tooltip label="Mark as done">
                    <span
                        data-button
                        className={cx.checkbox}
                        onClick={(event) => {
                            event.preventDefault();
                            event.stopPropagation();

                            handleComplete(value);
                        }}
                    >
                        {<IoCheckmark size={16} />}
                    </span>
                </Tooltip>
            ) : (
                <span className={cx.checkbox}>{<IoCheckmark size={16} />}</span>
            )}
        </>
    );

    return href ? (
        <UnstyledButton {...buttonProps} component={Link} href={href}>
            {content}
        </UnstyledButton>
    ) : (
        <UnstyledButton {...buttonProps} onClick={onClick}>
            {content}
        </UnstyledButton>
    );
};

const ChecklistItemWrapper = ({ children, ...props }: { children: React.ReactNode } & BoxProps) => {
    return (
        <Box className={cx.item} {...props}>
            {children}
        </Box>
    );
};

const ChecklistItemFinish = ({ mounted }: { mounted: boolean }) => {
    return (
        <Transition mounted={mounted} transition="pop">
            {(styles) => (
                <Box className={`${cx.item} ${cx.finish}`} style={styles}>
                    <span className={cx.icon}>{<IoCheckmark />}</span>
                    <span>Congrats, you are all set!</span>
                </Box>
            )}
        </Transition>
    );
};

ChecklistItem.Finish = ChecklistItemFinish;
ChecklistItem.Wrapper = ChecklistItemWrapper;

export { ChecklistItem };
