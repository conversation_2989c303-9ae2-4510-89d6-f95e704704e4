import { Alert, Stack, Text, UnstyledButton } from '@mantine/core';
import { BsRobot } from 'react-icons/bs';

import { useComponentChat } from 'components/component-chat/hooks/useComponentChat';
import { TrackingService } from 'services/TrackingService';

import classes from './ComponentChat.module.css';

const ComponentChat = () => {
    const { toggleChatOpen, chatOpen } = useComponentChat();

    return (
        <Alert color="brand" variant="light" icon={<BsRobot size={16} />}>
            <Stack gap={4}>
                <Text size="sm" fw={500}>
                    Explore the product documentation, ask a question!
                </Text>
                <UnstyledButton
                    disabled={chatOpen}
                    className={classes.button}
                    onClick={() => {
                        toggleChatOpen();

                        TrackingService.trackComponentChat('datasheet_top');
                    }}
                >
                    {chatOpen ? 'In progress...' : 'Your question...'}
                </UnstyledButton>
            </Stack>
        </Alert>
    );
};

export { ComponentChat };
