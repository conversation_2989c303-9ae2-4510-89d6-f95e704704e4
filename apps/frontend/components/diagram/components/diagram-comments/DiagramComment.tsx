import { FC, useEffect, useRef } from 'react';

import { Box, Indicator, Loader, Popover, ScrollArea, Stack, UnstyledButton } from '@mantine/core';
import { useDebouncedCallback } from '@mantine/hooks';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { DiagramCommentThread } from 'components/diagram/components/diagram-comments/DiagramComment.Thread';

import { DiagramCommentHeader } from './';
import { DiagramCommentReply } from './DiagramComment.Reply';

import { useDraggable } from '@dnd-kit/core';
import { useHotkeys } from 'react-hotkeys-hook';
import { useDiagramMode } from 'components/diagram/hooks/use-diagram-mode';
import { useActiveCanvasComment } from 'components/diagram/hooks/use-active-canvas-comment';
import { usePreviewComment } from 'components/diagram/hooks/use-preview-comment';
import { useSelectedComments } from 'components/diagram/hooks/selection/use-selected-comments';

import { getDragAndDropTranslate } from 'components/diagram/helpers';

import { CommentService, ViewportService } from 'components/diagram/services';
import { SelectionService } from 'components/diagram/services/SelectionService';

import { state as commentState } from 'components/diagram/state/comment';

import type { DiagramComment as DiagramCommentType } from '@repo/dcide-component-models';
import { Avatar } from 'components/avatar/Avatar';
import { PermissionDiagramElements } from '@repo/dcide-component-models';
import { usePermission } from 'hooks/use-permission';

import cx from './DiagramComment.module.css';
import { Z_INDEX } from 'components/diagram/diagram-z-index';
import { DraggingType } from 'types/DraggingType';

const DiagramComment: FC<{
    comment: DiagramCommentType;
}> = ({ comment }) => {
    const { mode } = useDiagramMode();
    const { newCommentLoading } = useSnapshot(commentState);
    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const threadContainer = useRef<HTMLDivElement>(null);

    const activeComment = useActiveCanvasComment();
    const activated = activeComment === comment.id;

    const previewComment = usePreviewComment();
    const previewed = previewComment === comment.id;

    const { isSelectedComment, isOnlySelectedComment } = useSelectedComments();
    const isSelected = isSelectedComment(comment.id);
    const isOnlySelected = isOnlySelectedComment(comment.id);

    const dragAndDropId = isSelected && !isOnlySelected ? 'selection' : `comment-${comment.id}`;
    const { attributes, listeners, setNodeRef, transform } = useDraggable({
        id: dragAndDropId,
        data: {
            type: DraggingType.COMMENT,
            comment: comment,
        },
        disabled: mode === 'navigate' || !canEdit,
    });

    useEffect(() => {
        setTimeout(() => {
            if (threadContainer.current) {
                // Scroll to the bottom of the thread
                threadContainer.current.scrollTo({ top: threadContainer.current.scrollHeight, behavior: 'smooth' });
            }
        }, 100);
    }, [comment.thread.length]);

    useHotkeys(
        ['backspace'],
        () => {
            if (CommentService.isDeletable(comment)) {
                CommentService.delete(comment);
                SelectionService.clear();
            }
        },
        {
            enabled: isOnlySelected && CommentService.isDeletable(comment),
        },
    );

    const enableViewport = useDebouncedCallback(() => {
        ViewportService.enable();
    }, 500);

    return (
        <Popover opened={activated} position="right-start" width={320} zIndex={Z_INDEX.CANVAS.COMMENT}>
            <Box
                className={cx.wrapper}
                style={{
                    left: comment.position.x,
                    top: comment.position.y,
                    zIndex: activated ? Z_INDEX.COMMENT_DIALOG : Z_INDEX.COMMENT_BUTTON,
                    transform: getDragAndDropTranslate(transform),
                }}
                onWheel={() => {
                    ViewportService.disable();

                    enableViewport();
                }}
                data-comment={comment.id}
                data-activated={activated}
                data-keep-selection
            >
                <Indicator size={8} offset={6} color="red" disabled={comment.seen}>
                    <Popover.Target>
                        <UnstyledButton
                            className={cx.target}
                            onClick={() => {
                                CommentService.activateOrDeactivateCanvasComment(comment.id);
                            }}
                            data-resolved={comment.status === 'resolved'}
                            data-preview={previewed}
                            data-selectable
                            data-selectable-type="comment"
                            data-selectable-id={comment.id}
                            data-selected={isSelected}
                            {...attributes}
                            {...listeners}
                            ref={setNodeRef}
                        >
                            {comment.user && <Avatar user={comment.user} size={28} />}
                            {newCommentLoading && !comment.user && <Loader size={20} color="primary" />}
                        </UnstyledButton>
                    </Popover.Target>
                </Indicator>
                {activated && (
                    <Popover.Dropdown
                        className={cx.popover}
                        style={ViewportService.nonScalableStyle(true)}
                        data-diagram-comment-popover
                    >
                        <DiagramCommentHeader comment={comment} />
                        <Stack gap="xs">
                            {comment.thread.length > 0 && (
                                <ScrollArea.Autosize mah={45 * 5 + 10 * 4} type="hover" viewportRef={threadContainer}>
                                    <DiagramCommentThread comment={comment} />
                                </ScrollArea.Autosize>
                            )}
                            <DiagramCommentReply comment={comment} />
                        </Stack>
                    </Popover.Dropdown>
                )}
            </Box>
        </Popover>
    );
};

export { DiagramComment };
