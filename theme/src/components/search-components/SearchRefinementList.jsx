import React from 'react';
import PropTypes from 'prop-types';

import { RefinementList } from 'react-instantsearch-dom';

import 'components/search-components/search-refinement-list.scss';

const SearchRefinementList = ({ label, attribute, horizontal, defaultRefinement }) => (
    <div className={`search-refinement-list ${horizontal && 'is-horizontal'}`}>
        <h4 className="search-refinement-list__title">{label}</h4>
        <RefinementList
            className="search-refinement-list__list"
            attribute={attribute}
            showMore
            showMoreLimit={999}
            transformItems={items => items.sort((a, b) => {
                return a.label.localeCompare(b.label);
            })}
            translations={{
                showMore: expanded => (expanded ? 'Toon minder' : 'Toon meer'),
            }}
            defaultRefinement={defaultRefinement}
        />
    </div>
);

SearchRefinementList.propTypes = {
    label: PropTypes.string.isRequired,
    attribute: PropTypes.string.isRequired,
    horizontal: PropTypes.bool,
    defaultRefinement: PropTypes.array,
};

SearchRefinementList.defaultProps = {
    horizontal: false,
    defaultRefinement: [],
};

export { SearchRefinementList };
