import type { IntercomChannel, User } from '@/payload-types';
import type { NotificationFormatter } from '../get-notification-formatter';
import { TipTapHelpers, type Notification } from '@repo/dcide-component-models';

import { getCollection } from '../../../helpers/get-collection';
import { getId } from '../../../helpers/get-id';

import { config } from '../../../config';
import { EmailCopyHelpers } from '../../../emails/helpers/EmailCopyHelpers';

type NotificationType = Extract<Notification, { type: 'intercom.message' }>;

const intercomMessage: NotificationFormatter<NotificationType> = {
    key: 'intercom.message',
    email: async (payload, notification) => {
        const from = notification.from as unknown as User;

        const channel = await getCollection('intercomChannels', notification.data.channelId);
        const message = await getCollection('intercomMessages', notification.data.messageId);

        let html = 'Sorry something went wrong with generating the content for message.';

        try {
            html = TipTapHelpers.getHtml(message.content[0].content);
        } catch (error) {
            console.log('Intercom Notification error', notification, error);
        }

        const url = await getChannelUrl(channel, Boolean(message.createdByCompany));
        const hasAttachment = Boolean(message.content[0].files && message.content[0].files.length > 0);

        if (message.createdByCompany) {
            const company = await getCollection('manufacturers', message.createdByCompany);

            return EmailCopyHelpers.intercomMessageByCompany({
                company,
                message: { html, hasAttachment },
                buttonUrl: url,
                linkUrl: `${config.urls.frontend}/inbox`,
            });
        }

        if (!channel.company) {
            throw new Error(`Intercom message channel does not have a company (${channel.id})`);
        }

        const channelCompany = await getCollection('manufacturers', channel.company);

        const component = channel.component ? await getCollection('components', channel.component) : undefined;
        const project = channel.project ? await getCollection('projects', channel.project) : undefined;

        return EmailCopyHelpers.intercomMessageByUser({
            user: from,
            company: channelCompany,
            component,
            project,
            message: {
                html,
                user: from,
                hasAttachment,
            },
            buttonUrl: url,
            linkUrl: `${config.urls.frontend}/inbox`,
        });
    },
    app: async (payload, notification) => {
        const from = notification.from as unknown as User;

        const channel = await getCollection('intercomChannels', notification.data.channelId);
        const message = await getCollection('intercomMessages', notification.data.messageId);
        const company = message.createdByCompany
            ? await getCollection('manufacturers', message.createdByCompany)
            : null;

        const url = await getChannelUrl(channel, Boolean(message.createdByCompany));

        return {
            text: `New support message from ${company?.name ?? from?.name}`,
            url,
        };
    },
};

const getChannelUrl = async (channel: IntercomChannel, fromCompany = false) => {
    if (channel.type === 'project') {
        return `${config.urls.frontend}/projects/${getId(channel.project)}/editor?action=intercom&channel=${channel.id}`;
    }

    if (channel.type === 'component') {
        return `${config.urls.frontend}/products/${getId(channel.component)}?action=intercom&channel=${channel.id}`;
    }

    if (channel.type === 'company') {
        const company = await getCollection('manufacturers', channel.company!);

        if (fromCompany) {
            return `${config.urls.frontend}/profiles/${company.slug}?action=intercom&channel=${channel.id}`;
        }

        return `${config.urls.frontend}/profiles/${company.slug}#support-center`;
    }

    return '';
};

export { intercomMessage };
