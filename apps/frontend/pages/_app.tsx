import React, { FC, useEffect, useState } from 'react';

import { SWRConfig } from 'swr';
import { AppContext, AppProps } from 'next/app';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Analytics } from '@vercel/analytics/react';

import * as Sentry from '@sentry/browser';
import TagManager from 'react-gtm-module';
import { loadSpace } from '@usersnap/browser';

import { AblyProvider } from 'ably/react';
import { SpacesProvider } from '@ably/spaces/react';

import Router, { useRouter } from 'next/router';

import {
    CompanyProfile,
    Project,
    Region,
    Team,
    TeamInfo,
    User,
    UserReferrer,
    UserType,
} from '@repo/dcide-component-models';

import { MantineProvider } from '@mantine/core';
import { useLocalStorage, useWindowEvent } from '@mantine/hooks';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';
import { Toaster } from 'sonner';
import { Spotlight } from 'components/spotlight/Spotlight';

import { config } from 'config';
import { getSession } from 'helpers/getSession';

import '@mantine/core/styles.css';
import '@mantine/tiptap/styles.css';
import '@mantine/carousel/styles.css';
import '@mantine/charts/styles.css';
import '@mantine/spotlight/styles.css';
import '../public/global-styles.css';
import '../public/animations.css';
import '../public/diagramStyles.css';

import { theme } from 'components/styles';

// Don't move this above the styles, it gets funky.
import { UserService } from 'services/UserService';
import { ApiService } from 'services/ApiService';
import { TeamService } from 'services/TeamService';
import { ProjectService } from 'services/ProjectService';
import { RealtimeService } from 'services/RealtimeService';

import { Metadata } from 'components/page/Metadata';

import { modals } from 'components/modals';

import { state as currentUserState } from '../state/current-user';
import { state as currentTeamState } from '../state/current-team';
import { currentTeamCompaniesState } from '../state/current-team-companies';
import { state as currentTeamsState } from '../state/current-teams';
import { state as recentProjectsState } from '../state/recent-projects';
import { state as defaultRegionState } from '../state/default-region';
import { state as defaultMeasurementSystem } from '../state/default-measurement-system';
import { geoState } from '../state/geo';
import { loadingState } from '../state/loading';

import { getTouchDeviceType } from 'helpers/isTouchDevice';
import { BannerMessage } from 'components/forms/BannerMessage';
import { RealtimeUserProvider } from 'components/realtime-user/RealtimeUserProvider';
import { MaintenancePageWrapper } from '../components/MaintenancePage';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { ErrorBoundary } from 'components/error-boundary/ErrorBoundary';
import { UserSuggestions } from 'components/user-suggestions/UserSuggestions';

import { useAppEffects } from 'hooks/use-app-effects';
import { useGlobalState } from 'hooks/use-global-state';
import { RealtimeTeamConnector } from 'components/realtime-user/RealtimeTeamConnector';
import { Geo } from '@vercel/functions';
import { GEOHelpers } from 'helpers/GEOHelpers';
import { UserHelpers } from 'helpers/UserHelpers';
import { RequestHelpers } from 'helpers/RequestHelpers';
import { useHeartbeat } from 'hooks/use-heartbeat';
import { CompanyProfileService } from 'services/CompanyProfileService';

declare module '@mantine/modals' {
    // @ts-ignore
    export interface MantineModalsOverride {
        modals: typeof modals;
    }
}

type ExtendedAppProps = {
    user: User | null;
    team: Team | null;
    currentTeamCompanies: CompanyProfile[];
    teams: TeamInfo[];
    recentProjects: Project[];
    geo: Geo;
    defaultRegion: Region | null;
};

const App = ({
    Component,
    pageProps,
    user,
    team,
    currentTeamCompanies,
    teams,
    recentProjects,
    geo,
    defaultRegion,
}: AppProps & ExtendedAppProps) => {
    const initial = pageProps?.initial || {};

    useAppEffects(initial.user || user);

    useGlobalState(currentUserState, 'user', initial.user || user);
    useGlobalState(currentTeamState, 'team', initial.team || team);
    useGlobalState(currentTeamCompaniesState, 'companies', initial.currentTeamCompanies || currentTeamCompanies);
    useGlobalState(currentTeamsState, 'teams', initial.teams || teams);
    useGlobalState(recentProjectsState, 'projects', initial.recentProjects || recentProjects);

    useGlobalState(geoState, 'geo', geo);
    useGlobalState(defaultRegionState, 'defaultRegion', initial.defaultRegion || defaultRegion);

    useHeartbeat();

    useEffect(() => {
        const touchType = getTouchDeviceType();

        document.body.setAttribute(`data-touch-device`, touchType);
    });

    useEffect(() => {
        if (!currentUserState.user) return;
        if (!currentUserState.user.measurementSystem) return;

        defaultMeasurementSystem.measurementSystem = currentUserState.user.measurementSystem;
    }, []);

    // switch current team if `?switchTeam=` query param is present
    useEffect(() => {
        const switchTeam = Router.query.switchTeam as string;

        if (!switchTeam || !currentUserState.user) return;

        UserService.switchTeam(switchTeam).then((result) => {
            const updatedQuery = Router.query;
            delete updatedQuery.switchTeam;

            Router.replace(
                {
                    query: updatedQuery,
                },
                undefined,
                { shallow: !result?.success },
            ).then();
        });
    }, [currentTeamState.team, currentUserState.user]);

    useSentrySetup();
    useTagManagerSetup();
    useUserSnapSetup();
    useInternalTracking();
    useRouterEventsSetup();
    useQueryParamSetup();

    const [render, setRender] = useState(false);

    useEffect(() => {
        setRender(true);
    });

    return (
        <ConfigWrapper>
            {render && (
                <ErrorBoundary onError={() => InternalTrackingService.track('error.boundary.app')}>
                    <RealtimeWrapper team={team} user={user}>
                        <MaintenancePageWrapper>
                            <ModalsProvider modals={modals}>
                                <Metadata title="DCIDE" />
                                <Notifications position="bottom-center" />
                                <Toaster position="top-right" offset={12} gap={4} />
                                {currentUserState.user && <UserSuggestions />}
                                {currentUserState.user && <Spotlight />}
                                <BannerMessage />
                                <Component {...pageProps} />
                                <SpeedInsights />
                                <Analytics />
                            </ModalsProvider>
                        </MaintenancePageWrapper>
                    </RealtimeWrapper>
                </ErrorBoundary>
            )}
        </ConfigWrapper>
    );
};

const useSentrySetup = () => {
    useEffect(() => {
        if (config.sentry) {
            const { goofy } = currentUserState.user || {};

            if (goofy) {
                Sentry.setUser({ username: goofy });
            } else {
                Sentry.setUser(null);
            }
        }
    }, []);
};

const useUserSnapSetup = () => {
    useEffect(() => {
        if (!config.userSnapApiKey) {
            return;
        }

        loadSpace(config.userSnapApiKey).then((api) => {
            // @ts-ignore
            window.Usersnap = api;

            api.init().then();
            api.on('open', function (event: any) {
                event.api.setValue('visitor', currentUserState.user?.email);
            });
        });
    }, []);
};

const useTagManagerSetup = () => {
    useEffect(() => {
        if (config.serverEnvironment !== 'production') return;

        if (!config.analytics) return;

        const tagManagerArgs = {
            gtmId: config.analytics,
            dataLayer: {
                event: 'page_view',
            },
        };

        TagManager.initialize(tagManagerArgs);
    }, []);
};

const useInternalTracking = () => {
    useEffect(() => {
        InternalTrackingService.start();
        InternalTrackingService.track('page.view');

        Router.events.on('routeChangeComplete', () => {
            UserService.ping().then();
            InternalTrackingService.track('page.view');
        });
    }, []);

    useWindowEvent('beforeunload', () => {
        UserService.ping().then();
    });
};

const useRouterEventsSetup = () => {
    useEffect(() => {
        Router.events.on('routeChangeStart', () => {
            loadingState.page = true;
        });
        Router.events.on('routeChangeComplete', () => {
            loadingState.page = false;
        });
        Router.events.on('routeChangeError', () => {
            loadingState.page = false;
        });
    }, []);
};

const useQueryParamSetup = () => {
    const router = useRouter();

    const referrer = router.query.referrer as UserReferrer | undefined;
    const email = router.query.email as string | undefined;
    const event = router.query.event as string | undefined;
    const type = router.query.type as UserType | undefined;

    const [, setEmail] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.email,
    });
    const [, setReferrer] = useLocalStorage<UserReferrer | undefined>({
        key: UserHelpers.localStorageKey.referrer,
    });
    const [, setEvent] = useLocalStorage<string | undefined>({
        key: UserHelpers.localStorageKey.event,
    });
    const [, setType] = useLocalStorage<UserType | undefined>({
        key: UserHelpers.localStorageKey.type,
    });

    useEffect(() => {
        if (referrer) {
            setReferrer(referrer);
        }

        if (email) {
            setEmail(email);
        }

        if (event) {
            setEvent(event);
        }

        if (type && Object.values(UserType).includes(type)) {
            setType(type);
        }
    }, [referrer, email, event, type]);
};

const ConfigWrapper: FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
        <MantineProvider theme={theme}>
            <SWRConfig value={{ revalidateIfStale: false }}>{children}</SWRConfig>
        </MantineProvider>
    );
};

const RealtimeWrapper: FC<{
    team: Team | null;
    user: User | null;
    children: React.ReactNode;
}> = ({ team, user, children }) => {
    if (typeof window === 'undefined') {
        return children;
    }

    const { ably, spaces } = RealtimeService.init(user);

    return (
        <AblyProvider client={ably}>
            <SpacesProvider client={spaces}>
                <RealtimeTeamConnector team={team} user={user} />
                <RealtimeUserProvider user={user}>{children}</RealtimeUserProvider>
            </SpacesProvider>
        </AblyProvider>
    );
};

App.getInitialProps = async (context: AppContext): Promise<ExtendedAppProps> => {
    const { req, res } = context.ctx;

    const initialProps: ExtendedAppProps = {
        user: null,
        team: null,
        currentTeamCompanies: [],
        teams: [],
        recentProjects: [],
        geo: {},
        defaultRegion: null,
    };

    if (!req || !res) {
        return initialProps;
    }

    const { token } = await getSession(req, res);
    ApiService.token = token || '';

    if (!token) {
        return initialProps;
    }

    if (req.headers['x-user']) {
        initialProps.user = RequestHelpers.decode(req.headers['x-user'] as string);

        const fetchTeam = async () => {
            initialProps.team = await TeamService.getCurrentTeam();
        };

        const fetchTeams = async () => {
            initialProps.teams = await UserService.getTeams();
        };

        const fetchCurrentTeamCompanies = async () => {
            const teamId = initialProps.user?.team;

            if (teamId) {
                const { docs: companies } = await CompanyProfileService.getByTeam(teamId);

                initialProps.currentTeamCompanies = companies;
            }
        };

        const fetchRecentProjects = async () => {
            initialProps.recentProjects = await ProjectService.getRecentProjects();
        };

        await Promise.all([fetchTeam(), fetchCurrentTeamCompanies(), fetchTeams(), fetchRecentProjects()]);
    }

    if (req.headers['x-geo']) {
        const geo = RequestHelpers.decode(req.headers['x-geo'] as string);

        initialProps.geo = geo;
        initialProps.defaultRegion = GEOHelpers.getRegionFromVercelRegion(geo.region ?? '');
    }

    return initialProps;
};

export default App;
