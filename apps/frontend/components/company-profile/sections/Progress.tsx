import { FC, useEffect, useState } from 'react';

import { sort } from 'radash';

import { Card, RingProgress, Stack, Text } from '@mantine/core';
import {
    IoChatbubblesOutline,
    IoImageOutline,
    IoImagesOutline,
    IoMapOutline,
    IoMegaphoneOutline,
    IoShareSocialOutline,
} from 'react-icons/io5';
import { BsBox, BsMarkerTip, BsMortarboard, BsPencil, BsPersonVcard } from 'react-icons/bs';

import { CompanyProfile } from '@repo/dcide-component-models';

import { useProfileProgress } from 'components/company-profile/hooks/use-profile-progress';
import { ProfileModal, useProfileContext } from 'components/company-profile/hooks/use-profile-context';

import { COMPONENT_PROGRESS_LEVELS } from 'components/component-progress/helpers/config';

import { CarouselSection } from 'components/section/CarouselSection';
import { DashboardTitle } from 'components/dashboard/components/Dashboard.Title';
import { ChecklistItem, ChecklistItemIconPosition, ChecklistItemType } from 'components/checklist-item/ChecklistItem';

import cx from './Progress.module.css';

const useProgressItems = (): ChecklistItemType[] => {
    const { openModal } = useProfileContext();

    return [
        {
            value: 'products',
            label: 'Add a product to the product catalog',
            href: '#products',
            icon: <BsBox />,
            iconPosition: ChecklistItemIconPosition.SW,
        },
        {
            value: 'services',
            label: 'Enable in-app support to connect with customers',
            href: '#support-center',
            icon: <IoChatbubblesOutline />,
        },
        {
            value: 'about',
            label: 'Fill in your company about',
            onClick: () => openModal(ProfileModal.EDIT_ABOUT),
            icon: <BsPencil />,
            iconPosition: ChecklistItemIconPosition.NE,
        },
        {
            value: 'logos',
            label: 'Add a logo',
            onClick: () => {
                (document.querySelector('[data-file-button="logos.small"]') as HTMLElement)?.click();
            },
            icon: <IoImageOutline />,
            iconPosition: ChecklistItemIconPosition.SW,
        },
        {
            value: 'cover',
            label: 'Add a cover image',
            onClick: () => {
                (document.querySelector('[data-file-button="cover"]') as HTMLElement)?.click();
            },
            icon: <IoImagesOutline />,
            iconPosition: ChecklistItemIconPosition.W,
        },
        {
            value: 'socials',
            label: 'Add your social media links',
            href: '#settings',
            icon: <IoShareSocialOutline />,
            iconPosition: ChecklistItemIconPosition.W,
        },
        {
            value: 'locations',
            label: 'Add your contact information',
            onClick: () => openModal(ProfileModal.ADD_LOCATION),
            icon: <IoMapOutline />,
            iconPosition: ChecklistItemIconPosition.N,
        },
        {
            value: 'contactPeople',
            label: 'Add your contact people',
            onClick: () => openModal(ProfileModal.ADD_CONTACT),
            icon: <BsPersonVcard />,
            iconPosition: ChecklistItemIconPosition.S,
        },
        {
            value: 'referenceDesigns',
            label: 'Create a reference design',
            href: '#templates',
            icon: <BsMarkerTip />,
            iconPosition: ChecklistItemIconPosition.S,
        },
        {
            value: 'caseStudies',
            label: 'Add a case study',
            href: '#caseStudies',
            icon: <BsMortarboard />,
            iconPosition: ChecklistItemIconPosition.NW,
        },
        {
            value: 'promos',
            label: 'Add an announcement',
            href: '#promos',
            icon: <IoMegaphoneOutline />,
        },
    ];
};

const Progress: FC<{ company: CompanyProfile }> = ({ company }) => {
    const progressItems = useProgressItems();

    const { progress, percentage } = useProfileProgress(company);
    const cleanPercentage = percentage ?? 0;

    const { color } = COMPONENT_PROGRESS_LEVELS.find((level) => cleanPercentage < level.maxPercent)!;

    const filteredProgress = progressItems.filter(({ value }) => progress.find((needle) => needle.key === value));

    const sortedProgress = sort(filteredProgress, ({ value }) => {
        const isCompleted = !!progress.find((needle) => needle.key === value)?.progress;

        return isCompleted ? 1 : 0;
    });

    const [originalProgress, setOriginalProgress] = useState<number | null>(null);

    useEffect(() => {
        if (percentage === null) return;
        if (originalProgress !== null) return;

        setOriginalProgress(percentage);
    }, [percentage]);

    if (percentage === 100 && (!originalProgress || originalProgress === 100)) return null;

    if (!progress.length) return null;

    return (
        <Card className={cx.wrapper}>
            <DashboardTitle>Complete your profile</DashboardTitle>
            <CarouselSection
                nbCols={6}
                cols={{
                    xs: 2,
                    sm: 4,
                    md: 6,
                }}
            >
                <ChecklistItem.Wrapper p={0}>
                    <RingProgress
                        className={cx.progress}
                        roundCaps
                        size={140}
                        thickness={9}
                        sections={[{ value: cleanPercentage, color }]}
                        label={
                            <Stack gap={0} align="center" ta="center">
                                <Text fz={32} fw={700} lh={1}>
                                    {percentage}
                                    <Text span fz="xs" c="dimmed" fw={500}>
                                        %
                                    </Text>
                                </Text>
                                <Text span fz="xs" c="dimmed" fw={500}>
                                    complete
                                </Text>
                            </Stack>
                        }
                    />
                </ChecklistItem.Wrapper>

                {sortedProgress.map((item) => {
                    const completed = !!progress.find((needle) => needle.key === item.value)?.progress;

                    return <ChecklistItem key={item.value} {...item} completed={completed} />;
                })}
            </CarouselSection>
        </Card>
    );
};

export { Progress };
