{% if entry is defined or category is defined %}

    {% set node = entry ?? category %}
    {% set seo = node.seo %}

    {% set title = '' %}
    {% set description = '' %}
    {% set image = '' %}

    {% if node.type is defined and node.type == 'homepage' %}
        {% set title = getenv('SITE_NAME') %}
    {% else %}
        {% set title = (node.seoTitle ?? node.title) ~ ' - ' ~ getenv('SITE_NAME') %}
    {% endif %}

    {% set description = node.seoDescription ?? '' %}
    {% set image = entry.seoImage.one %}

    <title>{{ title }}</title>
    <meta name="description" content="{{ description }}" />

    <meta property="og:title" content="{{ title }}" />
    <meta property="og:description" content="{{ description }}" />
    {% if image %}
        <meta property="og:image" content="{{ image.url ?? '' ~ '?fit=crop&w=1200&h=630' }}" />
        <meta property="og:image:width" content="1200">
        <meta property="og:image:height" content="630">
    {% endif %}
    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="{{ getenv('SITE_NAME') }}" />
    <meta property="og:url" content="{{ craft.app.request.absoluteUrl }}" />

    <meta name="twitter:title" content="{{ title }}" />
    <meta name="twitter:description" content="{{ description }}" />
    {% if image %}
        <meta name="twitter:image" content="{{ image.url ?? '' }}" />
    {% endif %}
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="{{ craft.app.request.absoluteUrl }}" />

    {% if seo.robots %}
        <meta name="robots" content="{{ seo.robots }}" />
    {% endif %}

    <link rel="home" href="{{ siteUrl }}" />
    <link rel="canonical" href="{{ seo.canonical }}">

{% endif %}
