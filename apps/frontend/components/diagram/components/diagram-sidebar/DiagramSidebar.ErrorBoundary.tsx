import React from 'react';
import type { FC } from 'react';

import { ErrorBoundary } from 'react-error-boundary';
import { IoWarningOutline } from 'react-icons/io5';

import cx from './DiagramSidebar.module.css';
import { InternalTrackingService } from 'services/InternalTrackingService';

const DiagramSidebarErrorBoundary: FC<{
    children: React.ReactNode;
    identifier?: string;
}> = ({ children, identifier }) => {
    const fallback = (
        <div className={cx.sidebarErrorBoundary}>
            <IoWarningOutline />
            Sorry, something went wrong on our side.
            <br />
            Our engineers are notified and working on it.
        </div>
    );
    return (
        <ErrorBoundary
            fallback={fallback}
            key={identifier}
            onError={() => InternalTrackingService.track('error.boundary.diagram.sideBar')}
        >
            {children}
        </ErrorBoundary>
    );
};

export { DiagramSidebarErrorBoundary };
