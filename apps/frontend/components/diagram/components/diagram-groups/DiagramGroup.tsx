import { FC } from 'react';

import { DiagramGroup as DiagramGroupType, PermissionDiagramGroups } from '@repo/dcide-component-models';
import { DraggingType } from 'types/DraggingType';

import { Box } from '@mantine/core';
import { useHover, useMouse } from '@mantine/hooks';

import { DiagramResizableElement } from 'components/diagram/components/diagram-textareas/DiagramResizableElement';

import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { GroupService } from 'components/diagram/services/GroupService';

import { SelectionService } from 'components/diagram/services/SelectionService';
import { BORDER_STYLES } from 'components/forms/fields/BorderStyleField';

import { useSelectedGroups } from 'components/diagram/hooks/selection/use-selected-groups';
import { usePermission } from 'hooks/use-permission';

import { deNullRef } from 'helpers/de-null-ref';

import cx from './DiagramGroups.module.css';
import { useDiagram } from 'components/diagram/hooks';

const DiagramGroup: FC<{ id: DiagramGroupType['id'] }> = ({ id }) => {
    const { groups } = useDiagram();
    const group = groups.find((group) => group.id === id)!;

    const { isSelectedGroup, isOnlySelectedGroup } = useSelectedGroups();
    const isSelected = isSelectedGroup(group.id);
    const isOnlySelected = isOnlySelectedGroup(group.id);
    const isPartOfSelection = isSelected && !isOnlySelected;

    const canEdit = usePermission(PermissionDiagramGroups.EDIT);

    // TODO: migrate from svg dasharray to css border-style
    const borderStyle = BORDER_STYLES.find((style) => style.value === group.borderStyle)?.borderStyle || 'solid';

    const { ref: innerGroupRef, x: innerGroupX, y: innerGroupY } = useMouse({ resetOnExit: true });
    const mouseIsOverInner = innerGroupX && innerGroupY;

    const { ref: hoverRef, hovered } = useHover();

    const activeEditHover = canEdit && hovered && !mouseIsOverInner;

    return (
        <DiagramResizableElement
            type={DraggingType.GROUP}
            element={{
                ...group,
                dimensions: {
                    width: group.width,
                    height: group.height,
                },
            }}
            draggable={canEdit && !mouseIsOverInner}
            onResizeStop={(data) => {
                GroupService.updatePositionAndSize(group.id, {
                    x: data.position.x,
                    y: data.position.y,
                    width: data.dimensions.width,
                    height: data.dimensions.height,
                });
                DiagramSyncService.save();
            }}
        >
            <Box
                ref={deNullRef(hoverRef)}
                className={cx.group}
                data-hovered={activeEditHover}
                style={{
                    '--group-bg': group.fillColor,
                    '--group-border-color': group.borderColor,
                    '--group-border-style': borderStyle,
                }}
                onContextMenu={(event) => {
                    event.preventDefault();

                    if (!canEdit) {
                        return;
                    }

                    if (isPartOfSelection) {
                        return;
                    }

                    SelectionService.selectGroup(group.id);
                }}
            >
                <Box
                    className={cx.label}
                    onDoubleClick={() => {
                        if (!canEdit) return;

                        SelectionService.selectGroup(group.id);
                    }}
                >
                    {group.label}
                </Box>
                <Box
                    ref={innerGroupRef}
                    className={cx.innerGroup}
                    onClick={(event) => {
                        event.stopPropagation();
                        event.preventDefault();

                        // pass click event to canvas to trigger select effects
                        const canvasContainer = document.getElementById('diagram-canvas');
                        canvasContainer?.click();
                    }}
                    data-selectable-inner
                />
            </Box>
        </DiagramResizableElement>
    );
};

export { DiagramGroup };
