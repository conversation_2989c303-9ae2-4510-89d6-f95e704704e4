import React, { FC, useEffect, useRef } from 'react';

import { Box, Space, Stack, Title, LoadingOverlay, ScrollArea } from '@mantine/core';
import { IoArrowBack } from 'react-icons/io5';

import { ComponentCount, ComponentQuery, ComponentSearchScore } from '@repo/dcide-component-models';

import useSWR from 'swr/immutable';

import { ComponentSearchForm } from './ComponentSearchForm';
import { ComponentOverviewHits, DiagramComponentOverviewHits } from './ComponentOverviewHits';
import { ComponentOverviewPagination } from './ComponentOverviewPagination';

import { SimpleButton } from 'elements/buttons';

import { Page } from 'components/page';

import { SearchBar } from 'components/component-overview/components/SearchBar';
import { InlineFilters, InlineFiltersProps } from 'components/component-overview/inline-filters/InlineFilters';
import { useComponentSearchData } from 'components/component-overview/hooks/use-component-search-data';
import { SearchService } from 'components/component-overview/services/SearchService';
import { ComponentLandingProps } from 'components/component-landing';
import { useComponentSearchDefaultValues } from './hooks/use-component-search-default-values';
import { ComponentOverviewCompanies } from 'components/component-overview/ComponentOverview.Companies';
import { ComponentOverviewCaseStudies } from 'components/component-overview/ComponentOverview.CaseStudies';
import { ComponentOverviewTabs } from 'components/component-overview/ComponentOverview.Tabs';
import { ComponentOverviewStickyFilters } from 'components/component-overview/ComponentOverview.StickyFilters';
import { ComponentOverviewActions } from 'components/component-overview/ComponentOverview.Actions';

import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import {
    useComponentSearchTabs,
    ComponentSearchTab,
} from 'components/component-overview/hooks/use-component-search-tabs';

import { CompanyProfileService } from 'services/CompanyProfileService';

import cx from './ComponentOverview.module.css';
import { useArticleSearch } from 'hooks/use-articles';
import { useLocalEvent } from 'hooks/use-local-event';

const ComponentOverview: FC<
    {
        componentCount?: ComponentCount;
        initialContent: React.ReactNode;
        title?: React.ReactNode;
        heroBg?: string;
    } & ComponentLandingProps
> = ({ componentCount, initialContent, title = 'Find microgrid products', heroBg }) => {
    useComponentSearchDefaultValues();

    const scrollRef = useRef<HTMLDivElement>(null);

    const { tab, setTab } = useComponentSearchTabs();
    const { search, query } = useComponentSearchFilters();

    const { docs: components, page, totalPages, totalComponents, isLoading, hasSearched } = useComponentSearchData();

    const params = new URLSearchParams();
    const queryParam = query ?? search;
    if (queryParam) {
        params.set('query', queryParam);
    }

    const {
        data: companies = [],
        isLoading: companiesAreLoading,
        isValidating: companiesAreValidating,
    } = useSWR(`/manufacturers/search?${params.toString()}`, async () => {
        // Use text search as fallback in case the search box query is not set
        const queryToSearch = query ?? search;

        if (!queryToSearch) {
            return [];
        }

        const result = await CompanyProfileService.search(queryToSearch);

        return result.docs || [];
    });

    const {
        articles: caseStudies = [],
        isLoading: caseStudiesAreLoading,
        isValidating: caseStudiesAreValidating,
    } = useArticleSearch({ query: query ?? undefined });

    const somethingIsLoading =
        isLoading || companiesAreLoading || companiesAreValidating || caseStudiesAreLoading || caseStudiesAreValidating;

    const scrollToTop = () => {
        if (scrollRef.current) {
            scrollTo({
                top: scrollRef.current.offsetTop,
                behavior: 'smooth',
            });
        }
    };

    useEffect(() => {
        if (!somethingIsLoading) {
            if (components.length > 0) {
                setTab(ComponentSearchTab.COMPONENTS);

                return;
            }

            if (companies.length > 0) {
                setTab(ComponentSearchTab.COMPANIES);

                return;
            }

            if (caseStudies.length > 0) {
                setTab(ComponentSearchTab.CASE_STUDIES);

                return;
            }

            setTab(ComponentSearchTab.COMPONENTS);
        }
    }, [somethingIsLoading]);

    return (
        <Page
            breadcrumbs={{
                type: 'floating.fullWidth',
                isSticky: false,
                rightSection: <ComponentOverviewActions />,
            }}
            showBackground
            showScrollToTop
            title="Products"
        >
            <Page.Hero src={heroBg}>
                <Title>{title}</Title>

                <Stack gap={8} maw={1000} mx="auto" mt="xl">
                    <SearchBar />
                    <ComponentSearchForm>
                        <ComponentOverviewStickyFilters componentCount={componentCount} />

                        <Box className={cx.filters}>
                            <ScrollArea w="100%" type="never">
                                <InlineFilters componentCount={componentCount} variant="dark" />
                            </ScrollArea>
                        </Box>
                    </ComponentSearchForm>
                </Stack>
            </Page.Hero>
            <Page.WideContent>
                {hasSearched ? (
                    <Stack gap="xs" ref={scrollRef}>
                        <InlineFilters.Wrapper>
                            <Box className={cx.navigation}>
                                <Box className={cx.navigationBack}>
                                    <InlineFilters.Back />
                                </Box>
                                <Box className={cx.navigationTabs}>
                                    <ComponentOverviewTabs
                                        totals={{
                                            components: totalComponents,
                                            companies: companies.length,
                                            caseStudies: caseStudies.length,
                                        }}
                                    />
                                </Box>
                                <Box className={cx.navigationSorting}>
                                    <InlineFilters.Sort />
                                </Box>
                            </Box>
                        </InlineFilters.Wrapper>
                        <Box pos="relative">
                            <LoadingOverlay
                                zIndex={200}
                                visible={somethingIsLoading}
                                loaderProps={{
                                    size: 'sm',
                                    styles: { root: { alignSelf: 'flex-start', paddingTop: 100 } },
                                }}
                                overlayProps={{ bg: 'rgba(248, 249, 250, 0.5)' }}
                            />
                            {tab === ComponentSearchTab.COMPONENTS && (
                                <>
                                    <ComponentOverviewHits hits={components} />
                                    {!!components.length && (
                                        <ComponentOverviewPagination
                                            page={page}
                                            setPage={(page) => {
                                                SearchService.setPage(page);
                                                scrollToTop();
                                            }}
                                            totalPages={totalPages}
                                        />
                                    )}
                                </>
                            )}
                            {tab === ComponentSearchTab.COMPANIES && (
                                <ComponentOverviewCompanies companies={companies} />
                            )}
                            {tab === ComponentSearchTab.CASE_STUDIES && (
                                <ComponentOverviewCaseStudies caseStudies={caseStudies} />
                            )}
                        </Box>
                    </Stack>
                ) : (
                    <Box pos="relative">
                        <LoadingOverlay
                            zIndex={200}
                            visible={somethingIsLoading}
                            loaderProps={{
                                size: 'sm',
                                styles: { root: { alignSelf: 'flex-start', paddingTop: 100 } },
                            }}
                            overlayProps={{ bg: 'rgba(248, 249, 250, 0.5)' }}
                        />
                        {initialContent}
                    </Box>
                )}
            </Page.WideContent>
        </Page>
    );
};

export type DiagramComponentOverviewProps = {
    componentCount?: ComponentCount;
    defaultValues?: Partial<ComponentQuery>;
    hideFilters?: InlineFiltersProps['hideFilters'];
    viewOnlyFilters?: InlineFiltersProps['viewOnlyFilters'];
    onGoBack?: () => void;
    draggable?: boolean;
    showSearchBar?: boolean;
};

const DiagramComponentOverview: FC<DiagramComponentOverviewProps> = ({
    componentCount,
    hideFilters,
    viewOnlyFilters,
    defaultValues,
    onGoBack,
    draggable,
    showSearchBar = true,
}) => {
    const { docs, page, totalPages, isLoading } = useComponentSearchData();

    const { localEvent, setLocalEvent } = useLocalEvent();

    useEffect(() => {
        if (localEvent) {
            setLocalEvent(null);
        }
    }, [localEvent]);

    useEffect(() => {
        SearchService.setSearchWeight(ComponentSearchScore.PORT_MATCH_SCORE, 15);

        return () => {
            SearchService.resetSearchWeights();
            SearchService.resetFilters();
        };
    }, []);

    return (
        <ComponentSearchForm defaultValues={defaultValues}>
            {onGoBack ? (
                <SimpleButton mb="sm" onClick={onGoBack}>
                    <IoArrowBack size={12} />
                    <span>Back to overview</span>
                </SimpleButton>
            ) : null}

            {showSearchBar && (
                <>
                    <SearchBar size="md" searchSuggestions={false} placeholder="Search products" />
                    <Space h="sm" />
                </>
            )}

            <InlineFilters
                hideFilters={hideFilters}
                viewOnlyFilters={viewOnlyFilters}
                componentCount={componentCount}
                size="sm"
            />
            <Space h="md" />
            <Stack gap="xs">
                <Box style={{ position: 'relative', flexGrow: 1, flexShrink: 1 }}>
                    <DiagramComponentOverviewHits draggable={draggable} hits={docs} isLoading={isLoading} />
                    {!!docs.length && (
                        <ComponentOverviewPagination
                            size="sm"
                            page={page}
                            setPage={(page) => {
                                SearchService.setPage(page);
                            }}
                            totalPages={totalPages}
                        />
                    )}
                </Box>
            </Stack>
        </ComponentSearchForm>
    );
};

export { ComponentOverview, DiagramComponentOverview };
