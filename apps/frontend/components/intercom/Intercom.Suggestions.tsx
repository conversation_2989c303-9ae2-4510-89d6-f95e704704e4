import React, { <PERSON> } from 'react';

import { Box, UnstyledButton, Stack } from '@mantine/core';
import { IoChevronForward } from 'react-icons/io5';

import cx from './Intercom.module.css';

const IntercomSuggestions: FC<{
    suggestions: string[];
}> = ({ suggestions }) => {
    const suggest = (suggestion: string) => {
        window.dangerouslySetIntercomContent?.(suggestion);
    };

    return (
        <Stack gap="xs">
            {suggestions.map((suggestion) => (
                <UnstyledButton
                    className={cx.suggestion}
                    onClick={() => {
                        suggest(suggestion);
                    }}
                    key={suggestion}
                >
                    <Box>{suggestion}</Box>
                    <IoChevronForward />
                </UnstyledButton>
            ))}
        </Stack>
    );
};

export { IntercomSuggestions };
