import React, { useState, FC } from 'react';
import { GetServerSideProps } from 'next';
import { useRouter } from 'next/router';

import { Box, Button } from '@mantine/core';

import { Page } from 'components/page';
import { useCurrentUser } from 'hooks/use-current-user';

import { ProjectService } from 'services/ProjectService';
import { ModalService } from 'services/ModalService';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import { getParam } from 'helpers/getParam';

import cx from './request-access.module.css';

const ProjectAccess: FC = () => {
    const router = useRouter();
    const user = useCurrentUser();

    const [loading, setLoading] = useState(false);
    const [accessRequested, setAccessRequested] = useState(false);

    const { projectId, designId, diagramId } = router.query as {
        projectId: string;
        designId: string;
        diagramId: string;
    };

    const redirect = router.asPath.split('/request-access')[0];

    const openLoginModal = () => {
        ModalService.openLoginModal({ redirect });
    };

    const requestAccess = async () => {
        setLoading(true);

        await ProjectService.requestAccess(projectId!, designId!, diagramId!);

        setAccessRequested(true);
        setLoading(false);
    };

    return (
        <Page showBackground title="Request access">
            <Page.CenteredContent>
                {user && !accessRequested && (
                    <Box className={cx.modal}>
                        <h2>You don&apos;t have access to this project</h2>
                        <Box className={cx.modalActions}>
                            <Button onClick={requestAccess} loading={loading}>
                                Request access
                            </Button>
                        </Box>
                    </Box>
                )}
                {user && accessRequested && (
                    <Box className={cx.modal}>
                        <h2>Your request has been sent</h2>
                        <p>We will notify you as soon as project owner accepts your request.</p>
                    </Box>
                )}
                {!user && (
                    <Box className={cx.modal}>
                        <h2>You need an account to access this page</h2>
                        <Box className={cx.modalActions}>
                            <Button onClick={openLoginModal}>Login</Button>
                            <Button variant="light" onClick={openLoginModal}>
                                Create an account
                            </Button>
                        </Box>
                    </Box>
                )}
            </Page.CenteredContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const projectId = getParam(params, 'projectId')!;
    const project = await ProjectService.get(projectId, { depth: 1 });

    return project
        ? {
              redirect: {
                  destination: ProjectHelpers.urls.editor(projectId),
                  permanent: true,
              },
          }
        : {
              props: {},
          };
};

export default ProjectAccess;
