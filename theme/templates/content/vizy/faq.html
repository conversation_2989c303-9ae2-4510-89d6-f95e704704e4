<div class="max-w-7xl mx-auto">
    {% for item in block.faq.all %}
        <details class="py-5 sm:py-6 md:py-8 lg:py-10 border-t last:border-b ">
            <summary class="flex sm:text-lg md:text-xl lg:text-2xl xl:text-3xl cursor-pointer">
                <span class="w-5 sm:w-16 lg:w-24">
                    {{ loop.index }}
                </span>
                <span class="font-bold">
                    {{ item.title }}
                </span>
            </summary>
            <div class="ml-5 sm:ml-16 lg:ml-24 md:text-lg lg:text-xl">
                <div class="h-5 sm:h-6 md:h-8 lg:h-10"></div>
                <div class="grid gap-5 sm:gap-6 md:grid-cols-2 md:gap-8 lg:gap-10">
                    {% if item.left %}
                        <div class="">
                            {{ item.left }}
                        </div>
                    {% endif %}
                    {% if item.right %}
                        <div class="flex items-start gap-5">
                            <svg
                                class="flex-grow-0 flex-shrink-0 w-5 md:w-6 aspect-1 mt-0.5 text-brand-purple"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2.5"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            >
                                <path d="M0 0h24v24H0z" stroke="none" />
                                <path d="M5 12h14M13 18l6-6M13 6l6 6" />
                            </svg>
                            <div>
                                {{ item.right }}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </details>
    {% endfor %}
</div>
