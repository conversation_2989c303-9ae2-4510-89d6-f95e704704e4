import type { FC } from 'react';
import type { DiagramConnection } from '@repo/dcide-component-models';

import { WireSize } from '@repo/dcide-component-models';

import { Combobox, ScrollArea } from '@mantine/core';
import { useCombobox } from '@mantine/core';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';
import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

import { ConnectionService } from 'components/diagram/services/ConnectionService';
import { FormatHelpers } from 'helpers/formatters';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

const ConnectionToolbarWireSize: FC<{
    connection: DiagramConnection;
}> = ({ connection }) => {
    const combobox = useCombobox();

    return (
        <Combobox
            store={combobox}
            classNames={{
                dropdown: cx.dropdown,
                option: cx.dropdownOption,
            }}
            onOptionSubmit={(value) => {
                ConnectionService.update({
                    id: connection.id,
                    wireSize: value as any,
                });

                DiagramSyncService.save();

                combobox.closeDropdown();
            }}
            position="top"
            withArrow
            withinPortal={false}
            arrowOffset={18}
        >
            <Combobox.Target>
                <div>
                    <DiagramToolbar.TextButton
                        active={!!connection.wireSize}
                        onClick={() => {
                            combobox.toggleDropdown();
                        }}
                    >
                        {FormatHelpers.formatWireSize(connection.wireSize) || 'Wire Size'}
                    </DiagramToolbar.TextButton>
                </div>
            </Combobox.Target>
            <Combobox.Dropdown>
                <Combobox.Options>
                    <ScrollArea.Autosize
                        mah={36 * 6}
                        type="auto"
                        styles={{
                            thumb: {
                                background: 'rgba(255, 255, 255, .25)',
                            },
                        }}
                    >
                        {WireSize.options.map((option) => (
                            <Combobox.Option
                                value={option.value}
                                active={option.value === connection.wireSize}
                                key={option.value}
                            >
                                {option.label}
                            </Combobox.Option>
                        ))}
                    </ScrollArea.Autosize>
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );
};

export { ConnectionToolbarWireSize };
