import React from 'react';

import { CELL_WIDTH } from 'components/diagram';

import { Box } from '@mantine/core';
import { Logo } from 'components/logo';

import cx from './DiagramCanvas.module.css';
import { FeatureKey, useFeatureAccess } from 'hooks/use-feature-access';

const DiagramLogo = () => {
    const hide = useFeatureAccess(FeatureKey.EXPORT_DIAGRAM_IMAGE_WITHOUT_WATERMARK);

    return hide ? null : (
        <Box id="diagram-canvas-logo" className={cx.logo}>
            <Logo width={CELL_WIDTH} />
        </Box>
    );
};

export { DiagramLogo };
