import { InputWrapperProps } from '@mantine/core';

import { POWER_LEVELS } from '@repo/dcide-component-models';

import { powerConverter } from 'units/power';

import { FormatHelpers } from 'helpers/formatters';

import { SliderField } from 'components/component-fields/SliderField';

type PowerLevelFieldProps = InputWrapperProps & {
    name: string;
};

const PowerLevelField = (props: PowerLevelFieldProps) => {
    return (
        <SliderField
            data={POWER_LEVELS.map((label, index) => ({
                value: index,
                savedValue: label,
                label: FormatHelpers.formatValue(label, powerConverter),
            }))}
            {...props}
        />
    );
};

export { PowerLevelField };
