import { range } from 'radash';

import { Box, Stepper, Title } from '@mantine/core';
import { IoCheckmarkSharp } from 'react-icons/io5';

import { SignupLayoutProps } from '../SignupLayout';

import cx from './styles.module.css';

type Props = Pick<SignupLayoutProps, 'title' | 'active' | 'nbSteps'>;

const Header = ({ title, active, nbSteps }: Props) => {
    return (
        <Box className={cx.header} px="xl" maw={700}>
            <Title fz={20} fw={700} pb="xl">
                {title}
            </Title>
            <Stepper
                active={active}
                size="xs"
                color="brand"
                iconSize={30}
                completedIcon={<IoCheckmarkSharp size={16} />}
            >
                {[...range(nbSteps - 1)].map((index) => (
                    <Stepper.Step key={index} />
                ))}
            </Stepper>
        </Box>
    );
};

export { Header };
