import { FC } from 'react';

import { Card } from '@mantine/core';

import { CompanyProfile } from '@repo/dcide-component-models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { Products as ProductsComponent } from 'components/company-profile/sections/Products';

import cx from './styles.module.css';

const Products: FC<{ company: CompanyProfile }> = ({ company }) => {
    return (
        <Card classNames={cx}>
            <ProductsComponent company={company} />
        </Card>
    );
};

const WrappedProducts = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <Products company={company} />;
};

export { WrappedProducts as Products };
