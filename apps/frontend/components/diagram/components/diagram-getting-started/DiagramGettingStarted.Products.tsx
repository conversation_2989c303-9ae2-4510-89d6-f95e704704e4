import React, { FC, useState } from 'react';
import { flat } from 'radash';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Unstyled<PERSON>utton } from '@mantine/core';
import { IoCheckmark } from 'react-icons/io5';

import { DiagramService } from 'components/diagram/services';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { useProductsByIds } from 'hooks/use-products-by-ids';
import { useCurrentProject } from 'hooks/use-current-project';

import { ComponentOverviewHitSimple } from 'components/component-overview/ComponentOverviewHit.Simple';

import cx from './DiagramGettingStarted.module.css';

type Props = {
    onClose?: () => void;
};

const DiagramGettingStartedProducts: FC<Props> = ({ onClose }) => {
    const project = useCurrentProject();

    const { components: referenceProducts, isLoading } = useProductsByIds(project?.template.components ?? []);

    const compatibleProductIds = flat(
        referenceProducts
            .filter(({ compatibleWith }) => !!compatibleWith)
            .map(({ compatibleWith }) => compatibleWith ?? []) ?? [],
    );
    const { components: _compatibleProducts } = useProductsByIds(compatibleProductIds);
    const compatibleProducts = _compatibleProducts.filter(
        (product) => !referenceProducts.some((ref) => ref.id === product.id),
    );

    const allProducts = [...referenceProducts, ...compatibleProducts];

    const [selectedProductIds, setSelectedProductIds] = useState<string[]>(project?.template.components ?? []);

    const toggleProduct = (productId: string) => {
        if (selectedProductIds.includes(productId)) {
            setSelectedProductIds(selectedProductIds.filter((selectedId) => selectedId !== productId));
        } else {
            setSelectedProductIds([...selectedProductIds, productId]);
        }
    };

    const generate = () => {
        const selectedProducts = allProducts.filter((product) => selectedProductIds.includes(product.id));

        if (selectedProducts.length > 0) {
            DiagramService.createDiagramFromComponents(selectedProducts);
            DiagramSyncService.save();
        }

        onClose?.();
    };

    return (
        <>
            <Divider label="Start design with these products?" />

            <Stack gap={4}>
                <InputLabel>Reference products</InputLabel>

                {isLoading && <Loader size="xs" color="gray.5" my="xs" mx="auto" />}

                {referenceProducts.map((component) => (
                    <UnstyledButton
                        className={cx.product}
                        onClick={() => {
                            toggleProduct(component.id);
                        }}
                        data-selected={selectedProductIds.includes(component.id)}
                        key={component.type}
                    >
                        <ComponentOverviewHitSimple
                            component={component}
                            rightSide={<IoCheckmark className={cx.selectedIcon} />}
                        />
                    </UnstyledButton>
                ))}
            </Stack>

            {compatibleProducts.length > 0 && (
                <Stack gap={4}>
                    <InputLabel>Compatible products</InputLabel>
                    {compatibleProducts.map((component) => (
                        <UnstyledButton
                            className={cx.product}
                            onClick={() => {
                                toggleProduct(component.id);
                            }}
                            data-selected={selectedProductIds.includes(component.id)}
                            key={component.type}
                        >
                            <ComponentOverviewHitSimple
                                component={component}
                                rightSide={<IoCheckmark className={cx.selectedIcon} />}
                            />
                        </UnstyledButton>
                    ))}
                </Stack>
            )}

            <Button onClick={generate}>Start designing</Button>
        </>
    );
};

export { DiagramGettingStartedProducts };
