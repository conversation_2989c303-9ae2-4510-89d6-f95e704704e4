import { CollectionConfig } from 'payload';

import { Views } from '../../fields/Views';
import { Viewed } from '../../fields/Viewed';

import { addLatestMessage } from './hooks/add-latest-message';
import { broadcast } from './hooks/broadcast';
import { changeCreatedByAndTeam } from './hooks/change-created-by-and-team';

import { view } from './endpoints/view';
import { adminList } from './endpoints/admin-list';
import { calculateViewed } from './helpers/calculate-viewed';

import { getRequestManufacturers } from '../../helpers/get-request-manufacturers';
import { getRequestTeam } from '../../helpers/get-request-team';

const IntercomChannels: CollectionConfig = {
    slug: 'intercomChannels',
    access: {
        read: async ({ req }) => {
            const team = await getRequestTeam(req);
            const manufacturers = await getRequestManufacturers(req);

            const conditions: any[] = [
                {
                    'access.company': {
                        in: manufacturers.map((manufacturer) => manufacturer.id),
                    },
                },
            ];

            if (team) {
                conditions.push({
                    team: {
                        equals: team.id,
                    },
                });
            }
            return { or: conditions };
        },
    },
    fields: [
        {
            name: 'name',
            type: 'text',
        },
        {
            name: 'type',
            type: 'select',
            options: [
                { value: 'component', label: 'Component' },
                { value: 'company', label: 'Company' },
                { value: 'project', label: 'Project' },
            ],
        },
        {
            name: 'project',
            type: 'relationship',
            relationTo: 'projects',
            hasMany: false,
            maxDepth: 0,
        },
        {
            name: 'company',
            type: 'relationship',
            relationTo: 'manufacturers', // this will become companies
            hasMany: false,
            maxDepth: 0,
            required: true,
        },
        {
            name: 'component',
            type: 'relationship',
            relationTo: 'components',
            hasMany: false,
            maxDepth: 0,
        },
        {
            name: 'access',
            type: 'group',
            fields: [
                {
                    name: 'company',
                    type: 'relationship',
                    relationTo: 'manufacturers',
                    maxDepth: 0,
                    defaultValue: () => [],
                },
            ],
        },
        Views,
        Viewed,
        { name: 'createdBy', type: 'relationship', relationTo: 'users', maxDepth: 0 },
        {
            name: 'team',
            type: 'relationship',
            relationTo: 'teams',
            maxDepth: 0,
            required: true,
            index: true,
        },
    ],
    hooks: {
        beforeChange: [changeCreatedByAndTeam],
        afterChange: [broadcast],
        afterRead: [
            addLatestMessage,
            async ({ req, doc }) => {
                return await calculateViewed(req, doc);
            },
        ],
    },
    endpoints: [view, adminList],
};

export default IntercomChannels;
