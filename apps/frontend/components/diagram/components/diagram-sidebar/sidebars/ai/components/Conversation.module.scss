.avatar {
    position: sticky;
    top: var(--mantine-spacing-md);
}

.content {
    flex-grow: 0;
    flex-shrink: 0;

    // Avatar + Gap
    width: calc(100% - 30px - 16px);
}

.markdown {
    p {
        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }

    table {
        border: 1px solid var(--mantine-color-gray-2);
        border-collapse: collapse;
    }

    td,
    th {
        padding: 0.5em 0.75em;

        white-space: nowrap;

        border: 1px solid var(--mantine-color-gray-2);
    }
}

.markdownScrollableTable {
    
}
