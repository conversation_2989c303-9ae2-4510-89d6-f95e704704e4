color: null
fieldLayouts:
  5715f56a-a73e-4088-9513-118ae76eee23:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-01-09T19:52:05+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: eb6cd80e-da5f-4bdf-afaf-00c958850ee8
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-06-17T20:08:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 03e54a6a-6f2a-453c-b8e4-994ae89bad83 # Font
            handle: bodyFont
            includeInCards: false
            instructions: null
            label: 'Body Font'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 19d36df1-b586-4865-886b-7113e3b07e58
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-06-17T20:08:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 83d4af74-427e-4a83-9b85-2877648e6335 # Font Size
            handle: bodyFontSize
            includeInCards: false
            instructions: null
            label: 'Body Font Size'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: c971aabe-179a-45d0-a86c-6c7ade5472f7
            userCondition: null
            warning: null
            width: 25
          -
            dateAdded: '2025-06-17T20:08:29+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 0c2ab25c-afd4-4827-ad03-2acbe67a0fe5 # Color
            handle: bodyFontColor
            includeInCards: false
            instructions: null
            label: 'Body Font Color'
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: d85ffbc3-b031-469d-8d94-18763219e089
            userCondition: null
            warning: null
            width: 25
        name: Content
        uid: 8a6dcc04-4ce6-4a11-8662-4d3b2be0ee83
        userCondition: null
handle: textStyle
hasTitleField: true
icon: null
name: 'Text Style'
showSlugField: false
showStatusField: false
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: none
