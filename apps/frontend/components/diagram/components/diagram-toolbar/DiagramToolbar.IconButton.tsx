import type { FC, ReactNode } from 'react';

import { Tooltip, UnstyledButton } from '@mantine/core';

import cx from './DiagramToolbar.module.css';

const DiagramToolbarIconButton: FC<{
    tooltip?: ReactNode;
    shortcut?: ReactNode;
    onClick: () => void;
    active?: boolean;
    disabled?: boolean;
    children: ReactNode;
}> = ({ tooltip = null, shortcut = null, onClick, active, disabled, children }) => {
    return (
        <Tooltip
            label={
                <>
                    <span>{tooltip}</span>
                    {shortcut && <span className={cx.tooltipShortcut}>{shortcut}</span>}
                </>
            }
            classNames={{
                tooltip: cx.tooltip,
            }}
            disabled={!tooltip && !shortcut}
            withArrow
        >
            <UnstyledButton className={cx.iconButton} onClick={onClick} disabled={disabled} data-active={active}>
                {children}
            </UnstyledButton>
        </Tooltip>
    );
};

export { DiagramToolbarIconButton };
