.root {
    position: relative;
    z-index: 199;

    height: var(--header-height);

    padding: var(--mantine-spacing-xs);

    background-color: var(--mantine-color-gray-9);

    color: var(--mantine-color-gray-1);

    font-weight: 500;
    font-size: var(--mantine-font-size-md);

    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.rightActions {
    @media (max-width: var(--mantine-breakpoint-xl)) {
        gap: var(--mantine-spacing-md) !important;

        :global(.mantine-Button-root) {
            padding: 0;
        }

        :global(.mantine-Button-section) {
            padding: 0;
            margin: 0;
        }

        :global(.mantine-Button-label) {
            display: none;
        }
    }
}
