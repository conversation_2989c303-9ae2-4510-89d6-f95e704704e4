import React, { FC } from 'react';

import { Anchor, Box, Card, Stack, Title } from '@mantine/core';
import { IoImageOutline } from 'react-icons/io5';

import { RTEContent } from 'components/rte-content/RTEContent';
import { IKImage } from 'components/ik-image/IKImage';

import cx from '../company-profile/components/Stub.module.css';

const CardTeaserPreview: FC<{
    name: string;
    description: any;
    image: any;
    button: any;
}> = ({ name, description, image, button }) => {
    return (
        <Card withBorder className={cx.root} radius="sm">
            {image ? (
                <Box className={cx.imageWrapper}>
                    <IKImage fileOrId={image as any} width={600} height={340} alt={name || ''} />
                </Box>
            ) : (
                <Box className={`${cx.imageWrapper} ${cx.imagePreview}`}>
                    <IoImageOutline size={20} />
                </Box>
            )}

            <Stack className={cx.content} gap="sm">
                {name && (
                    <Anchor component="div" td="none" c="brand">
                        <Title order={3} fz="lg" fw={700} c="brand">
                            {name}
                        </Title>
                    </Anchor>
                )}

                {description && <RTEContent content={description} />}

                {button?.label && button?.url && (
                    <Anchor href={button.url} target="_blank" style={{ alignSelf: 'flex-start' }} fw={600}>
                        {button.label}
                    </Anchor>
                )}
            </Stack>
        </Card>
    );
};

export { CardTeaserPreview };
