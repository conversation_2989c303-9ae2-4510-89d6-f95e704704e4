import type { FC, ReactNode } from 'react';

import { Box } from '@mantine/core';

import cx from './DiagramToolbar.module.css';

const DiagramToolbarMenuItem: FC<{
    icon?: ReactNode;
    disabled?: boolean;
    onClick: () => any;
    children: ReactNode;
}> = ({ children, icon = null, disabled = false, onClick }) => {
    return (
        <Box className={cx.menuItem} component="button" onClick={onClick} disabled={disabled}>
            <Box className={cx.menuItemIcon}>{icon}</Box>
            <Box>{children}</Box>
        </Box>
    );
};

export { DiagramToolbarMenuItem };
