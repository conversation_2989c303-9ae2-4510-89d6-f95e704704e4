import { z } from 'zod';

export type InsulationType =
    | 'PVC'
    | 'XLPE'
    | 'EPR'
    | 'PE'
    | 'PTFE'
    | 'silicone'
    | 'rubber'
    | 'fiberglass'
    | 'paper'
    | 'bare';

type InsulationInfo = {
    value: InsulationType;
    label: string;
    maxTemperature: number; // in °C
};

const options: InsulationInfo[] = [
    { value: 'PVC', label: 'PVC (Polyvinyl Chloride)', maxTemperature: 70 },
    { value: 'XLPE', label: 'XLPE (Cross-Linked Polyethylene)', maxTemperature: 90 },
    { value: 'EPR', label: 'EPR (Ethylene Propylene Rubber)', maxTemperature: 90 },
    { value: 'PE', label: 'PE (Polyethylene)', maxTemperature: 75 },
    { value: 'PTFE', label: 'PTFE (Teflon)', maxTemperature: 200 },
    { value: 'silicone', label: 'Silicone', maxTemperature: 180 },
    { value: 'rubber', label: 'Rubber', maxTemperature: 90 },
    { value: 'fiberglass', label: 'Fiberglass', maxTemperature: 200 },
    { value: 'paper', label: 'Paper (Oil-Impregnated)', maxTemperature: 105 },
    { value: 'bare', label: 'No Insulation (Bare Conductor)', maxTemperature: 0 },
];

const validator = z.enum(['PVC', 'XLPE', 'EPR', 'PE', 'PTFE', 'silicone', 'rubber', 'fiberglass', 'paper', 'bare']);

function getMaxTemperature(type: InsulationType): number {
    return options.find((opt) => opt.value === type)?.maxTemperature ?? 0;
}

export const Insulation = {
    validator,
    options,
    getMaxTemperature,
};
