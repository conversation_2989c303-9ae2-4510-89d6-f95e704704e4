import React, { FC, useEffect } from 'react';
import { UserTip } from '@repo/dcide-component-models';

import { Box } from '@mantine/core';

import { TipService } from 'components/diagram/services/TipService';

import { inviteCollaborator } from './formatters/invite-collaborator';
import { linkProduct } from './formatters/link-product';
import { addComment } from './formatters/add-comment';
import { downloadImage } from './formatters/download-image';

import cx from './diagram-tips.module.css';

const formatters = {
    [UserTip.INVITE_COLLABORATOR]: inviteCollaborator,
    [UserTip.LINK_PRODUCT]: linkProduct,
    [UserTip.ADD_COMMENT]: addComment,
    [UserTip.DOWNLOAD_IMAGE]: downloadImage,
    [UserTip.COMPANY_SIGNUP]: null,
};

const DiagramTip: FC<{
    tip: UserTip;
}> = ({ tip }) => {
    const formatter = formatters[tip];

    useEffect(() => {
        TipService.dismiss(tip).then();
    }, [tip]);

    return formatter ? (
        <React.Fragment>
            <Box className={cx.tipTitle}>
                {formatter.icon}
                {formatter.title}
            </Box>
            <Box className={cx.tipDescription}>{formatter.description}</Box>
        </React.Fragment>
    ) : null;
};

export { DiagramTip };
