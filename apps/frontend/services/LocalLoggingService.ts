import { LocalStorageService } from 'services/LocalStorageService';

class LocalLoggingService {
    static {
        if (typeof window !== 'undefined') {
            (<any>window).enableLocalLogging = () => {
                LocalLoggingService.enabled = true;
                LocalStorageService.store('localLoggingEnabled', 'true');

                // eslint-disable-next-line no-console
                console.log('Local logging enabled');
            };

            (<any>window).disableLocalLogging = () => {
                LocalLoggingService.enabled = false;
                LocalStorageService.store('localLoggingEnabled', 'false');

                // eslint-disable-next-line no-console
                console.log('Local logging disabled');
            };
        }
    }

    static enabled = LocalStorageService.get('localLoggingEnabled') === 'true';

    static log = (...data: any) => {
        if (LocalLoggingService.enabled) {
            // eslint-disable-next-line no-console
            console.log(...data);
        }
    };
}

export { LocalLoggingService };
