import React, { FC, useState } from 'react';

import { Box, Menu, Stack } from '@mantine/core';

import { TbCircle, TbCircleCheck, TbSortAscending, TbSortDescending, TbFilter } from 'react-icons/tb';
import { IoAddSharp } from 'react-icons/io5';

import { ContextMenu } from 'components/context-menu/ContextMenu';
import { DiagramCommentList } from 'components/diagram/components/diagram-comments/DiagramCommentList';
import { DiagramSidebar } from 'components/diagram';

import { CommentService } from 'components/diagram/services/CommentService';

import { useDiagramComments, useDiagramCommentsActions } from 'components/diagram/hooks';

import classes from './CommentsSidebar.module.css';

const CommentOverviewSidebar: FC = () => {
    const comments = useDiagramComments();
    const [mode, setMode] = useState<'all' | 'unread'>('all');

    const { sorting, filters } = useDiagramCommentsActions();

    const numberOfUnreadComments = comments.filter((comment) => !comment.seen).length;
    const filteredComments = mode === 'unread' ? comments.filter((comment) => !comment.seen) : comments;

    return (
        <React.Fragment>
            <DiagramSidebar.SimpleHeader
                leftSection={
                    <DiagramSidebar.ButtonGroup>
                        <DiagramSidebar.Button
                            onClick={() => {
                                setMode('all');
                            }}
                            data-active={mode === 'all'}
                        >
                            <span>All</span>
                            <strong>{comments.length}</strong>
                        </DiagramSidebar.Button>
                        <DiagramSidebar.Button
                            onClick={() => {
                                setMode('unread');
                            }}
                            data-active={mode === 'unread'}
                        >
                            <span>Unread</span>
                            <strong>{numberOfUnreadComments}</strong>
                        </DiagramSidebar.Button>
                    </DiagramSidebar.ButtonGroup>
                }
                rightSection={
                    <DiagramSidebar.ButtonGroup>
                        <AddCommentButton />
                        <ContextMenu
                            position="bottom-end"
                            target={
                                <DiagramSidebar.Button>
                                    <TbSortDescending strokeWidth={1.25} />
                                </DiagramSidebar.Button>
                            }
                        >
                            <Menu.Item
                                onClick={() => {
                                    CommentService.sortByLatestActivity();
                                }}
                                leftSection={
                                    sorting === 'latest-activity' ? (
                                        <TbSortDescending />
                                    ) : (
                                        <TbSortDescending data-disabled />
                                    )
                                }
                                closeMenuOnClick={false}
                            >
                                Latest activity
                            </Menu.Item>
                            <Menu.Item
                                onClick={() => {
                                    CommentService.sortByNewest();
                                }}
                                leftSection={
                                    sorting === 'newest' ? <TbSortDescending /> : <TbSortDescending data-disabled />
                                }
                                closeMenuOnClick={false}
                            >
                                Newest
                            </Menu.Item>
                            <Menu.Item
                                onClick={() => {
                                    CommentService.sortByOldest();
                                }}
                                leftSection={
                                    sorting === 'oldest' ? <TbSortAscending /> : <TbSortAscending data-disabled />
                                }
                                closeMenuOnClick={false}
                            >
                                Oldest
                            </Menu.Item>
                        </ContextMenu>
                        <ContextMenu
                            position="bottom-end"
                            target={
                                <DiagramSidebar.Button>
                                    <TbFilter strokeWidth={1.25} />
                                </DiagramSidebar.Button>
                            }
                        >
                            <Menu.Item
                                onClick={() => {
                                    CommentService.toggleResolvedComments();
                                }}
                                leftSection={
                                    filters.resolved ? <TbCircleCheck data-green /> : <TbCircle data-disabled />
                                }
                                closeMenuOnClick={false}
                            >
                                {filters.resolved ? 'Hide' : 'Show'} resolved comments
                            </Menu.Item>
                            <Menu.Item
                                onClick={() => {
                                    CommentService.toggleDeletedComments();
                                }}
                                leftSection={
                                    filters.deleted ? <TbCircleCheck data-green /> : <TbCircle data-disabled />
                                }
                                closeMenuOnClick={false}
                            >
                                {filters.deleted ? 'Hide' : 'Show'} your deleted comments
                            </Menu.Item>
                        </ContextMenu>
                    </DiagramSidebar.ButtonGroup>
                }
            />
            <Box>
                <DiagramCommentList comments={filteredComments} />
                {filteredComments.length === 0 && mode === 'all' && (
                    <Stack className={classes.listNoResults}>
                        <AddCommentButton label="Add your first comment" />
                        Or click anywhere on the canvas to add your first comment
                    </Stack>
                )}
                {filteredComments.length === 0 && mode === 'unread' && (
                    <Box className={classes.listNoResults}>You have no unread comments</Box>
                )}
            </Box>
        </React.Fragment>
    );
};

const AddCommentButton = ({ label = 'New comment' }: { label?: string }) => {
    return (
        <DiagramSidebar.Button
            onClick={() => {
                CommentService.activatePlaceholder();
            }}
        >
            <IoAddSharp />
            <span>{label}</span>
        </DiagramSidebar.Button>
    );
};

export { CommentOverviewSidebar };
