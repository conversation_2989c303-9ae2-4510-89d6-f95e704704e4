import type { FC, ReactNode } from 'react';

import { Box, BoxProps } from '@mantine/core';

import cx from './DiagramToolbar.module.css';

const DiagramToolbarText: FC<
    {
        children: ReactNode;
        dimmed?: boolean;
    } & BoxProps
> = ({ children, dimmed = false, ...rest }) => (
    <Box className={cx.text} data-dimmed={dimmed} {...rest}>
        {children}
    </Box>
);

export { DiagramToolbarText };
