import { SearchService } from '@/services/search/SearchService';
import type { Endpoint } from 'payload';
import {OpenAI} from 'openai';
import {config} from '@/config';

const testzone: Endpoint = {
    path: '/testzone',
    method: 'get',
    handler: async (request) => {
        const { payload } = request;

        const email = '<EMAIL>';
        const company = 'EOSE Energy';

        const { docs: companies } = await payload.find({
            collection: 'manufacturers',
            select: {
                name: true,
                internal: true,
            },
            pagination: false,
            depth: 0,
        });

        const openai = new OpenAI({
            apiKey: config.openai.apiKey,
            // baseURL: 'https://generativelanguage.googleapis.com/v1beta/openai/',
        });

        console.time('✅ Suggest Companies');

        const completion = await openai.chat.completions.create({
            model: 'gpt-4.1-mini',
            response_format: {
                type: 'json_object',
            },
            messages: [
                {
                    role: 'system',
                    content: `
                        You are an AI assistant designed to suggest potential company assignments for a given user based on their email address and provided company name.
                        You will receive user details (email, company name) and a list of existing companies from the system.

                        **Input Data Structure:**
                        - The user's email address ('email') and provided company name ('company').
                        - A JSON array of existing company objects provided within the 'COMPANIES DATA' block. Each object in this array is expected to have the following structure:
                          {
                            "id": string,    // Unique ID for the existing company
                            "name": string,  // Name of the existing company
                          }

                        **Your Task:**
                        Analyze the user's email and company name. Compare them against the provided list of existing companies ('COMPANIES DATA') to identify the most relevant potential company assignments for the user.

                        **Analysis & Relevance Criteria (Prioritized Order):**
                        1.  **Exact/Close Name Match:** The user's input company name is an exact or very close match (case-insensitive, potentially ignoring common suffixes like 'Inc.', 'Ltd.') to an existing company name.
                        2.  **Keyword Match:** A significant number of keywords (e.g., split the user's input 'company' name by spaces) are present within an existing company name (case-insensitive comparison).

                        **Instructions & Output Requirements:**
                        *   Analyze the user's email domain (text after '@').
                        *   Analyze the user's company name for exact/close matches and keyword matches against existing company names.
                        *   Generate suggestions based *only* on the provided data and the specified criteria. Do not invent companies or user data.
                        *   Your entire response MUST be a single, valid JSON object.
                        *   This JSON object must contain a single key: "suggestions".
                        *   The value of "suggestions" must be a JSON array.
                        *   Each element in the array must be an object representing a suggested company assignment with the following keys:
                            *   'id': The company id of the suggested existing company (string).
                            *   'reason': A brief explanation aligning with the criteria used (e.g., "Close match on company name", "Partial keyword match in company name", ...) (string).
                        *   The array must contain a **maximum of 4** suggested company objects.
                        *   The suggestions array MUST be ordered from **most relevant to least relevant** based on the prioritized criteria.
                        *   If no relevant companies are found, the "suggestions" array must be **empty** ([]).

                        **Ignoring commonly used words:**
                        When comparing company names and email domains, you **must** ignore common words and suffixes/prefixes like:
                        *   Energy
                        *   Partners
                        *   Group
                        *   Company
                        *   Corporation
                        *   Enterprises
                        *   Engineering
                        *   World
                        *   Inc.
                        *   Ltd.
                        *   LLC
                        *   The
                        *   DC
                        *   AC

                        === COMPANIES DATA ===
                        ${JSON.stringify(companies.filter((company) => !company.internal))}
                        === END COMPANIES DATA ===
                    `,
                },
                {
                    role: 'user',
                    content: `
                        Suggest potential company assignments for this user:
                        Email: ${email}
                        Company Name: ${company}
                    `,
                },
            ],
        });

        console.timeEnd('✅ Suggest Companies');

        return Response.json({ success: true, completion }, { status: 200 });
    },
};

export { testzone };
