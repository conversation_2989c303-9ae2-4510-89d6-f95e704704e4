import React, { FC } from 'react';

import { DiagramComponentInstance } from '@repo/dcide-component-models';

import { Box, FileButton, Loader } from '@mantine/core';
import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar';
import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';

import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { FileService as DiagramFileService } from 'components/diagram/services/FileService';
import { FileService } from 'services/FileService';
import { ProductSearchService } from 'components/diagram/services/ProductSearchService';

import { useComponent } from 'hooks/use-component';
import { useComponentFilesPublicOnly, useComponentFilesPrivateOnly } from 'hooks/use-component-files';
import { useFeatureAccess, FeatureKey } from 'hooks/use-feature-access';

import { FileWithIcon } from 'elements/file-with-icon/FileWithIcon';

import { BsBookmarkPlus } from 'react-icons/bs';
import { IoAddSharp, IoSearchOutline } from 'react-icons/io5';

import cx from './Files.module.css';

const Files: FC<{ componentInstance: DiagramComponentInstance }> = ({ componentInstance }) => {
    const { componentId } = componentInstance;
    const { component } = useComponent(componentId, {
        skipAccessCheck: true,
    });

    const { files: publicFiles, isLoading: isLoadingPublicFiles } = useComponentFilesPublicOnly(componentId);
    const { files: teamFiles, isLoading: isLoadingPrivateFiles } = useComponentFilesPrivateOnly(componentId);

    const onDeleteTeamComponentFile = async (fileId: string) => {
        await FileService.deleteFile({
            associateWith: 'teamComponent',
            associatedId: componentId!,
            file: fileId,
        });

        DiagramFileService.mutateComponentFilesPrivateOnly(componentId!);
    };

    const onRenameTeamComponentFile = async (fileId: string, name: string) => {
        await FileService.update(fileId, { name });

        DiagramFileService.mutateComponentFilesPrivateOnly(componentId!);
    };

    const onChangeType = async (componentFileId: string, type: string) => {
        if (!componentId) return;

        await DiagramFileService.updateComponentFileType({
            componentFileId,
            type,
        });

        DiagramFileService.mutateComponentFilesPrivateOnly(componentId);
    };

    return componentId ? (
        <React.Fragment>
            <DiagramSidebar.Section>
                <DiagramSidebar.SectionTitle>Public files</DiagramSidebar.SectionTitle>
                {isLoadingPublicFiles ? (
                    <Loader size="sm" />
                ) : (
                    <Box className={cx.files}>
                        {publicFiles.map((file, index) => (
                            <FileWithIcon file={file.file} type={file.type} key={index} />
                        ))}
                        {publicFiles.length === 0 && (
                            <Box className={cx.filesEmpty}>There are no public files for this component.</Box>
                        )}
                    </Box>
                )}
            </DiagramSidebar.Section>
            <DiagramSidebar.Divider />
            <DiagramSidebar.Section>
                <DiagramSidebar.SectionTitle>Team files</DiagramSidebar.SectionTitle>
                {isLoadingPrivateFiles ? (
                    <Loader size="sm" />
                ) : (
                    <>
                        <Box className={cx.files}>
                            {teamFiles.map(({ id: componentFileId, file, type }, index) => (
                                <FileWithIcon
                                    file={file}
                                    type={type}
                                    key={index}
                                    onRename={(name) => onRenameTeamComponentFile(file.id, name)}
                                    onDelete={() => onDeleteTeamComponentFile(file.id)}
                                    onChangeType={(type) => onChangeType(componentFileId, type)}
                                />
                            ))}
                            {teamFiles.length === 0 && (
                                <Box className={cx.filesEmpty}>Your team has no files attached to this component.</Box>
                            )}
                        </Box>
                        <Box className={cx.filesActions}>
                            <FileButton
                                onChange={(file) => {
                                    if (file) {
                                        DiagramFileService.openAddComponentFileModal(component, file);
                                    }
                                }}
                            >
                                {(props) => (
                                    <DiagramSidebar.Button {...props}>
                                        <IoAddSharp />
                                        <span>Add a team file</span>
                                    </DiagramSidebar.Button>
                                )}
                            </FileButton>
                        </Box>
                    </>
                )}
            </DiagramSidebar.Section>
        </React.Fragment>
    ) : (
        <FilesNoComponent componentInstance={componentInstance} />
    );
};

const FilesNoComponent: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const uploadFilesEnabled = useFeatureAccess(FeatureKey.UPLOAD_DIAGRAM_FILES);

    if (!uploadFilesEnabled) {
        return (
            <SubscriptionOnboardingMessage>
                Want to upload files to diagram and library components?
            </SubscriptionOnboardingMessage>
        );
    }

    return (
        <DiagramSidebar.Section>
            <Box className={cx.filesNoComponentDescription}>
                Link this component instance to an existing product or create a reusable component to add files.
            </Box>
            <Box className={cx.filesNoComponentActions}>
                <DiagramSidebar.Button
                    onClick={() => {
                        ComponentInstanceService.openCreateReusableComponentModal(componentInstance);
                    }}
                >
                    <BsBookmarkPlus />
                    <span>Save component</span>
                </DiagramSidebar.Button>
                <DiagramSidebar.Button
                    onClick={() => {
                        ProductSearchService.openProductSearchFor(componentInstance);
                    }}
                >
                    <IoSearchOutline />
                    <span>Search product catalog</span>
                </DiagramSidebar.Button>
            </Box>
        </DiagramSidebar.Section>
    );
};

export { Files };
