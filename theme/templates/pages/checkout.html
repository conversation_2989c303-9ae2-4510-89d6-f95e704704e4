{% extends 'app' %}

{% block content %}

    {% include 'components/header' with {
        'size': entry.headerSize,
        'image': entry.headerImage.one,
        'title': entry.title,
    } %}

    {% include 'content/sections' with {
        'sections': entry.sections.all,
    } %}

    <div class="container is-horizontal is-vertical">
        <div data-webshop-checkout></div>
    </div>

    <script type="text/javascript">
        {% set orderCartField = craft.app.fields.getFieldByHandle('orderNewCart') %}
        {% set orderCartFieldBlockType = craft.superTable.getSuperTableBlocks(orderCartField.id)[0] %}
        window.cartBlockType = {{ orderCartFieldBlockType }};
        window.checkout = {{ entry.presentJavaScriptData | json_encode | raw }};
        window.webshopUrl = '{{ craft.categories.group('productCategories').level(1).one.url ?? '' }}';
    </script>

{% endblock %}
