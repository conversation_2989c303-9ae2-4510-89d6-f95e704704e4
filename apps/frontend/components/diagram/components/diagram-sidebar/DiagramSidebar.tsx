import React, { FC, useEffect, useState } from 'react';

import { useHotkeys } from 'react-hotkeys-hook';
import { Resizable, ResizableProps } from 'react-resizable';
import { Box, Flex } from '@mantine/core';

import { SimpleButton, SimpleButtonGroup } from 'elements/buttons';

import {
    SidebarDivider,
    SidebarLoader,
    SidebarSection,
    SidebarSectionTitle,
    SidebarTabs,
    SidebarHeader,
    SidebarSimpleHeader,
} from 'components/sidebar/components';
import { SidebarService } from 'components/diagram/services/SidebarService';

import { Z_INDEX } from '../../diagram-z-index';
import { DIAGRAM_SIDEBAR_WIDTH_MIN } from '../../diagram-dimensions';

import {
    DiagramSidebarExport,
    DiagramSidebarNav,
    DiagramSidebarNavAction,
    DiagramSidebarNavItem,
    DiagramSidebarShare,
    DiagramSidebarSidebars,
} from './components';
import { SidebarEditSubtype, SidebarTab, SidebarType } from 'components/diagram/state/sidebar';
import { DiagramSidebarScrollArea } from './DiagramSidebarScrollArea';

import { useSidebar } from 'components/diagram/hooks/use-sidebar';

import cx from './DiagramSidebar.module.scss';

const DiagramSidebar: FC & {
    Button: typeof SimpleButton;
    ButtonGroup: typeof SimpleButtonGroup;
    Divider: typeof SidebarDivider;
    Header: typeof SidebarHeader;
    Loader: typeof SidebarLoader;
    SimpleHeader: typeof SidebarSimpleHeader;
    Section: typeof SidebarSection;
    SectionTitle: typeof SidebarSectionTitle;
    Tabs: typeof SidebarTabs;
    Sidebars: typeof DiagramSidebarSidebars;
    Nav: typeof DiagramSidebarNav;
    NavAction: typeof DiagramSidebarNavAction;
    NavItem: typeof DiagramSidebarNavItem;
    Share: typeof DiagramSidebarShare;
    Export: typeof DiagramSidebarExport;
} = () => {
    // biome-ignore lint/correctness/noUnusedVariables: trigger re-render when collapsing..
    const { collapsed, width, editComponentInstanceTab, editSidebar, type: sidebarType } = useSidebar();

    const showSidebar = SidebarService.getShowSidebar();

    const [resizing, setResizing] = useState(false);

    const handleStart: ResizableProps['onResizeStart'] = () => {
        document.body.setAttribute('data-resizing-ew', 'true');
        setResizing(true);
    };

    const handleResize: ResizableProps['onResize'] = (_, { size }) => {
        SidebarService.setWidth(size.width);
        document.documentElement.style.setProperty('--diagram-sidebar-width', `${size.width}px`);
    };

    const handleStop: ResizableProps['onResizeStop'] = () => {
        document.body.removeAttribute('data-resizing-ew');
        setResizing(false);
    };

    useHotkeys('esc', () => {
        SidebarService.openDefaultSidebar();
    });

    useEffect(() => {
        if (
            sidebarType === SidebarType.EDIT &&
            editSidebar === SidebarEditSubtype.EDIT_COMPONENT &&
            editComponentInstanceTab === SidebarTab.AI
        ) {
            document.body.setAttribute('data-ai-open', 'true');
        } else {
            document.body.removeAttribute('data-ai-open');
        }

        return () => {
            document.body.removeAttribute('data-ai-open');
        };
    }, [editComponentInstanceTab, editSidebar, sidebarType]);

    if (!showSidebar) return <DiagramSidebar.Nav />;

    return (
        <Resizable
            axis="x"
            width={width}
            height={0}
            onResize={handleResize}
            onResizeStart={handleStart}
            onResizeStop={handleStop}
            handle={(_, ref) => <Box ref={ref} className={cx.resizeHandle} data-resizing={resizing} />}
            resizeHandles={['w']}
            minConstraints={[DIAGRAM_SIDEBAR_WIDTH_MIN, 0]}
            maxConstraints={[window.innerWidth * 0.7, 0]}
            draggableOpts={{
                grid: [50, 0],
            }}
        >
            <Box
                w={width}
                visibleFrom="sm"
                className={['diagram-sidebar', cx.sidebar].join(' ')}
                style={{
                    zIndex: Z_INDEX.SIDEBAR,
                }}
                data-diagram-sidebar
                data-resizing={resizing}
            >
                <Flex w="100%" h="100%">
                    <DiagramSidebarScrollArea
                        style={{
                            flexGrow: 1,
                        }}
                    >
                        <DiagramSidebar.Sidebars />
                    </DiagramSidebarScrollArea>
                    <DiagramSidebar.Nav />
                </Flex>
            </Box>
        </Resizable>
    );
};

DiagramSidebar.Button = SimpleButton;
DiagramSidebar.ButtonGroup = SimpleButtonGroup;
DiagramSidebar.Divider = SidebarDivider;
DiagramSidebar.Header = SidebarHeader;
DiagramSidebar.Loader = SidebarLoader;
DiagramSidebar.SimpleHeader = SidebarSimpleHeader;
DiagramSidebar.Section = SidebarSection;
DiagramSidebar.SectionTitle = SidebarSectionTitle;
DiagramSidebar.Tabs = SidebarTabs;
DiagramSidebar.Sidebars = DiagramSidebarSidebars;
DiagramSidebar.Nav = DiagramSidebarNav;
DiagramSidebar.NavItem = DiagramSidebarNavItem;
DiagramSidebar.NavAction = DiagramSidebarNavAction;
DiagramSidebar.Share = DiagramSidebarShare;
DiagramSidebar.Export = DiagramSidebarExport;

export { DiagramSidebar };
