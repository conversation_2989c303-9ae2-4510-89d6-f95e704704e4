import { AIAccessModal } from 'components/modals/AIAccessModal';
import { LoginModal } from 'components/login-modal/LoginModal';
import { InfoModal } from 'components/info-modal/InfoModal';
import { SimpleSubscriptionModal } from 'components/subscriptions/SimpleSubscriptionModal';
import { RequestAccessModal } from 'components/diagram/modals/RequestAccessModal';
import { RequestInAppSupportModal } from 'components/diagram/modals/RequestInAppSupportModal';
import { CreateChatChannel } from 'components/diagram/modals/CreateChatChannel';
import { UpdateChatChannel } from 'components/diagram/modals/UpdateChatChannel';
import { ForceRefreshModal } from 'components/diagram/modals/ForceRefreshModal';
import { ZoneDroopCurvesModal } from 'components/diagram/modals/ZoneDroopCurvesModal';
import { CreateTeamModal } from 'components/create-team-modal/CreateTeamModal';
import { FeatureLimitTrackerModal } from 'components/subscriptions/FeatureLimitTrackerModal';
import { SwitchTeamModal as GenericSwitchTeamModal } from 'components/modals/SwitchTeamModal';
import { ExportDiagramModal } from 'components/diagram/modals/ExportDiagramModal';
import { CreateReusableComponent } from 'components/diagram/modals/CreateReusableComponent';
import { CreateSimulationProfileModal } from 'components/diagram/modals/CreateSimulationProfileModal';
import { DeviceTypeModal } from 'components/device-type-modal/DeviceTypeModal';
import { PublishModal } from 'components/diagram/components/diagram-sidebar/sidebars/share-diagram/components/PublishModal';
import { CompanySuggestionsModal } from 'components/modals/CompanySuggestionsModal';
import { TeamSuggestionsModal } from 'components/modals/TeamSuggestionsModal';
import { AIInspirationModal } from 'components/diagram/modals/AIInspirationModal';

import { companyProfileModals } from 'components/company-profile/modals';

const modals = {
    aiAccess: AIAccessModal,
    aiInspiration: AIInspirationModal,
    forceRefresh: ForceRefreshModal,
    infoModal: InfoModal,
    login: LoginModal,
    requestAccess: RequestAccessModal,
    requestInAppSupport: RequestInAppSupportModal,
    exportDiagram: ExportDiagramModal,
    createChatChannel: CreateChatChannel,
    updateChatChannel: UpdateChatChannel,
    simpleSubscriptionModal: SimpleSubscriptionModal,
    zoneDroopCurves: ZoneDroopCurvesModal,
    createTeam: CreateTeamModal,
    createReusableComponent: CreateReusableComponent,
    createSimulationProfile: CreateSimulationProfileModal,
    featureLimitTracker: FeatureLimitTrackerModal,
    genericSwitchTeam: GenericSwitchTeamModal,
    deviceType: DeviceTypeModal,
    publishDesign: PublishModal,
    teamSuggestions: TeamSuggestionsModal,
    companySuggestions: CompanySuggestionsModal,
    ...companyProfileModals,
};

export { modals };
