import { FC } from 'react';

import { CompanyProfile, File, PermissionCompany } from '@repo/dcide-component-models';

import { BackgroundImage, Box } from '@mantine/core';
import { BsPencil } from 'react-icons/bs';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { IKImage } from 'components/ik-image/IKImage';

import { AutoSave } from 'components/forms/AutoSave';
import { ImageField } from 'components/forms/fields/ImageField';
import { CompanyForm } from 'components/company-form/CompanyForm';

import cx from './Banner.module.css';

const Banner: FC<{ company: CompanyProfile; width?: number; height?: number }> = ({
    company,
    width = 1440,
    height = 200,
}) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    return (
        <Box className={cx.root} h={height}>
            {company.cover ? (
                <IKImage fileOrId={company.cover as File} width={width} height={height} alt={company.name} h={height} />
            ) : (
                <BackgroundImage src="/images/bg.jpg" w="100%" h={height} />
            )}

            {canEdit && (
                <Box className={cx.editCover}>
                    <CompanyForm
                        companyId={company.id}
                        defaultValues={{
                            cover: typeof company.cover === 'string' ? company.cover : company.cover?.id,
                        }}
                    >
                        <AutoSave instant={['cover']} />
                        <ImageField
                            name="cover"
                            group="manufacturers:images"
                            buttonLabel="Edit cover"
                            buttonProps={{
                                leftSection: <BsPencil />,
                                size: 'compact-xs',
                                variant: 'default',
                            }}
                            showPreview={false}
                        />
                    </CompanyForm>
                </Box>
            )}
        </Box>
    );
};

export { Banner };
