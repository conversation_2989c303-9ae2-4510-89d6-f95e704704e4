import { expect, test } from '@jest/globals';

import { DiagramSyncService } from '../services/DiagramSyncService';
import { ComponentInstanceService } from './ComponentInstanceService';
import { defaultDiagram } from '../data/default-diagram';
import {
    ComponentFileType,
    ComponentVisibility,
    Converter,
    DiagramComponentInstance,
} from '@repo/dcide-component-models';
import { ConnectionService } from '../services/ConnectionService';
import {
    ComponentImageType,
    FileVisibility,
} from '../../../../../packages/dcide-component-models/src/validators/component-sections';
import { PortService } from './PortService';

jest.mock('../../../services/LocalNotificationService');

beforeEach(() => {
    jest.restoreAllMocks();
});

test('Get by ID throws error when component instance is not found', async () => {
    DiagramSyncService.initialize(defaultDiagram(), undefined);
    const fakeId = 'not a real ID';

    const getByIdCall = () => ComponentInstanceService.getById(fakeId);

    expect(getByIdCall).toThrowError(`Component instance not found: ${fakeId}`);
});

test('Create a generic component from component instance spec', () => {
    const componentInstanceSpec = getBatterySpec();

    const component = ComponentInstanceService.convertComponentInstanceSpecToComponent(componentInstanceSpec);

    expect(component.manufacturer).toBe(null);
    expect(component.visibility).toEqual('private');
    expect(component.name).toEqual(componentInstanceSpec.label);
    expect(component.productIdentifier).toEqual(componentInstanceSpec.partNumber);
    expect(component.electrical).toEqual(componentInstanceSpec.specifications.electrical);
});

test('Generic components should use designator if label is not specified', () => {
    const specWithEmptyLabel = getBatterySpec();
    specWithEmptyLabel.label = '';

    const component = ComponentInstanceService.convertComponentInstanceSpecToComponent(specWithEmptyLabel);

    expect(component.name).toEqual(specWithEmptyLabel.designator);
});

test('Generic components should set product identifier to empty string if part number is not specified', () => {
    const specWithEmptyPartNumber = getBatterySpec();
    specWithEmptyPartNumber.partNumber = '';

    const specWithNullPartNumber = getBatterySpec();
    specWithNullPartNumber.partNumber = null;

    const componentFromEmptyPartNumber =
        ComponentInstanceService.convertComponentInstanceSpecToComponent(specWithEmptyPartNumber);
    const componentFromNullPartNumber =
        ComponentInstanceService.convertComponentInstanceSpecToComponent(specWithNullPartNumber);

    expect(componentFromEmptyPartNumber.productIdentifier).toEqual('');
    expect(componentFromNullPartNumber.productIdentifier).toEqual('');
});

test('Product catalog components should not be linked if they have fewer ports of a each voltage type than the component instance configuration', () => {
    jest.spyOn(PortService, 'isConnected').mockReturnValue(true);

    const converterInstanceWithThreeDcPorts = getConverterSpec();
    converterInstanceWithThreeDcPorts.configuration.ports[0].voltageType = 'DC';
    converterInstanceWithThreeDcPorts.configuration.ports[1].voltageType = 'DC';
    converterInstanceWithThreeDcPorts.configuration.ports[2].voltageType = 'DC';

    const converterInstanceWithThreeAcPorts = getConverterSpec();
    converterInstanceWithThreeAcPorts.configuration.ports[0].voltageType = 'AC';
    converterInstanceWithThreeAcPorts.configuration.ports[1].voltageType = 'AC';
    converterInstanceWithThreeAcPorts.configuration.ports[2].voltageType = 'AC';

    const converterProductWithTwoDcOneAcPorts = getConverterProduct();
    makePortAc(converterProductWithTwoDcOneAcPorts.electrical.ports[0]);
    makePortDc(converterProductWithTwoDcOneAcPorts.electrical.ports[1]);
    makePortDc(converterProductWithTwoDcOneAcPorts.electrical.ports[2]);

    const converterProductWithTwoAcOneDcPorts = getConverterProduct();
    makePortAc(converterProductWithTwoAcOneDcPorts.electrical.ports[0]);
    makePortDc(converterProductWithTwoAcOneDcPorts.electrical.ports[1]);
    makePortAc(converterProductWithTwoAcOneDcPorts.electrical.ports[2]);

    const attemptLinkDcInstance = () => {
        ComponentInstanceService.linkProduct({
            componentInstance: converterInstanceWithThreeDcPorts,
            productCatalogComponent: converterProductWithTwoDcOneAcPorts,
        });
    };

    const attemptLinkAcInstance = () => {
        ComponentInstanceService.linkProduct({
            componentInstance: converterInstanceWithThreeAcPorts,
            productCatalogComponent: converterProductWithTwoAcOneDcPorts,
        });
    };

    expect(attemptLinkDcInstance).toThrowError('Not enough DC ports!');
    expect(attemptLinkAcInstance).toThrowError('Not enough AC ports!');
});

test('Link DC/DC/AC product to AC/DC/DC component instance', () => {
    DiagramSyncService.initialize(defaultDiagram(), undefined);

    const utility = ComponentInstanceService.add({ position: { x: 0, y: 0 }, componentType: 'utility' });
    const converter = ComponentInstanceService.add({ position: { x: 0, y: 1 }, componentType: 'converter' });
    const battery1 = ComponentInstanceService.add({ position: { x: 0, y: 2 }, componentType: 'battery' });
    const battery2 = ComponentInstanceService.add({ position: { x: 1, y: 1 }, componentType: 'battery' });

    const dcConnection1 = ConnectionService.addConnectionWithEffects({
        from: {
            componentInstanceId: converter,
            edge: 'bottom',
            offset: 0,
            port: 0,
        },
        to: {
            componentInstanceId: battery1,
            edge: 'top',
            offset: 0,
            port: 0,
        },
    });

    const dcConnection2 = ConnectionService.addConnectionWithEffects({
        from: {
            componentInstanceId: converter,
            edge: 'right',
            offset: 0,
            port: 1,
        },
        to: {
            componentInstanceId: battery2,
            edge: 'left',
            offset: 0,
            port: 0,
        },
    });

    const acConnection = ConnectionService.addConnectionWithEffects({
        from: {
            componentInstanceId: utility,
            edge: 'bottom',
            offset: 0,
            port: 0,
        },
        to: {
            componentInstanceId: converter,
            edge: 'top',
            offset: 0,
            port: 0,
        },
    });

    const converterInstance = ComponentInstanceService.getById(converter);

    const converterProduct = getConverterProduct();
    makePortAc(converterProduct.electrical.ports[0]);
    makePortDc(converterProduct.electrical.ports[1]);
    makePortDc(converterProduct.electrical.ports[2]);

    ComponentInstanceService.linkProduct({
        componentInstance: converterInstance,
        productCatalogComponent: converterProduct,
    });

    const diagram = DiagramSyncService.get();

    const dcConnection1VoltageType = ConnectionService.getVoltageType(diagram.connections[dcConnection1]);
    const dcConnection2VoltageType = ConnectionService.getVoltageType(diagram.connections[dcConnection2]);
    const acConnectionVoltageType = ConnectionService.getVoltageType(diagram.connections[acConnection]);

    expect(dcConnection1VoltageType).toBe('DC');
    expect(dcConnection2VoltageType).toBe('DC');
    expect(acConnectionVoltageType).toBe('AC');
});

test('Inserting a component instance on a connection should split that connection in 2', () => {
    DiagramSyncService.initialize(defaultDiagram(), undefined);

    const AId = ComponentInstanceService.add({ position: { x: 0, y: 0 }, componentType: 'battery' });
    const A = ComponentInstanceService.getById(AId);

    const BId = ComponentInstanceService.add({ position: { x: 0, y: 2 }, componentType: 'battery' });
    const B = ComponentInstanceService.getById(BId);

    ConnectionService.addConnectionWithEffects({
        from: {
            componentInstanceId: A.id,
            edge: 'bottom',
            offset: 0,
            port: 0,
        },
        to: {
            componentInstanceId: B.id,
            edge: 'top',
            offset: 0,
            port: 0,
        },
    });

    const CId = ComponentInstanceService.add({ position: { x: 0, y: 1 }, componentType: 'converter' });
    const C = ComponentInstanceService.getById(CId);

    const diagram = DiagramSyncService.get();
    const connections = Object.values(diagram.connections);
    const [firstConnection, secondConnection] = connections;

    expect(connections.length).toBe(2);
    expect(firstConnection.from.componentInstanceId).toBe(A.id);
    expect(firstConnection.to.componentInstanceId).toBe(C.id);
    expect(secondConnection.from.componentInstanceId).toBe(C.id);
    expect(secondConnection.to.componentInstanceId).toBe(B.id);

    expect(ConnectionService.getVoltageType(firstConnection)).toBe('DC');
    expect(ConnectionService.getVoltageType(secondConnection)).toBe('DC');

    const numberOfPorts = C.configuration.ports.length;
    expect(numberOfPorts).toBe(2);
});

test('Inserting a component instance with dynamic ports on a connection should split that connection in 2', () => {
    DiagramSyncService.initialize(defaultDiagram(), undefined);

    const AId = ComponentInstanceService.add({ position: { x: 0, y: 0 }, componentType: 'battery' });
    const A = ComponentInstanceService.getById(AId);

    const BId = ComponentInstanceService.add({ position: { x: 0, y: 2 }, componentType: 'battery' });
    const B = ComponentInstanceService.getById(BId);

    ConnectionService.addConnectionWithEffects({
        from: {
            componentInstanceId: A.id,
            edge: 'bottom',
            offset: 0,
            port: 0,
        },
        to: {
            componentInstanceId: B.id,
            edge: 'top',
            offset: 0,
            port: 0,
        },
    });

    const CId = ComponentInstanceService.add({ position: { x: 0, y: 1 }, componentType: 'panel' });
    const C = ComponentInstanceService.getById(CId);

    const diagram = DiagramSyncService.get();
    const connections = Object.values(diagram.connections);
    const [firstConnection, secondConnection] = connections;

    expect(connections.length).toBe(2);
    expect(firstConnection.from.componentInstanceId).toBe(A.id);
    expect(firstConnection.to.componentInstanceId).toBe(C.id);
    expect(secondConnection.from.componentInstanceId).toBe(C.id);
    expect(secondConnection.to.componentInstanceId).toBe(B.id);

    expect(ConnectionService.getVoltageType(firstConnection)).toBe('DC');
    expect(ConnectionService.getVoltageType(secondConnection)).toBe('DC');
});

const getBatterySpec = (): DiagramComponentInstance => ({
    id: 'component-nkGojpjl',
    indicator: 1,
    designator: 'BAT-1',
    position: {
        x: 23,
        y: 23,
    },
    colSpan: 1,
    rowSpan: 1,
    componentType: 'battery',
    componentId: '',
    label: '1.4 MWh Battery',
    labelPlacement: {
        edge: null,
        offset: 0,
        auto: true,
    },
    notes: '',
    multiple: {
        type: 'parallel',
        amount: 1,
    },
    specifications: {
        electrical: {
            ports: [
                {
                    features: [],
                    powerFlowDirection: 'bidirectional',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: 320,
                            nom: 350,
                            max: 380,
                            unit: 'V',
                        },
                        current: {
                            nom: 30,
                            max: 50,
                            unit: 'A',
                        },
                        power: {
                            nom: 1000000,
                            max: 2000000,
                            unit: 'W',
                        },
                        configuration: null,
                        earthingConfigurations: [],
                    },
                },
            ],
            batteryTechnologies: ['li-ion'],
            energyCapacity: {
                value: 5040000000,
                unit: 'J',
            },
            chargeCapacity: {
                value: null,
                unit: 'C',
            },
            standards: [],
            parallelableCapacity: 1,
        },
    },
    manufacturer: '',
    partNumber: '1400B',
    configuration: {
        stateOfCharge: null,
        ports: [
            {
                controlMethod: null,
                label: '',
                powerFlowDirection: 'bidirectional',
                voltageType: 'DC',
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
        ],
    },
});

const getConverterSpec = (): DiagramComponentInstance => ({
    id: 'component-iJet66KW',
    indicator: 1,
    designator: 'U-1',
    position: {
        x: 11,
        y: 9,
    },
    colSpan: 1,
    rowSpan: 1,
    componentType: 'converter',
    componentId: '',
    label: '',
    labelPlacement: {
        edge: null,
        offset: 0,
        auto: true,
    },
    notes: '',
    multiple: {
        type: 'parallel',
        amount: 1,
    },
    specifications: {
        electrical: {
            ports: [
                {
                    features: [],
                    powerFlowDirection: 'bidirectional',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    AC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        frequency: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'Hz',
                        },
                        powerFactor: 1,
                        configuration: null,
                        earthingConfigurations: [],
                        controlMethods: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        configuration: null,
                        controlMethods: [],
                        earthingConfigurations: [],
                    },
                    capacitance: {
                        value: null,
                        unit: 'F',
                    },
                    isolated: null,
                    parallelableCapacity: 1,
                    purpose: null,
                },
                {
                    features: [],
                    powerFlowDirection: 'output',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    AC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        frequency: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'Hz',
                        },
                        powerFactor: 1,
                        configuration: null,
                        controlMethods: [],
                        earthingConfigurations: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        configuration: null,
                        controlMethods: [],
                        earthingConfigurations: [],
                    },
                    capacitance: {
                        value: null,
                        unit: 'F',
                    },
                    isolated: null,
                    parallelableCapacity: 1,
                    purpose: null,
                },
                {
                    features: [],
                    powerFlowDirection: 'output',
                    terminal: {
                        temperature: {
                            min: null,
                            max: null,
                            unit: 'K',
                        },
                        type: null,
                        torque: {
                            nom: null,
                            max: null,
                            unit: 'N m',
                        },
                    },
                    wireSize: {
                        min: null,
                        max: null,
                    },
                    AC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        frequency: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'Hz',
                        },
                        powerFactor: 1,
                        configuration: null,
                        controlMethods: [],
                        earthingConfigurations: [],
                    },
                    DC: {
                        enabled: true,
                        voltage: {
                            min: null,
                            nom: null,
                            max: null,
                            unit: 'V',
                        },
                        current: {
                            nom: null,
                            max: null,
                            unit: 'A',
                        },
                        power: {
                            nom: null,
                            max: null,
                            unit: 'W',
                        },
                        configuration: null,
                        controlMethods: [],
                        earthingConfigurations: [],
                    },
                    capacitance: {
                        value: null,
                        unit: 'F',
                    },
                    isolated: null,
                    parallelableCapacity: 1,
                    purpose: null,
                },
            ],
            standards: [],
            isolationVoltage: {
                value: null,
                unit: 'V',
            },
        },
    },
    manufacturer: null,
    partNumber: null,
    configuration: {
        stateOfCharge: null,
        ports: [
            {
                label: '',
                voltageType: 'DC',
                powerFlowDirection: 'bidirectional',
                controlMethod: null,
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
            {
                label: '',
                voltageType: 'DC',
                powerFlowDirection: 'output',
                controlMethod: null,
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
            {
                label: '',
                voltageType: 'DC',
                powerFlowDirection: 'output',
                controlMethod: null,
                voltage: {
                    value: null,
                    unit: 'V',
                },
                current: {
                    value: null,
                    unit: 'A',
                },
                power: {
                    value: null,
                    unit: 'W',
                },
                droopResistance: {
                    value: null,
                    unit: 'ohm',
                },
                droopCurrent: {
                    value: null,
                    unit: 'A',
                },
                voltageSeries: null,
                powerSeries: null,
            },
        ],
    },
});

const getConverterProduct = (): Converter => ({
    id: '64cbd308ac05e7f93f9082e6',
    name: 'String Optimizer',
    description:
        'Ampt String Optimizers are DC/DC converters that are used in large-scale PV plants to lower the cost and improve performance of new systems, upgrade existing systems to produce more energy, enable low-cost DC-coupled solar+storage systems, and provide string-level data for improved O&M. String Optimizer models are for system voltages ranging from 600 to 1500 VDC.',
    type: 'converter',
    manufacturer: '64cbd363ac05e7f93f90830a',
    productSeries: 'i12',
    msrp: null,
    leadTime: null,
    productIdentifier: 'V425-12-12',
    website: 'https://www.ampt.com/',
    electrical: {
        ports: [
            {
                AC: {
                    enabled: false,
                    voltage: {
                        unit: 'V',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    current: {
                        unit: 'A',
                        nom: null,
                        max: null,
                    },
                    power: {
                        unit: 'W',
                        nom: null,
                        max: null,
                    },
                    frequency: {
                        unit: 'Hz',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    powerFactor: 0,
                    controlMethods: [],
                    configuration: null,
                    earthingConfigurations: [],
                },
                DC: {
                    enabled: true,
                    voltage: {
                        unit: 'V',
                        min: null,
                        nom: null,
                        max: 585,
                    },
                    current: {
                        unit: 'A',
                        nom: null,
                        max: 11,
                    },
                    power: {
                        unit: 'W',
                        nom: null,
                        max: 6435,
                    },
                    configuration: 'unipolar',
                    controlMethods: ['constant-voltage'],
                    earthingConfigurations: [],
                },
                powerFlowDirection: null,
                isolated: true,
                parallelableCapacity: 1,
                capacitance: {
                    unit: 'F',
                    value: null,
                },
                wireSize: {
                    min: '0.05',
                    max: '0.5',
                },
                features: [],
                terminal: {
                    type: null,
                    temperature: {
                        unit: 'K',
                        min: null,
                        max: null,
                    },
                    torque: {
                        unit: 'N m',
                        nom: null,
                        max: null,
                    },
                },
                purpose: null,
            },
            {
                AC: {
                    enabled: false,
                    voltage: {
                        unit: 'V',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    current: {
                        unit: 'A',
                        nom: null,
                        max: null,
                    },
                    power: {
                        unit: 'W',
                        nom: null,
                        max: null,
                    },
                    frequency: {
                        unit: 'Hz',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    powerFactor: 0,
                    controlMethods: [],
                    configuration: null,
                    earthingConfigurations: [],
                },
                DC: {
                    enabled: true,
                    voltage: {
                        unit: 'V',
                        min: 0,
                        nom: 405,
                        max: 425,
                    },
                    current: {
                        unit: 'A',
                        nom: null,
                        max: 12,
                    },
                    power: {
                        unit: 'W',
                        nom: null,
                        max: 4700,
                    },
                    configuration: 'unipolar',
                    controlMethods: [],
                    earthingConfigurations: [],
                },
                powerFlowDirection: 'input',
                isolated: false,
                parallelableCapacity: 1,
                capacitance: {
                    unit: 'F',
                    value: null,
                },
                wireSize: {
                    min: '0.05',
                    max: '0.5',
                },
                features: [],
                terminal: {
                    type: null,
                    temperature: {
                        unit: 'K',
                        min: null,
                        max: null,
                    },
                    torque: {
                        unit: 'N m',
                        nom: null,
                        max: null,
                    },
                },
                purpose: null,
            },
            {
                AC: {
                    enabled: false,
                    voltage: {
                        unit: 'V',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    current: {
                        unit: 'A',
                        nom: null,
                        max: null,
                    },
                    power: {
                        unit: 'W',
                        nom: null,
                        max: null,
                    },
                    frequency: {
                        unit: 'Hz',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    powerFactor: 0,
                    controlMethods: [],
                    configuration: null,
                    earthingConfigurations: [],
                },
                DC: {
                    enabled: false,
                    voltage: {
                        unit: 'V',
                        min: null,
                        nom: null,
                        max: null,
                    },
                    current: {
                        unit: 'A',
                        nom: null,
                        max: null,
                    },
                    power: {
                        unit: 'W',
                        nom: null,
                        max: null,
                    },
                    controlMethods: [],
                    configuration: null,
                    earthingConfigurations: [],
                },
                powerFlowDirection: 'output',
                isolated: false,
                parallelableCapacity: 1,
                capacitance: {
                    unit: 'F',
                    value: null,
                },
                wireSize: {
                    min: '0.05',
                    max: '0.5',
                },
                features: [],
                terminal: {
                    type: null,
                    temperature: {
                        unit: 'K',
                        min: null,
                        max: null,
                    },
                    torque: {
                        unit: 'N m',
                        nom: null,
                        max: null,
                    },
                },
                purpose: null,
            },
        ],
        standards: [],
        isolationVoltage: {
            unit: 'V',
            value: null,
        },
    },
    communication: {
        interfaces: ['wifi', 'ethernet'],
        protocols: ['modbus-tcp-ip'],
    },
    environmental: {
        operatingTemperature: {
            unit: 'K',
            min: 313.15,
            max: 323.15,
        },
        storageTemperature: {
            unit: 'K',
            min: 303.15,
            max: 353.15,
        },
        operatingHumidity: {
            unit: '%',
            min: null,
            max: null,
        },
        storageHumidity: {
            unit: '%',
            min: 0,
            max: 80,
        },
        ingressProtection_IP: 'IP66',
        ingressProtection_NEMA: '4X',
        maximumOperatingAltitude: {
            unit: 'm',
            value: 3000,
        },
        coolingMethod: 'passive',
    },
    performance: {
        standbyPower: {
            unit: 'W',
            value: null,
        },
        efficiency: {
            unit: '%',
            nom: 99.2,
            max: 99.5,
        },
        losses: {
            unit: 'W',
            nom: null,
            max: null,
        },
    },
    mechanical: {
        dimensions: {
            unit: 'm',
            width: 0.259,
            length: 0.22,
            height: 0.08,
        },
        weight: {
            unit: 'kg',
            value: 3.4,
        },
        mountingType: ['wall', 'panel'],
    },
    files: [
        {
            file: '64d0e249cb456658b15354be',
            type: ComponentFileType.INSTALLATION_MANUAL,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '64d0e25ccb456658b15354f7',
            type: ComponentFileType.DATASHEET,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc2a6cfcbe898a506b6c5',
            type: ComponentFileType.APPLICATION_NOTES,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc2ffcfcbe898a506b722',
            type: ComponentFileType.APPLICATION_NOTES,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc35bcfcbe898a506b780',
            type: ComponentFileType.APPLICATION_NOTES,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc374cfcbe898a506b7e2',
            type: ComponentFileType.APPLICATION_NOTES,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc39bcfcbe898a506b848',
            type: ComponentFileType.CERTIFICATE,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc3a7cfcbe898a506b8b2',
            type: ComponentFileType.CERTIFICATE,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc450cfcbe898a506ba49',
            type: ComponentFileType.CERTIFICATE,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc4cbcfcbe898a506bb8e',
            type: ComponentFileType.APPLICATION_NOTES,
            visibility: FileVisibility.PUBLIC,
        },
        {
            file: '653bc4d7cfcbe898a506bc04',
            type: ComponentFileType.APPLICATION_NOTES,
            visibility: FileVisibility.PUBLIC,
        },
    ],
    images: [
        {
            file: '653bc5f0cfcbe898a506bf26',
            type: ComponentImageType.THUMBNAIL,
        },
        {
            file: '653bc5fdcfcbe898a506bfa5',
            type: ComponentImageType.ISO_PICTURE,
        },
        {
            file: null,
            type: ComponentImageType.FRONT,
        },
        {
            file: null,
            type: ComponentImageType.REAR,
        },
        {
            file: null,
            type: ComponentImageType.LEFT,
        },
        {
            file: null,
            type: ComponentImageType.RIGHT,
        },
        {
            file: null,
            type: ComponentImageType.TOP,
        },
        {
            file: null,
            type: ComponentImageType.BOTTOM,
        },
    ],
    compliance: {
        CE: true,
        UL: true,
        currentOS: true,
        emergeAlliance: true,
        ODCA: true,
        other: true,
    },
    standards: [
        '64f23b4b098c58f1c0bf116a',
        '64f9d3869b7801542f8ef4ca',
        '64f9d3949b7801542f8ef4d4',
        '64f9e3cc9b7801542f8f1a51',
        '64f238f6098c58f1c0bf0e69',
    ],
    archivedAt: null,
    createdBy: '65454b6925fd45182e5ca838',
    team: '65454b6925fd45182e5ca83a',
    // @ts-ignore
    createdAt: '2023-08-03T16:17:12.338Z',
    updatedAt: '2023-10-27T17:44:42.438Z',
    metadata: {
        electrical: {
            isolationVoltage: {
                display: 'unknown',
            },
            ports: [
                {
                    capacitance: {
                        display: 'unknown',
                    },
                    features: {
                        display: 'na',
                    },
                },
                {
                    DC: {
                        controlMethods: {
                            display: 'unknown',
                        },
                    },
                    capacitance: {
                        display: 'unknown',
                    },
                    features: {
                        display: 'na',
                    },
                },
                {
                    capacitance: {
                        display: 'unknown',
                    },
                    features: {
                        display: 'na',
                    },
                },
            ],
        },
        environmental: {
            operatingHumidity: {
                display: 'unknown',
            },
        },
        performance: {
            losses: {
                display: 'unknown',
            },
            standbyPower: {
                display: 'unknown',
            },
        },
    },
    reviewed: false,
    visibility: ComponentVisibility.PUBLIC,
    videos: [],
    projects: [],
    lifecycle: {
        release: null,
        endOfLife: null,
    },
});

const makePortAc = (port: Converter['electrical']['ports'][number]) => {
    port.AC.enabled = true;
    port.DC.enabled = false;
};

const makePortDc = (port: Converter['electrical']['ports'][number]) => {
    port.AC.enabled = false;
    port.DC.enabled = true;
};
