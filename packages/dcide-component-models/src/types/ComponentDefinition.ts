import type { ComponentValidator } from '../types/ComponentValidator';
import type { ComponentPayloadValidator } from '../types/ComponentPayloadValidator';

import { ComponentType, PortControlMethod, PowerFlowDirection } from '../validators/fields';

import { VoltageType } from './VoltageType';

export type ComponentDefinition = {
    type: ComponentType;
    name: string;
    description?: string;
    plural: string;
    indicator: string;
    actsAsConverter?: boolean;
    canServeAsOptions?: ComponentType[];
    hasDynamicNumberOfPorts: boolean;
    disableDiagramUse?: boolean; // components cannot be used in diagrams eg. cables
    ports: {
        min: number;
        max: number;
        powerFlowDirections: PowerFlowDirection[];
        defaultVoltageType?: VoltageType;
        displayAsSinglePort?: boolean;
        allowedControlMethods?: readonly PortControlMethod[];
    };
    validators: {
        component: ComponentValidator;
        payload: ComponentPayloadValidator;
    };
};
