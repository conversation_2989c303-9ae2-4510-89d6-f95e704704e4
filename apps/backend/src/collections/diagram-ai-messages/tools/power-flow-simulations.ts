import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

import { PayloadService } from '@/services/PayloadService';
import { getId } from '@/helpers';
import { pick } from 'radash';

import { generateToolPrompt } from '../helpers/generate-tool-prompt';

const name = 'powerFlowSimulations';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: `
        Retrieves information about the latest power flow simulation for this diagram.
        Possible responses are:
        * The latest simulation with a status and results
        * A message indicating that no simulations have been run for this diagram
    `,
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {
    run: {
        name: 'runPowerFlowSimulation',
        action: () => `
            === ACTION ===
            {
                "action": "runPowerFlowSimulation"
            }
            === END ACTION ===
        `,
    },
    request: {
        name: 'requestPowerFlowSimulation',
        action: () => `
            === ACTION ===
            {
                "action": "requestPowerFlowSimulation"
            }
            === END ACTION ===
        `,
    },
    show: {
        name: 'showPowerFlowSimulation',
        action: (simulationId: string) => `
            === ACTION ===
            {
                "action": "showPowerFlowSimulation",
                "simulationId": "${simulationId}"
            }
            === END ACTION ===
        `,
    },
} as const;

const handler = async (message: DiagramAIMessage) => {
    const payload = await PayloadService.getPayload();
    const {
        docs: [simulation],
    } = await payload.find({
        collection: 'simulations',
        where: {
            diagram: {
                equals: getId(message.diagram),
            },
        },
        depth: 0,
        limit: 1,
        sort: '-createdAt',
    });

    if (simulation) {
        return JSON.stringify(pick(simulation, ['id', 'name', 'status', 'error', 'result']));
    }

    return 'There are no simulations for this diagram.';
};

const prompt = generateToolPrompt({
    name: 'Power Flow Simulation Questions',
    objective: `
        To address user questions or requests related to the latest power flow simulation,
        including running new simulations, checking the status of the latest simulation, or viewing simulation results.
    `,
    activationCondition: `
        Only activate this section if the user explicitly asks a question or makes a request
        directly related to power flow simulations, running new simulations, checking the status
        of existing simulations, or viewing simulation results.
    `,
    exampleQuestions: [],
    handler: `
        **Scenario 1: User directly asks to run a new power flow simulation**

        1.  Acknowledge the user's request clearly (e.g., "Okay, I will start a new power flow simulation.").
        2.  **Mandatory:** Always return the following "ACTION" block in your response to initiate the simulation directly:
            "${actions.run.action()}"

        **Scenario 2: User asks a question requiring simulation data**
        *(This applies if the user's question depends on dynamic operational states like voltage levels, line loadings, power balance, or fault analysis.)*

        **1. Query Simulation Status & Results:**
        * **Mandatory:** You *must* first call the '${name}' function. This function is expected to:
            * Attempt to fetch the latest available simulation results relevant to the current context.
            * Provide the 'status' (e.g., "done", "pending", "processing", "error") and 'id' of the latest simulation.
            * Indicate if no simulations have been run for this diagram.

        **2. Interpreting and Acting on the '${name}' Function's Response:**

        * **If No Prior Simulation is Found:**
            1.  Inform the user clearly (e.g., "It seems no power flow simulation has been run for this diagram yet.").
            2.  Briefly explain *why* a simulation is necessary for their specific question.
            3.  Ask the user if they would like to run a new simulation.
            4.  **Mandatory:** Always return the ${actions.request.name} "ACTION" block to allow the user to easily request a new simulation:
                '${actions.request.action()}'

        * **If the Latest Simulation is "pending" or "processing":**
            1.  Inform the user that the relevant simulation is still running (e.g., "The simulation is still running.").
            2.  Explain that you can provide an answer once it's complete.
            3.  **Mandatory:** Always return the ${actions.show.name} "ACTION" block to show simulation progress, using the 'id' provided by the function:
                '${actions.show.action('simulation-id-from-tool')}'

        * **If the Latest Simulation has an "error" status:**
            *(This refers to a failed simulation run, not a failure of the function call itself.)*
            1.  Inform the user about the error in that specific simulation run.
            2.  If the function provides specific error details for the run, relay them.
            3.  **Mandatory:** Always return the ${actions.show.name} "ACTION" block to show error details, using the 'id' provided by the function:
                '${actions.show.action('simulation-id-from-tool')}'

        * **If the Latest Simulation is "completed" and results are available:**
            1.  **Evaluate Relevance and Sufficiency:** Determine if the fetched results *directly and adequately* address the user's current question.
            2.  **If results ARE relevant and sufficient:**
                * Use these results explicitly in your answer. Clearly state the context (e.g., "Based on your latest simulation, the voltage at Bus 3 is...").
            3.  **If results are NOT relevant or sufficient:**
                * Inform the user clearly (e.g., "While there's a completed simulation, its results don't cover your current question about [specifics of their new query].").
                * Explain why a *new* simulation would be needed for their specific scenario.
                * Ask the user if they would like to run a new simulation tailored to this scenario.
                * **Mandatory:** Always return the ${actions.request.name} "ACTION" block:
                  '${actions.request.action()}'
    `,
    notes: [
        {
            title: 'Never mention simulation id',
            description: '**Never** mention the simulation ID in your user facing response. You can only use the simulation id\'s within the "=== ACTION ===" block.',
        }
    ],
});

const powerFlowSimulations = {
    name,
    definition,
    actions,
    handler,
    prompt,
};

export { powerFlowSimulations };
