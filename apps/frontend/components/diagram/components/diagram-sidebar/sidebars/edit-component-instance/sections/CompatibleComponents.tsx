import React, { FC } from 'react';
import { DiagramComponentInstance } from '@repo/dcide-component-models';

import { proxy } from 'valtio';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box, ScrollArea, Stack } from '@mantine/core';
import { useElementSize } from '@mantine/hooks';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';

import { useComponent } from 'hooks/use-component';

import { DraggableDiagramComponentOverviewHit } from 'components/component-overview/ComponentOverviewHit.Diagram';

import cx from './ComponentSuggestions.module.css';

const localState = proxy({
    showSpecifications: false,
});

const CompatibleComponents: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const { component } = useComponent(componentInstance.componentId, {
        skipAccessCheck: true,
    });
    const { ref, width, height } = useElementSize();

    const compatibles = [...(component?.compatibleWith || []), ...(component?.compatibleWithCrossReference || [])];

    return compatibles.length > 0 ? (
        <React.Fragment>
            <Box style={{ width, height }} />
            <Box className={cx.section} ref={ref}>
                <DiagramSidebar.Divider />
                <DiagramSidebar.Section
                    title="Compatible Components"
                    collapsable={{
                        enabled: true,
                        key: 'diagram.compatible-components',
                        initial: false,
                    }}
                >
                    <ScrollArea.Autosize mah={192} type="always">
                        <Stack gap="xs">
                            {compatibles.map((compatibleComponentId) => (
                                <CompatibleComponent id={compatibleComponentId} key={compatibleComponentId} />
                            ))}
                        </Stack>
                    </ScrollArea.Autosize>
                </DiagramSidebar.Section>
            </Box>
        </React.Fragment>
    ) : null;
};

const CompatibleComponent: FC<{
    id: string;
}> = ({ id }) => {
    const { component } = useComponent(id, {
        skipAccessCheck: true,
    });
    const { showSpecifications } = useSnapshot(localState);

    return component ? (
        <DraggableDiagramComponentOverviewHit
            component={component}
            showLinkButton={false}
            showSpecifications={showSpecifications}
            setShowSpecifications={() => {
                localState.showSpecifications = true;
            }}
        />
    ) : null;
};

export { CompatibleComponents };
