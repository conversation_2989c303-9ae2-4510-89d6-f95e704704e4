import type { Endpoint } from 'payload';

import { z } from 'zod';
import { intersects } from 'radash';

import { enhanceProjectWithPermissions, getCollection, getRequestUser } from '../../../helpers';

const comments: Endpoint = {
    path: '/:diagramId/comments',
    method: 'get',
    handler: async (request) => {
        const { payload, routeParams } = request;
        const user = await getRequestUser(request);

        if (!user) {
            return Response.json([]);
        }

        const { diagramId } = z.object({ diagramId: z.string() }).parse(routeParams);

        const {
            docs: [project],
        } = await request.payload.find({
            collection: 'projects',
            where: {
                'designs.diagrams': {
                    equals: diagramId,
                },
            },
            limit: 1,
            depth: 1,
        });

        if (!project) {
            console.error('Fetching comments from a non-existing diagram');

            return Response.json([]);
        }

        const team = await getCollection('teams', project.team);

        const projectWithPermissions = await enhanceProjectWithPermissions({
            project,
            user,
            team,
        });

        let { docs: comments } = await payload.find({
            collection: 'diagramComments',
            where: {
                diagram: {
                    equals: diagramId,
                },
            },
            pagination: false,
            depth: 0,
            req: request,
        });

        if (projectWithPermissions.permissions.includes('project.company')) {
            const { docs: manufacturers } = await payload.find({
                collection: 'manufacturers',
                where: {
                    team: { equals: user.team },
                },
                depth: 0,
            });
            const manufacturerIds = manufacturers.map((manufacturer) => manufacturer.id);

            comments = comments.filter((comment) => {
                return intersects(
                    // @ts-expect-error it exists.
                    comment.mentions.manufacturers,
                    manufacturerIds,
                );
            });
        }

        return Response.json(comments);
    },
};

export { comments };
