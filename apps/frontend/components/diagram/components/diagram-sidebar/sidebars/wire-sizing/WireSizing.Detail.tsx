import React, { FC } from 'react';

import { useWireSizings } from 'components/diagram/hooks/use-wire-sizings';
import { useComponentInstance, useDiagram } from 'components/diagram/hooks';
import { useDiagramValidationErrorCount } from 'components/diagram/hooks/use-diagram-validation-error-count';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { WireSizingService } from 'components/diagram/services/WireSizingService';

import {
    DiagramComponentInstance,
    DiagramConnection,
    Zone,
    currentConverter,
    voltageConverter,
} from '@repo/dcide-component-models';
import { SidebarType } from 'components/diagram/state/sidebar';

import { ZoneService } from 'components/diagram/services/ZoneService';
import { ActionIcon, Box, Group, HoverCard, ScrollArea, Tooltip } from '@mantine/core';

import { IoWarningOutline } from 'react-icons/io5';
import { ConnectionForm } from 'components/diagram/components/diagram-connections/ConnectionForm';

import { ConnectionService } from 'components/diagram/services';
import { SidebarService } from 'components/diagram/services/SidebarService';

import { FormatHelpers } from 'helpers/formatters';

import { ConnectionContext, useConnectionContext } from 'contexts/connection';
import { ConnectionHelpers } from 'components/diagram/helpers/ConnectionHelpers';

import { TbCircleCheck } from 'react-icons/tb';
import { SimpleButton } from 'elements/buttons';

import { useSidebarWidth } from 'components/diagram/hooks/use-sidebar-width';

import cx from './WireSizing.module.css';

const Detail: FC = () => {
    const { wireSizing } = useWireSizings();

    const diagram = useDiagram();
    const connections = Object.values(diagram.connections);

    useSidebarWidth(600);

    return wireSizing ? (
        <React.Fragment>
            <DiagramSidebar.SimpleHeader
                rightSection={
                    <DiagramSidebar.Button
                        onClick={() => {
                            alert('todo :(');
                        }}
                    >
                        Apply calculated values
                    </DiagramSidebar.Button>
                }
                handleBackClick={() => {
                    WireSizingService.deactivate();
                }}
            >
                {wireSizing.name}
            </DiagramSidebar.SimpleHeader>
            <DiagramSidebar.Section>
                <ScrollArea className={cx.wireSizingWrapper} type="never">
                    <Box className={cx.wireSizing}>
                        <Box className={[cx.gridHeader, cx.row].join(' ')}>
                            <Box />
                            <Box>Max. Voltage</Box>
                            <Box>Max. Current</Box>
                            <Box></Box>
                        </Box>
                        {connections.map((connection) => (
                            <Connection connection={connection} key={connection.id} />
                        ))}
                    </Box>
                </ScrollArea>
            </DiagramSidebar.Section>
        </React.Fragment>
    ) : (
        <DiagramSidebar.Loader />
    );
};

const Connection: FC<{
    connection: DiagramConnection;
}> = ({ connection }) => {
    const fromComponentInstance = useComponentInstance(connection.from.componentInstanceId);
    const toComponentInstance = useComponentInstance(connection.to.componentInstanceId);

    const componentInstances: DiagramComponentInstance[] = [];

    if (fromComponentInstance) {
        componentInstances.push(fromComponentInstance);
    }

    if (toComponentInstance) {
        componentInstances.push(toComponentInstance);
    }

    const zone: Zone = {
        id: 'temp',
        type: 'control',
        connections: [connection],
        componentInstances,
    };

    const highlight = () => {
        ZoneService.highlight(zone);
    };

    const lowlight = () => {
        ZoneService.highlight();
    };

    const focus = () => {
        ZoneService.moveIntoView(zone);
    };

    const result = {
        voltage: { unit: 'V', value: 1500.0 },
        current: {
            'L+': { unit: 'A', value: 2165.714285714286 },
            'PE': { unit: 'A', value: 0.0 },
            'L-': { unit: 'A', value: -2165.714285714286 },
        },
    };

    const maxCurrent = Math.max(
        ...[result.current['L+'].value, result.current['L-'].value, result.current['PE'].value],
    );

    const hasZoneErrors = Boolean(useDiagramValidationErrorCount());
    const showWarning = hasZoneErrors;

    const apply = () => {
        ConnectionService.update({
            id: connection.id,
            requirements: {
                // @ts-ignore
                voltage: result.voltage,
                // @ts-ignore
                current: result.current,
            },
        });
    };

    const applied =
        connection.requirements.voltage.value === result.voltage.value &&
        connection.requirements.current['L+'].value === result.current['L+'].value &&
        connection.requirements.current['L-'].value === result.current['L-'].value &&
        connection.requirements.current['PE'].value === result.current['PE'].value;

    return fromComponentInstance && toComponentInstance ? (
        <ConnectionContext.Provider value={{ connection, fromComponentInstance, toComponentInstance }}>
            <ConnectionForm connection={connection}>
                <Box className={[cx.grid, cx.row].join(' ')}>
                    <Group
                        gap={8}
                        align="center"
                        onMouseEnter={highlight}
                        onMouseLeave={lowlight}
                        onClick={focus}
                        style={{ cursor: 'pointer' }}
                    >
                        <HoverCard position="bottom-start" radius="xs" withArrow>
                            <HoverCard.Target>
                                <div>
                                    {fromComponentInstance.designator}
                                    {' • '}
                                    {toComponentInstance.designator}
                                </div>
                            </HoverCard.Target>
                            <HoverCard.Dropdown p="xs">
                                <ConnectionInformation />
                            </HoverCard.Dropdown>
                        </HoverCard>
                        {showWarning && (
                            <Tooltip label="No wire sizing result found. Click to check the validation errors.">
                                <ActionIcon
                                    size="xs"
                                    radius={99}
                                    variant="light"
                                    color="orange"
                                    onClick={() => SidebarService.open({ type: SidebarType.VALIDATIONS })}
                                >
                                    <IoWarningOutline size={10} style={{ marginTop: -1 }} />
                                </ActionIcon>
                            </Tooltip>
                        )}
                    </Group>
                    <Box className={cx.gridConnectionValue}>{FormatHelpers.formatVoltage(result.voltage) || '-'}</Box>
                    <Tooltip
                        label={
                            <Box>
                                L+: {FormatHelpers.formatCurrent(result.current['L+']) || '-'}
                                <br />
                                PE: {FormatHelpers.formatCurrent(result.current['PE']) || '-'}
                                <br />
                                L-: {FormatHelpers.formatCurrent(result.current['L-']) || '-'}
                                <br />
                            </Box>
                        }
                        position="bottom"
                        withArrow
                    >
                        <Box className={cx.gridConnectionValue}>
                            {FormatHelpers.formatCurrent({ value: maxCurrent, unit: 'A' }) || '-'}
                        </Box>
                    </Tooltip>
                    <Box className={cx.gridConnectionValue} onClick={apply}>
                        {applied ? (
                            <Tooltip label="The required current and voltage are already applied.">
                                <TbCircleCheck size={14} color="#37b24d" />
                            </Tooltip>
                        ) : (
                            <Tooltip label="This will apply the rated current and voltage for this connection.">
                                <SimpleButton onClick={apply} size="xs">
                                    Apply
                                </SimpleButton>
                            </Tooltip>
                        )}
                    </Box>
                </Box>
            </ConnectionForm>
        </ConnectionContext.Provider>
    ) : null;
};

const ConnectionInformation: FC = () => {
    const { connection, fromComponentInstance, toComponentInstance } = useConnectionContext();
    const voltageType = ConnectionHelpers.getVoltageType(connection, fromComponentInstance, toComponentInstance);

    const lines: string[] = [];

    if (voltageType) {
        Object.entries(connection.lines[voltageType]).forEach(([line, active]) => {
            if (active) {
                lines.push(line);
            }
        });
    }

    return (
        <Box className={cx.connectionInformation}>
            {[
                { label: 'Voltage Type', value: voltageType },
                { label: 'Voltage', value: FormatHelpers.formatMinNomMax(connection.voltage, voltageConverter) },
                { label: 'Current', value: FormatHelpers.formatMinNomMax(connection.current, currentConverter) },
                { label: 'Lines', value: lines.join(', ') },
            ].map((item) => (
                <Box className={cx.connectionInformationLine} key={item.label}>
                    <Box>{item.label}:</Box>
                    <Box>{item.value || '-'}</Box>
                </Box>
            ))}
        </Box>
    );
};

export { Detail };
