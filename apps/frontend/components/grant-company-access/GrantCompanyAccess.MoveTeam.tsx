import React, { useState } from 'react';

import { <PERSON>ert, Button, Checkbox, Group, List, Stack, Text, Title } from '@mantine/core';

import { CompanyProfile, Team, User } from '@repo/dcide-component-models';

import { asyncLoader } from 'helpers/asyncLoader';

import { useCurrentTeam } from 'hooks/use-current-team';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { GrantAccessGoBack } from 'components/grant-access/GoBack';

import cx from 'components/grant-access/GrantAccess.module.scss';

const GrantCompanyAccessMoveTeam = ({
    company,
    requestUser,
    requestTeam,
    goBack,
}: {
    company: CompanyProfile;
    requestUser: User;
    requestTeam: Team;
    goBack: () => void;
}) => {
    const [movedTeam, setMovedTeam] = useState(false);
    const [message, setMessage] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState(false);

    const [checked, setChecked] = useState(false);

    const currentTeam = useCurrentTeam();

    if (!currentTeam) {
        return null;
    }

    const moveTeam = asyncLoader(async () => {
        try {
            const result: {
                success: boolean;
                components: number;
                designs: number;
            } = await CompanyProfileService.moveTeam(company.id, {
                userId: requestUser.id,
                teamId: requestTeam.id,
                companyId: company.id,
            });

            if (result.success) {
                setMovedTeam(true);

                setMessage(
                    `We have successfully moved permissions of ${company.name}, ${result.components} products and ${result.designs} reference designs to ${requestTeam.name}.`,
                );
            }
        } catch (error) {
            setError('An error occurred while moving permissions.');
        }
    }, setLoading);

    return (
        <Stack>
            <GrantAccessGoBack goBack={goBack} />
            <Title size="h2">
                <Group component="span" gap={4} mt={4}>
                    Transfer ownership to {requestUser.email}
                </Group>
            </Title>

            <Text>
                By transferring ownership of {company.name} to {requestUser.email}:
            </Text>

            <List>
                <List.Item>You will no longer have access to the {company.name} profile</List.Item>
                <List.Item>We will transfer ownership of all products</List.Item>
                <List.Item>We will transfer ownership of all reference designs</List.Item>
            </List>

            {!movedTeam && (
                <>
                    <Checkbox
                        checked={checked}
                        className={cx.checkbox}
                        onChange={(event) => setChecked(event.currentTarget.checked)}
                        label={`I understand that I will lose access to ${company.name} profile and all associated products and reference designs and that this action is irreversible.`}
                    />

                    <Button fullWidth disabled={!checked} onClick={moveTeam} loading={loading} color="red">
                        Move permissions to {requestTeam.name}
                    </Button>
                </>
            )}

            {message && <Alert color="green">{message}</Alert>}

            {error && <Alert color="red">{error}</Alert>}
        </Stack>
    );
};

export { GrantCompanyAccessMoveTeam };
