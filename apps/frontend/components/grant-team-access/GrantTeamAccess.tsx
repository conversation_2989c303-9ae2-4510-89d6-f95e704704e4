import React, { useEffect, FC } from 'react';

import { <PERSON><PERSON>, Card } from '@mantine/core';

import { Team, User } from '@repo/dcide-component-models';

import { getId } from 'helpers/getId';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { UserService } from 'services/UserService';
import { TeamHelpers } from 'helpers/TeamHelpers';

import { Page } from 'components/page';
import { GrantTeamAccessChoose } from 'components/grant-team-access/GrantTeamAccess.Choose';

import { RouterService } from 'services/RouterService';

import cx from 'components/grant-access/GrantAccess.module.scss';

const GrantTeamAccess: FC<{
    team: Team;
    user: User;
}> = ({ team, user }) => {
    const currentUser = useCurrentUser();
    const currentTeam = useCurrentTeam();

    // switch current team to the team being accessed
    useEffect(() => {
        if (currentTeam && currentTeam.id !== team.id) {
            UserService.switchTeam(team.id).then((result) => {
                if (result?.success) {
                    RouterService.reload();
                }
            });
        }
    }, [currentUser, currentTeam, team.id]);

    if (!user || !team || !currentUser || !currentTeam) {
        return null;
    }

    const currentUserHasTeamAccess = TeamHelpers.isTeamMember(team, currentUser);
    const requestingUserIsAlreadyOnTeam = team.users.some((needle) => {
        return getId(needle.user) === user.id;
    });

    return (
        <Page showBackground title="Grant Team Access">
            <Page.CenteredContent maxWidth={500}>
                <Card withBorder className={cx.root}>
                    {requestingUserIsAlreadyOnTeam && (
                        <Alert color="green">{user.email} has been invited to your team.</Alert>
                    )}

                    {!currentUserHasTeamAccess && (
                        <Alert color="red">You do not have access to manage this team.</Alert>
                    )}

                    {currentUserHasTeamAccess && !requestingUserIsAlreadyOnTeam && (
                        <GrantTeamAccessChoose team={team} user={user} />
                    )}
                </Card>
            </Page.CenteredContent>
        </Page>
    );
};

export { GrantTeamAccess };
