import React, { FC, useState } from 'react';

import { all, ComponentDefinition } from '@repo/dcide-component-models';

import { Box, Button, UnstyledButton } from '@mantine/core';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import { DiagramService } from 'components/diagram/services';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { DiagramGenerators, generatorState } from 'components/diagram/state/generator';

import { DiagramGeneratorBack } from './DiagramGenerator.Back';

import cx from './DiagramGenerator.module.css';

const DiagramGeneratorComponents: FC = () => {
    const [selectedComponentTypes, setSelectedComponentTypes] = useState<ComponentDefinition['type'][]>([]);

    const components: ComponentDefinition[] = [
        all.utility,
        all.solar,
        all.wind,
        all.battery,
        all.charger,
        all.hvac,
        all.light,
        all.generator,
    ];

    const toggleComponentType = (type: ComponentDefinition['type']) => {
        if (selectedComponentTypes.includes(type)) {
            setSelectedComponentTypes(selectedComponentTypes.filter((selectedType) => selectedType !== type));
        } else {
            setSelectedComponentTypes([...selectedComponentTypes, type]);
        }
    };

    const generate = () => {
        DiagramService.createDiagramFromComponentTypes(selectedComponentTypes);
        DiagramSyncService.save();

        generatorState.generator = DiagramGenerators.EMPTY;
    };

    return (
        <Box className={cx.componentsContent}>
            <DiagramGeneratorBack />

            <h2 className={cx.title} data-size="large">
                Select your components
            </h2>
            <Box className={cx.componentsGrid}>
                {components.map((component) => (
                    <UnstyledButton
                        className={cx.componentsGridItem}
                        onClick={() => {
                            toggleComponentType(component.type);
                        }}
                        data-selected={selectedComponentTypes.includes(component.type)}
                        key={component.type}
                    >
                        <ComponentIcon type={component.type} />
                        <Box>{component.name}</Box>
                    </UnstyledButton>
                ))}
            </Box>
            <Box className={cx.componentsActions}>
                <Button onClick={generate} disabled={selectedComponentTypes.length === 0}>
                    Generate diagram
                </Button>
            </Box>
        </Box>
    );
};

export { DiagramGeneratorComponents };
