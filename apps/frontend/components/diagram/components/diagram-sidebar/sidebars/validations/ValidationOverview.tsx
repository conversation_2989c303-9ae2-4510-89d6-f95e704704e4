import React, { FC, useState } from 'react';

import type { Zone } from '@repo/dcide-component-models';

import { Box, Stack, SegmentedControl, Switch } from '@mantine/core';
import { DiagramSidebar } from 'components/diagram';
import { ComponentInstanceTeaser } from './ComponentInstanceTeaser';
import { ZoneTeaser } from './ZoneTeaser';
import { ComponentInstanceValidationError } from './ComponentInstanceError';
import { ZoneError } from './ZoneError';

import { useZones } from 'components/diagram/hooks/use-zones';
import { useDiagramValidationErrors } from 'components/diagram/hooks/use-diagram-validation-errors';
import { ZoneHelpers } from 'components/diagram/helpers/ZoneHelpers';

import cx from './Validations.module.scss';

enum Tabs {
    TEASERS = 'teasers',
    QUICKFIXES = 'quickfixes',
}

const ValidationOverview: FC = () => {
    const { zones } = useZones();
    const { componentInstances, zones: zoneErrors } = useDiagramValidationErrors();

    const [errorsOnly, setErrorsOnly] = useState(false);
    const [tab, setTab] = useState(Tabs.TEASERS);

    const filterErrorsOnly = (zone: Zone) => {
        if (errorsOnly) {
            return ZoneHelpers.getZoneErrors(zone, zoneErrors[zone.type]).length > 0;
        }

        return true;
    };

    const controlZones = zones.control.filter(filterErrorsOnly);

    const galvanicZones = zones.galvanic.filter(filterErrorsOnly);

    const currentOSZones = [
        ...zones.currentOS['0'],
        ...zones.currentOS['1'],
        ...zones.currentOS['2.1'],
        ...zones.currentOS['2.2'],
        ...zones.currentOS['3'],
        ...zones.currentOS['4'],
    ].filter(filterErrorsOnly);

    const groupedComponentInstances: {
        [key: string]: {
            componentInstance: ComponentInstanceValidationError['payload']['componentInstance'];
            errors: ComponentInstanceValidationError[];
        };
    } = {};

    componentInstances.forEach((error) => {
        const { componentInstance } = error.payload;

        if (errorsOnly && error.level === 'warning') {
            return;
        }

        groupedComponentInstances[componentInstance.id] = groupedComponentInstances[componentInstance.id] || {
            componentInstance,
            errors: [],
        };
        groupedComponentInstances[componentInstance.id].errors.push(error);
    });

    const groupedComponentInstancesAsArray = Object.values(groupedComponentInstances);

    return (
        <React.Fragment>
            <DiagramSidebar.SimpleHeader
                rightSection={
                    <Switch
                        checked={errorsOnly}
                        onChange={(event) => setErrorsOnly(event.currentTarget.checked)}
                        label="Hide warnings"
                        size="xs"
                    />
                }
            >
                Validation Center
            </DiagramSidebar.SimpleHeader>

            <DiagramSidebar.Section>
                <SegmentedControl
                    fullWidth
                    data={[
                        { value: Tabs.TEASERS, label: 'Overview' },
                        { value: Tabs.QUICKFIXES, label: 'Quickfixes' },
                    ]}
                    value={tab}
                    onChange={(value) => {
                        setTab(value as Tabs);
                    }}
                    size="xs"
                />
            </DiagramSidebar.Section>

            {groupedComponentInstancesAsArray.length > 0 && (
                <DiagramSidebar.Section
                    title="Components"
                    collapsable={{
                        enabled: true,
                        key: `diagram.validation.componentInstances`,
                        initial: false,
                    }}
                    rightSection={
                        <Box className={cx.teaserBadge} data-type="error" data-compact>
                            {componentInstances.length} {componentInstances.length === 1 ? 'error' : 'errors'}
                        </Box>
                    }
                    rightSectionVisibility={{
                        collapsed: true,
                        expanded: false,
                    }}
                >
                    {tab === Tabs.TEASERS && (
                        <Stack gap="xs">
                            {groupedComponentInstancesAsArray.map((error) => (
                                <ComponentInstanceTeaser
                                    componentInstance={error.componentInstance}
                                    key={error.componentInstance.id}
                                />
                            ))}
                        </Stack>
                    )}
                    {tab === Tabs.QUICKFIXES && (
                        <Stack gap="xs">
                            {groupedComponentInstancesAsArray.map((error) => (
                                <>
                                    {error.errors.map((error) => (
                                        <ComponentInstanceValidationError error={error} key={error.id} />
                                    ))}
                                </>
                            ))}
                        </Stack>
                    )}
                </DiagramSidebar.Section>
            )}

            {[
                {
                    key: 'control',
                    title: 'Control Zones',
                    teaserTitle: 'Control Zone',
                    zones: controlZones,
                    errors: zoneErrors.control,
                },
                {
                    key: 'galvanic',
                    title: 'Galvanic Zones',
                    teaserTitle: 'Galvanic Zone',
                    zones: galvanicZones,
                    errors: zoneErrors.galvanic,
                },
                {
                    key: 'currentOS',
                    title: 'CurrentOS Zones',
                    teaserTitle: 'Current OS Zone',
                    zones: currentOSZones,
                    errors: zoneErrors.currentOS,
                },
            ]
                .filter((item) => item.zones.length > 0)
                .map((item) => (
                    <DiagramSidebar.Section
                        title={item.title}
                        collapsable={{
                            enabled: true,
                            key: `diagram.validation.zones.${item.key}`,
                            initial: false,
                        }}
                        rightSection={
                            item.errors.length > 0 && (
                                <Box className={cx.teaserBadge} data-type="error" data-compact>
                                    {item.errors.length} {item.errors.length === 1 ? 'error' : 'errors'}
                                </Box>
                            )
                        }
                        rightSectionVisibility={{
                            collapsed: true,
                            expanded: false,
                        }}
                        key={item.key}
                    >
                        {tab === Tabs.TEASERS && (
                            <Box className={cx.teaserGroup}>
                                {item.zones.map((zone, index) => (
                                    <ZoneTeaser
                                        name={`${item.teaserTitle} ${indexToLetters(index)}`}
                                        zone={zone}
                                        key={zone.id}
                                    />
                                ))}
                            </Box>
                        )}
                        {tab === Tabs.QUICKFIXES && (
                            <Stack gap="xs">
                                {item.errors.map((error) => (
                                    <ZoneError error={error} key={error.id} />
                                ))}
                            </Stack>
                        )}
                    </DiagramSidebar.Section>
                ))}
        </React.Fragment>
    );
};

const lookup = [
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
];

const indexToLetters = (index: number) => {
    let string = '';

    while (index >= 0) {
        string = lookup[index % 26] + string;
        index = Math.floor(index / 26) - 1;
    }

    return string;
};

export { ValidationOverview };
