import React, { FC } from 'react';

import { Anchor, Grid, Stack } from '@mantine/core';

import { useScrollNav } from 'hooks/use-scroll-nav';

import { activeScrollItemState, useActiveScrollItem } from 'components/scroll-nav/hooks';

import cx from './ScrollNav.module.css';

export type ScrollNavItem<Type = string> = {
    id: Type;
    label: React.ReactNode;
    disabled?: boolean;
};

interface ScrollNavProps {
    items: ScrollNavItem[];
    topSpacing?: number;
    beforeNavSection?: React.ReactNode;
    children?: React.ReactNode;
    navCols?: number;
    hideNav?: boolean;
}

const ScrollNav: FC<ScrollNavProps> = ({
    items,
    beforeNavSection,
    topSpacing = 0,
    navCols: incomingNavCols = 2,
    hideNav,
    children,
}) => {
    useScrollNav({ topSpacing });

    const navCols = hideNav ? 0 : incomingNavCols;

    const activeItem = useActiveScrollItem();
    const setActiveItem = (key: string | null) => {
        activeScrollItemState.activeScrollItem = key;
    };

    const navItems = items.map(({ id, label, disabled }) => (
        <Anchor
            key={id}
            href={`#${id}`}
            onClick={() => setActiveItem(id)}
            className={`${cx.item} ${activeItem === id && cx.itemActive}`}
            c="black"
            aria-disabled={disabled}
        >
            {label}
        </Anchor>
    ));

    return (
        <Grid>
            {!hideNav && (
                <Grid.Col span={navCols} visibleFrom="lg">
                    <Stack gap={0} className={cx.nav} style={{ top: topSpacing }}>
                        {beforeNavSection}
                        {navItems}
                    </Stack>
                </Grid.Col>
            )}

            <Grid.Col span={{ base: 12, lg: 12 - navCols }}>{children}</Grid.Col>
        </Grid>
    );
};

export { ScrollNav };
