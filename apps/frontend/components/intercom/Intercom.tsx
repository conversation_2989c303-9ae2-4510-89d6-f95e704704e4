import React, { useEffect, useState, FC } from 'react';
import type { CompanyProfile, IntercomChannel as IntercomChannelType } from '@repo/dcide-component-models';

import { Box, Button, ButtonProps, Indicator, Popover, PopoverProps } from '@mantine/core';

import { IoChatbubblesOutline, IoPeopleOutline } from 'react-icons/io5';

import { IntercomNavigation } from './Intercom.Navigation';
import { IntercomEmpty } from './Intercom.Empty';
import { IntercomContent } from './Intercom.Content';
import { IntercomChannel } from './Intercom.Channel';
import { IntercomChannels } from './Intercom.Channels';

import { IntercomService } from 'services/IntercomService';
import { ModalService } from 'services/ModalService';

import { useCurrentUser } from 'hooks/use-current-user';

import { useIntercom } from './hooks/use-intercom';
import { useIntercomDeeplink } from './hooks/use-intercom-deeplink';

import cx from './Intercom.module.css';

const Intercom: FC<{
    channels: IntercomChannelType[];
    tooltip?: React.ReactNode;
    tooltipType?: string;
    children: React.ReactNode;
    userPartOfCompany?: boolean;
    company: CompanyProfile;
    buttonProps?: Partial<ButtonProps>;
}> = ({ channels, tooltip, tooltipType = '', children, userPartOfCompany, company, buttonProps }) => {
    const { opened, channel } = useIntercom(channels);
    const { tooltipOpened, hideTooltip } = useIntercomTooltip(tooltipType);

    const openLoginModal = () => {
        ModalService.openLoginModal();
    };

    const user = useCurrentUser();
    const viewed = channels.every((channel) => channel.viewed);

    const popoverProps: PopoverProps = {
        position: 'top-end',
        withArrow: true,
        arrowOffset: 16,
        arrowSize: 12,
    };

    useIntercomDeeplink();

    const longName = company.name.length > 10;

    return (
        <Box pos="relative" data-intercom-opened={opened}>
            {tooltip && user && (
                <Popover opened={!opened && tooltipOpened} withinPortal={false} {...popoverProps}>
                    <Popover.Target>
                        <div></div>
                    </Popover.Target>
                    <Popover.Dropdown className={cx.tooltip}>{tooltip}</Popover.Dropdown>
                </Popover>
            )}
            <Popover
                opened={opened}
                onOpen={() => {
                    hideTooltip();
                }}
                {...popoverProps}
            >
                <Popover.Target>
                    <Indicator color="red" disabled={viewed}>
                        <Button
                            variant="gradient"
                            leftSection={<IoChatbubblesOutline />}
                            onClick={IntercomService.toggle}
                            {...buttonProps}
                        >
                            {userPartOfCompany ? 'Messages' : `Chat with ${longName ? 'Company' : company.name}`}
                        </Button>
                    </Indicator>
                </Popover.Target>
                <Popover.Dropdown className={cx.dropdown}>
                    {user ? (
                        <React.Fragment>
                            {channels.length === 0 ? (
                                children
                            ) : channel ? (
                                <IntercomChannel channel={channel} />
                            ) : (
                                <IntercomChannels channels={channels} userPartOfCompany={userPartOfCompany} />
                            )}
                        </React.Fragment>
                    ) : (
                        <React.Fragment>
                            <IntercomNavigation />
                            <IntercomContent>
                                <IntercomEmpty>
                                    <IoPeopleOutline size={32} />
                                    Login or register to start a conversation.
                                    <Button onClick={openLoginModal} variant="gradient">
                                        Login or register
                                    </Button>
                                </IntercomEmpty>
                            </IntercomContent>
                        </React.Fragment>
                    )}
                </Popover.Dropdown>
            </Popover>
        </Box>
    );
};

const useIntercomTooltip = (type: string) => {
    const [tooltipOpened, setTooltipOpened] = useState(false);

    const hideTooltip = () => {
        setTooltipOpened(false);
        IntercomService.setTooltipVisibility(type, false);
    };

    useEffect(() => {
        if (type) {
            if (IntercomService.getTooltipVisibility(type) !== false) {
                setTooltipOpened(true);
            }
        }
    }, [type]);

    return { tooltipOpened, hideTooltip };
};

export { Intercom };
