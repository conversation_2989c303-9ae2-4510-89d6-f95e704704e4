import { EnvironmentLockHelpers } from '../helpers/EnvironmentLockHelpers';
import { config } from '../config';

class AIService {
    static getEmbedding = async (text: string, jwt: string): Promise<number[]> => {
        const urlEncodedText = encodeURIComponent(text || '');

        const response = await fetch(`${config.urls.ai}/embedding?text=${urlEncodedText}`, {
            method: 'GET',
            headers: generateHeaders(jwt),
            credentials: 'include',
        });

        if (!response.ok) {
            throw new Error(`Failed to generate embeddings: ${response.status} ${text} ${await response.text()}`);
        }

        return response.json();
    };

    static qna = async (componentId: string, question: string, jwt: string) => {
        const response = await fetch(`${config.urls.ai}/qna`, {
            method: 'POST',
            headers: generateHeaders(jwt),
            body: JSON.stringify({
                component_id: componentId,
                question,
            }),
        });

        if (!response.ok) {
            throw new Error(`Failed to get AI response: ${response.status} ${await response.text()}`);
        }

        return response.json();
    };

    static processFile = async (id: string, filename: string, url: string, jwt: string) => {
        try {
            await fetch(`${config.urls.ai}/knowledgebase/process-document`, {
                method: 'POST',
                headers: generateHeaders(jwt),
                body: JSON.stringify({
                    id,
                    filename,
                    url,
                }),
            });
        } catch (error) {
            console.error('AI Service failed to process document: ', id, filename, url, error);
        }
    };

    static enhanceProfile = async (id: string, name: string, website: string, linkedin: string, jwt: string) => {
        try {
            await fetch(`${config.urls.ai}/enhance-profile`, {
                method: 'POST',
                headers: generateHeaders(jwt),
                body: JSON.stringify({
                    profile_id: id,
                    profile_name: name,
                    website: website,
                    linkedin: linkedin,
                }),
            });
        } catch (error) {
            console.error('AI Service failed to enhance profile: ', id, name, website, linkedin, error);
        }
    };
}

const generateHeaders = (jwt?: string) => {
    const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    };

    if (jwt) {
        headers['Cookie'] = `dcide-jwt=${jwt}; ${EnvironmentLockHelpers.COOKIE_NAME}=${EnvironmentLockHelpers.TOKEN}`;
    }

    if (config.environmentLockAccess) {
        headers['environmentlockaccess'] = config.environmentLockAccess;
    }

    return headers;
};

export { AIService };
