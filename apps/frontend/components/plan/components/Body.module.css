.container {
    position: relative;

    display: flex;
    flex-direction: column;
    align-items: center;

    --input-height: 100px;

    height: calc(100dvh - var(--app-shell-header-offset, 0rem) - var(--app-shell-footer-offset, 0rem));
}

.content {
    overflow-y: scroll; /*  scroll to make sure the content doesn't jump when scrollbars are shown */
    overflow-x: hidden;
    scrollbar-color: rgba(255, 255, 255, 0.1) transparent;

    [data-streaming='true'] & {
        scrollbar-color: transparent transparent; /*  hide scrollbars when AI is thinking */
    }

    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    flex: 1;

    gap: var(--mantine-spacing-sm);
    padding: 0 var(--mantine-spacing-md); /*  don't cut off carousel sliders */
    padding-bottom: var(--mantine-spacing-xl); /*  some breathing space between content and textarea */

    width: calc(100% - var(--mantine-spacing-md) * 2);
    max-width: 800px;
}

.block {
    --radius: var(--mantine-radius-xl);
    --padding: var(--mantine-spacing-xl);

    display: flex;

    width: 100%;

    border-radius: var(--radius);

    padding: var(--mantine-spacing-sm) 0;

    font-size: 14px;

    &[data-type='user'] {
        align-self: flex-end;

        border-top-right-radius: 0;

        background-image: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

        width: auto;
        max-width: 500px;

        padding: var(--mantine-spacing-md) var(--mantine-spacing-xl);
        margin: var(--mantine-spacing-xl) 0;
    }

    &[data-type='assistant'] {
        align-self: flex-start;
    }
}

.input {
    position: relative;

    display: flex;
    flex-direction: column;

    align-items: center;

    width: 100%;
    height: 50%;

    padding: 0 var(--mantine-spacing-md);

    &[data-position='bottom'] {
        height: var(--input-height);

        @media (max-width: $mantine-breakpoint-sm) {
            &[data-bottom-nav='true'] {
                margin-bottom: 50px;
            }
        }
    }

    transition: height 0.5s ease-in-out;
}

.scrollDown {
    position: absolute;
    bottom: calc(var(--input-height) + var(--mantine-spacing-xs));

    border-radius: 99px;
}
