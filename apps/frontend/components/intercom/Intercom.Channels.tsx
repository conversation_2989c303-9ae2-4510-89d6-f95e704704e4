import React, { <PERSON> } from 'react';
import { IntercomChannel } from '@repo/dcide-component-models';

import Link from 'next/link';

import { Box, Button, Card, Group, ScrollArea, Stack } from '@mantine/core';
import { IoChevronForward } from 'react-icons/io5';

import { IntercomContent } from 'components/intercom/Intercom.Content';
import { IntercomEmpty } from 'components/intercom/Intercom.Empty';
import { IntercomNavigation } from 'components/intercom/Intercom.Navigation';

import { useTeam } from 'hooks/use-team';

import { SupportCenterHelpers } from 'helpers/SupportCenterHelpers';

import { intercomState } from 'components/intercom/state/intercom';

import cx from 'components/intercom/Intercom.module.css';

const IntercomChannels: FC<{
    channels: IntercomChannel[];
    userPartOfCompany?: boolean;
}> = ({ channels, userPartOfCompany }) => {
    const unreadOnly = (channel: IntercomChannel) => !channel.viewed;
    const all = userPartOfCompany ? channels.filter(unreadOnly) : channels;

    return (
        <React.Fragment>
            <IntercomNavigation />
            {userPartOfCompany && all.length === 0 ? (
                <IntercomContent>
                    <IntercomEmpty>
                        You have no new conversations.
                        <Button component={Link} href={SupportCenterHelpers.urls.overview()} variant="gradient">
                            View all conversations
                        </Button>
                    </IntercomEmpty>
                </IntercomContent>
            ) : (
                <ScrollArea>
                    <IntercomContent>
                        <Stack gap="xs">
                            {all.map((channel) => (
                                <Channel channel={channel} key={channel.id} />
                            ))}
                            {userPartOfCompany && (
                                <Button
                                    component={Link}
                                    href={SupportCenterHelpers.urls.overview()}
                                    variant="transparent"
                                    fullWidth
                                >
                                    View all conversations
                                </Button>
                            )}
                        </Stack>
                    </IntercomContent>
                </ScrollArea>
            )}
        </React.Fragment>
    );
};

const Channel: FC<{ channel: IntercomChannel }> = ({ channel }) => {
    const { team } = useTeam(channel.team);

    return (
        <Card
            className={cx.overviewChannel}
            onClick={() => {
                intercomState.activeChannel = channel.id;
            }}
            p="xs"
            withBorder
        >
            <Group gap="xs">
                <Box style={{ flexGrow: '1' }}>{channel.name ?? team?.name ?? ' '}</Box>
                {!channel.viewed && (
                    <Box
                        style={{
                            width: 8,
                            height: 8,
                            borderRadius: '50%',
                            backgroundColor: 'var(--mantine-color-red-7)',
                        }}
                    />
                )}
                <IoChevronForward />
            </Group>
        </Card>
    );
};

export { IntercomChannels };
