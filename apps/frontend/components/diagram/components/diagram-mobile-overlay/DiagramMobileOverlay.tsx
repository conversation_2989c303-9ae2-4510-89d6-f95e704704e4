import { useState } from 'react';

import { Box, Button } from '@mantine/core';

import cx from './DiagramMobileOverlay.module.css';

const DiagramMobileOverlay = () => {
    const [open, setOpen] = useState(true);

    if (!open) {
        return null;
    }

    return (
        <Box className={cx.root} hiddenFrom="sm">
            <span>This page is not optimized for mobile devices.</span>
            <span>
                If you want to experience the full capabalities of the design editor, please use a desktop device.
            </span>
            <span>In the meantime, you can still view the design.</span>
            <Button variant="white" size="xs" onClick={() => setOpen(false)}>
                Continue to design
            </Button>
        </Box>
    );
};

export { DiagramMobileOverlay };
