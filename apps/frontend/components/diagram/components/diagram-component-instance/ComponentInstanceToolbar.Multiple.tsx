import type { FC } from 'react';
import { useCallback, useState } from 'react';

import { useController } from 'react-hook-form';

import { Box, Tooltip } from '@mantine/core';
import { useFocusWithin } from '@mantine/hooks';

import { debounce } from 'radash';

import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

const ComponentInstanceToolbarMultiple: FC = () => {
    const { ref, focused } = useFocusWithin();

    const { field } = useController({
        name: 'multiple.amount',
    });
    const [value, setValue] = useState(field.value);

    const placeholder = '1x';
    const formattedValue = value && value > 1 ? `${value}x` : '';

    const persistValue = (value: string) => {
        const valueAsNumber = +value;

        field.onChange(Number.isInteger(valueAsNumber) && valueAsNumber > 1 ? valueAsNumber : 1);
    };

    const debouncedPersistValue = useCallback(
        debounce(
            {
                delay: 777,
            },
            persistValue,
        ),
        [],
    );

    return (
        <Tooltip label="Components in parallel" disabled={focused} withArrow>
            <Box className={cx.text}>
                <Box className={cx.autoInput} ref={ref}>
                    <input
                        {...field}
                        type="text"
                        value={focused ? value : formattedValue}
                        onChange={(event) => {
                            setValue(event.target.value);
                            debouncedPersistValue(event.target.value);
                        }}
                        onBlur={() => {
                            persistValue(value);
                        }}
                        autoComplete="off"
                        className={cx.autoInputInput}
                        placeholder={placeholder}
                    />
                    <span className={cx.autoInputSizer}>{(focused ? value : formattedValue) || placeholder}</span>
                </Box>
            </Box>
        </Tooltip>
    );
};

export { ComponentInstanceToolbarMultiple };
