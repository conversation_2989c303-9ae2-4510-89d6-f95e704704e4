import React from 'react';

import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    UnitWithTooltip,
    useRenderVariableMultiInput,
    MeasurementInputWrapper,
} from 'components/forms/fields/measurement';
import { MeasurementFieldType, powerConverter } from '@repo/dcide-component-models';

type PowerFieldProps = {
    name: string;
    fields?: MeasurementFieldType[];
    defaultUnit?: 'W' | 'kW' | 'MW';
    hideIcons?: boolean;
} & MeasurementInputWrapperProps;

const PowerField = ({ name, fields = ['nom', 'max'], defaultUnit, hideIcons, ...wrapperProps }: PowerFieldProps) => {
    console.log(defaultUnit);

    const {
        unit,
        cycleUnit,
        fields: fieldsRecord,
    } = useMultiUnitMeasurementController({
        name,
        converter: powerConverter,
        defaultUnit: defaultUnit || powerConverter.baseUnit,
    });

    const Unit = <UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>;
    const Fields = useRenderVariableMultiInput({
        fields,
        fieldsRecord,
        Unit,
        extraProps: {
            hideIcon: hideIcons,
            size: wrapperProps.size,
        },
    });

    return <MeasurementInputWrapper {...wrapperProps}>{Fields}</MeasurementInputWrapper>;
};

export { PowerField };
