import React, { FC } from 'react';

import { MantineProvider, useMantineTheme } from '@mantine/core';
import { DatasheetMode, useDatasheetMode } from './DatasheetMode';

const DatasheetMantineProvider: FC<{ children: React.ReactNode }> = ({ children }) => {
    const mode = useDatasheetMode();
    const theme = useMantineTheme();

    const isViewMode = mode === DatasheetMode.VIEW;

    return (
        <MantineProvider
            theme={{
                components: {
                    ...theme.components,
                    Autocomplete: {
                        defaultProps: {
                            readOnly: isViewMode,
                        },
                    },
                    Button: {
                        styles: {
                            root: {
                                pointerEvents: isViewMode ? 'none' : 'all',
                            },
                        },
                    },
                    Checkbox: {
                        defaultProps: {
                            disabled: isViewMode,
                        },
                    },
                    Input: {
                        defaultProps: {
                            readOnly: isViewMode,
                        },
                        styles: () => ({
                            input: {
                                background: 'transparent',
                                border: 'none',
                            },
                            icon: {
                                color: 'currentColor',
                            },
                        }),
                    },
                    MultiSelect: {
                        defaultProps: {
                            readOnly: isViewMode,
                        },
                        ...(isViewMode
                            ? {
                                  styles: {
                                      searchInput: {
                                          cursor: 'default !important',
                                      },
                                      input: {
                                          cursor: 'default',
                                      },
                                      section: {
                                          display: 'none',
                                      },
                                  },
                              }
                            : {}),
                    },
                    NumberInput: {
                        defaultProps: {
                            hideControls: true,
                        },
                        styles: {
                            section: {
                                pointerEvents: isViewMode ? 'none' : 'all',
                                fontSize: theme.fontSizes.sm,
                            },
                        },
                    },
                    Popover: {
                        defaultProps: {
                            styles: {
                                dropdown: {
                                    boxShadow: theme.shadows.md,
                                },
                            },
                        },
                    },
                    Select: {
                        defaultProps: {
                            readOnly: isViewMode,
                        },
                        ...(isViewMode
                            ? {
                                  styles: {
                                      input: {
                                          cursor: 'default !important',
                                      },
                                      section: {
                                          display: 'none',
                                      },
                                  },
                              }
                            : {}),
                    },
                    SegmentedControl: {
                        ...theme.components.SegmentedControl,
                        defaultProps: {
                            readOnly: isViewMode,
                            size: 'xs',
                        },
                    },
                    Table: {
                        styles: {
                            root: {
                                color: theme.colors.gray[7],
                                borderTop: `1px solid ${theme.colors.gray[3]}`,
                                borderBottom: `1px solid ${theme.colors.gray[3]}`,
                            },
                        },
                    },
                    Pill: {
                        styles: {
                            root: {
                                border: `1px solid ${theme.colors.gray[3]}`,
                                background: 'transparent',
                            },
                        },
                    },
                },
            }}
        >
            {children}
        </MantineProvider>
    );
};

export { DatasheetMantineProvider };
