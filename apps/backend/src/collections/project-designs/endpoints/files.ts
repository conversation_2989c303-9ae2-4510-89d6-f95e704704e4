import type { Endpoint } from 'payload';

const files: Endpoint = {
    path: '/:designId/files',
    method: 'get',
    handler: async (request) => {
        const { routeParams, payload } = request;

        const { files } = await payload.findByID({
            collection: 'projectDesigns',
            id: routeParams?.designId as string,
            depth: 1,
            overrideAccess: false,
            req: request,
        });

        return Response.json(files);
    },
};

export { files };
