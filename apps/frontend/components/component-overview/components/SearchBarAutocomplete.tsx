import React from 'react';

import Link from 'next/link';

import { useDebouncedValue, useHash } from '@mantine/hooks';
import { Menu, Text, Box, Badge } from '@mantine/core';

import { BsBox } from 'react-icons/bs';
import { IoArrowForward, IoBriefcaseOutline, IoSearchSharp, IoTimeOutline } from 'react-icons/io5';
import { TbSparkles } from 'react-icons/tb';

import {
    all,
    CompanyProfile,
    CompanyServiceOptions,
    Component,
    ComponentDefinition,
} from '@repo/dcide-component-models';

import { useCompanyMeta } from 'hooks/use-company-meta';
import { useComponentMeta } from 'hooks/use-component-meta';
import { useSearchHistory } from 'hooks/use-search-history';
import { useSearchAutosuggest } from 'hooks/use-search-autosuggest';
import { useCompanyServiceTags } from 'hooks/use-company-service-tags';

import { TextHelpers } from 'helpers/TextHelpers';
import { RouterHelpers } from 'helpers/RouterHelpers';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { RouterService } from 'services/RouterService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { SearchService } from 'components/component-overview/services/SearchService';
import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';

import { CompanyLogo } from 'components/company-logo';
import { ComponentThumbnail } from 'components/thumbnail';
import { ComponentIcon } from 'components/component-icons/ComponentIcon';
import { CompanyLogoFallback } from 'components/company-logo/CompanyLogoFallback';

import { SEARCH_SUGGESTIONS } from 'components/component-overview/components/SearchSuggestions';

import cx from './SearchBarAutocomplete.module.css';

const MIN_QUERY_LENGTH = 3;

const SearchBarAutocomplete = ({
    query,
    setQuery,
    handleSearch,
}: {
    query: string;
    setQuery: (query: string) => void;
    handleSearch: (query: string) => void;
}) => {
    const [debouncedQuery] = useDebouncedValue(query, 300);

    const { companies, products, isLoading } = useSearchAutosuggest(debouncedQuery);

    const { searchHistory } = useSearchHistory();
    const cleanHistory = searchHistory.filter(
        (history) => TextHelpers.textMatches(history, query) && !TextHelpers.textMatches(history, query, true),
    );

    const componentTypes =
        query.length >= MIN_QUERY_LENGTH
            ? Object.values(all).filter((component) => TextHelpers.textMatches(component.name, query))
            : [];

    const staticServices =
        query.length >= MIN_QUERY_LENGTH
            ? CompanyServiceOptions.filter((service) => TextHelpers.textMatches(service.label, query))
            : [];

    const { serviceTags } = useCompanyServiceTags();
    const dynamicServices =
        query.length >= MIN_QUERY_LENGTH ? serviceTags.filter((tag) => TextHelpers.textMatches(tag, query)) : [];

    const children: React.ReactNode[] = [];

    if (query.length) {
        children.push(
            <TextItem label={query} icon={<IoSearchSharp size={16} />} onClick={() => handleSearch(query)} />,
        );
    }

    if (cleanHistory.length) {
        children.push(<History history={cleanHistory} handleSearch={handleSearch} />);
    }

    children.push(
        ...componentTypes.map((component) => (
            <ComponentType key={component.type} component={component} setQuery={setQuery} />
        )),
        ...companies.map((company) => <ProductManufacturer key={company.id} company={company} setQuery={setQuery} />),
        ...staticServices.map((service) => <Service key={service.value} {...service} setQuery={setQuery} />),
        ...dynamicServices.map((service) => <Service key={service} label={service} setQuery={setQuery} />),
        ...companies.map((company) => <Company key={company.id} company={company} />),
        ...products.map((component) => <Product key={component.id} component={component} />),
    );

    if (isLoading) {
        children.push(<Menu.Label>Loading…</Menu.Label>);
    }

    if (!children.length) {
        children.push(
            SEARCH_SUGGESTIONS.map((suggestion) => (
                <TextItem
                    key={suggestion}
                    label={suggestion}
                    icon={<IoSearchSharp size={16} />}
                    onClick={() => handleSearch(suggestion)}
                />
            )),
        );
    }

    return (
        <Menu.Dropdown className={cx.dropdown}>
            <TextItem
                label="Try our Search Assistant"
                icon={<TbSparkles size={16} />}
                suffix={
                    <Badge color="red" radius="xs">
                        New
                    </Badge>
                }
                onClick={() => {
                    RouterService.push(RouterHelpers.urls.searchAssistant());
                }}
            />
            {children}
        </Menu.Dropdown>
    );
};

const History = ({ history, handleSearch }: { history: string[]; handleSearch: (query: string) => void }) => {
    return history.map((query) => (
        <TextItem key={query} label={query} icon={<IoTimeOutline size={16} />} onClick={() => handleSearch(query)} />
    ));
};

const TextItem = ({
    prefix,
    suffix,
    label,
    icon,
    onClick,
}: {
    prefix?: string;
    suffix?: React.ReactNode;
    label: string;
    icon: React.ReactNode;
    onClick: () => void;
}) => {
    return (
        <Menu.Item leftSection={<Box className={cx.icon}>{icon}</Box>} rightSection={suffix} onClick={onClick}>
            <Text inherit>
                {prefix && <Text span inherit>{`${prefix} > `}</Text>}
                <Text span inherit fw={600}>
                    {label}
                </Text>
            </Text>
        </Menu.Item>
    );
};

const ComponentType = ({
    component,
    setQuery,
}: {
    component: ComponentDefinition;
    setQuery: (query: string) => void;
}) => {
    const [, setHash] = useHash();

    const onClick = () => {
        SearchService.resetFilters();
        SearchService.setFilter('type', component.type);

        InternalTrackingService.track('product.search.query', {
            query: component.plural,
            autocomplete: 'products.type',
        });

        setHash('products');

        setQuery(component.plural);
    };

    return (
        <Menu.Item
            leftSection={
                <Box className={`${cx.icon} ${cx.componentIcon}`}>
                    <ComponentIcon type={component.type} />
                </Box>
            }
            onClick={onClick}
        >
            <Text inherit>
                {'Products > '}
                <Text span inherit fw={600}>
                    All {component.plural}
                </Text>
            </Text>
        </Menu.Item>
    );
};

const Product = ({ component }: { component: Component }) => {
    const { uniqueName, typeName, manufacturerName } = useComponentMeta(component);

    const onClick = () => {
        InternalTrackingService.track('product.search.query', {
            query: uniqueName,
            autocomplete: 'products.link',
        });
    };

    return (
        <Menu.Item
            component={Link}
            href={ComponentHelpers.urls.view(component.id)}
            target="_blank"
            onClick={onClick}
            leftSection={
                <Box className={cx.icon}>
                    <ComponentThumbnail showEmpty component={component} />
                </Box>
            }
            rightSection={<IoArrowForward />}
        >
            <Text inherit fw={600}>
                {uniqueName}
            </Text>
            <Text inherit c="dimmed">
                {`${typeName} • ${manufacturerName}`}
            </Text>
        </Menu.Item>
    );
};

const Company = ({ company }: { company: CompanyProfile }) => {
    const { subtitle } = useCompanyMeta(company);

    const onClick = () => {
        InternalTrackingService.track('product.search.query', {
            query: company.name,
            autocomplete: 'profiles.link',
        });
    };

    return (
        <Menu.Item
            component={Link}
            href={CompanyProfileHelpers.urls.view(company.slug)}
            target="_blank"
            onClick={onClick}
            leftSection={
                <Box className={cx.icon}>
                    <CompanyLogo
                        logos={company.logos}
                        fallback={<CompanyLogoFallback name={company.name} size="sm" />}
                    />
                </Box>
            }
            rightSection={<IoArrowForward />}
        >
            <Text inherit fw={600}>
                {company.name}
            </Text>
            <Text inherit c="dimmed">
                {subtitle}
            </Text>
        </Menu.Item>
    );
};

const Service = ({ value, label, setQuery }: { value?: string; label: string; setQuery: (query: string) => void }) => {
    const [, setHash] = useHash();

    const onClick = () => {
        ProfileOverviewService.resetFilters();
        ProfileOverviewService.setFilter('services', [value ?? label]);

        InternalTrackingService.track('product.search.query', {
            query: label,
            autocomplete: 'profiles.service',
        });

        setHash('profiles');

        setQuery(label);
    };

    return (
        <TextItem
            prefix="Profiles > Services"
            label={label}
            icon={<IoBriefcaseOutline size={16} />}
            onClick={onClick}
        />
    );
};

const ProductManufacturer = ({ company, setQuery }: { company: CompanyProfile; setQuery: (query: string) => void }) => {
    const [, setHash] = useHash();

    const onClick = () => {
        SearchService.resetFilters();
        SearchService.setFilter('manufacturer', company.id);

        InternalTrackingService.track('product.search.query', {
            query: company.name,
            autocomplete: 'products.manufacturer',
        });

        setHash('products');

        setQuery(company.name);
    };

    return (
        <TextItem prefix="Products > Manufacturer" label={company.name} icon={<BsBox size={16} />} onClick={onClick} />
    );
};

export { SearchBarAutocomplete };
