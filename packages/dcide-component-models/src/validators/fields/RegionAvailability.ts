import { z } from 'zod';

enum Region {
    EU = 'EU',
    US = 'US',
    CHINA = 'China',
    JAPAN = 'Japan',
}

const Regions = [Region.EU, Region.US, Region.CHINA, Region.JAPAN] as const;

const options: { value: Region; label: string }[] = Regions.map((region) => ({ value: region, label: region }));

const RegionAvailability = {
    validator: z.array(z.enum(Regions)),
    options,
};

export { RegionAvailability, Region };
