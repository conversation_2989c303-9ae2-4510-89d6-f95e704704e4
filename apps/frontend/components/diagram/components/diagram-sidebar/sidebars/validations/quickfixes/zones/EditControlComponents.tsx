import React, { FC, useState } from 'react';

import {
    getComponentDefinition,
    type DiagramComponentInstance,
    type ZoneValidationError,
} from '@repo/dcide-component-models';

import ComponentInstanceSelector from '../ComponentInstanceSelector';
import { useComponentInstance } from 'components/diagram/hooks';
import { Box, Stack } from '@mantine/core';
import cx from 'components/diagram/components/diagram-sidebar/sidebars/validations/Validations.module.css';
import { SimulationPorts } from 'components/diagram/components/diagram-sidebar/sidebars/edit-component-instance/sections/Simulation.Ports';
import { DiagramSidebarMantineProvider } from 'components/diagram/components/diagram-sidebar/DiagramSidebarMantineProvider';
import { SubmitButton } from 'components/diagram/components/diagram-sidebar/sidebars/validations/quickfixes/SubmitButton';
import { ComponentInstanceForm } from 'components/diagram/components/diagram-component-instance/ComponentInstanceForm';
import { useWatch } from 'react-hook-form';

const EditControlComponents: FC<{ error: ZoneValidationError }> = ({ error }) => {
    const { zone } = error.payload;
    const { componentInstances } = zone;

    const [selected, select] = useState<string | null>(null);
    const componentInstance = useComponentInstance(selected ?? undefined);

    const controllableComponentInstances = componentInstances.filter((component) => {
        const definition = getComponentDefinition(component.componentType);

        return definition.actsAsConverter;
    });

    return (
        <Stack>
            <ComponentInstanceSelector
                componentInstances={controllableComponentInstances}
                selected={selected}
                select={select}
            />
            {componentInstance && (
                <DiagramSidebarMantineProvider>
                    <ComponentInstanceForm componentInstance={componentInstance} autosave={false}>
                        <Box className={cx.quickfixComponentInstanceConfiguration}>
                            <Fields zone={zone} />
                        </Box>
                        <SubmitButton />
                    </ComponentInstanceForm>
                </DiagramSidebarMantineProvider>
            )}
        </Stack>
    );
};

const Fields: FC<{
    zone: ZoneValidationError['payload']['zone'];
}> = ({ zone }) => {
    const componentInstance = useWatch() as DiagramComponentInstance;
    const connectedPorts: number[] = [];

    zone.connections.forEach((connection) => {
        if (connection.from.componentInstanceId === componentInstance.id && connection.from.port !== null) {
            connectedPorts.push(connection.from.port);
        }

        if (connection.to.componentInstanceId === componentInstance.id && connection.to.port !== null) {
            connectedPorts.push(connection.to.port);
        }
    });

    return (
        <>
            <SimulationPorts
                componentInstance={componentInstance}
                filter={(_, index) => {
                    return connectedPorts.includes(index);
                }}
            />
        </>
    );
};

export { EditControlComponents };
