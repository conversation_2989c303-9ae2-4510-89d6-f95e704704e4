{# ------------------------ #}
{# Available Variables #}
{# ------------------------ #}
{# Attributes: #}
{# type, name, handle, instructions, attribute, default, feed, feedData #}
{# ------------------------ #}
{# Fields: #}
{# name, handle, instructions, feed, feedData, field, fieldClass #}
{# ------------------------ #}

{% import 'feed-me/_macros' as feedMeMacro %}
{% import '_includes/forms' as forms %}

{% if field is defined %}
    {% set default = default ?? {
        type: 'elementselect',
        options: {
            elementType: fieldClass.elementType,
            selectionLabel: "Default Form" | t('feed-me'),
        },
    } %}
{% endif %}

{# Get any sub-fields for the element. Also check to go only one level deep #}
{% if field is defined and isSubElementField is not defined %}
    {% set elementFields = [] %}

    {# Prevent infinite loop by only allow elements that are not the same as this #}
    {% for elementField in craft.feedme.getElementLayoutByField(className(field), field) %}
        {% if craft.feedme.supportedSubField(className(elementField)) %}
            {% set elementFields = elementFields|merge([ elementField ]) %}
        {% endif %}
    {% endfor %}
{% endif %}

{% extends 'feed-me/_includes/fields/_base' %}

{% block extraSettings %}
    <div class="element-match">
        <span>{{ 'Data provided for this form is:' | t('feed-me') }}</span>

        {% set matchAttributes = [
            { value: 'title', label: 'Title'|t('feed-me') },
            { value: 'elements.id', label: 'ID'|t('feed-me') },
            { value: 'handle', label: 'Handle'|t('feed-me') },
        ] %}

        {{ forms.selectField({
            name: 'options[match]',
            class: '',
            value: hash_get(feed.fieldMapping, optionsPath ~ '.match') ?: '',
            options: matchAttributes,
        }) }}
    </div>
{% endblock %}
