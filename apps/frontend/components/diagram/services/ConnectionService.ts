import { snapshot } from 'valtio';
import { isNumber } from 'radash';
import { SVG } from '@svgdotjs/svg.js';

import { Current, Voltage, VoltageType } from '@repo/dcide-component-models';

import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { DiagramService } from 'components/diagram/services/DiagramService';
import { PortService } from 'components/diagram/services/PortService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { FormatHelpers } from 'helpers/formatters';

import { CELL_WIDTH, CELL_HEIGHT, CELL_PADDING, ICON_WIDTH } from 'components/diagram/diagram-dimensions';

import {
    Point,
    Diagram,
    DiagramComponentInstance,
    DiagramConnectionAnchor,
    DiagramConnectionAnchorWithoutPort,
    DiagramConnection,
    DiagramConnectionSchema,
    DiagramPosition,
} from '@repo/dcide-component-models';

import { PowerFlowDirection } from '@repo/dcide-component-models';

import { roundedPath } from '../helpers/roundedPath';

import { getReversedPowerFlowDirection } from '../helpers/get-reversed-power-flow-direction';
import { diagram } from 'components/diagram/state/diagram';
import { diagramHelpers } from '../state/grid';
import { copyPasteSpecificationsState } from 'components/diagram/state/copy-paste-specifications';

import { ConnectionHelpers } from 'components/diagram/helpers/ConnectionHelpers';
import { PortHelpers } from 'components/diagram/helpers/PortHelpers';

type Port = DiagramConnection['from' | 'to']['port'];

const ConnectionService = {
    addConnection: ({
        from,
        to,
        ...connectionProps
    }: Required<Pick<DiagramConnection, 'from' | 'to'>> & Partial<DiagramConnection>) => {
        const connection = DiagramConnectionSchema.parse({
            ...connectionProps,
            id: connectionProps.id || ConnectionHelpers.generateId(),
            from,
            to,
        });

        DiagramSyncService.create(`connections.${connection.id}`, connection, true);

        return connection.id;
    },

    getById: (connectionId: DiagramConnection['id']) => {
        const diagram = DiagramService.get();

        return diagram.connections[connectionId] || null;
    },

    getByComponentInstanceAndPort: (componentInstanceId: string, port: number | null) => {
        if (port === null) {
            return null;
        }

        const { connections } = diagram;

        return (
            Object.values(connections).find((connection) => {
                return (
                    (connection.from.componentInstanceId === componentInstanceId && connection.from.port === port) ||
                    (connection.to.componentInstanceId === componentInstanceId && connection.to.port === port)
                );
            }) || null
        );
    },

    copySpecifications: (id: DiagramConnection['id']) => {
        copyPasteSpecificationsState.connection = ConnectionService.getById(id);
    },

    pasteSpecifications: (id: DiagramConnection['id']) => {
        const connection = ConnectionService.getById(id);
        const { connection: copy } = snapshot(copyPasteSpecificationsState);

        if (connection && copy) {
            DiagramSyncService.updateByKey(`connections.${connection.id}.voltage`, copy.voltage, true);
            DiagramSyncService.updateByKey(`connections.${connection.id}.current`, copy.current, true);
            DiagramSyncService.updateByKey(`connections.${connection.id}.lines`, copy.lines, true);
            DiagramSyncService.updateByKey(`connections.${connection.id}.wireSize`, copy.wireSize, true);
            DiagramSyncService.updateByKey(`connections.${connection.id}.length`, copy.length, true);
            DiagramSyncService.updateByKey(`connections.${connection.id}.resistance`, copy.resistance, true);
            DiagramSyncService.updateByKey(`connections.${connection.id}.material`, copy.material, true);
        }
    },

    getConnectionsForComponentInstance: (
        diagram: Diagram = DiagramService.get(),
        componentInstance: DiagramComponentInstance,
    ) => {
        const { connections } = diagram;

        return Object.values(connections).filter((connection) => {
            return (
                connection.from.componentInstanceId === componentInstance.id ||
                connection.to.componentInstanceId === componentInstance.id
            );
        });
    },

    getInternalConnectionsForComponentInstances: (
        componentInstanceIds: DiagramComponentInstance['id'][],
        diagram = DiagramService.get(),
    ) => {
        const { connections } = diagram;

        return Object.values(connections).filter((connection) => {
            return (
                componentInstanceIds.includes(connection.from.componentInstanceId) &&
                componentInstanceIds.includes(connection.to.componentInstanceId)
            );
        });
    },

    getIconPoint: (anchor: DiagramConnectionAnchor, diagram = DiagramService.get()): Point | undefined => {
        const { componentInstances } = diagram;
        const { componentInstanceId: id, edge, offset } = anchor;

        const componentInstance = componentInstances[id];

        if (!componentInstance) {
            return undefined;
        }

        const { position, rowSpan = 1, colSpan = 1 } = componentInstance;

        const { x, y } = position;

        const point = {
            x: 0,
            y: 0,
        };

        if (edge === 'top') {
            point.x = (x + offset) * CELL_WIDTH + CELL_WIDTH - CELL_PADDING - ICON_WIDTH / 2;
            point.y = y * CELL_HEIGHT + CELL_PADDING + 1;
        } else if (edge === 'bottom') {
            point.x = (x + offset) * CELL_WIDTH + CELL_WIDTH - CELL_PADDING - ICON_WIDTH / 2;
            point.y = (y + rowSpan) * CELL_HEIGHT - CELL_PADDING - 1;
        } else if (edge === 'left') {
            point.x = (x + 1) * CELL_WIDTH - CELL_PADDING - ICON_WIDTH + 1;
            point.y = (y + offset) * CELL_HEIGHT + CELL_PADDING + ICON_WIDTH / 2;
        } else if (edge === 'right') {
            point.x = (x + colSpan) * CELL_WIDTH - CELL_PADDING - 1;
            point.y = (y + offset) * CELL_HEIGHT + CELL_PADDING + ICON_WIDTH / 2;
        }

        return point;
    },

    getCellPoint: (anchor: DiagramConnectionAnchor, diagram = DiagramService.get()): Point | undefined => {
        const { componentInstances } = diagram;
        const { componentInstanceId: id, edge, offset } = anchor;

        const componentInstance = componentInstances[id];

        if (!componentInstance) {
            return undefined;
        }

        const { position, rowSpan = 1, colSpan = 1 } = componentInstance;

        const { x, y } = position;

        const point = {
            x: 0,
            y: 0,
        };

        if (edge === 'top') {
            point.x = (x + offset) * CELL_WIDTH + CELL_WIDTH - CELL_PADDING - ICON_WIDTH / 2;
            point.y = y * CELL_HEIGHT;
        } else if (edge === 'bottom') {
            point.x = (x + offset) * CELL_WIDTH + CELL_WIDTH - CELL_PADDING - ICON_WIDTH / 2;
            point.y = (y + rowSpan) * CELL_HEIGHT;
        } else if (edge === 'left') {
            point.x = x * CELL_WIDTH;
            point.y = (y + offset) * CELL_HEIGHT + CELL_PADDING + ICON_WIDTH / 2;
        } else if (edge === 'right') {
            point.x = (x + colSpan) * CELL_WIDTH;
            point.y = (y + offset) * CELL_HEIGHT + CELL_PADDING + ICON_WIDTH / 2;
        }

        return point;
    },

    getPathPoints: (
        from: DiagramConnectionAnchor,
        to: DiagramConnectionAnchor,
        diagram = DiagramService.get(),
    ): Point[] | undefined => {
        const p2 = ConnectionService.getCellPoint(from, diagram);
        const p4 = ConnectionService.getCellPoint(to, diagram);

        if (!p2 || !p4) {
            return undefined;
        }

        const dx = p4.x - p2.x;
        const dy = p4.y - p2.y;

        const lr = from.edge === 'left' || from.edge === 'right';
        const tb = from.edge === 'top' || from.edge === 'bottom';

        const p3 = {
            x: p2.x + (lr ? dx : 0),
            y: p2.y + (tb ? dy : 0),
        };

        return [
            ConnectionService.getIconPoint(from, diagram),
            p2,
            p3,
            p4,
            ConnectionService.getIconPoint(to, diagram),
        ].filter((point, index, points) => {
            if (!point) {
                return false;
            }

            const { x, y } = point;

            const { x: x_, y: y_ } = points[index - 1] || {};

            return x !== x_ || y !== y_;
        }) as Point[];
    },

    getPath: (
        from: DiagramConnectionAnchor,
        to: DiagramConnectionAnchor,
        diagram = DiagramService.get(),
    ):
        | {
              separatePaths: string[];
              path: string;
              points: Point[];
              length: number;
              getPointAtPosition: (position: number) => Point;
          }
        | undefined => {
        const points = ConnectionService.getPathPoints(from, to, diagram);

        if (!points) {
            return;
        }

        const radius = 8;

        const pathWithRoundedCorners = roundedPath({ points, radius });

        const separatePaths: string[] = points.reduce((res: string[], point, index) => {
            const nextPoint = points[index + 1];

            if (index < points.length - 1) {
                res.push(`M ${point.x} ${point.y} L ${nextPoint.x} ${nextPoint.y}`);
            }

            return res;
        }, []);

        const draw = SVG();
        const path = draw.path(pathWithRoundedCorners);
        const length = path.length();
        const getPointAtPosition = (position: number) => {
            return path.pointAt(position);
        };

        return {
            path: pathWithRoundedCorners,
            points,
            separatePaths,
            length,
            getPointAtPosition,
        };
    },

    // TODO: fix this type, technically it can be a crushed object as well
    update: (values: Partial<DiagramConnection> & { id: DiagramConnection['id'] }) => {
        const { id, ...rest } = values;

        if (!id) {
            // Remove this after debugging!
            console.error('Cannot update connection without id');
            console.log(values);

            return;
        }

        const data: { [k: string]: any } = {};

        Object.entries(rest).forEach(([key, value]) => {
            data[`connections.${id}.${key}`] = value;
        });

        DiagramSyncService.update(data);
    },

    updateCurrent: (connectionId: DiagramConnection['id'], current: Current) => {
        ConnectionService.update({ id: connectionId, current });
    },

    updateVoltage: (connectionId: DiagramConnection['id'], voltage: Voltage) => {
        ConnectionService.update({ id: connectionId, voltage });
    },

    updateVoltageType: (connection: DiagramConnection, voltageType: 'AC' | 'DC') => {
        const { from } = connection;

        const fromComponentInstance = ComponentInstanceService.getById(from.componentInstanceId);

        PortService.updateVoltageType(fromComponentInstance.id, from.port, voltageType, true);
    },

    updatePowerFlow: (connection: DiagramConnection, powerFlowDirection: PowerFlowDirection) => {
        const { from, to } = connection;
        const fromComponentInstance = ComponentInstanceService.getById(from.componentInstanceId);
        const toComponentInstance = ComponentInstanceService.getById(to.componentInstanceId);
        const reversedPowerFlowDirection = getReversedPowerFlowDirection(powerFlowDirection);

        if (fromComponentInstance && isNumber(from.port)) {
            PortService.updatePowerFlowDirection(fromComponentInstance, from.port, powerFlowDirection);
        }

        if (toComponentInstance && isNumber(to.port)) {
            PortService.updatePowerFlowDirection(toComponentInstance, to.port, reversedPowerFlowDirection);
        }
    },

    updatePort: (connection: DiagramConnection, endpoint: 'from' | 'to', port: Port) => {
        DiagramSyncService.updateByKey(`connections.${connection.id}.${endpoint}.port`, port, true);
    },

    removePort: (connectionId: string, endpoint: 'from' | 'to') => {
        DiagramSyncService.updateByKey(`connections.${connectionId}.${endpoint}.port`, null, true);
    },

    setAnchorOffset: (connectionId: DiagramConnection['id'], endpoint: 'from' | 'to', offset: number) => {
        DiagramSyncService.updateByKey(`connections.${connectionId}.${endpoint}.offset`, offset, true);
    },

    moveConnectionAnchor: (
        connection: DiagramConnection,
        current: DiagramConnectionAnchor,
        next: DiagramConnectionAnchor,
    ) => {
        const fixedAnchor =
            current.componentInstanceId === connection.from.componentInstanceId ? connection.to : connection.from;

        if (fixedAnchor.componentInstanceId === next.componentInstanceId) {
            throw new Error('Cannot connect a component to itself');
        }

        const diagram = DiagramSyncService.get();

        const nextComponentInstance = ComponentInstanceService.getById(next.componentInstanceId, diagram);
        const currentComponentInstance = ComponentInstanceService.getById(current.componentInstanceId, diagram);

        const voltageType =
            current.port !== null
                ? PortHelpers.getVoltageTypeConfiguration(currentComponentInstance, current.port)
                : null;

        const isMovingToSameComponent = current.componentInstanceId === next.componentInstanceId;

        // If portToConnectTo is null, it means that no port is available on the component
        // Next, try and create a new port on the next component
        // If still null is returned, it means that the component has reached the maximum number of ports
        const portToConnectTo =
            next.port ??
            (isMovingToSameComponent
                ? current.port
                : (ComponentInstanceService.getNextAvailablePort(next.componentInstanceId) ??
                  ComponentInstanceService.addDynamicPort(nextComponentInstance)));

        if (portToConnectTo !== null) {
            if (connection.from.componentInstanceId === current.componentInstanceId) {
                DiagramSyncService.updateByKey(
                    `connections.${connection.id}.from`,
                    {
                        ...connection.from,
                        ...next,
                        port: portToConnectTo,
                    },
                    true,
                );
            } else if (connection.to.componentInstanceId === current.componentInstanceId) {
                DiagramSyncService.updateByKey(
                    `connections.${connection.id}.to`,
                    {
                        ...connection.to,
                        ...next,
                        port: portToConnectTo,
                    },
                    true,
                );
            }

            if (voltageType) {
                PortService.updateVoltageType(nextComponentInstance.id, portToConnectTo, voltageType, true);
            }
        }

        if (portToConnectTo === null) {
            console.error(
                JSON.stringify({
                    connection,
                    current,
                    next,
                }),
            );

            throw new Error('Cannot move connection anchor because the maximum number of connections has been reached');
        }
    },

    delete: (idOrArray: DiagramConnection['id'] | DiagramConnection['id'][]): void => {
        if (typeof idOrArray === 'string') {
            return ConnectionService.delete([idOrArray]);
        }

        const keysToDelete = idOrArray.map((connectionId) => `connections.${connectionId}`);
        DiagramSyncService.delete(keysToDelete, true);
    },

    isValidConnection: (from: DiagramConnectionAnchor, to: DiagramConnectionAnchor): boolean => {
        const diagram = DiagramService.get();

        const { componentInstances } = diagram;

        // Do not allow connections to the same component
        if (from.componentInstanceId === to.componentInstanceId) {
            return false;
        }

        const fromComponent = componentInstances[from.componentInstanceId];
        const toComponent = componentInstances[to.componentInstanceId];

        // Do not allow connections to components that do not exist
        if (!fromComponent || !toComponent) {
            return false;
        }

        const x_ =
            fromComponent.position.x +
            (from.edge === 'top' || from.edge === 'bottom' ? from.offset : 0) +
            (from.edge === 'right' ? fromComponent.colSpan - 1 : 0);
        const y_ =
            fromComponent.position.y +
            (from.edge === 'left' || from.edge === 'right' ? from.offset : 0) +
            (from.edge === 'bottom' ? fromComponent.rowSpan - 1 : 0);

        const x =
            toComponent.position.x +
            (to.edge === 'top' || to.edge === 'bottom' ? to.offset : 0) +
            (to.edge === 'right' ? toComponent.colSpan - 1 : 0);
        const y =
            toComponent.position.y +
            (to.edge === 'left' || to.edge === 'right' ? to.offset : 0) +
            (to.edge === 'bottom' ? toComponent.rowSpan - 1 : 0);

        if (x == x_ && y < y_) {
            if (from.edge === 'top' && to.edge === 'bottom') return true;
            else if (from.edge === 'left' && to.edge === 'left') return true;
            else if (from.edge === 'right' && to.edge === 'right') return true;
        } else if (x == x_ && y > y_) {
            if (from.edge === 'bottom' && to.edge === 'top') return true;
            else if (from.edge === 'left' && to.edge === 'left') return true;
            else if (from.edge === 'right' && to.edge === 'right') return true;
        } else if (y == y_ && x < x_ && from.edge === 'left' && to.edge === 'right') return true;
        else if (y == y_ && x > x_ && from.edge === 'right' && to.edge === 'left') return true;
        else if (x < x_ && y < y_) {
            if (from.edge === 'left' && to.edge === 'bottom') return true;
            else if (from.edge === 'top' && to.edge === 'right') return true;
        } else if (x < x_ && y > y_) {
            if (from.edge === 'left' && to.edge === 'top') return true;
            if (from.edge === 'bottom' && to.edge === 'right') return true;
        } else if (x > x_ && y < y_) {
            if (from.edge === 'right' && to.edge === 'bottom') return true;
            if (from.edge === 'top' && to.edge === 'left') return true;
        } else if (x > x_ && y > y_) {
            if (from.edge === 'right' && to.edge === 'top') return true;
            if (from.edge === 'bottom' && to.edge === 'left') return true;
        }

        return false;
    },

    updateAnchors: (connection: DiagramConnection) => {
        // update connection anchors so that the connection is valid

        const isValid = ConnectionService.isValidConnection(connection.from, connection.to);

        if (isValid) return;

        const fromComponentInstance = ComponentInstanceService.getById(connection.from.componentInstanceId);
        const toComponentInstance = ComponentInstanceService.getById(connection.to.componentInstanceId);

        if (!fromComponentInstance || !toComponentInstance) return;

        const fromAnchors = ComponentInstanceService.getAnchors(fromComponentInstance);
        const toAnchors = ComponentInstanceService.getAnchors(toComponentInstance);

        const options: {
            distance: number;
            from: any;
            to: any;
        }[] = [];

        const getPoint = (componentInstance: DiagramComponentInstance, anchor: DiagramConnectionAnchor) => {
            return {
                left: {
                    x: componentInstance.position.x,
                    y: componentInstance.position.y + anchor.offset,
                },
                right: {
                    x: componentInstance.position.x + componentInstance.colSpan,
                    y: componentInstance.position.y + anchor.offset,
                },
                top: {
                    x: componentInstance.position.x + anchor.offset,
                    y: componentInstance.position.y,
                },
                bottom: {
                    x: componentInstance.position.x + anchor.offset,
                    y: componentInstance.position.y + componentInstance.rowSpan,
                },
            }[anchor.edge];
        };

        // find matching anchors for which the connection is valid
        for (const fromAnchor of fromAnchors) {
            for (const toAnchor of toAnchors) {
                const isOriginalFromAnchor = ConnectionService.isEqual(fromAnchor, connection.from);
                const isOriginalToAnchor = ConnectionService.isEqual(toAnchor, connection.to);

                if (isOriginalFromAnchor && isOriginalToAnchor) continue;

                const fromAnchorIsValid = isOriginalFromAnchor || !ConnectionService.getConnectionForAnchor(fromAnchor);
                const toAnchorIsValid = isOriginalToAnchor || !ConnectionService.getConnectionForAnchor(toAnchor);
                const newConnectionIsValid = ConnectionService.isValidConnection(fromAnchor, toAnchor);

                if (fromAnchorIsValid && toAnchorIsValid && newConnectionIsValid) {
                    const from = getPoint(fromComponentInstance, fromAnchor);
                    const to = getPoint(toComponentInstance, toAnchor);

                    const deltaX = to.x - from.x;
                    const deltaY = to.y - from.y;
                    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                    options.push({
                        distance,
                        from: fromAnchor,
                        to: toAnchor,
                    });
                }
            }
        }

        if (options.length) {
            let shortest = options[0];

            options.forEach((option) => {
                if (option.distance < shortest.distance) {
                    shortest = option;
                }
            });

            ConnectionService.moveConnectionAnchor(connection, connection.from, {
                ...shortest.from,
                port: connection.from.port,
            });
            ConnectionService.moveConnectionAnchor(connection, connection.to, {
                ...shortest.to,
                port: connection.to.port,
            });
        }
    },

    getConnectionForAnchor: (anchor: DiagramConnectionAnchorWithoutPort): DiagramConnection | undefined => {
        const { connections } = DiagramService.get();

        return Object.values(connections).find(
            (connection) =>
                ConnectionService.isEqual(connection.from, anchor) || ConnectionService.isEqual(connection.to, anchor),
        );
    },

    isEqual: (a: DiagramConnectionAnchorWithoutPort | null, b: DiagramConnectionAnchorWithoutPort | null): boolean => {
        if (a === null && b === null) return true;
        else if (a === null || b === null) return false;

        return a.componentInstanceId === b.componentInstanceId && a.offset === b.offset && a.edge === b.edge;
    },

    getConnectionsCrossingPosition: (position: DiagramPosition): DiagramConnection[] => {
        const { connections } = DiagramService.get();

        return Object.values(connections).filter((connection) => ConnectionService.crosses(connection, position));
    },

    /**
     * Return true when a `connection` crosses a `position`
     *
     * @param connection
     * @param position
     * @returns true when a connection crosses a position
     */
    crosses: (connection: DiagramConnection, position: DiagramPosition): boolean => {
        const points = ConnectionService.getPathPoints(connection.from, connection.to) || [];

        const X = position.x;
        const Y = position.y;

        for (let i = 1; i < points.length; i++) {
            const p1 = points[i - 1];
            const p2 = points[i];

            const x1 = Math.floor(p1.x / CELL_WIDTH);
            const y1 = Math.floor(p1.y / CELL_HEIGHT);
            const x2 = Math.floor(p2.x / CELL_WIDTH);
            const y2 = Math.floor(p2.y / CELL_HEIGHT);

            if (x1 === x2 && x1 === X && Y >= Math.min(y1, y2) && Y <= Math.max(y1, y2)) return true;
            if (y1 === y2 && y1 === Y && X >= Math.min(x1, x2) && X <= Math.max(x1, x2)) return true;
        }

        return false;
    },

    addConnectionWithEffects: ({
        from,
        to,
        suggestedVoltagTypeOverride,
        connectionProps = {},
    }: {
        from: DiagramConnectionAnchor;
        to: DiagramConnectionAnchor;
        suggestedVoltagTypeOverride?: VoltageType | null;
        connectionProps?: Partial<Omit<DiagramConnection, 'id' | 'to' | 'from'>>;
    }): string => {
        if (ConnectionService.isEqual(from, to)) {
            throw new Error('Cannot create a connection between the same anchors');
        }

        const fromComponentInstance = ComponentInstanceService.getById(from.componentInstanceId);
        const toComponentInstance = ComponentInstanceService.getById(to.componentInstanceId);

        const diagram = DiagramService.get();

        const prepareComponentInstanceForConnecting = (componentInstance: DiagramComponentInstance) => {
            const numberOfPorts = componentInstance.specifications.electrical.ports.length;

            const numberOfConnections = DiagramService.getNumberOfConnections(componentInstance);

            if (numberOfConnections >= numberOfPorts) {
                ComponentInstanceService.addDynamicPort(componentInstance);
            }

            if (DiagramService.hasMaximumNumberOfConnections(componentInstance)) {
                const connections = ConnectionService.getConnectionsForComponentInstance(diagram, componentInstance);

                ConnectionService.delete(connections[connections.length - 1].id);
            }
        };

        prepareComponentInstanceForConnecting(fromComponentInstance);
        prepareComponentInstanceForConnecting(toComponentInstance);

        const matchPortsResult = PortService.findMatchingPorts({
            fromComponentInstance,
            toComponentInstance,
        });

        const { fromPort, toPort } = matchPortsResult;
        const suggestedVoltageType = suggestedVoltagTypeOverride ?? matchPortsResult.suggestedVoltageType;

        const connectionId = ConnectionService.addConnection({
            from,
            to,
            ...connectionProps,
        });

        DiagramSyncService.updateByKey(`connections.${connectionId}.from.port`, fromPort?.port.key ?? null, true);
        DiagramSyncService.updateByKey(`connections.${connectionId}.to.port`, toPort?.port.key ?? null, true);

        if (fromPort && isNumber(fromPort?.port.key) && toPort && isNumber(toPort?.port.key)) {
            const suggestedPowerFlowDirections = {
                from: PortService.getSuggestedPowerFlowDirections(fromComponentInstance, fromPort.port.key),
                to: PortService.getSuggestedPowerFlowDirections(toComponentInstance, toPort.port.key),
            };

            const bestPowerFlowDirection: {
                from: PowerFlowDirection | null;
                to: PowerFlowDirection | null;
            } = {
                from: null,
                to: null,
            };

            for (const suggestedFromPowerFlow of suggestedPowerFlowDirections.from) {
                if (suggestedFromPowerFlow === 'input' && suggestedPowerFlowDirections.to.includes('output')) {
                    bestPowerFlowDirection.from = 'input';
                    bestPowerFlowDirection.to = 'output';

                    break;
                }

                if (suggestedFromPowerFlow === 'output' && suggestedPowerFlowDirections.to.includes('input')) {
                    bestPowerFlowDirection.from = 'output';
                    bestPowerFlowDirection.to = 'input';

                    break;
                }

                if (
                    suggestedFromPowerFlow === 'bidirectional' &&
                    suggestedPowerFlowDirections.to.includes('bidirectional')
                ) {
                    bestPowerFlowDirection.from = 'bidirectional';
                    bestPowerFlowDirection.to = 'bidirectional';

                    break;
                }
            }

            if (bestPowerFlowDirection.from && bestPowerFlowDirection.to) {
                PortService.updatePowerFlowDirection(
                    fromComponentInstance,
                    fromPort.port.key,
                    bestPowerFlowDirection.from,
                );
                PortService.updatePowerFlowDirection(toComponentInstance, toPort.port.key, bestPowerFlowDirection.to);
            }
        }

        if (suggestedVoltageType && fromPort && toPort) {
            PortService.updateVoltageType(fromComponentInstance.id, fromPort.port.key, suggestedVoltageType, false);
            PortService.updateVoltageType(toComponentInstance.id, toPort.port.key, suggestedVoltageType, false);

            const fromPortSpecifications =
                // @ts-ignore
                fromComponentInstance.specifications.electrical.ports[fromPort.port.key][suggestedVoltageType];
            const toPortSpecifications =
                // @ts-ignore
                toComponentInstance.specifications.electrical.ports[toPort.port.key][suggestedVoltageType];

            const fromVoltage = fromPortSpecifications.voltage;
            const toVoltage = toPortSpecifications.voltage;

            if (fromVoltage.nom && toVoltage.nom && fromVoltage.nom === toVoltage.nom) {
                DiagramSyncService.updateByKey(`connections.${connectionId}.voltage.nom`, fromVoltage.nom, true);
            }

            if (fromVoltage.max && toVoltage.max) {
                const max = Math.max(fromVoltage.max, toVoltage.max);

                DiagramSyncService.updateByKey(`connections.${connectionId}.voltage.max`, max, true);
            }

            const fromCurrent = fromPortSpecifications.current;
            const toCurrent = toPortSpecifications.current;

            if (fromCurrent.max && toCurrent.max) {
                const max = Math.min(fromCurrent.max, toCurrent.max);
                DiagramSyncService.updateByKey(`connections.${connectionId}.current.max`, max, true);
            }

            if (fromCurrent.nom && toCurrent.nom) {
                const min = Math.min(fromCurrent.nom, toCurrent.nom);
                DiagramSyncService.updateByKey(`connections.${connectionId}.current.nom`, min, true);
            }
        }

        if (fromPort && isNumber(fromPort.port.key) && toPort && isNumber(toPort.port.key) && suggestedVoltageType) {
            const specifications = {
                // @ts-ignore suggestedVoltageType exists
                from: fromComponentInstance.specifications.electrical.ports[fromPort.port.key][suggestedVoltageType],
                // @ts-ignore suggestedVoltageType exists
                to: toComponentInstance.specifications.electrical.ports[toPort.port.key][suggestedVoltageType],
            };

            const syncSpecifications = (
                measurement: 'current' | 'power' | 'voltage',
                setPortValue:
                    | typeof PortService.updateCurrent
                    | typeof PortService.updatePower
                    | typeof PortService.updateVoltage,
                setConnectionValue:
                    | typeof ConnectionService.updateVoltage
                    | typeof ConnectionService.updateCurrent
                    | null = null,
            ) => {
                if (!(measurement in specifications.from) || !(measurement in specifications.to)) {
                    return;
                }

                const hasFromMeasurement = FormatHelpers.isEmpty(specifications.from[measurement]);
                const hasToMeasurement = FormatHelpers.isEmpty(specifications.to[measurement]);

                if (hasFromMeasurement && !hasToMeasurement && !toComponentInstance.componentId) {
                    const value =
                        // @ts-ignore suggestedVoltageType exists
                        fromComponentInstance.specifications.electrical.ports[fromPort.port.key!][suggestedVoltageType][
                            measurement
                        ];

                    setPortValue(toComponentInstance, toPort.port.key, suggestedVoltageType, value);

                    if (setConnectionValue) {
                        setConnectionValue(connectionId, value);
                    }
                }

                if (hasToMeasurement && !hasFromMeasurement && !fromComponentInstance.componentId) {
                    const value =
                        // @ts-ignore suggestedVoltageType exists
                        toComponentInstance.specifications.electrical.ports[toPort.port.key][suggestedVoltageType][
                            measurement
                        ];

                    setPortValue(fromComponentInstance, fromPort.port.key, suggestedVoltageType, value);

                    if (setConnectionValue) {
                        setConnectionValue(connectionId, value);
                    }
                }
            };

            syncSpecifications('current', PortService.updateCurrent, ConnectionService.updateCurrent);
            syncSpecifications('power', PortService.updatePower);
            syncSpecifications('voltage', PortService.updateVoltage, ConnectionService.updateVoltage);
        }

        return connectionId;
    },

    getVoltageType: (connection?: DiagramConnection | null) => {
        if (!connection || !connection.from || !connection.to) {
            return null;
        }

        const { from, to } = connection;
        const fromComponentInstance = ComponentInstanceService.getById(from.componentInstanceId)!;
        const toComponentInstance = ComponentInstanceService.getById(to.componentInstanceId)!;

        return ConnectionHelpers.getVoltageType(connection, fromComponentInstance, toComponentInstance);
    },

    getSuggestedAndForbiddenAnchors: (
        from: DiagramConnectionAnchor,
    ): { suggested: DiagramConnectionAnchor[]; forbidden: DiagramConnectionAnchor[] } => {
        const { componentInstanceId, edge, offset } = from;

        const forbidden: DiagramConnectionAnchor[] = [];
        const suggested: DiagramConnectionAnchor[] = [];

        const { position } = diagram.componentInstances[componentInstanceId];

        const tb = edge === 'top' || edge === 'bottom';
        const X = position.x + (tb ? offset : 0);
        const Y = position.y + (tb ? 0 : offset);

        // For all 10 cells in close vicinity, check if a connection can be made
        const componentInstanceIds = [componentInstanceId];
        for (let x = X - 10; x <= X + 10; x++) {
            for (let y = Y - 10; y <= Y + 10; y++) {
                const componentInstanceId = diagramHelpers.grid[y]?.[x];

                if (!componentInstanceId) continue;

                if (componentInstanceIds.includes(componentInstanceId)) continue;
                componentInstanceIds.push(componentInstanceId);

                const componentInstance = diagram.componentInstances[componentInstanceId];
                const anchors = ComponentInstanceService.getAnchors(componentInstance);

                for (const anchor of anchors) {
                    const isValid = ConnectionService.isValidConnection(from, anchor);

                    if (isValid) {
                        suggested.push(anchor);
                    } else {
                        forbidden.push(anchor);
                    }
                }
            }
        }

        return {
            suggested,
            forbidden,
        };
    },
};

export { ConnectionService };
