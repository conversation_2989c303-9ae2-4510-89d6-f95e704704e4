import React from 'react';

import { Flex, FlexProps, Group, Stack, Text } from '@mantine/core';
import { Page } from './Page';

import classes from './Content.module.css';
import { Metadata } from 'components/page/Metadata';

interface Props {
    title?: string | React.ReactNode;
    subtitle?: string | React.ReactNode;
    afterTitle?: React.ReactNode;
    rightSection?: React.ReactNode;
    width?: number;
    align?: 'center' | 'start';
    children?: React.ReactNode;
}

export const CONTENT_WIDTH = 1200;
export const WIDE_CONTENT_WIDTH = 1600;

export const Content = ({
    title,
    subtitle,
    afterTitle,
    rightSection,
    width,
    align = 'center',
    children,
    ...props
}: Props & FlexProps) => {
    return (
        <Flex
            className={classes.wrapper}
            direction="column"
            gap="xl"
            maw={width ?? CONTENT_WIDTH}
            mx={align === 'center' ? 'auto' : 0}
            {...props}
        >
            {typeof title === 'string' && <Metadata title={title} />}

            {(title || rightSection) && (
                <Flex w="100%" justify="space-between" align="start" gap="xs">
                    {title && (
                        <Stack gap="xs" style={{ flex: 1 }}>
                            <Group align="center" gap="xs">
                                {typeof title === 'string' ? <Page.Title>{title}</Page.Title> : title}
                                {afterTitle}
                            </Group>
                            {subtitle && (
                                <Text fw={500} c="dimmed">
                                    {subtitle}
                                </Text>
                            )}
                        </Stack>
                    )}
                    {rightSection}
                </Flex>
            )}

            {children}
        </Flex>
    );
};

export const WideContent = (props: Props & FlexProps) => {
    return <Content width={WIDE_CONTENT_WIDTH} {...props} />;
};
