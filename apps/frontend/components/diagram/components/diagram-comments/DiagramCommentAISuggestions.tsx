import React, { FC, useState } from 'react';

import { Menu, UnstyledButton } from '@mantine/core';
import { AISuggestion, DiagramComponentInstance, FeatureKey, all } from '@repo/dcide-component-models';
import { useAISuggestions } from 'components/diagram/hooks/use-ai-suggestions';

import { BsRobot } from 'react-icons/bs';
import cx from './DiagramComment.module.css';
import { openContextModal } from '@mantine/modals';
import { useFeatureAccess } from 'hooks/use-feature-access';

type DiagramCommentEditorProps = {
    editor: any;
    linkedComponentInstance?: DiagramComponentInstance;
};

const DiagramCommentAISuggestions: FC<DiagramCommentEditorProps> = ({ editor, linkedComponentInstance }) => {
    const hasAiAccess = useFeatureAccess(FeatureKey.ARTIFICIAL_INTELLIGENCE);
    const aiSuggestions = useAISuggestions();
    const [opened, setOpened] = useState(false);

    const onClick = () => {
        if (!hasAiAccess) {
            openContextModal({
                modal: 'aiAccess',
                innerProps: {},
            });
            return;
        }
        setOpened((o) => !o);
    };
    const onAiSuggestion = (value: string) => {
        if (value) {
            editor
                .chain()
                .clearContent()
                .insertContent([
                    { type: 'mention', attrs: { type: 'ai', id: 'ai' } },
                    { type: 'text', text: ' ' + value },
                ])
                .focus()
                .run();
        } else {
            editor
                .chain()
                .insertContent({
                    type: 'mention',
                    attrs: { type: 'ai', id: 'ai' },
                })
                .focus()
                .run();
        }
    };

    const componentType = linkedComponentInstance?.componentType;
    const componentName = componentType ? all[componentType]?.plural : 'component';

    return (
        <Menu
            width={360}
            transitionProps={{ transition: 'pop-bottom-right' }}
            position="bottom-start"
            withinPortal
            opened={opened}
            onChange={onClick}
        >
            <Menu.Target>
                <UnstyledButton className={cx.messageComposerAction}>
                    <BsRobot />
                </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown>
                <Menu.Label>
                    {componentType
                        ? `Ask AI - Suggestions for ${componentName}:`
                        : 'Move comment closer to a component to get AI suggestions'}
                </Menu.Label>

                {componentType && (
                    <>
                        <Menu.Item
                            onClick={() => {
                                onAiSuggestion('');
                            }}
                        >
                            Ask your own question
                        </Menu.Item>
                        {aiSuggestions
                            .filter((aiSuggestions) => aiSuggestions.types.includes(componentType))
                            .map((aiSuggestion: AISuggestion) => (
                                <Menu.Item
                                    onClick={() => {
                                        onAiSuggestion(aiSuggestion.suggestion);
                                    }}
                                    key={aiSuggestion.suggestion}
                                >
                                    {aiSuggestion.suggestion}
                                </Menu.Item>
                            ))}
                    </>
                )}
            </Menu.Dropdown>
        </Menu>
    );
};

export { DiagramCommentAISuggestions };
