{% set from = from ?? ('now' | date_modify('-30 days') | date('Y-m-d')) %}
{% set to = (to ?? ('now' | date('Y-m-d'))) | date('Y-m-d') %}

{% set fromAtom = from | atom %}
{% set toAtom = to | date_modify("+1 day") | atom %}

{% set query = query ?? '' %}
{% set orderStatus = orderStatus ?? '' %}
{% set orderMethod = orderMethod ?? '' %}

{% set viewy = viewy ?? 'details' %}

{% set allOrders = craft
    .entries
    .section('order')
    .dateCreated(['and', ">= #{fromAtom}", "<= #{toAtom}"])
    .orderStatus(orderStatus)
    .orderMethod(orderMethod)
    .orderEmail(query)
    .orderBy('dateCreated desc')
    .limit(9999)
%}

{% set orderQuery = craft
    .entries
    .section('order')
    .dateCreated(['and', ">= #{fromAtom}", "<= #{toAtom}"])
    .orderStatus(orderStatus)
    .orderMethod(orderMethod)
    .orderEmail(query)
    .orderBy('dateCreated desc')
    .limit(20)
%}

{% set page = page ?? 1 %}
{% set pageInfo = sprig.paginate(orderQuery, page) %}

{% set orders = pageInfo.pageResults %}

{% set units = {
    none: { singular: '', plural: '' },
    piece: { singular: 'stuk', plural: 'stuks' },
    gram: { singular: 'g', plural: 'g' },
    kilogram: { singular: 'kg', plural: 'kg' },
    millilitre: { singular: 'ml', plural: 'ml' },
    centilitre: { singular: 'cl', plural: 'cl' },
    litre: { singular: 'l', plural: 'l' },
    person: { singular: 'persoon', plural: 'personen' },
} %}

<div class="container is-horizontal is-vertical">
    <div class="order-overview">
        <div class="block print-hidden">
            <div class="order-overview-filters">
                <div class="form-item">
                    <label class="form-label font-base--semi-bold">E-mailadres</label>
                    <input class="text-field" type="text" name="query" value="{{ query }}" data-sprig>
                </div>
                <div class="form-item">
                    <label class="form-label font-base--semi-bold">Van</label>
                    <input class="text-field" type="date" name="from" value="{{ from }}" data-sprig>
                </div>
                <div class="form-item">
                    <label class="form-label font-base--semi-bold">Tot</label>
                    <input class="text-field" type="date" name="to" value="{{ to }}" data-sprig>
                </div>
                <div class="form-item">
                    <label class="form-label font-base--semi-bold">Status</label>
                    <select class="text-field" name="orderStatus" data-sprig>
                        <option value="" {{ orderStatus == '' ? 'selected' }}>All</option>
                        <option value="new" {{ orderStatus == 'new' ? 'selected' }}>New</option>
                        <option value="processing" {{ orderStatus == 'processing' ? 'selected' }}>Processing</option>
                        <option value="finished" {{ orderStatus == 'finished' ? 'selected' }}>Finished</option>
                    </select>
                </div>
                <div class="form-item">
                    <label class="form-label font-base--semi-bold">Bestelwijze</label>
                    <select class="text-field" name="orderMethod" data-sprig>
                        <option value="" {{ orderMethod == '' ? 'selected' }}>All</option>
                        <option value="pickup" {{ orderMethod == 'pickup' ? 'selected' }}>Pickup</option>
                        <option value="delivery" {{ orderMethod == 'delivery' ? 'selected' }}>Delivery</option>
                    </select>
                </div>
                <div class="form-item">
                    <label class="form-label font-base--semi-bold">Weergave</label>
                    <select class="text-field" name="viewy" data-sprig>
                        <option value="overview" {{ viewy == 'overview' ? 'selected' }}>Overzicht</option>
                        <option value="details" {{ viewy == 'details' ? 'selected' }}>Details</option>
                        <option value="table" {{ viewy == 'table' ? 'selected' }}>Table</option>
                        <option value="export" {{ viewy == 'export' ? 'selected' }}>Export</option>
                    </select>
                </div>
                <div>
                    {{ allOrders | length }} {{ orders | length == 1 ? 'bestelling' : 'bestellingen' }}
                </div>
            </div>
        </div>
        {% if viewy == 'overview' %}
            {% set orderOverview = getOrderOverview(allOrders) %}
            <div class="block is-large">
                <table class="order-overview-overview-table">
                    <thead>
                        <tr>
                            <td></td>
                            <td colspan="2" style="text-align: center;">Besteld door klant</td>
                            <td colspan="2" style="text-align: center;">Uitgerekend intern</td>
                        </tr>
                        <tr>
                            <td></td>
                            <td><strong>Hoeveelheid</strong></td>
                            <td><strong>Aantal</strong></td>
                            <td><strong>Aantal stuks</strong></td>
                            {% if orderOverview.hasInternalAmounts %}
                                <td><strong>Aantal in kg</strong></td>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for line in orderOverview.items %}
                            <tr>
                                <td>
                                    {{ line.product }}{{ line.variant ? ': ' }}{{ line.variant }}
                                    {% if line.remarks %}
                                        <br />
                                        <small>
                                            {{ line.remarks }}
                                        </small>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ line.amount }}
                                    {{ line.amount == 1 ? units[line.unit.value].singular : units[line.unit.value].plural }}
                                </td>
                                <td>
                                    {{ line.quantity }}
                                </td>
                                <td style="white-space: nowrap">{{ line.total }} {{ line.total == 1 ? units[line.unit.value].singular : units[line.unit.value].plural }}</td>
                                {% if orderOverview.hasInternalAmounts %}
                                    {% if line.internalUnit.value %}
                                        <td style="white-space: nowrap">
                                            {% if line.internalTotal %}
                                                {{ line.internalTotal }}
                                            {% else %}
                                                -
                                            {% endif %}
                                            {{ line.internalTotal == 1 ? units[line.internalUnit.value].singular : units[line.internalUnit.value].plural }}
                                        </td>
                                    {% else %}
                                        <td></td>
                                    {% endif %}
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% endif %}

        {% if viewy == 'details' %}
            {% for order in orders %}
                <div class="block print-avoid-break-inside print-break-after">
                    {% include 'teasers/order-teaser' with {
                        order: order,
                    } %}
                </div>
            {% endfor %}
            <div class="block">
                currentpage: {{ page }}
                {% for i in pageInfo.getDynamicRange(7) %}
                    <button data-sprig data-sprig-vals='{"page": "{{ i }}"}'>
                        {{ i }}
                    </button>
                {% endfor %}
            </div>
        {% endif %}

        {% if viewy == 'table' %}

            <div class="block is-full-width">

                <table class="order-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Datum</th>
                            <th>Naam</th>
                            <th>E-mail</th>
                            <th>Telefoon</th>
                            <th>Bestelwijze</th>
                            <th>Betalingsmethode</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in allOrders %}
                            <tr>
                                <td>{{ order.id }}</td>
                                <td>{{ order.orderDate | date('j F Y') }}</td>
                                <td>{{ order.orderFirstName }} {{ order.orderLastName }}</td>
                                <td>{{ order.orderEmail }}</td>
                                <td>{{ order.orderPhone }}</td>
                                <td>{{ order.orderMethod }}</td>
                                <td>{{ order.orderPaymentMethod }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>

            </div>

        {% endif %}

        {% if viewy == 'export' %}
            <div class="block">

<textarea class="textarea-field" id="textarea-export">
{% for item in getOrderExport(allOrders) %}
{{ item.id }};{{ item.name }};{{ item.email }};"{{ item.phone }}";{{ item.method }};{{ item.date }};
{% endfor %}
</textarea>
                <script>
                    function downloadCSVFromTextarea() {
                        const textarea = document.getElementById('textarea-export');
                        const csvContent = "data:text/csv;charset=utf-8," + textarea.value;

                        // Create a hidden download link
                        const encodedUri = encodeURI(csvContent);
                        const link = document.createElement("a");
                        link.setAttribute("href", encodedUri);
                        link.setAttribute("download", 'export.csv');
                        document.body.appendChild(link);

                        // Programmatically click the link to trigger the download
                        link.click();
                        document.body.removeChild(link);
                    }
                    downloadCSVFromTextarea();
                </script>
            </div>
        {% endif %}
    </div>
</div>
