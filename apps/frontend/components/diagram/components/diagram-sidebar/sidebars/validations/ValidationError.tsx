import React, { useState, FC } from 'react';

import {
    type ComponentInstanceValidationError,
    type ZoneValidationError,
    ComponentInstanceValidationErrorType,
    ZoneValidationErrorType,
} from '@repo/dcide-component-models';

import { Box, Button, Stack, Transition } from '@mantine/core';

import { IoEyeOutline, IoHelpCircleOutline, IoColorWandOutline } from 'react-icons/io5';
import { TbSparkles } from 'react-icons/tb';

import { Configuration } from './quickfixes/component-instances/Configuration';
import { PortCurrent } from './quickfixes/component-instances/PortCurrent';
import { PortPower } from './quickfixes/component-instances/PortPower';
import { PortVoltage } from './quickfixes/component-instances/PortVoltage';

import { EditControlComponents } from './quickfixes/zones/EditControlComponents';
import { DeleteMultipleGroundings } from './quickfixes/zones/DeleteMultipleGroundings';

import { AIConversationService } from 'components/diagram/services/AIConversationService';

import cx from './Validations.module.css';

const ValidationError = <T extends ComponentInstanceValidationError | ZoneValidationError>({
    title,
    description,
    highlight,
    error,
    resolved,
}: {
    title: string;
    description: string;
    highlight?: () => void;
    error: T;
    resolved?: boolean;
    features?: {
        simulations: boolean;
        wireSizing: boolean;
    };
}) => {
    const [panel, setPanel] = useState<'info' | 'quickfix' | null>(null);

    const QuickfixComponent = quickfixes[error.type] as React.FC<{ error: T }> | undefined;
    const InfoComponent = null as any;

    const toggleInfo = () => {
        setPanel(panel === 'info' ? null : 'info');
    };

    const toggleQuickfix = () => {
        setPanel(panel === 'quickfix' ? null : 'quickfix');
    };

    const askAI = () => {
        AIConversationService.setPrefilledQuestion(`
            Can you help me fix this error?

            ${title}:
            ${description}
        `);
        AIConversationService.openSidebar();
    };

    return (
        <Transition mounted={!resolved} transition="fade-right" keepMounted>
            {(styles) => (
                <Stack style={styles} gap={0}>
                    <Box className={cx.error} data-panel={!!panel}>
                        <Box className={cx.errorContent}>
                            <Box className={cx.errorTitle}>{title}</Box>
                            <Box className={cx.errorDescription}>{description}</Box>
                        </Box>
                        <Box className={cx.errorActions}>
                            <Button.Group>
                                {highlight && (
                                    <Button size="xs" variant="default" onClick={highlight}>
                                        <IoEyeOutline />
                                        Highlight
                                    </Button>
                                )}
                                {InfoComponent && (
                                    <Button size="xs" variant="default" onClick={toggleInfo}>
                                        <IoHelpCircleOutline />
                                        Info
                                    </Button>
                                )}
                                {QuickfixComponent && (
                                    <Button size="xs" variant="default" onClick={toggleQuickfix}>
                                        <IoColorWandOutline />
                                        Quick fix
                                    </Button>
                                )}
                                <Button size="xs" variant="default" onClick={askAI}>
                                    <TbSparkles size={14} />
                                    Ask AI
                                </Button>
                            </Button.Group>
                        </Box>
                    </Box>
                    {QuickfixComponent && (
                        <Transition
                            mounted={panel === 'quickfix'}
                            transition="scale-y"
                            duration={400}
                            timingFunction="ease"
                        >
                            {(styles) => (
                                <Box style={styles} className={cx.panel}>
                                    <QuickfixComponent error={error} />
                                </Box>
                            )}
                        </Transition>
                    )}
                    {InfoComponent && (
                        <Transition
                            mounted={panel === 'info'}
                            transition="scale-y"
                            duration={400}
                            timingFunction="ease"
                        >
                            {(styles) => (
                                <Box style={styles} className={cx.panel}>
                                    {InfoComponent}
                                </Box>
                            )}
                        </Transition>
                    )}
                </Stack>
            )}
        </Transition>
    );
};

type QuickfixComponents = {
    [key in ComponentInstanceValidationErrorType]: FC<{ error: ComponentInstanceValidationError }>;
} & {
    [key in ZoneValidationErrorType]: FC<{ error: ZoneValidationError }>;
};

const quickfixes: Partial<QuickfixComponents> = {
    [ComponentInstanceValidationErrorType.CONTROL_METHODS_MISSING]: Configuration,
    [ComponentInstanceValidationErrorType.CONTROL_METHODS_TOO_MANY]: Configuration,
    [ComponentInstanceValidationErrorType.REQUIRED_CONFIGURATION_BATTERY_VOLTAGE]: PortVoltage,
    [ComponentInstanceValidationErrorType.REQUIRED_CONFIGURATION_GENERATOR_VOLTAGE]: PortVoltage,
    [ComponentInstanceValidationErrorType.REQUIRED_CONFIGURATION_UTILITY_VOLTAGE]: PortVoltage,
    [ComponentInstanceValidationErrorType.CORE_SPECIFICATIONS_CURRENT]: PortCurrent,
    [ComponentInstanceValidationErrorType.CORE_SPECIFICATIONS_POWER]: PortPower,
    [ComponentInstanceValidationErrorType.CORE_SPECIFICATIONS_VOLTAGE]: PortVoltage,

    [ZoneValidationErrorType.CONTROL_METHODS_NO_CONTROL_COMPONENT]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_NO_CONTROL_COMPONENT_M]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_NO_CONTROL_COMPONENT_Lp]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_NO_CONTROL_COMPONENT_Ln]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_Lp_M]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_M_Ln]: EditControlComponents,
    [ZoneValidationErrorType.CONTROL_METHODS_MULTIPLE_VOLTAGE_CONTROL_COMPONENTS_Lp_Ln]: EditControlComponents,

    [ZoneValidationErrorType.GROUNDING_MULTIPLE]: DeleteMultipleGroundings,
    // Other entries remain undefined or null
};

export { ValidationError };
