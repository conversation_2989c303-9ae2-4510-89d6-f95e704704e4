import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

import { PayloadService } from '@/services/PayloadService';
import { getId } from '@/helpers';
import { pick } from 'radash';

const name = 'powerFlowSimulations';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: `
        Retrieves information about the latest worst case load simulation for this diagram.
        Possible responses are:
        * The latest worst case load simulation with a status and results
        * A message indicating that no simulations have been run for this diagram
    `,
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {
    newPowerFlowSimulation: {
        name: 'newPowerFlowSimulation',
        action: () => `
            === ACTION ===
            {
                "action": "newPowerFlowSimulation"
            }
            === END ACTION ===
        `,
    },
    showPowerFlowSimulation: {
        name: 'showPowerFlowSimulation',
        action: (simulationId: string) => `
            === ACTION ===
            {
                "action": "showPowerFlowSimulation",
                "simulationId": "${simulationId}"
            }
            === END ACTION ===
        `,
    },
    requestPowerFlowSimulation: {
        name: 'requestPowerFlowSimulation',
        action: () => `
            === ACTION ===
            {
                "action": "requestPowerFlowSimulation"
            }
            === END ACTION ===
        `,
    },
} as const;

const handler = async (message: DiagramAIMessage) => {
    const payload = await PayloadService.getPayload();
    const {
        docs: [simulation],
    } = await payload.find({
        collection: 'simulations',
        where: {
            diagram: {
                equals: getId(message.diagram),
            },
        },
        depth: 0,
        limit: 1,
        sort: '-createdAt',
    });

    if (simulation) {
        return JSON.stringify(pick(simulation, ['id', 'name', 'status', 'error', 'result']));
    }

    return 'There are no simulations for this diagram.';
};

const powerFlowSimulations = {
    name,
    definition,
    actions,
    handler,
};

export { powerFlowSimulations };
