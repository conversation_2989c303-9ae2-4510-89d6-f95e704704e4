import { useEffect } from 'react';
import { useURLQueryParameter } from 'hooks/use-url-query-parameter';
import { SearchAgentService } from 'services/AISearchAgentService';

type Options = {
    key?: string;
    createIfNotFound?: boolean;
};

export const useSearchThread = (options: Options = {}) => {
    const { key = 'id', createIfNotFound = true } = options;

    const [searchThreadId, setSearchThreadId] = useURLQueryParameter(key);

    const create = async () => {
        const newId = await SearchAgentService.create();
        setSearchThreadId(newId);
    };

    const fetchSearchThreadId = async () => {
        if (createIfNotFound && searchThreadId === undefined) {
            await create();
        }
    };

    useEffect(() => {
        fetchSearchThreadId();
    }, [searchThreadId]);

    return {
        searchThreadId: searchThreadId as string | undefined,
        create,
    };
};
