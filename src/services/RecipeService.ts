import { PayloadService } from '@/services/PayloadService';

import { draw } from 'radash';

const RecipeService = {
    random: async (limit: number = 4) => {
        const payload = await PayloadService.get();
        const randoms: string[] = [];

        const { docs: all } = await payload.find({
            collection: 'recipes',
            select: {},
            pagination: false,
            depth: 0,
        });

        while (randoms.length < limit) {
            const random = draw(all);

            if (random && !randoms.includes(random.id)) {
                randoms.push(random.id);
            }
        }

        const { docs: recipes } = await payload.find({
            collection: 'recipes',
            where: {
                id: {
                    in: randoms,
                },
            },
            limit,
            depth: 0,
        });

        console.log('randoms', randoms);
        console.log('all', recipes);

        return recipes;
    },

    get: async (slug: string) => {
        const payload = await PayloadService.get();

        const { docs: [recipe] } = await payload.find({
            collection: 'recipes',
            where: {
                slug: {
                    equals: slug,
                },
            },
            depth: 1,
            limit: 1,
        });

        return recipe;
    },

    search: async (query: string) => {
        const payload = await PayloadService.get();

        const { docs: recipes } = await payload.find({
            collection: 'recipes',
            where: {
                or: [
                    {
                        title: {
                            contains: query,
                        },
                    },
                    {
                        description: {
                            contains: query,
                        },
                    }
                ],
            },
            depth: 0,
            pagination: false,
        });

        return recipes;
    },
};

export { RecipeService };
