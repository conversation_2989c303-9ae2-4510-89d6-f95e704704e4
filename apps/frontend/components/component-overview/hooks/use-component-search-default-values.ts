import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

import { useRouter } from 'next/router';

import { SearchService } from '../services/SearchService';
import { ComponentHelpers } from 'helpers/ComponentHelpers';

const useComponentSearchDefaultValues = () => {
    const router = useRouter();
    const searchParams = useSearchParams();

    useEffect(() => {
        const queryAsString = searchParams?.get('query');

        if (queryAsString) {
            const query = JSON.parse(queryAsString);

            if (query) {
                SearchService.mergeFilters(query);
                router.replace(ComponentHelpers.urls.overview() + '#products', undefined, { shallow: true });
            }
        }
    }, [searchParams]);
};

export { useComponentSearchDefaultValues };
