import React, { FC } from 'react';

import { Alert, Box, Group, SimpleGrid, Space, Stack, Title } from '@mantine/core';

import { DiagramConnection, DiagramConnectionSchema } from '@repo/dcide-component-models';

import { DiagramSidebar } from 'components/diagram';
import { Form } from 'components/forms/Form';
import { VoltageField } from 'components/component-fields/VoltageField';
import { CurrentField } from 'components/component-fields/CurrentField';
import { ConductorMaterialField } from 'components/component-fields/ConductorMaterialField';
import { InstallationMethodField } from 'components/component-fields/InstallationMethodField';
import { ConductorTemperatureField } from 'components/component-fields/ConductorTemperatureField';
import { TemperatureField } from 'components/component-fields/TemperatureField';
import { ConnectionCoresField } from 'components/component-fields/ConnectionCores';
import { IntegerField } from 'components/forms/fields/IntegerField';
import { WireSizeField } from 'components/component-fields/WireSizeField';
import { LengthField } from 'components/component-fields/LengthField';
import { ResistanceField } from 'components/component-fields/ResistanceField';
import { VoltageTypeSwitch } from 'components/buttons/VoltageTypeSwitch';
import { LinesField } from 'components/component-fields/LinesFields';
import { AutoSave } from 'components/forms/AutoSave';
import { PowerFactorField } from 'components/component-fields/PowerFactorField';

import { ConnectionService } from 'components/diagram/services/ConnectionService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { useConnections, useDiagram } from 'components/diagram/hooks';
import { useSelectedConnections } from 'components/diagram/hooks/selection/use-selected-connections';

import { crush, get, set } from 'radash';

const EditConnections: FC = () => {
    useDiagram();

    const { selection } = useSelectedConnections();

    const connections = useConnections({
        ids: selection,
    });

    const bulkConnection = createBulkConnection(connections);

    const { availableVoltageTypes, voltageType } = getConnectionsVoltageType(connections);

    const setVoltageType = (voltageType: 'AC' | 'DC') => {
        connections.forEach((connection) => {
            ConnectionService.updateVoltageType(connection, voltageType);
        });

        DiagramSyncService.save();
    };

    const updateConnections = (values: Partial<DiagramConnection>) => {
        const changes: { [k: string]: any } = {};

        const crushedBulkConnection: { [k: string]: any } = crush(bulkConnection);
        const crushedValues: { [k: string]: any } = crush(values);

        Object.entries(crushedValues).forEach(([key, value]) => {
            if (value !== crushedBulkConnection[key]) {
                changes[key] = value;
            }
        });

        connections.forEach((connection) => {
            ConnectionService.update({
                id: connection.id,
                ...changes,
            });
        });

        DiagramSyncService.save();
    };

    return (
        <React.Fragment>
            <DiagramSidebar.Section>
                <Title>Edit {selection.length} connections</Title>
            </DiagramSidebar.Section>
            <DiagramSidebar.Section>
                <Alert title="Important Note" color="blue">
                    The prefilled values are currently the same for all selected connections.
                    <Space h="xs" />
                    <strong>Please note</strong>: Changing these values will apply the changes to all selected
                    connections and will override the existing settings for these fields.
                </Alert>
            </DiagramSidebar.Section>
            <DiagramSidebar.Section>
                <Form<DiagramConnection> defaultValues={bulkConnection} onSubmit={updateConnections}>
                    <AutoSave />
                    <Stack gap="xs">
                        <Group gap="xs">
                            {availableVoltageTypes.AC &&
                                availableVoltageTypes.DC === 0 &&
                                availableVoltageTypes.ACDC === 0 && (
                                    <VoltageTypeSwitch
                                        voltageTypes={['AC']}
                                        voltageType={voltageType}
                                        onChange={setVoltageType}
                                    />
                                )}
                            {availableVoltageTypes.AC === 0 &&
                                availableVoltageTypes.DC &&
                                availableVoltageTypes.ACDC === 0 && (
                                    <VoltageTypeSwitch
                                        voltageTypes={['DC']}
                                        voltageType={voltageType}
                                        onChange={setVoltageType}
                                    />
                                )}
                            {availableVoltageTypes.AC === 0 &&
                                availableVoltageTypes.DC === 0 &&
                                availableVoltageTypes.ACDC && (
                                    <VoltageTypeSwitch
                                        voltageTypes={['AC', 'DC']}
                                        voltageType={voltageType}
                                        onChange={setVoltageType}
                                    />
                                )}
                            <LinesField voltageType={voltageType} />
                        </Group>
                        <VoltageField name="voltage" label="Voltage" fields={['max']} size="sm" />
                        <CurrentField name="current" label="Current" fields={['max']} size="sm" />
                        <ConductorMaterialField name="material" label="Material" size="sm" />
                        <InstallationMethodField name="installationMethod" label="Installation Method" size="sm" />
                        <SimpleGrid cols={2} spacing="xs">
                            <ConductorTemperatureField
                                name="conductorTemperature"
                                label="Conductor Temperature"
                                size="sm"
                            />
                            <TemperatureField
                                name="ambientTemperature"
                                label="Ambient Temperature"
                                fields={['value']}
                                size="sm"
                            />
                            <ConnectionCoresField name="cores" label="Single-core/Multi-core" size="sm" />
                            {voltageType === 'DC' ? (
                                <PowerFactorField
                                    name="powerFactor"
                                    label="Power Factor"
                                    allowNegative={false}
                                    size="sm"
                                />
                            ) : (
                                <Box />
                            )}
                            <IntegerField
                                name="multiple.parallel"
                                label="Number of Cables"
                                allowNegative={false}
                                min={1}
                                size="sm"
                            />
                            <WireSizeField name="wireSize" label="Wire Size" size="sm" />
                            <LengthField
                                name="length"
                                label="Length"
                                size="sm"
                                decimalScales={{
                                    m: 2,
                                    km: 6,
                                    in: 2,
                                    ft: 2,
                                }}
                                hideIcons
                            />
                            <ResistanceField name="resistance" label="Resistance" size="sm" />
                        </SimpleGrid>
                    </Stack>
                </Form>
            </DiagramSidebar.Section>
        </React.Fragment>
    );
};

const getConnectionsVoltageType = (connections: DiagramConnection[]) => {
    const voltageTypes = {
        AC: 0,
        DC: 0,
    };

    const availableVoltageTypes = {
        AC: 0,
        DC: 0,
        ACDC: 0,
    };

    connections.forEach((connection) => {
        const voltageType = ConnectionService.getVoltageType(connection);

        if (voltageType) {
            voltageTypes[voltageType]++;
        }

        const availableVoltageTypesForConnection = ConnectionService.getAvailableVoltageTypes(connection);

        if (availableVoltageTypesForConnection.length === 2) {
            availableVoltageTypes.ACDC++;
        } else if (availableVoltageTypesForConnection.length === 1) {
            availableVoltageTypes[availableVoltageTypesForConnection[0]]++;
        }
    });

    let voltageType: 'AC' | 'DC' | null = null;

    if (connections.length === voltageTypes.AC) {
        voltageType = 'AC';
    }

    if (connections.length === voltageTypes.DC) {
        voltageType = 'DC';
    }

    return {
        availableVoltageTypes,
        voltageType,
    };
};

const createBulkConnection = (connections: DiagramConnection[]) => {
    const bulkConnection = DiagramConnectionSchema.parse({
        id: 'bulkConnection',
        from: {
            componentInstanceId: '',
            edge: 'right',
            offset: 0,
            port: null,
        },
        to: {
            componentInstanceId: '',
            edge: 'left',
            offset: 0,
            port: null,
        },
    });

    const properties = [
        'ambientTemperature.value',
        'conductorTemperature.value',
        'cores',
        'current.max',
        'current.nom',
        'installationMethod',
        'length.value',
        'lines.AC.L1',
        'lines.AC.L2',
        'lines.AC.L3',
        'lines.AC.N',
        'lines.AC.PE',
        'lines.DC.L+',
        'lines.DC.M',
        'lines.DC.L-',
        'lines.DC.PE',
        'material',
        'multiple.parallel',
        'powerFactor',
        'resistance.value',
        'voltage.max',
        'voltage.min',
        'voltage.nom',
    ];

    const source = connections[0];

    properties.forEach((property) => {
        let settable = true;

        connections.forEach((connection) => {
            if (get(connection, property) !== get(source, property)) {
                settable = false;
            }
        });

        if (settable) {
            set(bulkConnection, property, get(source, property));
        }
    });

    return bulkConnection;
};

export { EditConnections };
