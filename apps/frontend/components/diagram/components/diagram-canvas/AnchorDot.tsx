import { forwardRef } from 'react';

import { Box } from '@mantine/core';

import { VoltageType } from '@repo/dcide-component-models';

import { AnchorState } from './Anchor.type';

import cx from './Anchor.module.css';

type Props = {
    state: AnchorState;
    voltageType?: VoltageType | null;
    onPointerUp?: (event: React.PointerEvent<SVGCircleElement>) => void;
    onPointerDown?: () => void;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
    cx: number;
    cy: number;
    radius: number;
    highlighted?: boolean;
    connected?: boolean;
    suggested?: boolean;
    forbidden?: boolean;
};

export const AnchorDot = forwardRef<SVGCircleElement, Props>((props, ref) => {
    const { state, voltageType, radius, highlighted, connected, suggested, forbidden, ...otherProps } = props;

    return (
        <Box
            component="circle"
            r={radius}
            data-state={state}
            data-highlighted={highlighted}
            data-voltagetype={voltageType}
            data-connected={connected}
            data-suggested={suggested}
            data-forbidden={forbidden}
            {...otherProps}
            data-selection-block
            className={cx.anchor}
            ref={ref}
        />
    );
});

AnchorDot.displayName = 'AnchorDot';
