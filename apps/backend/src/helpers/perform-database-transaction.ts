import { Payload } from 'payload';
import { getPayload } from './get-payload';

const performDatabaseTransaction = async (callback: () => Promise<void>) => {
    const payload = await getPayload();

    const transactionId = await getTransactionId(payload);

    if (transactionId) {
        try {
            await callback();
            await payload.db.commitTransaction(transactionId);
        } catch (error) {
            console.error('Error performing database transaction, rolling back', error);
            payload.db.rollbackTransaction(transactionId);
        }
    } else {
        console.warn('Attempted to perform database transaction without transaction support');
        await callback();
    }
};

const getTransactionId = async (payload: Payload) => {
    if (
        typeof payload.db.beginTransaction === 'function' &&
        typeof payload.db.commitTransaction === 'function' &&
        typeof payload.db.rollbackTransaction === 'function'
    ) {
        return await payload.db.beginTransaction();
    }

    return null;
};

export { performDatabaseTransaction };
