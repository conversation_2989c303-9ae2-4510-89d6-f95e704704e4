import { Stack } from '@mantine/core';
import { HorizontalTabType } from 'components/horizontal-tabs';

import { ComponentLanding } from 'components/component-landing';

import { SearchTabName } from 'components/search/components/SearchTabName';

const DCMicrogridsLabel = () => {
    return <SearchTabName>DC Microgrids</SearchTabName>;
};

const DCMicroGrids = () => {
    return (
        <Stack gap="xl">
            <ComponentLanding.DCMicrogrid />
            <ComponentLanding.DCMicrogridProducts />
        </Stack>
    );
};

export const dcMicrogrids: HorizontalTabType = {
    value: 'dc-microgrids',
    label: <DCMicrogridsLabel />,
    content: <DCMicroGrids />,
};
