color: null
fieldLayouts:
  343ccd6e-b4c9-4c4d-aec5-b7b640d7bf7b:
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-03-22T10:10:30+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 68d9a91f-320b-4370-a22b-ee0fa6918bc8
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-03-22T10:11:49+00:00'
            elementCondition: null
            fieldUid: f335d354-3103-458d-a5f4-f54b334ae58a # Grid
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: ed149026-2426-4e15-be78-b8ab370b2def
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: 3c79e280-dead-414c-8814-233c8759ec68
        userCondition: null
handle: gridStyle
hasTitleField: true
icon: null
name: 'Grid Style'
showSlugField: false
showStatusField: false
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: none
