{"name": "@repo/frontend", "version": "0.3.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start", "lint": "next lint --fix", "cypress:open": "cypress open", "cypress:loadtest": "cypress run --spec cypress/e2e/load_test_local.cy.ts --record", "cypress:loadteststaging": "cypress run --spec cypress/e2e/load_test_staging.cy.ts --record", "cypress:loadtestproduction": "cypress run --spec cypress/e2e/load_test_production.cy.ts --record", "test:unit": "jest"}, "dependencies": {"@ably/spaces": "^0.4.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^7.0.0", "@emotion/css": "^11.13.5", "@emotion/server": "^11.11.0", "@fireworks-js/react": "^2.10.5", "@hookform/resolvers": "^3.3.4", "@mantine/carousel": "^7.17.0", "@mantine/charts": "^7.17.0", "@mantine/core": "^7.17.0", "@mantine/dates": "^7.17.0", "@mantine/dropzone": "^7.17.0", "@mantine/form": "^7.17.0", "@mantine/hooks": "^7.17.0", "@mantine/modals": "^7.17.0", "@mantine/next": "^6.0.21", "@mantine/notifications": "^7.17.0", "@mantine/nprogress": "^7.17.0", "@mantine/spotlight": "^7.17.0", "@mantine/tiptap": "^7.17.0", "@mapbox/search-js-react": "^1.0.0-beta.22", "@payloadcms/live-preview-react": "3.24.0", "@repo/dcide-component-models": "*", "@repo/dcide-sync-engine": "*", "@sentry/nextjs": "^9.5.0", "@svgdotjs/svg.js": "^3.2.0", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-mention": "^2.9.1", "@tiptap/extension-placeholder": "^2.9.1", "@tiptap/extension-task-item": "^2.9.1", "@tiptap/extension-task-list": "^2.9.1", "@tiptap/extension-text-align": "^2.9.1", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/suggestion": "^2.9.1", "@use-gesture/react": "^10.3.1", "@usersnap/browser": "^1.0.2", "@vercel/analytics": "^1.2.2", "@vercel/functions": "^1.5.2", "@vercel/speed-insights": "^1.0.7", "ably": "^2.5.0", "dayjs": "^1.11.13", "derive-valtio": "^0.1.0", "embla-carousel-auto-height": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "eslint-config-next": "15.1.7", "export-to-csv": "^1.2.2", "fflate": "^0.8.2", "fireworks-js": "^2.10.7", "framer-motion": "^11.2.3", "html2canvas": "^1.4.1", "iron-session": "^8.0.1", "js-cookie": "^3.0.5", "mongodb": "^6.11.0", "patch-package": "^8.0.0", "radash": "^12.1.0", "react": "19.0.0", "react-dom": "19.0.0", "react-dropzone": "^14.2.3", "react-dropzone-esm": "^15.0.1", "react-error-boundary": "^4.0.13", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.51.3", "react-hotkeys-hook": "^4.5.0", "react-icons": "^5.5.0", "react-international-phone": "^4.5.0", "react-markdown": "^9.0.0", "react-resizable": "^3.0.5", "react-simple-typewriter": "^5.0.1", "react-text-transition": "^3.1.0", "recharts": "^2.15.1", "remark-gfm": "^4.0.0", "socket.io-client": "^4.7.5", "sonner": "^1.5.0", "swr": "^2.2.5", "tiptap-unique-id": "^1.2.1", "valtio": "^2.1.3", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@types/cookie": "^0.6.0", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.12.7", "@types/react-gtm-module": "^2.0.2", "@types/react-resizable": "^3.0.4", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "cypress": "^13.5.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-chai-friendly": "^0.7.4", "eslint-plugin-cypress": "^2.15.2", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "jest": "^29.7.0", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.2.5", "sass": "^1.75.0", "smtp-tester": "^2.1.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2"}, "overrides": {"react-is": "19.0.0"}}