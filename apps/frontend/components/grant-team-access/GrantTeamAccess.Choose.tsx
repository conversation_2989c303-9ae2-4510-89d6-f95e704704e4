import React, { FC, useState } from 'react';

import { Box, Card, Divider, Text, Title, Stack } from '@mantine/core';

import { Team, User } from '@repo/dcide-component-models';

import { GrantTeamAccessInviteUser } from 'components/grant-team-access/GrantTeamAccess.InviteUser';
import { GrantTeamAccessDenyAccess } from 'components/grant-team-access/GrantTeamAccess.DenyAccess';

import cx from 'components/grant-access/GrantAccess.module.css';

enum GrantTeamAccessView {
    INIT = 'init',
    INVITE_USER = 'inviteUser',
    DENY_ACCESS = 'denyAccess',
}

const GrantTeamAccessChoose: FC<{
    team: Team;
    user: User;
}> = ({ team, user }) => {
    const [view, setView] = useState<GrantTeamAccessView>(GrantTeamAccessView.INIT);

    if (view === GrantTeamAccessView.INVITE_USER) {
        return <GrantTeamAccessInviteUser team={team} user={user} goBack={() => setView(GrantTeamAccessView.INIT)} />;
    }

    if (view === GrantTeamAccessView.DENY_ACCESS) {
        return <GrantTeamAccessDenyAccess team={team} user={user} goBack={() => setView(GrantTeamAccessView.INIT)} />;
    }

    return (
        <Stack>
            <Title size="h2">
                {user.name} ({user.email}) wants to join your team {team.name}.
            </Title>

            <Card className={cx.option} onClick={() => setView(GrantTeamAccessView.INVITE_USER)} withBorder>
                <Box>
                    <Title size="h4">Invite user to your team</Title>
                    <Text size="sm">
                        The user will be able to access team projects, designs, and collaborate with team members.
                    </Text>
                </Box>
            </Card>

            <Divider label="or" labelPosition="center" />

            <Card className={cx.option} onClick={() => setView(GrantTeamAccessView.DENY_ACCESS)} withBorder>
                <Box>
                    <Title size="h4">Deny access</Title>
                    <Text size="sm">The user will not be able to join your team.</Text>
                </Box>
            </Card>
        </Stack>
    );
};

export { GrantTeamAccessChoose };
