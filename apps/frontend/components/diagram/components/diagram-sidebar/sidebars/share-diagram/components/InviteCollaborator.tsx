import React from 'react';
import { useRouter } from 'next/router';

import { Flex } from '@mantine/core';

import { LocalNotificationService } from 'services/LocalNotificationService';
import { ProjectService } from 'services/ProjectService';
import { TrackingService } from 'services/TrackingService';

import { useCurrentProject } from 'hooks/use-current-project';

import { Form } from 'components/forms/Form';
import { TextField } from 'components/forms/fields/TextField';
import { SelectField } from 'components/forms/fields/SelectField';
import { FormSubmit } from 'components/forms/FormSubmit';
import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';
import { FeatureKey, useFeatureAccess } from 'hooks/use-feature-access';

import cx from './InviteCollaborator.module.css';
import { InternalTrackingService } from 'services/InternalTrackingService';

type FormValues = {
    collaborator: {
        email: string;
        permission: 'view' | 'edit';
    };
};

const InviteCollaborator = ({ email }: { email?: string }) => {
    const router = useRouter();
    const project = useCurrentProject();
    const featureEnabled = useFeatureAccess(FeatureKey.INVITE_EDITOR_COLLABORATOR);

    const sendShareInvite = async (values: FormValues, context: any) => {
        const { collaborator } = values;

        TrackingService.trackShareDiagram('invite_user');
        InternalTrackingService.track('project.inviteCollaborator', collaborator);

        try {
            await ProjectService.inviteCollaborator(
                project!.id,
                {
                    email: collaborator.email,
                    permissions:
                        collaborator.permission === 'edit' ? ['project.view', 'project.edit'] : ['project.view'],
                },
                router.asPath,
            );

            context.setValue('collaborator.email', '');

            LocalNotificationService.showSuccess({
                message: 'Invitation sent!',
            });
        } catch {
            LocalNotificationService.showError({
                message: 'Could not send invitation!',
            });
        }
    };

    return (
        <Form<FormValues>
            defaultValues={{
                collaborator: {
                    email,
                    permission: 'view',
                },
            }}
            onSubmit={sendShareInvite}
        >
            <Flex gap={4}>
                <Flex className={cx.emailWithPermission} style={{ flexGrow: 1 }}>
                    <TextField
                        required
                        name="collaborator.email"
                        type="email"
                        placeholder="Email"
                        style={{ flexGrow: 1 }}
                        disabled={!!email}
                        size="sm"
                    />
                    <SelectField
                        name="collaborator.permission"
                        data={[
                            { value: 'view', label: 'View' },
                            { value: 'edit', label: 'Edit' },
                        ]}
                        style={{ width: 100 }}
                        disabled={!featureEnabled}
                        size="sm"
                    />
                </Flex>

                <FormSubmit variant="default" size="xs">
                    Invite
                </FormSubmit>
            </Flex>
            {!featureEnabled && (
                <SubscriptionOnboardingMessage mt="md">
                    Want to invite editors to collaborate on projects?
                </SubscriptionOnboardingMessage>
            )}
        </Form>
    );
};

export { InviteCollaborator };
