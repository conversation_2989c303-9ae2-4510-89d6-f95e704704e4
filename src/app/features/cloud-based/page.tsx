import React from 'react';

import { Box } from '@mantine/core';

import { ContactSection } from '@/components/contact-section/ContactSection';
import { Features } from '@/components/features/Features';
import { PageHeader } from '@/components/page-header/PageHeader';
import { Section } from '@/components/section/Section';
import { Block } from '@/components/blocks/Block';

import { Columns } from '@/components/blocks/Columns';
import { TextBlock } from '@/components/blocks/TextBlock';

import { SectionSpacer } from '@/components/section/SectionSpacer';

import { VideoBlock } from '@/components/blocks/VideoBlock';

import { features } from '@/data/features';

import shareWithEverybody from './share-with-everybody.png';
import {ImageBlock} from '@/components/blocks/ImageBlock';

export default function Home() {
    return (
        <>
            <PageHeader
                title={features.cloudBased.title}
                titleGradient="green"
                description={features.cloudBased.description}
            />

            <Section>
                <Block size="xl">
                    <Columns>
                        <TextBlock title="No installation required" titleGradient="green">
                            <Box>
                                Our design editor is build for the web and can be used from any device with a modern
                                browser. No need to install gigabytes of software.
                            </Box>
                            <Box>
                                Creating a profile only takes a minute, all you need is an email address.
                            </Box>
                        </TextBlock>
                        <VideoBlock
                            video={{
                                src: '/videos/features/cloud-based-login.mp4',
                                width: 2302,
                                height: 2008,
                                playbackRate: 1.25,
                            }}
                        />
                    </Columns>
                </Block>
                <SectionSpacer size="lg" />
                <Block size="xl">
                    <Columns switchDesktopOrder>
                        <TextBlock title="Sharing built-in" titleGradient="orange">
                            <Box>
                                Share your designs with your team, partners or clients.</Box>
                            <Box>
                                Everybody accesses the same design, no matter where they are, on their computer, tablet or smartphone.
                            </Box>
                        </TextBlock>
                        <ImageBlock image={shareWithEverybody} />
                    </Columns>
                </Block>
            </Section>

            <Features title="There's More to Discover" titleGradient="blue" variant="light">
                {[features.realtimeCollaboration, features.productCatalog, features.annotations]}
            </Features>

            <ContactSection />
        </>
    );
}
