import React from 'react';

import Link from 'next/link';

import { Anchor } from '@mantine/core';

import cx from './ComponentLanding.Link.module.css';

const ComponentLandingLink = ({ label, href, onClick }: { href?: string; label: string; onClick?: () => void }) => {
    if (href) {
        return (
            <Anchor classNames={cx} component={Link} href={href} fw={600} onClick={onClick}>
                {label}
            </Anchor>
        );
    }

    return (
        <Anchor classNames={cx} fw={600} onClick={onClick}>
            {label}
        </Anchor>
    );
};

export { ComponentLandingLink };
