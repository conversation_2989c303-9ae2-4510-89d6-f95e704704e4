/* eslint-disable */
// @ts-nocheck

'use client';

import { useEffect } from 'react';

const VideoTransition = () => {
    useEffect(() => {
        const source = document.querySelector('[data-video-transition-source]');
        const target = document.querySelector('[data-video-transition-target]');

        if (source && target) {
            const execute = () => {
                const sourceBCR = source.getBoundingClientRect();
                const targetBCR = target.getBoundingClientRect();

                const delta = {
                    x: targetBCR.x - sourceBCR.x,
                    y: targetBCR.y - sourceBCR.y,
                    width: targetBCR.width - sourceBCR.width,
                    height: targetBCR.height - sourceBCR.height,
                };

                const percentage = window.scrollY / 200;

                console.log(delta, percentage);

                // @ts-ignore
                source.style.transform = `translate(${delta.x * percentage}px, ${delta.y * percentage}px)`;

                window.requestAnimationFrame(execute);
            };

            window.requestAnimationFrame(execute);
        }
    });

    return null;
};

export { VideoTransition };
