.root {
    display: flex;
    align-items: end;
    flex-wrap: wrap;
    gap: var(--mantine-spacing-md);
}

.logo {
    width: 80px;
    height: auto;

    display: flex;

    @media (min-width: var(--mantine-breakpoint-sm)) {
        width: 120px;
    }
}

.titleWrapper {
    flex: 1;

    @media (max-width: var(--mantine-breakpoint-md)) {
        order: 1;
        min-width: 100%;
    }
}

.title {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.right {
    position: absolute;
    top: 0;
    right: var(--mantine-spacing-xs);

    display: flex;
    gap: var(--mantine-spacing-xs);
    justify-content: flex-end;
    align-items: center;

    @media (max-width: var(--mantine-breakpoint-sm)) {
        flex: 1;
    }
}

.rightlogo {
    display: flex;

    width: 100px;
    height: auto;

    filter: grayscale(1);

    transition: filter 200ms;

    &:hover {
        filter: grayscale(0);
    }

    @media (max-width: var(--mantine-breakpoint-sm)) {
        width: 70px;
    }
}

.linkWrapper {
    > * {
        vertical-align: middle;
    }

    > * + * {
        margin-left: 8px;
    }
}

.link {
    margin-bottom: -1px; /*  align with non border text */
    border-bottom: 1px dashed rgba(255, 255, 255, 0.4);

    line-height: 1.2;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none !important;

    opacity: 0.5;

    transition: border-color 200ms;

    &[data-active="true"] {
        opacity: 0.8;
    }

    &:hover {
        border-bottom-color: rgba(255, 255, 255, 0.6);
    }

    @media (min-width: var(--mantine-breakpoint-sm)) {
        font-size: 15px;
    }
}
