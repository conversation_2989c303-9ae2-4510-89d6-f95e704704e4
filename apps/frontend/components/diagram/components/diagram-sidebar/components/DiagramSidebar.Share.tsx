import React, { FC } from 'react';

import { IoShareOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentProject } from 'hooks/use-current-project';

import { ModalService } from 'services/ModalService';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { SidebarType } from 'components/diagram/state/sidebar';
import { PermissionDiagramGeneral } from '@repo/dcide-component-models';
import { usePermission } from 'hooks/use-permission';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

const DiagramSidebarShare: FC = () => {
    const user = useCurrentUser();
    const project = useCurrentProject();
    const canShare = usePermission(PermissionDiagramGeneral.SHARE);

    const openLoginModal = () => {
        ModalService.openLoginModal({
            title: 'Login to save and share project',
            redirect: ProjectHelpers.urls.editor(project!.id),
        });
    };

    const handleClick = () => {
        if (user) {
            SidebarService.openShareSidebar();

            return;
        }

        openLoginModal();
    };

    if (user && !canShare) return null;

    return (
        <DiagramSidebar.NavItem type={SidebarType.SHARE} label="Share diagram" onClick={handleClick}>
            <IoShareOutline size={16} strokeWidth={1.5} />
            LOL
        </DiagramSidebar.NavItem>
    );
};

export { DiagramSidebarShare };
