import { componentQuerySchema } from '@repo/dcide-component-models';
import { z } from 'zod';

export const ProductSearchFilterSchema = z.object({
    type: componentQuerySchema.shape.type,
    search: componentQuerySchema.shape.search.optional(),
    // manufacturer: componentQuerySchema.shape.manufacturer.optional(),
    compliances: componentQuerySchema.shape.compliances.optional(),
    ports: z
        .object({
            voltageType: z.enum(['AC', 'DC']).optional(),
            voltage: z
                .object({
                    min: z.number().optional(),
                    nom: z.number().optional(),
                    max: z.number().optional(),
                })
                .optional(),
            power: z
                .object({
                    nom: z.number().optional(),
                    max: z.number().optional(),
                })
                .optional(),
            current: z
                .object({
                    nom: z.number().optional(),
                })
                .optional(),
            powerFlowDirection: z.enum(['bidirectional', 'input', 'output']).optional(),
            isolated: z.boolean().optional(),
            purpose: z.enum(['battery', 'converter', 'solar', 'utility', 'panel', 'charger', 'generator']).optional(),
        })
        .array()
        .optional(),
    energyCapacity: z
        .object({
            value: z.number(),
            unit: z.literal('kWh'),
        })
        .optional(),
    regionAvailability: componentQuerySchema.shape.regionAvailability.optional(),
    application: componentQuerySchema.shape.application.optional(),
    charging: componentQuerySchema.shape.charging.optional(),
});
