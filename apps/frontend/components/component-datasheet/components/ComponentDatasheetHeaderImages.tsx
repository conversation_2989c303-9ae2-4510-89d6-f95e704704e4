import React, { FC, useRef, useState } from 'react';

import { Flex, Modal } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IoArrowBack, IoArrowForward } from 'react-icons/io5';

import { Carousel } from '@mantine/carousel';
import Autoplay, { AutoplayType } from 'embla-carousel-autoplay';

import { Component, COMPONENT_IMAGE_TYPE_OPTIONS } from '@repo/dcide-component-models';

import { VerifiedImage } from 'components/VerifiedImage';

import classes from './ComponentDatasheetHeaderImages.module.css';

const IMAGE_SIZE = 200;
const LABEL_SIZE = 40;

type Props = {
    images: Component['images'];
    imageHeight?: number;
    showType?: boolean;
    onImageClick?: (index: number) => void;
    enableAutoplay?: boolean;
    initialSlide?: number;
};

const ComponentDatasheetHeaderImages: FC<Props> = ({
    images: passedImages,
    imageHeight = IMAGE_SIZE,
    showType,
    onImageClick,
    enableAutoplay,
    initialSlide,
}) => {
    const autoplay = useRef<AutoplayType>(Autoplay({ delay: 5000 }));

    const carouselHeight = showType ? imageHeight + LABEL_SIZE : imageHeight;
    const images = (passedImages || []).filter((image: any) => Boolean(image.file));

    return images.length ? (
        <Carousel
            emblaOptions={{
                loop: true,
            }}
            slideGap="xs"
            classNames={classes}
            initialSlide={initialSlide}
            withControls={images.length > 1}
            height={carouselHeight}
            plugins={enableAutoplay ? [autoplay.current as any] : []}
            previousControlIcon={<IoArrowBack />}
            nextControlIcon={<IoArrowForward />}
        >
            {images.map((image: any, index: number) => (
                <DatasheetHeaderImage
                    index={index}
                    image={image}
                    height={imageHeight}
                    showType={showType}
                    key={index}
                    handleClick={onImageClick}
                />
            ))}
        </Carousel>
    ) : null;
};

const DatasheetHeaderImage: FC<{
    index: number;
    image: Component['images'][number];
    height: number;
    showType?: boolean;
    handleClick?: (index: number) => void;
}> = ({ index, image, height, showType, handleClick }) => {
    const slideHeight = showType ? height + LABEL_SIZE : height;
    const type = COMPONENT_IMAGE_TYPE_OPTIONS.find((item) => item.value === image.type);

    return (
        <>
            <Carousel.Slide
                // Center the image
                m="auto"
                w="100%"
                mah={slideHeight}
                onClick={handleClick ? () => handleClick(index) : undefined}
                style={{ cursor: handleClick ? 'zoom-in' : 'default' }}
            >
                {image.file && (
                    <VerifiedImage
                        fileOrId={image.file as any}
                        width={height === IMAGE_SIZE ? 200 : 740}
                        radius="xs"
                        m="auto"
                        mah={height}
                        alt={type?.label}
                        fit="contain"
                        styles={{
                            root: {
                                overflow: 'hidden',
                            },
                        }}
                    />
                )}
                {showType && (
                    <Flex align="center" justify="center" h={LABEL_SIZE} ta="center" fz="sm" c="dimmed">
                        {type?.label}
                    </Flex>
                )}
            </Carousel.Slide>
        </>
    );
};

const ComponentDatasheetHeaderImagesWithModal: FC<Props> = (props) => {
    const [fullscreen, handlers] = useDisclosure();
    const [clickedImageIndex, setClickedImageIndex] = useState(0);

    const handleClick = (index: number) => {
        handlers.open();
        setClickedImageIndex(index);
    };

    return (
        <>
            <Modal opened={fullscreen} onClose={handlers.close} withCloseButton={false} size="xl">
                <ComponentDatasheetHeaderImages
                    {...props}
                    imageHeight={600}
                    showType
                    initialSlide={clickedImageIndex}
                />
            </Modal>

            <ComponentDatasheetHeaderImages {...props} onImageClick={handleClick} enableAutoplay />
        </>
    );
};

export { ComponentDatasheetHeaderImages, ComponentDatasheetHeaderImagesWithModal };
