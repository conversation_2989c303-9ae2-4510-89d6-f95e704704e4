import { Stack, StackProps, Text, Title, Transition } from '@mantine/core';

import { useCurrentUser } from 'hooks/use-current-user';

import cx from './Welcome.module.css';

export const Welcome = ({ hidden, ...props }: StackProps) => {
    const user = useCurrentUser();

    return (
        <Transition mounted={!hidden} transition="fade" duration={300} timingFunction="ease">
            {(styles) => (
                <Stack gap={'xs'} align="center" p="xl" style={styles} {...props}>
                    {user?.name && <Text className={cx.subtitle}>Hello {user.name},</Text>}

                    <Title ta="center" className={cx.title}>
                        What are you looking for?
                    </Title>
                    <Text className={cx.subtitle}>
                        Get matched with <span className={cx.highlight}>companies</span> in the electric power industry,
                        their <span className={cx.highlight}>services</span>,{' '}
                        <span className={cx.highlight}>products</span> and more.
                    </Text>
                </Stack>
            )}
        </Transition>
    );
};
