import { useEffect, useState } from 'react';

import { useRouter } from 'next/router';

import { Anchor, Button, ButtonProps, Modal, Radio, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { Component, Manufacturer } from '@repo/dcide-component-models';

import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';
import { ComponentService } from 'services/ComponentService';

const BecomeDistributorButton = ({ component, ...props }: { component: Component } & ButtonProps) => {
    const router = useRouter();
    const [opened, handlers] = useDisclosure();

    const { distrbutors } = useCurrentTeamCompanies();

    useEffect(() => {
        const { action, ...updatedQuery } = router.query;

        if (action === 'become-distributor') {
            handlers.open();

            const newPath = {
                pathname: router.pathname,
                query: updatedQuery,
            };

            router.replace(newPath, undefined, { shallow: true }).then();
        }
    }, [handlers, router.query]);

    if (!distrbutors?.length) {
        return null;
    }

    return (
        <>
            <Button onClick={handlers.open} {...props}>
                Become distributor
            </Button>
            {opened && (
                <BecomeDistributorModal component={component} distributors={distrbutors} handleClose={handlers.close} />
            )}
        </>
    );
};

const BecomeDistributorModal = ({
    component,
    distributors,
    handleClose,
}: {
    component: Component;
    distributors: Manufacturer[];
    handleClose: () => void;
}) => {
    const [selectedDistributor, setSelectedDistributor] = useState<string>();

    const [loading, setLoading] = useState(false);
    const [success, setSuccess] = useState(false);
    const [error, setError] = useState<string>();

    const handleConfirm = async () => {
        if (!selectedDistributor) return;

        setLoading(true);
        setError('');
        setSuccess(false);

        try {
            const response = await ComponentService.addDistributor(component.id, {
                distributorId: selectedDistributor,
            });

            if (response.success) {
                setLoading(false);
                setSuccess(true);
            } else {
                setLoading(false);
                setError(response.error);
            }
        } catch {
            setError('Something went wrong');
            setLoading(false);
        }
    };

    const modalContent = success ? (
        <Stack>
            <Text>Congratulations, you are now a distributor for this product!</Text>
            <Button onClick={handleClose}>Close</Button>
        </Stack>
    ) : (
        <Stack>
            <Text>
                By becoming a distributor for this product, you will be able to provide pricing, availability, lead time
                information and more to your customers.
            </Text>

            <Text>
                Please contact us at{' '}
                <Anchor href="mailto:<EMAIL>"><EMAIL></Anchor> if you want
                an integration with your ERP system.
            </Text>
            {distributors.map((distributor) => (
                <Radio
                    key={distributor.id}
                    name={distributor.id}
                    label={
                        <Text>
                            I want to distribute this product with <b>{distributor.name}</b>
                        </Text>
                    }
                    checked={selectedDistributor === distributor.id}
                    onChange={(event) => {
                        if (event.target.checked) {
                            setSelectedDistributor(distributor.id);
                            return;
                        }
                    }}
                />
            ))}
            <Button disabled={!selectedDistributor} onClick={handleConfirm} loading={loading}>
                Confirm
            </Button>
            {error && <Text c="red">{error}</Text>}
        </Stack>
    );

    return (
        <Modal opened onClose={handleClose} title={`Become a distributor for ${component.name}`} withinPortal>
            {modalContent}
        </Modal>
    );
};

export { BecomeDistributorButton };
