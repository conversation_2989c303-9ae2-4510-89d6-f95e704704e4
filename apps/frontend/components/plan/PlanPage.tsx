import { AppShell } from '@mantine/core';

import { usePlanPage } from 'components/plan/hooks/use-plan-page';

import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { Body } from './components/Body';

import classNames from './AppShell.module.scss';

export const PlanPage = () => {
    const { isInitial } = usePlanPage();

    return (
        <AppShell
            layout="alt"
            header={{ height: 72 }}
            footer={{ height: isInitial ? 72 : 'var(--mantine-spacing-xl)' }}
            navbar={{ width: 300, breakpoint: 'sm', collapsed: { desktop: true, mobile: true } }}
            aside={{ width: 300, breakpoint: 'md', collapsed: { desktop: true, mobile: true } }}
            classNames={classNames}
            withBorder={false}
        >
            <AppShell.Navbar />
            <AppShell.Header>
                <Header />
            </AppShell.Header>
            <AppShell.Main>
                <Body />
            </AppShell.Main>
            {isInitial && (
                <AppShell.Footer>
                    <Footer />
                </AppShell.Footer>
            )}
        </AppShell>
    );
};
