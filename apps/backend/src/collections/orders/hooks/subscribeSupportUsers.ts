import { CollectionBeforeChangeHook } from 'payload';

import { diff, flat } from 'radash';

import { Order } from '@/payload-types';
import { CompanyService } from '@repo/dcide-component-models';

const subscribeSupportUsers: CollectionBeforeChangeHook<Order> = async ({ data, originalDoc, req }) => {
    const newManufacturers = diff(data.reviewers?.manufacturers || [], originalDoc?.reviewers?.manufacturers || []);

    if (newManufacturers.length === 0) return;

    const newManufacturerUsers = await Promise.all(
        newManufacturers.map(async (manufacturerId: string) => {
            const { users, services } = await req.payload.findByID({
                collection: 'manufacturers',
                id: manufacturerId,
                depth: 0,
            });

            if (services.includes(CompanyService.IN_APP_SUPPORT)) {
                return users;
            }

            return [];
        }),
    );

    return {
        ...data,
        subscribers: flat([originalDoc?.subscribers || [], flat(newManufacturerUsers || []), data.subscribers || []]),
    };
};

export { subscribeSupportUsers };
