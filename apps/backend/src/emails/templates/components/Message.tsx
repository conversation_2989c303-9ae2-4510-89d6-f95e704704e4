import React from 'react';

import { Text, Row, Column, Section } from '@react-email/components';

import { File } from '@/payload-types';

import { Avatar } from './Avatar';
import { borderColor } from '../styles';
import { EmailTemplateProps } from '../General';

type Props = Required<EmailTemplateProps>['message'];

const AVATAR_SIZE = 28;

const Message = ({ user, hasAttachment, ...props }: Props) => {
    const hasHtml = 'html' in props;
    const content = hasHtml ? props.html : props.text;

    return (
        <Section style={container}>
            {user && (
                <Row style={messageBy}>
                    <Column style={avatar}>
                        <Avatar size={AVATAR_SIZE} name={user.name} image={(user.profileImage as File)?.url} />
                    </Column>
                    <Column style={messageContainer}>
                        <Text style={name}>{user.name}</Text>
                    </Column>
                </Row>
            )}

            <Row>
                {hasHtml ? (
                    <div style={textStyle} dangerouslySetInnerHTML={{ __html: content }} />
                ) : (
                    <Text style={textStyle}>{content}</Text>
                )}

                {hasAttachment && <Text style={hasAttachmentStyle}>(This message has an attachment.)</Text>}
            </Row>
        </Section>
    );
};

export { Message };

const container = {
    padding: '15px 20px',
    border: `1px solid ${borderColor}`,
};

const messageBy = {
    marginBottom: '15px',
};

const avatar = {
    verticalAlign: 'top',
    width: `${AVATAR_SIZE}px`,
};

const messageContainer = {
    paddingLeft: '10px',
};

const name = {
    margin: 0,
    fontSize: '14px',
    fontWeight: '600',
};

const textStyle = {
    margin: 0,
};

const hasAttachmentStyle = {
    margin: 0,
    marginTop: '10px',
    fontSize: '11px',
    fontColor: 'grey',
};
