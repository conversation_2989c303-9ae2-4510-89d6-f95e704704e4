import React, { useState } from 'react';

import { Badge, Box, Button, Card, Divider, Space, Text, Title } from '@mantine/core';
import { IoBanSharp } from 'react-icons/io5';

import {
    CompanyProfile,
    DesignerSubscription,
    getDesignerSubscriptionData,
    Team,
    User,
} from '@repo/dcide-component-models';

import { useCurrentTeam } from 'hooks/use-current-team';

import { GrantCompanyAccessInviteUser } from 'components/grant-company-access/GrantCompanyAccess.InviteUser';
import { GrantCompanyAccessMoveTeam } from 'components/grant-company-access/GrantCompanyAccess.MoveTeam';
import { GrantCompanyAccessDenyAccess } from 'components/grant-company-access/GrantCompanyAccess.DenyAccess';

import { MaxUsersMessage } from 'components/subscriptions/MaxUsersMessage';
import { SubscriptionUpdateWrapper } from 'components/subscriptions/SubscriptionUpdateWrapper';

import cx from 'components/grant-access/GrantAccess.module.css';

enum GrantCompanyAccessView {
    INIT = 'init',
    INVITE_USER = 'inviteUser',
    MOVE_TEAM = 'moveTeam',
    DENY_ACCESS = 'denyAccess',
}

const GrantCompanyAccessChoose = ({
    company,
    requestUser,
    requestTeam,
}: {
    company: CompanyProfile;
    requestUser: User;
    requestTeam: Team;
}) => {
    const [view, setView] = useState<GrantCompanyAccessView>(GrantCompanyAccessView.INIT);

    const currentTeam = useCurrentTeam();

    if (!currentTeam) return null;

    const subscriptionDetails = getDesignerSubscriptionData(currentTeam?.subscriptions);
    const maxNumberOfUsersReached =
        Boolean(subscriptionDetails?.quantity && currentTeam.users.length >= subscriptionDetails.quantity) &&
        subscriptionDetails?.subscription !== DesignerSubscription.FREE;

    switch (view) {
        case GrantCompanyAccessView.INVITE_USER:
            return (
                <GrantCompanyAccessInviteUser
                    company={company}
                    goBack={() => setView(GrantCompanyAccessView.INIT)}
                    requestUser={requestUser}
                />
            );
        case GrantCompanyAccessView.MOVE_TEAM:
            return (
                <GrantCompanyAccessMoveTeam
                    company={company}
                    requestTeam={requestTeam}
                    requestUser={requestUser}
                    goBack={() => setView(GrantCompanyAccessView.INIT)}
                />
            );
        case GrantCompanyAccessView.DENY_ACCESS:
            return (
                <GrantCompanyAccessDenyAccess
                    company={company}
                    goBack={() => setView(GrantCompanyAccessView.INIT)}
                    requestUser={requestUser}
                    requestTeam={requestTeam}
                />
            );
        default:
            return (
                <>
                    <Title size="h2">Grant access to {company.name}</Title>
                    <Text>
                        <strong>{requestUser.email}</strong> requests access to <strong>{company.name}</strong>
                    </Text>
                    <Text>There are two options to grant access:</Text>
                    <Card
                        withBorder
                        className={cx.option}
                        style={{ cursor: maxNumberOfUsersReached ? 'cursor' : 'pointer' }}
                        onClick={
                            !maxNumberOfUsersReached ? () => setView(GrantCompanyAccessView.INVITE_USER) : undefined
                        }
                    >
                        <Badge>Recommended</Badge>
                        <Title size="h2">Invite to your team</Title>
                        <Space />
                        <Text>
                            Invite {requestUser.name} to your team
                            <br />
                            <strong>{currentTeam.name}</strong>
                        </Text>
                        {maxNumberOfUsersReached && (
                            <SubscriptionUpdateWrapper
                                currentSubscription={subscriptionDetails?.subscription ?? DesignerSubscription.FREE}
                                team={currentTeam}
                            >
                                <Box bg="yellow.0" p="sm">
                                    <MaxUsersMessage />
                                </Box>
                            </SubscriptionUpdateWrapper>
                        )}
                    </Card>
                    <Card withBorder className={cx.option} onClick={() => setView(GrantCompanyAccessView.MOVE_TEAM)}>
                        <Badge color="red">Danger zone</Badge>
                        <Title size="h2">Transfer ownership</Title>
                        <Space />
                        <Text>
                            Transfer ownership of the {company.name} profile to
                            <br />
                            <strong>{requestUser.name}</strong> in <strong>{requestTeam.name}</strong>
                        </Text>
                    </Card>

                    <Divider label="or" my="xs" />

                    <Button
                        variant="outline"
                        color="red"
                        leftSection={<IoBanSharp />}
                        onClick={() => setView(GrantCompanyAccessView.DENY_ACCESS)}
                    >
                        Deny Access Request
                    </Button>
                </>
            );
    }
};

export { GrantCompanyAccessChoose };
