import type { FC } from 'react';
import { type DiagramConnection, type DiagramComponentInstance, powerConverter } from '@repo/dcide-component-models';

import { get } from 'radash';

import { Box, Popover } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';
import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

import { PowerField } from 'components/component-fields/PowerField';

import { FormatHelpers } from 'helpers/formatters';

const DiagramToolbarPower: FC<{
    source: DiagramConnection | DiagramComponentInstance;
    path: string;
}> = ({ source, path }) => {
    const [opened, handlers] = useDisclosure(false);
    const formattedPower = FormatHelpers.formatMinNomMax(get(source, path), powerConverter);

    return (
        <Popover
            classNames={{
                dropdown: cx.dropdown,
            }}
            opened={opened}
            onClose={handlers.close}
            position="top"
            withArrow
            withinPortal={false}
        >
            <Popover.Target>
                <div>
                    <DiagramToolbar.TextButton
                        tooltip={!opened && formattedPower ? 'Power' : ''}
                        onClick={() => {
                            handlers.toggle();
                        }}
                        active={!!formattedPower}
                    >
                        {formattedPower || 'Power'}
                    </DiagramToolbar.TextButton>
                </div>
            </Popover.Target>
            <Popover.Dropdown>
                <Box className={cx.measurement} style={{ '--measurement-fields': 2 }}>
                    <PowerField name={path} hideIcons />
                </Box>
            </Popover.Dropdown>
        </Popover>
    );
};

export { DiagramToolbarPower };
