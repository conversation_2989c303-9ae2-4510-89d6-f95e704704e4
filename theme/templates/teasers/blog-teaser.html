<a class="relative block" href="{{ blog.url }}" data-blog-teaser>
    {% for image in blog.teaserImage.all %}
        {% include 'components/image' with {
            class: 'w-full h-auto aspect-[4/3] | rounded-xl | object-cover',
            src: image.url,
            width: image.width,
            height: image.height,
            transforms: [
                { breakpoint: '', parameters: 'auto=format,compress&w=360' },
                { breakpoint: '(min-width: 640px)', parameters: 'auto=format,compress&w=640' },
                { breakpoint: '(min-width: 768px)', parameters: 'auto=format,compress&w=768' },
                { breakpoint: '(min-width: 1024px)', parameters: 'auto=format,compress&w=1024' }
            ],
            attributes: [
                'data-blog-teaser-image',
            ],
        } %}
    {% endfor %}
    <div class="lg:absolute lg:bottom-0 lg:left-0 lg:w-2/3 mt-2 lg:pt-5" data-blog-teaser-content>
        <div class="lg:text-lg">
            {{ blog.dateCreated | date('medium') }}
        </div>
        <div class="font-bold text-xl md:text-xl lg:text-2xl">
            {{ blog.teaserTitle ?? blog.title }}
        </div>
    </div>
</a>
