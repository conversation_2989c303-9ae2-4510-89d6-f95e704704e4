import { config } from 'config';

const ProductCsvService = {
    bulkUpload: async (file: File, type: string, manufacturer: string | null) => {
        return ProductCsvService.submitBulkUploadEndpoint(file, type, manufacturer, 'bulk-upload/components');
    },

    bulkUploadImages: async (file: File, type: string, manufacturer: string | null, bulkUploadId: string | null) => {
        return ProductCsvService.submitBulkUploadEndpoint(file, type, manufacturer, 'bulk-upload/images', bulkUploadId);
    },

    submitBulkUploadEndpoint: async (
        file: File,
        type: string,
        manufacturer: string | null,
        endpoint: string,
        bulkUploadId: string | null = null,
    ) => {
        const body = new FormData();
        body.append('file', file);
        body.append('type', type);
        if (manufacturer) {
            body.append('manufacturer', manufacturer);
        }
        if (bulkUploadId) {
            body.append('bulkUploadId', bulkUploadId);
        }

        const response = await fetch(`${config.api.backend}/${endpoint}`, {
            method: 'POST',
            body,
            credentials: 'include',
        });
        try {
            if (!response.ok) {
                throw new Error(JSON.stringify(await response.json(), null, 2));
            }
        } catch (e: any) {
            throw new Error(e.message);
        }
        return response.json();
    },

    checkTemplateIsAvailable: async (href: string) => {
        try {
            const headers = {};
            const response = await fetch(href, {
                method: 'HEAD',
                headers,
                credentials: 'include',
            });

            return !!response.ok;
        } catch (error) {
            return false;
        }
    },
};

export { ProductCsvService };
