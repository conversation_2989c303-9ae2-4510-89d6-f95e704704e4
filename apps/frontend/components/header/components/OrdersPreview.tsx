import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { Menu } from '@mantine/core';
import { TbLayoutGrid } from 'react-icons/tb';

import { useOrderMeta, useOrders } from 'hooks';
import { Order } from '@repo/dcide-component-models';

const OrdersPreview = () => {
    const { orders } = useOrders({ depth: 1, limit: 5 });

    return (
        <React.Fragment>
            <Link href="/quotes" legacyBehavior>
                <Menu.Item component="a" leftSection={<TbLayoutGrid size={16} strokeWidth={1.5} />}>
                    View all quotes
                </Menu.Item>
            </Link>

            <Menu.Divider />

            <LatestOrders orders={orders} />
        </React.Fragment>
    );
};

const LatestOrders: FC<{ orders: Order[] }> = ({ orders }) => {
    return (
        <React.Fragment>
            <Menu.Label>Latest quotes</Menu.Label>
            {orders.map((order) => (
                <LatestOrdersItem key={order.id} order={order} />
            ))}
        </React.Fragment>
    );
};

const LatestOrdersItem: FC<{ order: Order }> = ({ order }) => {
    const { url } = useOrderMeta(order);

    return (
        <Link href={url} key={order.id} legacyBehavior>
            <Menu.Item component="a">{order.name}</Menu.Item>
        </Link>
    );
};

export { OrdersPreview };
