import type { FC } from 'react';
import type { Option } from '@repo/dcide-component-models';

import { useController, useFormContext } from 'react-hook-form';

import { Box, Combobox, Divider, ScrollArea } from '@mantine/core';
import { useCombobox } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';

import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

type Props = {
    label: string;
    options: (Option<string | number, React.ReactNode> & {
        dropdownLabel?: React.ReactNode;
    })[];
};

const DiagramToolbarMenu: FC<
    Props & {
        value?: string | number;
        hiddenInput?: React.ReactNode;
        handleChange: (value: string | number) => void;
        afterSection?: React.ReactNode;
    }
> = ({ label, value, hiddenInput, options, handleChange, afterSection }) => {
    const [opened, handlers] = useDisclosure();
    const combobox = useCombobox({
        opened,
    });

    const selectedOption = options.find((option) => option.value === value);

    return (
        <Combobox
            store={combobox}
            classNames={{
                dropdown: cx.dropdown,
                option: cx.dropdownOption,
            }}
            onOptionSubmit={(value) => {
                handleChange(value);
                handlers.close();
            }}
            onClose={() => {
                handlers.close();
            }}
            position="top"
            withArrow
            withinPortal={false}
            arrowOffset={18}
        >
            <Combobox.Target>
                <div>
                    <DiagramToolbar.TextButton
                        active={!!value}
                        onClick={() => {
                            handlers.toggle();
                        }}
                        tooltip={opened ? null : label}
                    >
                        {selectedOption?.label || label}
                    </DiagramToolbar.TextButton>
                    {hiddenInput}
                </div>
            </Combobox.Target>
            <Combobox.Dropdown>
                <Combobox.Options>
                    <ScrollArea
                        mah={36 * 10}
                        type="auto"
                        styles={{
                            thumb: {
                                background: 'rgba(255, 255, 255, .25)',
                            },
                        }}
                    >
                        {options.map((option) => (
                            <Combobox.Option
                                value={option.value.toString()}
                                active={option.value === value}
                                key={option.value}
                            >
                                {option.dropdownLabel || option.label}
                            </Combobox.Option>
                        ))}
                    </ScrollArea>
                </Combobox.Options>
                {afterSection && (
                    <>
                        <Divider color="gray.7" />
                        <Box className={cx.dropdownSection}>{afterSection}</Box>
                    </>
                )}
            </Combobox.Dropdown>
        </Combobox>
    );
};

const DiagramToolbarMenuInput: FC<
    Props & {
        name: string;
    }
> = ({ name, label, options }) => {
    const { setValue } = useFormContext();
    const { field } = useController({
        name,
    });
    const { value } = field;

    const handleChange = (value: string | number) => {
        setValue(name, value, { shouldDirty: true });
    };

    return (
        <DiagramToolbarMenu
            label={label}
            value={value}
            options={options}
            handleChange={handleChange}
            hiddenInput={<input {...field} type="hidden" />}
        />
    );
};

export { DiagramToolbarMenu, DiagramToolbarMenuInput };
