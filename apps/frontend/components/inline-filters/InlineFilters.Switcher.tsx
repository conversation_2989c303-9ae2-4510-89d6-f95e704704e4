import { FC } from 'react';

import { Tooltip, UnstyledButton } from '@mantine/core';

import cx from './InlineFilters.Switcher.module.css';

const InlineFiltersSwitcher: FC<{
    value: any;
    placeholder: string;
    options: {
        value: any;
        label: string;
        render?: React.ReactNode;
    }[];
    handleClick: (value: any) => void;
}> = ({ value, placeholder, options, handleClick }) => {
    const currentIndex = options.findIndex((option) => option.value === value);

    const currentOption = options[currentIndex];
    const nextOption = options[currentIndex + 1] || options[0];

    const tooltipLabel = `Set to ${nextOption.label}`;

    return (
        <Tooltip label={tooltipLabel}>
            <UnstyledButton
                tabIndex={-1}
                onClick={() => handleClick(nextOption.value)}
                className={currentOption ? cx.value : cx.placeholder}
            >
                {currentOption ? currentOption.render || currentOption.label : placeholder}
            </UnstyledButton>
        </Tooltip>
    );
};

export { InlineFiltersSwitcher };
