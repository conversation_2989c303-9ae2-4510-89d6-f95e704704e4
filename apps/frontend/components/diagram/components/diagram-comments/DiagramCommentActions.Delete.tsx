import React, { FC } from 'react';

import { Tooltip } from '@mantine/core';
import { IoTrashOutline } from 'react-icons/io5';

import { CommentService } from 'components/diagram/services';

import { DiagramComment } from '@repo/dcide-component-models';

const Delete: FC<{
    comment: DiagramComment;
    onUpdate?: (comment: DiagramComment) => void;
    withTooltip?: boolean;
    children: (props: { deleted: boolean; Icon: any; label: string; handle: () => void }) => React.ReactNode;
}> = ({ comment, onUpdate, withTooltip = false, children }) => {
    const deleted = comment.status === 'deleted';

    const Icon = deleted ? IoTrashOutline : IoTrashOutline;
    const label = deleted ? 'Restore' : 'Delete';

    const handle = () => {
        const next = CommentService.delete(comment);

        if (onUpdate) {
            onUpdate(next);
        }
    };

    return withTooltip ? (
        <Tooltip label={label} withArrow>
            {children({ deleted, Icon, label, handle })}
        </Tooltip>
    ) : (
        children({ deleted, Icon, label, handle })
    );
};

export { Delete };
