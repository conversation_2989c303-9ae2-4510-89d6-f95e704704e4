import { FC, useState } from 'react';
import { Button, Group, Modal, MultiSelect, Select, Stack, Text } from '@mantine/core';
import { CompanyDataHeaders, runExport } from './company-data-export';
import { CompanySubscription, PublishedStatus } from '@repo/dcide-component-models';
import { capitalize } from 'radash';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { AsyncButton } from 'components/async-button';

type HeaderKey = (typeof CompanyDataHeaders)[number]['key'];

export const CompanyDataExportModal: FC<{ opened: boolean; onClose: () => void }> = ({ opened, onClose }) => {
    const [columns, setColumns] = useState<HeaderKey[]>(
        CompanyDataHeaders.filter((header) => !header.defaultHidden).map((header) => header.key),
    );

    const [status, setStatus] = useState<PublishedStatus | undefined>();
    const [subscription, setSubscription] = useState<CompanySubscription | undefined>();

    const onExport = async () => {
        const { docs: companies } = await CompanyProfileService.list({
            status,
            subscription,
        });

        runExport(columns, companies);
    };

    return (
        <Modal opened={opened} onClose={onClose} size="xl" title="Export Data">
            <Stack>
                <Text>Columns to include:</Text>

                <MultiSelect
                    label="Columns"
                    value={columns}
                    onChange={(values) => setColumns(values as HeaderKey[])}
                    data={CompanyDataHeaders.map((header) => ({ value: header.key, label: header.displayLabel }))}
                />

                <Select
                    label="Status"
                    value={status}
                    onChange={(value) => setStatus((value as PublishedStatus) ?? undefined)}
                    data={Object.values(PublishedStatus).map((status) => ({
                        value: status,
                        label: capitalize(status),
                    }))}
                    clearable
                />

                <Select
                    label="Subscription"
                    value={subscription}
                    onChange={(values) => setSubscription((values as CompanySubscription) ?? undefined)}
                    data={Object.values(CompanySubscription).map((subscription) => ({
                        value: subscription,
                        label: capitalize(subscription.split('.')[1]),
                    }))}
                    clearable
                />

                <Group ml="auto" gap="lg">
                    <Button variant="subtle" onClick={onClose}>
                        Cancel
                    </Button>
                    <AsyncButton onClick={onExport}>Export</AsyncButton>
                </Group>
            </Stack>
        </Modal>
    );
};
