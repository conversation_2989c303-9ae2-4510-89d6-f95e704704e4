import { FC } from 'react';
import { uid } from 'radash';

import { Button } from '@mantine/core';

import { getComponentDefinition } from '@repo/dcide-component-models';

import { Datasheet } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetPort } from '../ComponentDatasheetPort.Team';

import { CapacityField } from 'components/component-fields/CapacityField';
import { ChargeField } from 'components/component-fields/ChargeField';

import { useDatasheetPorts } from 'components/component-datasheet/hooks/use-datasheet-ports';
import { useDatasheetElectrical } from 'components/component-datasheet/hooks/use-datasheet-electrical';

import { useComponentContext } from '../../hooks/use-component-context';
import { componentTypeHasElectrical } from 'helpers/componentTypeHasElectrical';

const Electrical: FC = () => {
    const { component } = useComponentContext();
    const { addPort } = useDatasheetPorts();
    const electrical = useDatasheetElectrical();

    const componentDefinition = getComponentDefinition(component.type || 'custom');

    if (!componentDefinition || !componentTypeHasElectrical(component.type)) {
        return null;
    }

    return (
        <ComponentDatasheetSection title="Electrical">
            <Datasheet.Table header={{ prefix: ['Electrical'], label: 'General' }}>
                {'energyCapacity' in electrical && (
                    <Datasheet.TableRow name="electrical.energyCapacity" label="Energy Capacity">
                        <CapacityField name={`electrical.energyCapacity`} />
                    </Datasheet.TableRow>
                )}
                {'chargeCapacity' in electrical && (
                    <Datasheet.TableRow name="electrical.chargeCapacity" label="Charge Capacity">
                        <ChargeField name={`electrical.chargeCapacity`} />
                    </Datasheet.TableRow>
                )}
            </Datasheet.Table>
            {electrical.ports.map((_: any, index: number) => (
                <ComponentDatasheetPort index={index} label={`Port ${index + 1}`} key={uid(10)} />
            ))}
            {electrical.ports.length < componentDefinition.ports.max && (
                <Button onClick={() => addPort()} size="xs">
                    Add port
                </Button>
            )}
        </ComponentDatasheetSection>
    );
};

export { Electrical };
