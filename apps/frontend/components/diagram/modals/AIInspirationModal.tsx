import React, { FC } from 'react';

import {Badge, Box, Paper, SimpleGrid, Stack, Text, Title} from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { ModalTitle } from 'components/modals/ModalTitle';
import { ModalDescription } from 'components/modals/ModalDescription';
import { AIConversationService } from 'components/diagram/services/AIConversationService';

const sections = [
    {
        title: 'Diagram Questions',
        suggestions: [
            'What is the total capacity of the solar panels?',
            'What is the total storage capacity in my diagram?',
            'How many converters are there?',
            'List all the wire sizes in this diagram and organize them by size.',
            'What is the optimal size for fuse F-3?',
            'What is the maximum input and output current converter U-10 will have to support?',
        ],
    },
    {
        title: 'Diagram Interactions',
        suggestions: [
            'Double the battery capacity of my project',
            'Increase the possible solar production by 50%',
            'Make my design more compact',
            'Can you help me fix the "no power specified" errors in the validation center?',
            'Create a generation profile for the month of April? I live in New York.',
        ],
    },
    {
        title: 'Project Documentation',
        description: 'Search the files and documentation attached to this diagram.',
        suggestions: [
            'Can you search my project files for the total budget of the project?',
            'Search my project documentation for the contact information of the city',
        ],
    },
    {
        title: 'Product Documentation',
        description: 'Search the product documentation for linked components.',
        suggestions: [
            'Search the product documentation for BAT-1 for the maximum charge and discharge current',
            'Search the product documentation of U-1 for the installation instructions',
            'Can you search the product documentation for the maximum input and output current converter U-2 will have to support?',
        ],
    },
    {
        title: 'Consult the Current/OS Guidelines',
        suggestions: [
            'Is my design compliant with the Current/OS guidelines?',
            'Based on the Current/OS guidelines, ...',
        ],
    },
];

const AIInspirationModal: FC<ContextModalProps> = ({ id, context }) => {
    const ask = (question: string) => {
        AIConversationService.setPrefilledQuestion(question);
        context.closeModal(id);
    };

    return (
        <Stack gap="xs" p="sm">
            <Box>
                <Badge variant="gradient" radius="xs" mb="xs">Beta</Badge>
                <ModalTitle align="left">
                    Your Diagram AI Assistant
                </ModalTitle>
            </Box>
            <ModalDescription>
                Our AI Assistant is here to help you with your microgrid design.
                <br />
                You can ask it questions about your design, and it will answer them based on the information in your
                design.
            </ModalDescription>
            <Stack gap="xl" mt="lg">
                {sections.map((section) => (
                    <Box key={section.title}>
                        <Title size="h2">{section.title}</Title>
                        {section.description && <Text mt={2}>{section.description}</Text>}
                        <SimpleGrid mt="xs" spacing="xs" cols={3}>
                            {section.suggestions.map((suggestion) => (
                                <Paper
                                    p="md"
                                    radius="md"
                                    shadow="none"
                                    withBorder
                                    onClick={() => ask(suggestion)}
                                    key={suggestion}
                                >
                                    <Text key={suggestion}>{suggestion}</Text>
                                </Paper>
                            ))}
                        </SimpleGrid>
                    </Box>
                ))}
            </Stack>
        </Stack>
    );
};

export { AIInspirationModal };
