import React, { FC, useState } from 'react';

import Link from 'next/link';

import { useDisclosure } from '@mantine/hooks';
import { Box, Button, Card, Group, Modal, Space, Stack, Text } from '@mantine/core';
import { IoCopyOutline, IoPencilOutline, IoPeople, IoPerson } from 'react-icons/io5';

import { PermissionProject, Project, PublishedStatus } from '@repo/dcide-component-models';

import { ProjectService } from 'services/ProjectService';

import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';

import { useProjectMeta } from '../hooks';
import { ProjectMetaItem } from './ProjectGrid';

import cx from './ProjectGridTeaser.module.css';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { useCurrentUser } from 'hooks/use-current-user';
import { TeamHelpers } from 'helpers/TeamHelpers';
import { useTeam } from 'hooks/use-team';
import { PublishedBadge } from 'components/published-badge/PublishedBadge';

export const ProjectGridTeaser: FC<{
    project: Project;
    onProjectDelete?: () => Promise<void>;
}> = ({ project, onProjectDelete }) => {
    const user = useCurrentUser();
    const { team } = useTeam(project.team);
    const { updatedBy, updatedWhen, projectUrl, teamName } = useProjectMeta(project);

    const canDelete = team && user && TeamHelpers.hasPermission(team, user, PermissionProject.DELETE);

    return (
        <Box className={cx.wrapper}>
            <Card component={Link} href={projectUrl} p="lg" className={cx.card}>
                <Stack gap="sm" h="100%">
                    <Text size="lg" fw={600} lh={1.2} data-project-name>
                        {project.name}
                    </Text>

                    <Stack gap={4} mt="auto">
                        {project.isReferenceDesign && (
                            <PublishedBadge
                                status={
                                    project.visibility === 'marketplace'
                                        ? PublishedStatus.PUBLISHED
                                        : PublishedStatus.DRAFT
                                }
                                variant="light"
                                publishedLabel="Published design"
                                notPublishedLabel="Unpublished design"
                                style={{
                                    marginBottom: 8,
                                }}
                            />
                        )}

                        {project.customer && (
                            <ProjectMetaItem
                                label={project.customer}
                                icon={<IoPeople size={16} strokeWidth={1.5} />}
                                c="gray.8"
                            />
                        )}

                        {teamName && (
                            <ProjectMetaItem
                                label={`Team: ${teamName}`}
                                icon={<IoPeople size={16} strokeWidth={1.5} />}
                                c="gray.8"
                            />
                        )}

                        {updatedBy && (
                            <ProjectMetaItem
                                label={`Last edited by: ${updatedBy}`}
                                icon={<IoPerson size={16} strokeWidth={1.5} />}
                                c="gray.8"
                            />
                        )}

                        {updatedWhen && (
                            <ProjectMetaItem
                                label={`${updatedWhen}`}
                                icon={<IoPencilOutline size={16} strokeWidth={1.5} />}
                                c="gray.6"
                            />
                        )}

                        {/* {project.status && (
            <Box mt="xs">
                <ProjectStatusBadge status={project.status} />
            </Box>
        )} */}
                    </Stack>
                </Stack>
            </Card>
            <SettingsDropdown data-project-action>
                <DuplicateButton project={project} />
                {canDelete && <DeleteButton project={project} onProjectDelete={onProjectDelete} />}
            </SettingsDropdown>
        </Box>
    );
};

const DuplicateButton = ({ project }: { project: Project }) => {
    const handleDuplicate = () => {
        ProjectService.navigate.duplicate(project.id, project.name, project.isReferenceDesign).then();
    };

    return (
        <SettingsDropdown.Item onClick={handleDuplicate} leftSection={<IoCopyOutline />}>
            Duplicate project
        </SettingsDropdown.Item>
    );
};

const DeleteButton = ({ project, onProjectDelete }: { project: Project; onProjectDelete?: () => Promise<void> }) => {
    const [opened, handlers] = useDisclosure();
    const [loading, setLoading] = useState(false);

    const handleDelete = async () => {
        setLoading(true);

        try {
            await ProjectService.delete(project.id);

            if (onProjectDelete) {
                await onProjectDelete();
            }

            setLoading(false);

            LocalNotificationService.showSuccess({ message: 'Project deleted' });
        } catch (error) {
            setLoading(false);

            LocalNotificationService.showError({ message: "Couldn't delete project" });
            console.error(`Error deleting project: ${project.name} (ID: ${project.id})`, error);
        }

        handlers.close();
    };

    return (
        <>
            <SettingsDropdown.Delete onClick={handlers.open} />
            <Modal opened={opened} onClose={handlers.close} title="Confirm delete project">
                <Text>
                    Are you sure you want to delete{' '}
                    <Text span fw={600}>
                        {project.name}
                    </Text>{' '}
                    and all its designs and diagrams?
                </Text>

                <Space h="md" />

                <Group gap={4}>
                    <Button variant="light" color="red" onClick={handleDelete} loading={loading}>
                        Yes, delete
                    </Button>
                    <Button variant="subtle" onClick={handlers.close}>
                        Cancel
                    </Button>
                </Group>
            </Modal>
        </>
    );
};
