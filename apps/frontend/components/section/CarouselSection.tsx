import React, { FC, useRef } from 'react';

import { Carousel, CarouselProps } from '@mantine/carousel';
import Autoplay, { AutoplayType } from 'embla-carousel-autoplay';
import { IoArrowBack, IoArrowForward } from 'react-icons/io5';

import { Breakpoint } from 'hooks/use-breakpoint';
import { useResponsiveNbCols } from 'components/section/useResponsiveNbCols';

import { GridSection } from 'components/section/GridSection';

import cx from './CarouselSection.module.css';

export type CarouselSectionProps = {
    children: React.ReactNode;
    nbCols?: number;
    cols?: Record<Breakpoint, number>;
    loop?: boolean;
    autoplay?: boolean;
    autoplayDelay?: number;
} & Omit<CarouselProps, 'title'>;

const CarouselSection: FC<CarouselSectionProps> = ({
    children,
    nbCols = 4,
    cols,
    autoplay,
    autoplayDelay = 5000,
    ...props
}) => {
    const autoplayRef = useRef<AutoplayType>(Autoplay({ delay: autoplayDelay, stopOnMouseEnter: true }));

    const responsiveNbCols = useResponsiveNbCols(nbCols, cols);

    if (!responsiveNbCols) return null;

    if (React.Children.toArray(children).length <= responsiveNbCols) {
        return (
            <GridSection nbCols={nbCols} cols={cols}>
                {children}
            </GridSection>
        );
    }

    return (
        <Carousel
            classNames={cx}
            emblaOptions={{
                align: 'start',
                containScroll: 'trimSnaps',
                slidesToScroll: responsiveNbCols,
            }}
            slideGap="sm"
            slideSize={`${100 / responsiveNbCols}%`}
            previousControlIcon={<IoArrowBack />}
            nextControlIcon={<IoArrowForward />}
            plugins={autoplay ? [autoplayRef.current as any] : []}
            key={responsiveNbCols}
            {...props}
        >
            {React.Children.map(children, (child) => (
                <Carousel.Slide>{child}</Carousel.Slide>
            ))}
        </Carousel>
    );
};

export { CarouselSection };
