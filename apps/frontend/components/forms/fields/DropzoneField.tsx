import { useState } from 'react';

import { Dropzone, DropzoneProps, FileWithPath } from '@mantine/dropzone';
import { Badge, Box, Stack, Text, Tooltip } from '@mantine/core';

import { IoCloseCircleOutline, IoCloudUploadOutline } from 'react-icons/io5';
import { BsFilePdf } from 'react-icons/bs';

import { File } from '@repo/dcide-component-models';

import { FileService, FileSizeLimitExceededError } from 'services/FileService';

import cx from './DropzoneField.module.css';
import { LocalNotificationService } from 'services/LocalNotificationService';

type DropzoneFieldProps = Omit<DropzoneProps, 'onDrop'> & {
    group: string;
    onUpload?: (files: File[]) => void;
    title?: string;
    subtitle?: string;
};

const DropzoneField = ({ group, onUpload, title, subtitle, children, ...rest }: DropzoneFieldProps) => {
    const [uploading, setUploading] = useState(false);

    const handleUpload = async (files: FileWithPath[]) => {
        try {
            setUploading(true);

            const uploadedFiles = await Promise.all(
                files.map(async (file) => {
                    return FileService.create({
                        file,
                        group,
                    });
                }),
            );

            onUpload?.(uploadedFiles);
        } catch (error) {
            if (error instanceof FileSizeLimitExceededError) {
                LocalNotificationService.showError({ title: error.title, message: error.message });
            }

            console.error(error);
        } finally {
            setUploading(false);
        }
    };

    return (
        <Stack className={cx.root} gap={4}>
            <Tooltip label="Use AI to complete the datasheet" fz="xs">
                <Badge size="md" variant="gradient" tt="initial" className={cx.badge}>
                    AI Enabled
                </Badge>
            </Tooltip>
            <Dropzone {...rest} onDrop={handleUpload} loading={uploading}>
                <Stack
                    justify="center"
                    align="center"
                    gap="md"
                    p="xl"
                    style={{
                        pointerEvents: 'none',
                        border: '2px dashed var(--mantine-color-gray-2)',
                        borderRadius: 'var(--mantine-radius-sm)',
                    }}
                >
                    <Dropzone.Accept>
                        <IoCloudUploadOutline size={48} color="var(--mantine-color-primary-6)" />
                    </Dropzone.Accept>
                    <Dropzone.Reject>
                        <IoCloseCircleOutline size={48} color="var(--mantine-color-red-6)" />
                    </Dropzone.Reject>
                    <Dropzone.Idle>
                        <BsFilePdf size={48} color="var(--mantine-color-gray-4)" />
                    </Dropzone.Idle>

                    <Box ta="center">
                        <Text fw={500}>{title || 'Drag files here or click to select files'}</Text>
                        <Text size="sm" c="dimmed">
                            {subtitle || 'Attach as many files as you like'}
                        </Text>
                    </Box>
                </Stack>
            </Dropzone>
        </Stack>
    );
};

export { DropzoneField };
