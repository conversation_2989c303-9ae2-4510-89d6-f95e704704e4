import { FC } from 'react';

import { Box, Flex, NumberInput, Tooltip } from '@mantine/core';
import { TbMinus, TbPlus } from 'react-icons/tb';
import { IoTrashOutline } from 'react-icons/io5';

import classes from './QuantityField.module.css';
import { AsyncActionIcon } from 'components/async-button/AsyncActionIcon';

type QuantityFieldProps = {
    quantity: number;
    onChange: (newQuantity: number) => void;
    onRemove: () => void;
    size?: 'xs' | 'sm' | 'md';
};

const baseSizeProps = {
    gap: 'xs',
    buttonSize: 30,
    buttonIconSize: 12,
    inputWidth: 40,
    actionIconSize: 26,
    trashIconSize: 14,
};

const sizesProps = {
    xs: {
        gap: 6,
        buttonSize: 22,
        buttonIconSize: 10,
        inputWidth: 30,
        actionIconSize: 'sm',
        trashIconSize: 12,
    },
    sm: {
        ...baseSizeProps,
        buttonSize: 26,
    },
    md: baseSizeProps,
};
const QuantityField: FC<QuantityFieldProps> = ({ quantity, onChange, onRemove, size = 'md' }) => {
    const sizeProps = sizesProps[size];

    const buttonSizeProps = {
        width: sizeProps.buttonSize,
        minWidth: sizeProps.buttonSize,
        minHeight: sizeProps.buttonSize,
        height: sizeProps.buttonSize,
    };

    return (
        <Flex align="center" gap={sizeProps.gap}>
            <Flex>
                <AsyncActionIcon
                    disabled={quantity <= 1}
                    color="gray"
                    variant="outline"
                    onClick={async (event) => {
                        event.stopPropagation();

                        if (quantity <= 1) return;

                        return onChange(quantity - 1);
                    }}
                    style={(theme) => ({
                        ...buttonSizeProps,

                        borderTopRightRadius: 0,
                        borderBottomRightRadius: 0,

                        borderColor: theme.colors.gray[2],
                        borderRightWidth: 0,

                        backgroundColor: theme.white,
                    })}
                >
                    <TbMinus size={sizeProps.buttonIconSize} strokeWidth={3} />
                </AsyncActionIcon>
                <NumberInput
                    readOnly={false}
                    value={quantity}
                    min={1}
                    max={99}
                    size="xs"
                    hideControls
                    onClick={(event) => {
                        event.stopPropagation();
                    }}
                    onChange={(value) => {
                        if (value === '') return;

                        onChange(+value);
                    }}
                    classNames={{
                        input: classes.input,
                    }}
                    styles={(theme) => ({
                        root: {
                            width: sizeProps.inputWidth,
                        },
                        input: {
                            borderRadius: 0,
                            border: `1px solid ${theme.colors.gray[2]}`,

                            minHeight: sizeProps.buttonSize,
                            height: sizeProps.buttonSize,

                            padding: '0 4px',

                            textAlign: 'center',
                        },
                    })}
                />
                <AsyncActionIcon
                    disabled={quantity >= 99}
                    color="gray"
                    variant="outline"
                    onClick={async (event) => {
                        event.stopPropagation();

                        if (quantity >= 99) return;

                        return onChange(quantity + 1);
                    }}
                    style={(theme) => ({
                        ...buttonSizeProps,

                        borderTopLeftRadius: 0,
                        borderBottomLeftRadius: 0,

                        borderColor: theme.colors.gray[2],
                        borderLeftWidth: 0,

                        backgroundColor: theme.white,
                    })}
                >
                    <TbPlus size={sizeProps.buttonIconSize} strokeWidth={3} />
                </AsyncActionIcon>
            </Flex>

            <Tooltip label="Remove from order" withArrow>
                {/* Use Box as ref, forwardRef + async button don't work nicely */}
                <Box>
                    <AsyncActionIcon
                        size={sizeProps.actionIconSize}
                        onClick={async (event) => {
                            event.stopPropagation();

                            return onRemove();
                        }}
                        color="red"
                        variant="light"
                    >
                        <IoTrashOutline size={sizeProps.trashIconSize} />
                    </AsyncActionIcon>
                </Box>
            </Tooltip>
        </Flex>
    );
};

export { QuantityField };
