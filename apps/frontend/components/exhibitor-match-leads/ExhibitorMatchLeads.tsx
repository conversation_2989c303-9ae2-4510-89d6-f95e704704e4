import { useState } from 'react';
import { useRouter } from 'next/router';

import { <PERSON><PERSON>, But<PERSON>, Spoiler, Table, Text, Tooltip } from '@mantine/core';

import { CompanyProfile, ExhibitorMatchLead } from '@repo/dcide-component-models';

import { RouterHelpers } from 'helpers/RouterHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { DateService } from 'services/DateService';
import { RouterService } from 'services/RouterService';
import { IntercomService } from 'services/IntercomService';

import { useUser } from 'hooks/use-user';
import { useTeam } from 'hooks/use-team';
import { useEvent } from 'hooks/use-event';
import { useUserInvitations } from 'hooks/use-user-invitations';
import { useExhibitorMatchLeadsForCompany } from 'hooks/use-exhibitor-match-leads-for-company';
import { useIntercomChannelForTeam } from 'components/intercom/hooks/use-intercom-channel-for-team';

import { Section } from 'components/section/Section';
import { RTEContent } from 'components/rte-content/RTEContent';
import { StartChatModal } from 'components/exhibitor-match-leads/components/StartChatModal';
import { InviteUserModal } from 'components/exhibitor-match-leads/components/InviteUserModal';

import cx from './ExhibitorMatchLead.module.css';

const ExhibitorMatchLeads = ({ company }: { company: CompanyProfile }) => {
    const { leads } = useExhibitorMatchLeadsForCompany(company.id);

    const [startChatWithUserId, setStartChatWithUserId] = useState<string | null>(null);
    const [inviteUserLeadId, setInviteUserLeadId] = useState<string | null>(null);

    if (!leads.length) return null;

    const handleStartChat = (userId: string) => {
        setStartChatWithUserId(userId);
    };

    const handleInviteUser = (lead: ExhibitorMatchLead) => {
        setInviteUserLeadId(lead.id);
    };

    return (
        <Section title="Plan Your Show Leads">
            {startChatWithUserId && (
                <StartChatModal
                    userId={startChatWithUserId}
                    company={company}
                    handleClose={() => setStartChatWithUserId(null)}
                />
            )}
            {inviteUserLeadId && (
                <InviteUserModal
                    lead={leads.find((lead) => lead.id === inviteUserLeadId)!}
                    company={company}
                    handleClose={() => setInviteUserLeadId(null)}
                />
            )}
            <Text c="dimmed" mb="sm" maw={700}>
                You are receiving these leads because your company ranked high in our exhibitor match algorithm that
                matches what users are looking for with your products and services. These leads originate from{' '}
                <Anchor href={RouterHelpers.urls.searchAssistant()} target="_blank">
                    Plan Your Show
                </Anchor>
                .
            </Text>

            <Table striped highlightOnHover withTableBorder bg="white">
                <Table.Thead>
                    <Table.Tr>
                        <Table.Th style={{ width: '50%' }}>Requirements</Table.Th>
                        <Table.Th style={{ width: '15%' }}>Date & Event</Table.Th>
                        <Table.Th style={{ width: '15%' }}>Lead</Table.Th>
                        <Table.Th style={{ width: '10%' }}>Actions</Table.Th>
                    </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                    {leads.map((lead) => (
                        <ExbitiorMatchLeadItem
                            key={lead.id}
                            lead={lead}
                            company={company}
                            handleStartChat={handleStartChat}
                            handleInviteUser={handleInviteUser}
                        />
                    ))}
                </Table.Tbody>
            </Table>
        </Section>
    );
};

const ExbitiorMatchLeadItem = ({
    lead,
    company,
    handleStartChat,
    handleInviteUser,
}: {
    lead: ExhibitorMatchLead;
    company: CompanyProfile;
    handleStartChat: (userId: string) => void;
    handleInviteUser: (lead: ExhibitorMatchLead) => void;
}) => {
    const { event } = useEvent(lead.event);

    return (
        <Table.Tr key={lead.id}>
            <Table.Td className={cx.requirements}>
                <Spoiler maxHeight={100} showLabel="read more" hideLabel="read less">
                    <RTEContent content={lead.requirements} />
                </Spoiler>
            </Table.Td>
            <Table.Td fz="sm" c="dimmed">
                {DateService.format(lead.createdAt)}
                {event && (
                    <Text inherit>
                        Event:{' '}
                        <Text span inherit fw={500} c="black">
                            {event.name}
                        </Text>
                    </Text>
                )}
            </Table.Td>
            <Table.Td>
                <UserOrEmail userId={lead.user ?? ''} email={lead.email ?? ''} />
            </Table.Td>
            <Table.Td>
                {lead.user ? (
                    <StartChatButton userId={lead.user} company={company} handleStartChat={handleStartChat} />
                ) : lead.email ? (
                    <InviteUserButton lead={lead} handleInvite={handleInviteUser} />
                ) : null}
            </Table.Td>
        </Table.Tr>
    );
};

const InviteUserButton = ({
    lead,
    handleInvite,
}: {
    lead: ExhibitorMatchLead;
    handleInvite: (lead: ExhibitorMatchLead) => void;
}) => {
    const { userInvitations } = useUserInvitations({
        email: lead.email ?? '',
        exhibitorMatchLeadId: lead.id,
    });

    if (!lead.email) return null;

    if (userInvitations.length) {
        return (
            <Button size="compact-xs" disabled variant="outline">
                Invitation sent
            </Button>
        );
    }

    return (
        <Tooltip label="Invite this user to the platform to start a chat">
            <Button size="compact-xs" onClick={() => handleInvite(lead)}>
                Invite to platform
            </Button>
        </Tooltip>
    );
};

const StartChatButton = ({
    userId,
    company,
    handleStartChat,
}: {
    userId: string;
    company: CompanyProfile;
    handleStartChat: (userId: string) => void;
}) => {
    const { asPath } = useRouter();

    const { user } = useUser(userId);
    const { team } = useTeam(user?.team);

    const { channel } = useIntercomChannelForTeam('company', company.id, team?.id);

    const handleOpenChat = () => {
        if (!channel) return;

        IntercomService.open(channel.id);

        const companyUrl = CompanyProfileHelpers.urls.view(company.slug);

        if (!asPath.includes(companyUrl)) {
            RouterService.push(companyUrl).then();
        }
    };

    if (channel)
        return (
            <Button size="compact-xs" onClick={handleOpenChat}>
                Open chat
            </Button>
        );

    return (
        <Button size="compact-xs" onClick={() => handleStartChat(userId)}>
            Start chat
        </Button>
    );
};

const UserOrEmail = ({ userId, email }: { userId?: string; email?: string }) => {
    const { user } = useUser(userId);

    if (user) {
        return <Text fz="sm">{user.name || user.email}</Text>;
    }

    if (email) {
        return (
            <Text fz="sm" c="dimmed">
                {email.split('@')[0]}@...
            </Text>
        );
    }

    return (
        <Text fz="sm" c="dimmed">
            No email provided
        </Text>
    );
};

export { ExhibitorMatchLeads };
