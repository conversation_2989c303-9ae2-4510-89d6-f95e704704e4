/* Define CSS custom properties for your breakpoints,
   values must be the same as in your theme */
:root {
    --mantine-breakpoint-xs: 30em;
    --mantine-breakpoint-sm: 48em;
    --mantine-breakpoint-md: 64em;
    --mantine-breakpoint-lg: 74em;
    --mantine-breakpoint-xl: 90em;
    --mantine-breakpoint-xxl: 100em;
}

/* Note: SCSS mixins and functions have been removed.
   You'll need to use plain CSS or PostCSS alternatives.

   For rem() function, you can use calc() or define values directly.
   For mixins, you'll need to write the CSS directly where needed.

   Example replacements:
   - @include light { ... } becomes [data-mantine-color-scheme='light'] & { ... }
   - @include dark { ... } becomes [data-mantine-color-scheme='dark'] & { ... }
   - @include hover { ... } becomes @media (hover: hover) { &:hover { ... } } @media (hover: none) { &:active { ... } }
   - @include smaller-than($breakpoint) becomes @media (max-width: var(--mantine-breakpoint-*)) { ... }
   - @include larger-than($breakpoint) becomes @media (min-width: var(--mantine-breakpoint-*)) { ... }
   - @include rtl { ... } becomes [dir='rtl'] & { ... }
   - @include ltr { ... } becomes [dir='ltr'] & { ... }
*/
