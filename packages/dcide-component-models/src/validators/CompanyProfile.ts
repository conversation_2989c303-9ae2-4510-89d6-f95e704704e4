import { z } from 'zod';

import { files } from './component-sections';
import { AddressSchema } from './Address';
import { RTESchema } from './RTE';
import { ImageSchema } from './Image';
import { StubSchema } from './Stub';
import { VideoSchema } from './Video';
import { PublishedStatus } from '../types/PublishedStatus';
import { PowerLevel } from './fields/PowerLevel';
import { CompanySubscription, CompanySubscriptionSchema } from '../types/Subscription';
import { ProjectBudget } from './fields/ProjectBudget';

export enum PermissionCompany {
    VIEW = 'company.view',
    EDIT = 'company.edit',
    ALL = 'company.all',
}

// TODO: remove after migration
export enum CompanyProfileType {
    MANUFACTURER = 'manufacturer',
    DISTRIBUTOR = 'distributor',
    REPLUS = 'replus',
    ORGANIZATION = 'organization',
    DESIGNER = 'designer', // system integrator, engineering firms etc.
}

// TODO: remove after migration
export const CompanyProfileTypeOptions = [
    {
        label: 'Manufacturer',
        value: CompanyProfileType.MANUFACTURER,
    },
    {
        label: 'Distributor',
        value: CompanyProfileType.DISTRIBUTOR,
    },
    {
        label: 'RE+ event',
        value: CompanyProfileType.REPLUS,
    },
    {
        label: 'Organization',
        value: CompanyProfileType.ORGANIZATION,
    },
    {
        label: 'Designer',
        value: CompanyProfileType.DESIGNER,
    },
];

export enum CompanyService {
    MANUFACTURING = 'manufacturing',
    DISTRIBUTION = 'distribution',
    IN_APP_SUPPORT = 'inAppSupport',
    ENGINEERING = 'engineering',
    INSTALLATION = 'installation',
    INTEGRATOR = 'integrator',
    ENGINEERING_PROCUREMENT_CONSTRUCTION = 'engineeringProcurementConstruction',
    ORGANIZATION = 'organization',
    REPLUS = 'replus',
    OTHER = 'other',
}

export const CompanyServiceOptions = [
    {
        value: CompanyService.MANUFACTURING,
        label: 'Manufacturer',
        description: 'We manufacture products and want to add them to the online product catalog.',
    },
    {
        value: CompanyService.ENGINEERING,
        label: 'Architecture & Engineering services',
        description:
            'We provide expert guidance (technical, financial, or regulatory) services to design and engineer microgrid projects.',
    },
    {
        value: CompanyService.INSTALLATION,
        label: 'Installer',
        description:
            'We perform the physical setup (mounting solar arrays, wiring batteries, etc.) according to the approved design.',
    },
    {
        value: CompanyService.INTEGRATOR,
        label: 'System integrator',
        description:
            'We orchestrate various hardware, software, and communication layers into a cohesive, functional microgrid.',
    },
    {
        value: CompanyService.DISTRIBUTION,
        label: 'Distributor',
        description:
            'We handle the supply chain and logistics, delivering key microgrid components to the installation site.',
    },
    {
        value: CompanyService.ENGINEERING_PROCUREMENT_CONSTRUCTION,
        label: 'EPC firm',
        description:
            'We provide turnkey solutions for microgrid projects, handling engineering, procurement, and construction.',
    },
    {
        value: CompanyService.IN_APP_SUPPORT,
        label: 'Enable in-app support',
        description: 'Connect with customers via in-app support on your profile, product pages and reference designs.',
    },
    {
        value: CompanyService.OTHER,
        label: 'Other',
        description: 'Please describe the services you provide in the "About" section.',
    },
];

const ContactSchema = z.object({
    email: z.string().default(''),
    phone: z.string().default(''),
});

const CompanySizeSchema = z.union([
    z.literal('Self-employed'),
    z.literal('1-10 employees'),
    z.literal('11-50 employees'),
    z.literal('51-200 employees'),
    z.literal('201-500 employees'),
    z.literal('501-1000 employees'),
    z.literal('1001-5000 employees'),
    z.literal('5001-10,000 employees'),
    z.literal('10,001+ employees'),
]);

const CompanyProfileContactSchema = z.object({
    id: z.string().optional().default(''),
    name: z.string(),
    email: z.string().optional().default(''),
    phone: z.string().optional().default(''),
    position: z.string().optional().default(''),
    image: z.string().optional().nullable().default(''),
});

const CompanyLocationSchema = z.object({
    id: z.string().optional(),
    name: z.string().default('').optional(),
    description: RTESchema.optional().nullable(),
    image: z.string().default('').nullable(),
    address: AddressSchema.optional().nullable(),
    isHeadquarter: z.boolean().optional().default(false),
    contactInformation: ContactSchema.optional(),
    technicalSupport: ContactSchema.optional(),
});

const CompanyProfileBaseSchema = z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().default(''),
    logos: z
        .object({
            small: z.union([z.string(), ImageSchema.nullable().default(null)]).optional(),
            large: z.union([z.string(), ImageSchema.nullable().default(null)]).optional(),
        })
        .default({}),
    createdAt: z.string().optional(),
    createdBy: z.string().optional(),
    updatedAt: z.string().optional(),
    publishedAt: z.string().optional(),
    internal: z.boolean().optional(),
    imported: z.boolean().optional(),
    embedding: z.array(z.number()).default([]),
    subscription: CompanySubscriptionSchema.optional().default(CompanySubscription.NONE).nullable(),
});

const CompanyProfileSchema = CompanyProfileBaseSchema.extend({
    systemSize: PowerLevel.validator.optional(),
    projectBudget: ProjectBudget.validator.optional(),
    color: z.string().default(''),
    cover: z.union([z.string(), ImageSchema.nullable().default(null)]),
    highlights: z.array(StubSchema).default([]),
    about: RTESchema.optional().nullable(),
    mission: RTESchema.optional().nullable(),
    foundedIn: z.string().default(''),
    companySize: CompanySizeSchema.optional(),
    certificates: z.string().default(''),
    website: z.union([z.string().url('URL should start with https://').nullable(), z.literal('')]).default(null),
    locations: z.array(CompanyLocationSchema).default([]),
    socials: z
        .object({
            linkedin: z.string().default(''),
            facebook: z.string().default(''),
            twitter: z.string().default(''),
            youtube: z.string().default(''),
        })
        .optional(),
    contactPeople: z.array(CompanyProfileContactSchema).default([]),
    highlightedComponents: z.array(z.string()).default([]),
    highlightedProjects: z.array(z.string()).default([]),
    partners: z
        .array(
            z.object({
                id: z.string().optional(),
                company: z.string(),
                name: z.string().optional().nullable(),
                description: z.string().optional().nullable(),
                linkedin: z.string().optional().nullable(),
                logo: z
                    .union([z.string(), ImageSchema.nullable().default(null)])
                    .optional()
                    .nullable(),
            }),
        )
        .default([]),
    partnersCrossReference: z.array(z.string()).default([]),
    caseStudies: z.array(StubSchema).default([]),
    promos: z.array(StubSchema).default([]),
    videos: z.array(VideoSchema).default([]),
    files,
    slug: z.string().default(''),
    team: z.string().optional().default(''),
    status: z.nativeEnum(PublishedStatus).default(PublishedStatus.DRAFT),
    services: z.array(z.nativeEnum(CompanyService)).default([]),
    serviceTags: z.string().array().optional().nullable(),
    applicationTags: z.string().array().optional().nullable(),
    verified: z.boolean().default(false),
    users: z.string().array().default([]),
    permissions: z.array(z.nativeEnum(PermissionCompany)).optional().default([]),
    publishedBy: z.string().optional(),
    publishedAt: z.string().optional(),
    completeness: z.number().optional(),
    compliance: z
        .object({
            currentOS: z.boolean().optional().default(false),
            emergeAlliance: z.boolean().optional().default(false),
            ODCA: z.boolean().optional().default(false),
            other: z.boolean().optional().default(false),
            otherInput: z.string().optional(),
        })
        .optional(),
    subscription: z.nativeEnum(CompanySubscription).optional().nullable().default(CompanySubscription.NONE),
    admin: z
        .object({
            note: z.string().optional().nullable(),
        })
        .optional(),
});

export { CompanyProfileBaseSchema, CompanyProfileSchema, CompanyProfileContactSchema, CompanyLocationSchema };
