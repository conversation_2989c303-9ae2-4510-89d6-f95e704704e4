import React from 'react';

import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    UnitWithTooltip,
    useRenderVariableMultiInput,
    MeasurementInputWrapper,
} from 'components/forms/fields/measurement';
import { MeasurementFieldType, voltageConverter } from '@repo/dcide-component-models';

export type VoltageFieldProps = {
    name: string;
    fields?: MeasurementFieldType[];
    hideIcons?: boolean;
    placeholder?: string;
    disabled?: boolean;
} & MeasurementInputWrapperProps;

const VoltageField = ({
    name,
    fields = ['min', 'nom', 'max'],
    hideIcons,
    placeholder,
    disabled,
    ...wrapperProps
}: VoltageFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: fieldsRecord,
    } = useMultiUnitMeasurementController({ name, converter: voltageConverter });

    const Unit = <UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>;
    const Fields = useRenderVariableMultiInput({
        fields,
        fieldsRecord,
        Unit,
        extraProps: {
            hideIcon: hideIcons,
            size: wrapperProps.size,
            placeholder,
            disabled,
        },
    });

    return <MeasurementInputWrapper {...wrapperProps}>{Fields}</MeasurementInputWrapper>;
};

export { VoltageField };
