import { CollectionConfig } from 'payload';
import { CreatedBy } from '@/fields';

import { broadcast } from './hooks/broadcast';
import { cancelPendingQuestions } from './hooks/cancel-pending-questions';
import { generateAnswer } from './hooks/generate-answer';

import { AccessChecks } from '../projects/access-checks';

import { prompt } from './prompt';

const DiagramAIMessages: CollectionConfig = {
    slug: 'diagramAIMessages',
    labels: {
        singular: 'Diagram AI Message',
        plural: 'Diagram AI Messages',
    },
    access: {
        create: ({ req }) => {
            return req.user
                ? {
                      or: [
                          AccessChecks.userIsPartOfTeam(req.user, 'project'),
                          AccessChecks.userIsACollaborator(req.user, 'project'),
                      ],
                  }
                : false;
        },
        read: ({ id, req }) => {
            if (id && AccessChecks.userIsDeveloper(req.user)) {
                return true;
            }

            return req.user
                ? {
                      or: [
                          AccessChecks.userIsPartOfTeam(req.user, 'project'),
                          AccessChecks.userIsACollaborator(req.user, 'project'),
                      ],
                  }
                : false;
        },
        update: ({ req }) => {
            return AccessChecks.userIsDeveloper(req.user);
        },
        delete: ({ req }) => {
            return AccessChecks.userIsDeveloper(req.user);
        },
    },
    fields: [
        {
            name: 'question',
            type: 'textarea',
        },
        {
            name: 'answer',
            type: 'textarea',
        },
        {
            name: 'project',
            type: 'relationship',
            relationTo: 'projects',
            hasMany: false,
            required: true,
            maxDepth: 0,
        },
        {
            name: 'diagram',
            type: 'relationship',
            relationTo: 'projectDesignDiagrams',
            hasMany: false,
            required: true,
            maxDepth: 0,
        },
        {
            name: 'diagramSnapshot',
            type: 'json',
            required: true,
        },
        {
            name: 'prompt',
            type: 'group',
            fields: [
                {
                    name: 'system',
                    type: 'textarea',
                },
                {
                    name: 'user',
                    type: 'textarea',
                },
            ],
        },
        {
            name: 'status',
            type: 'select',
            options: [
                { value: 'pending', label: 'Pending' },
                { value: 'processing', label: 'Processing' },
                { value: 'done', label: 'Done' },
                { value: 'cancelled', label: 'Cancelled' },
                { value: 'error', label: 'Error' },
            ],
            defaultValue: 'pending',
        },
        CreatedBy,
    ],
    hooks: {
        beforeChange: [
            async ({ data, operation, req }) => {
                if (operation === 'create') {
                    data.diagramSnapshot = await req.payload.findByID({
                        collection: 'projectDesignDiagrams',
                        id: data.diagram,
                        depth: 0,
                        req,
                    });

                    data.prompt = {
                        system: prompt.system(),
                        user: prompt.user(data.question),
                    };
                }
            },
        ],
        afterChange: [cancelPendingQuestions, generateAnswer, broadcast],
        afterRead: [
            async ({ req, doc }) => {
                if (req.href) {
                    // Don't send the diagram snapshot to the client
                    delete doc.diagramSnapshot;
                    delete doc.prompt;
                }
            },
        ],
    },
};

export default DiagramAIMessages;
