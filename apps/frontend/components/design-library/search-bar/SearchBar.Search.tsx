import React from 'react';

import { Menu, Kbd } from '@mantine/core';
import { IoSearchOutline } from 'react-icons/io5';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import cx from './SearchBar.module.css';

const SearchBarSearch = () => {
    const { searchBarQuery } = useDesignLibrarySearch();

    if (!searchBarQuery) return null;

    return (
        <Menu.Item
            className={cx.section}
            leftSection={<IoSearchOutline />}
            onClick={() => {
                DesignLibraryService.setFilter('search', searchBarQuery);
                DesignLibraryService.clearSearchBarQuery();
            }}
        >
            <Kbd size="xs">↵ Enter</Kbd> to search for <b>&quot;{searchBarQuery}&quot;</b> in design name.
        </Menu.Item>
    );
};

export { SearchBarSearch };
