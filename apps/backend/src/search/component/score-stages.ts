import { PipelineStage, Types } from 'mongoose';

import {
    ENERGY_DEFAULT_UNIT,
    ComponentQuery,
    ComponentSearchScore,
    energyConverter,
} from '@repo/dcide-component-models';

import { ComponentSearchState } from '@/search/types';
import { getWeightedAverageScoreFieldStages, WeightedScoreField } from '@/search/helpers/query-helpers';
import { getConditionalAbsoluteNormalizedDifference } from './port-stages/port-match-score-helpers';
import { getPremiumManufacturerScoreField } from '../helpers/get-premium-manufacturer-score-field';
import { booleanScore, fieldInArray } from '../helpers/query-logic-helpers';

const DEFAULT_WEIGHTS: Record<ComponentSearchScore, number> = {
    [ComponentSearchScore.PREMIUM_MANUFACTURER]: 10,
    [ComponentSearchScore.PORT_MATCH_SCORE]: 8,
    [ComponentSearchScore.ENERGY_CAPACITY_SCORE]: 8,
    [ComponentSearchScore.COMPATIBLE_PRODUCT]: 7,
    [ComponentSearchScore.TEXT_SCORE]: 6,
    [ComponentSearchScore.VECTOR_SCORE]: 5,
    [ComponentSearchScore.SORT_RANK]: 4,
    [ComponentSearchScore.COMPLETENESS]: 3,
    [ComponentSearchScore.PRIORITY_MANUFACTURER]: 0,
    [ComponentSearchScore.MANUFACTURER_FOR_ACTIVE_EVENTS]: 1,
};

export const getScoreStages = (search: ComponentSearchState) => {
    const query = search.query;
    const scoreStages: PipelineStage[] = [];
    const weightedFields: WeightedScoreField[] = [];
    const weights = { ...DEFAULT_WEIGHTS, ...(search.customWeights ?? {}) };

    if (search.hasTextSearch) {
        weightedFields.push({
            name: 'searchScore',
            weight: weights[ComponentSearchScore.TEXT_SCORE],
            normalize: true,
        });
    }

    if (search.hasVectorSearch) {
        weightedFields.push({
            name: 'vectorSearchScore',
            weight: weights[ComponentSearchScore.VECTOR_SCORE],
            normalize: true,
        });
    }

    if (search.hasPortFilters) {
        weightedFields.push({
            name: 'portMatchScore',
            weight: weights[ComponentSearchScore.PORT_MATCH_SCORE],
            inverse: true,
        });
    }

    if (search.hasEnergyCapacity) {
        const energyCapacityStoredUnits = energyConverter.convert({
            value: search.query.energyCapacity!.value!,
            from: search.query.energyCapacity!.unit,
            to: ENERGY_DEFAULT_UNIT,
        });
        scoreStages.push(addEnergyCapacityScoreField(energyCapacityStoredUnits.value));
        weightedFields.push({
            name: 'energyCapacityScore',
            weight: weights[ComponentSearchScore.ENERGY_CAPACITY_SCORE],
            inverse: true,
        });
    }

    if (
        'productsForCompatibility' in query &&
        query.productsForCompatibility &&
        query.productsForCompatibility.length > 0
    ) {
        scoreStages.push(addCompatibleProductField(query.productsForCompatibility));
        weightedFields.push({
            name: 'isCompatibleProduct',
            weight: weights[ComponentSearchScore.COMPATIBLE_PRODUCT],
        });
    }

    if (!query.manufacturer) {
        addMultiManufacturerScoreStages(query, scoreStages, weightedFields, weights);
    }

    weightedFields.push({
        name: 'completeness',
        // Completeness is between 0 and 100, divide the weight by 100 to get a value between 0 and 1
        weight: weights[ComponentSearchScore.COMPLETENESS] / 100,
    });

    weightedFields.push({
        name: 'sortRank',
        weight: weights[ComponentSearchScore.SORT_RANK],
    });

    scoreStages.push(...getWeightedAverageScoreFieldStages(weightedFields));

    return scoreStages;
};

const addMultiManufacturerScoreStages = (
    query: ComponentQuery,
    scoreStages: PipelineStage[],
    weightedFields: WeightedScoreField[],
    weights: Record<ComponentSearchScore, number>,
) => {
    if (
        'manufacturersForActiveEvents' in query &&
        query.manufacturersForActiveEvents &&
        query.manufacturersForActiveEvents.length > 0
    ) {
        scoreStages.push(addManufacturerForActiveEventsField(query.manufacturersForActiveEvents));
        weightedFields.push({
            name: 'isManufacturerForActiveEvent',
            weight: weights[ComponentSearchScore.MANUFACTURER_FOR_ACTIVE_EVENTS],
        });
    }

    if ('priorityManufacturer' in query && query.priorityManufacturer) {
        scoreStages.push(addPriorityManufacturerField(query.priorityManufacturer));
        weightedFields.push({
            name: 'isPriorityManufacturer',
            weight: weights[ComponentSearchScore.PRIORITY_MANUFACTURER],
        });
    }

    scoreStages.push(getPremiumManufacturerScoreField('manufacturerDetails'));

    weightedFields.push({
        name: 'isPremiumManufacturer',
        weight: weights[ComponentSearchScore.PREMIUM_MANUFACTURER],
    });
};

const addManufacturerForActiveEventsField = (manufacturerIds: string[]): PipelineStage => ({
    $addFields: {
        isManufacturerForActiveEvent: booleanScore(
            fieldInArray(
                'manufacturer',
                manufacturerIds.map((id) => new Types.ObjectId(id)),
            ),
        ),
    },
});

const addEnergyCapacityScoreField = (queryEnergyCapactity: number): PipelineStage => ({
    $addFields: {
        energyCapacityScore: getConditionalAbsoluteNormalizedDifference(
            queryEnergyCapactity,
            '$electrical.energyCapacity.value',
        ),
    },
});

const addPriorityManufacturerField = (manufacturerId: string): PipelineStage => ({
    $addFields: {
        isPriorityManufacturer: booleanScore({ $eq: ['$manufacturer', new Types.ObjectId(manufacturerId)] }),
    },
});

const addCompatibleProductField = (compatibleProducts: string[]): PipelineStage => ({
    $addFields: {
        isCompatibleProduct: booleanScore(fieldInArray('compatibleWithCrossReference', compatibleProducts)),
    },
});
