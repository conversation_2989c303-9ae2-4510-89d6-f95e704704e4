import { useEffect } from 'react';

import { useRouter } from 'next/router';
import { GetServerSideProps } from 'next/types';

import { Skeleton } from '@mantine/core';

import { UserReferrer } from '@repo/dcide-component-models';

import { config } from 'config';

import { getSession } from 'helpers/getSession';
import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { ComponentHelpers } from 'helpers/ComponentHelpers';

import { useLocalUserInfo } from 'hooks/use-local-user-info';

import { Page } from 'components/page';
import { GridSection } from 'components/section/GridSection';
import { TeaserLoader } from 'components/loaders/TeaserLoader';

const DashboardPage = () => {
    const router = useRouter();

    const { referrer, initialized } = useLocalUserInfo();

    useEffect(() => {
        if (!initialized) return;

        if (referrer === UserReferrer.REPLUS) {
            router.replace(ComponentHelpers.urls.overview());
            return;
        }

        router.replace(ProjectHelpers.urls.overview());
    }, [initialized, referrer]);

    return (
        <Page>
            <Page.WideContent>
                <Skeleton height={200} />
                <GridSection nbCols={4}>
                    <TeaserLoader />
                    <TeaserLoader />
                    <TeaserLoader />
                    <TeaserLoader />
                </GridSection>
            </Page.WideContent>
        </Page>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    const session = await getSession(req, res);

    if (!session.user) {
        return {
            redirect: {
                destination: config.urls.landingPage,
                permanent: false,
            },
        };
    }

    return {
        props: {},
    };
};

export default DashboardPage;
