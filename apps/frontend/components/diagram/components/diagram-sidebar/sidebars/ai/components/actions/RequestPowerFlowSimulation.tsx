import React, { <PERSON> } from 'react';
import { DiagramAIMessage } from 'components/diagram/services/AIConversationService';

import { Box, Button, Paper, Stack } from '@mantine/core';
import { JobTeaser } from 'components/diagram/components/diagram-jobs/JobTeaser';

import { SidebarService } from 'components/diagram/services/SidebarService';

import { useSimulationsData } from 'components/diagram/hooks/use-simulations';

const RequestPowerFlowSimulation: FC<{
    message: DiagramAIMessage;
    action: {
        action: 'requestPowerFlowSimulation';
        [k: string]: any;
    };
}> = () => {
    const { all } = useSimulationsData();
    const latestSimulation = all[0];

    const openSimulations = () => {
        SidebarService.openSimulationsSidebar();
    };

    return (
        <Paper mt="xs" p="sm" radius="md" shadow="md">
            {latestSimulation ? (
                <JobTeaser type="simulation" job={latestSimulation} active={false} isLatest />
            ) : (
                <Stack gap="xs" align="flex-start">
                    <Box>Manage your simulations in the simulations sidebar</Box>
                    <Button size="xs" variant="gradient" onClick={openSimulations}>
                        Go to simulations
                    </Button>
                </Stack>
            )}
        </Paper>
    );
};

export { RequestPowerFlowSimulation };
