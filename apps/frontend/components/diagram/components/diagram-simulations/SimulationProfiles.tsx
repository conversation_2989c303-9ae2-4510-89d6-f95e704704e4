import React, { FC } from 'react';

import {Detail} from './components/Detail';
import {Overview} from './components/Overview';

import {useSimulationProfilesState} from 'components/diagram/hooks/use-simulation-profiles-state';

const SimulationProfiles = () => {
    const { active } = useSimulationProfilesState();

    return active ? (
        <Detail id={active} />
    ) : (
        <Overview />
    );
};

export { SimulationProfiles };
