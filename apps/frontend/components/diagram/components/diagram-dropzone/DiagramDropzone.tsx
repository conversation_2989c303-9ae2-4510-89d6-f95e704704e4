import { FC, ReactNode, useRef } from 'react';

import { Box } from '@mantine/core';
import { useDropzone } from 'react-dropzone-esm';
import { useCheckFeatureLimit } from 'hooks/use-check-feature-limit';

import { DiagramService } from 'components/diagram/services';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { ImageService } from 'components/diagram/services/ImageService';

import cx from './DiagramDropzone.module.css';
import { FileService } from 'components/diagram/services/FileService';
import { FeatureLimit } from '@repo/dcide-component-models';
import { openContextModal } from '@mantine/modals';
import { useCurrentDesign } from 'hooks/use-current-design';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { FileSizeLimitExceededError } from 'services/FileService';

const DiagramDropzone: FC<{ children: ReactNode }> = ({ children }) => {
    const design = useCurrentDesign();
    const designId = design?.id;
    const diagramId = DiagramService.getId();

    const { status: imagesStatus } = useCheckFeatureLimit(FeatureLimit.IMAGES, diagramId);
    const limitReachedImages = !imagesStatus?.pass;

    const { status: filesStatus } = useCheckFeatureLimit(FeatureLimit.DESIGN_FILE_UPLOADS, diagramId);
    const limitReachedFiles = !filesStatus?.pass;

    const mouse = useRef({
        x: 0,
        y: 0,
    });

    const { getRootProps, isDragActive } = useDropzone({
        onDragOver: (event) => {
            mouse.current = {
                x: event.clientX,
                y: event.clientY,
            };
        },
        onDrop: async (files: File[]) => {
            if (!designId) return;

            const file = files[0];

            if (file && file.type.includes('image/')) {
                if (limitReachedImages) {
                    openContextModal({
                        modal: 'featureLimitTracker',
                        innerProps: {
                            feature: FeatureLimit.IMAGES,
                            diagramId: diagramId,
                        },
                        withCloseButton: false,
                    });

                    return;
                }

                try {
                    await ImageService.add({
                        file,
                        position: {
                            x: mouse.current.x,
                            y: mouse.current.y - 45, // 45 = header height.
                        },
                    });

                    DiagramSyncService.save();
                } catch (error) {
                    if (error instanceof FileSizeLimitExceededError) {
                        LocalNotificationService.showError({ title: error.title, message: error.message });
                    }
                }

                return;
            }

            if (limitReachedFiles) {
                openContextModal({
                    modal: 'featureLimitTracker',
                    innerProps: {
                        feature: FeatureLimit.DESIGN_FILE_UPLOADS,
                        diagramId: diagramId,
                    },
                    withCloseButton: false,
                });

                return;
            }

            FileService.openAddDesignFileModal(designId, file);
        },
        multiple: false,
        noClick: true,
        noKeyboard: true,
        disabled: !designId,
    });

    return (
        <Box className={cx.wrapper} {...getRootProps()}>
            {children}
            <Box className={cx.overlay} data-active={isDragActive} />
        </Box>
    );
};

export { DiagramDropzone };
