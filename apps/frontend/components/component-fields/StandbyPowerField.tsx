import React from 'react';

import { powerConverter } from '@repo/dcide-component-models';
import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    MeasurementInputWrapper,
    MeasurementInput,
    UnitWithTooltip,
} from 'components/forms/fields/measurement';

type StandbyPowerFieldProps = { name: string } & MeasurementInputWrapperProps;

const StandbyPowerField = ({ name, ...wrapperProps }: StandbyPowerFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: { value },
    } = useMultiUnitMeasurementController({ name, converter: powerConverter });

    return (
        <MeasurementInputWrapper {...wrapperProps}>
            <MeasurementInput {...value} unit={<UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>} />
        </MeasurementInputWrapper>
    );
};

export { StandbyPowerField };
