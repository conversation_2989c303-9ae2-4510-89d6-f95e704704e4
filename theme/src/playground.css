html,
body {
    scroll-behavior: smooth;
}

[data-header-carousel] {
    --header-carousel-active-slide: 1;

    transition: transform .333s ease-in-out;
    transform: translateX(calc(-1 * (var(--header-carousel-active-slide) - 1) * 100vw));
}

.prose {
    > * + * {
        margin-top: 1em;
    }

    ul {
        margin-left: 2em;
        list-style: disc;
    }

    li + li {
        margin-top: calc(1em / 8 * 3);
    }
}
