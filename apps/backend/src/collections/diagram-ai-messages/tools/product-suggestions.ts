import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

const name = 'getProductSuggestions';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: '',
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {
    showProductCatalog: {
        name: 'showProductCatalog',
        action: () => `
            === ACTION ===
            {
                "action": "showProductCatalog",
            }
            === END ACTION ===
        `,
    },
    showProductSuggestions: {
        name: 'showProductSuggestions',
        action: (componentInstanceId: string) => `
            === ACTION ===
            {
                "action": "showProductSuggestions",
                "componentInstanceId": "${componentInstanceId}"
            }
            === END ACTION ===
        `,
    },
} as const;

const handler = async (message: DiagramAIMessage) => {
    return '';
};

const prompt = `
## Suggesting Products

This section activates only if the user explicitly asks a question or makes a request
directly related to suggesting products.

**Tool Calling:**
*Mandatory*: You must call the ${name} function.
This function will return information about any validation errors found or confirm that no errors were detected.

**Possible Triggers:**
* Can you help me find a product for COMPONENT-X
* Can you suggest a battery for my diagram
* Can you recommend a product for this use case

**Possible Responses:**
Based on what the user asks, you can respond in one of the following ways:

*   **Suggesting Products for a Specific Component:**

    When the user asks for products related to a specific component in the diagram:
    * Return the "${actions.showProductSuggestions.name}" action with the component instance ID

*   **Suggesting Products in General:**

    When the user asks for products in general:
    * Return the "${actions.showProductCatalog.name}" action

**Notes:**
* These actions will open another tool sidebar that allows the user to browse products and add them to the diagram
`;

const productSuggestions = {
    name,
    definition,
    actions,
    handler,
    prompt,
};

export { productSuggestions };
