import Link from 'next/link';

import { Box } from '@mantine/core';

import cx from './FinishAction.module.css';

const FinishAction = ({
    href,
    icon,
    isFocus,
    children,
}: {
    href: string;
    icon: React.ReactNode;
    isFocus?: boolean;
    children: React.ReactNode;
}) => {
    const content = (
        <Box component={Link} href={href} className={cx.action} target="_blank">
            <Box className={cx.icon}>{icon}</Box>
            {children}
        </Box>
    );

    if (isFocus) {
        return <Box className={`${cx.animationWrapper} animation-border-spin`}>{content}</Box>;
    }

    return content;
};

export { FinishAction };
