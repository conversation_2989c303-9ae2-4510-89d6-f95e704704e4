import React, { useEffect } from 'react';
import { useWatch } from 'react-hook-form';

import { IoBatteryHalfOutline } from 'react-icons/io5';

import { DesignLibraryService } from 'services/DesignLibraryService';

import { FormatHelpers } from 'helpers/formatters';
import { EnergyBaseUnit, EnergyConverter, EnergyDefaultViewUnit } from 'units/energy';

import { CapacityField } from 'components/component-fields/CapacityField';
import { useDesignLibrarySearch } from 'components/design-library/hooks/use-design-library-search';

import { InlineFilters } from './InlineFilters';

const CAPACITY_FIELD_NAME = 'storage';

const InlineFiltersEnergyCapacity = () => {
    const energyCapacityInput = useWatch({
        name: CAPACITY_FIELD_NAME,
    });

    const {
        filters: { storage },
    } = useDesignLibrarySearch();

    useEffect(() => {
        if (energyCapacityInput?.value) {
            const baseUnitValue = EnergyConverter(energyCapacityInput.value)
                .from(EnergyDefaultViewUnit)
                .to(EnergyBaseUnit);

            DesignLibraryService.setFilter(CAPACITY_FIELD_NAME, baseUnitValue);
        }
    }, [energyCapacityInput]);

    const formattedEnergyCapacity = FormatHelpers.formatValue(storage, EnergyConverter, EnergyBaseUnit);

    return (
        <InlineFilters.SectionWithIcon
            icon={<IoBatteryHalfOutline />}
            label={storage ? 'Storage' : ''}
            onRemove={
                storage
                    ? () => {
                          DesignLibraryService.setFilter(CAPACITY_FIELD_NAME, undefined);
                      }
                    : undefined
            }
            body={
                <InlineFilters.MeasurementWrapper
                    placeholder="Storage"
                    label={formattedEnergyCapacity ? formattedEnergyCapacity : undefined}
                >
                    <CapacityField name={CAPACITY_FIELD_NAME} size="xs" hideIcons />
                </InlineFilters.MeasurementWrapper>
            }
            active={!!storage}
        />
    );
};
export { InlineFiltersEnergyCapacity };
