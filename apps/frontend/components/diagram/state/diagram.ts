import { DiagramComponentInstance, DiagramConnection } from '@repo/dcide-component-models';

import { proxy, subscribe } from 'valtio';
import { derive } from 'derive-valtio';

import { defaultDiagram } from 'components/diagram/data/default-diagram';

import { DiagramSyncService } from '../services/DiagramSyncService';
import { isEqual, isObject } from 'radash';

export const diagram = proxy(defaultDiagram());

if (typeof window !== 'undefined') {
    // @ts-ignore
    window.internalSubscribeThing = subscribe;
    // @ts-ignore
    window.internalDiagramProxy = diagram;
}

export const derivedDiagramState = derive({
    connectionKeys: (get) => {
        const connections = get(diagram.connections);

        return Object.keys(connections);
    },
    connectionsAsArray: (get) => {
        const connections = get(diagram.connections);

        return Object.values(connections) as DiagramConnection[];
    },
    connectionAnchorLookup: (get) => {
        const connections = get(diagram.connections);

        const lookup: {
            [key: string]: DiagramConnection;
        } = {};

        const createKey = (anchor: DiagramConnection['from']) => {
            return `${anchor.componentInstanceId}-${anchor.edge}-${anchor.offset}`;
        };

        Object.values(connections).forEach((connection) => {
            lookup[createKey(connection.from)] = connection;
            lookup[createKey(connection.to)] = connection;
        });

        return lookup;
    },
    componentInstanceKeys: (get) => {
        const componentInstances = get(diagram.componentInstances);

        return Object.keys(componentInstances);
    },
    componentInstancesAsArray: (get) => {
        const componentInstances = get(diagram.componentInstances);

        return Object.values(componentInstances) as DiagramComponentInstance[];
    },
});

subscribe(
    diagram,
    (changes) => {
        changes.forEach((change) => {
            const [type, paths, value_, previousValue_] = change;

            const value = value_ === undefined ? undefined : JSON.parse(JSON.stringify(value_));
            const previousValue = previousValue_ === undefined ? undefined : JSON.parse(JSON.stringify(previousValue_));

            const path = paths.join('.');

            if (!path) return;

            if (isEqual(value, previousValue)) return;

            if (isObject(value) && Object.keys(value).length > 0 && !process.env.TEST) {
                // Maybe throw an error in the future
                console.warn(
                    `value is an object with keys. Use set(${path}, value) instead. value: ${JSON.stringify(value)}`,
                );
            }

            const skippable = [/textareas\.[0-9]+\.content/].filter((regex) => regex.test(path)).length > 0;

            if (isObject(previousValue) && isObject(value) && Object.keys(value).length > 0 && !skippable) {
                throw new Error(
                    `value cannot be an object to update ${path} Use set(${path}, value) instead or update key-value pairs individually. value: ${JSON.stringify(value)} previousValue: ${JSON.stringify(previousValue)}`,
                );
            }

            if (type === 'set') {
                if (previousValue === undefined) {
                    DiagramSyncService.create(path, value, false);
                } else {
                    DiagramSyncService.updateByKey(path, value, false, previousValue);
                }
            } else if (type === 'delete') {
                DiagramSyncService.delete(path, false, previousValue);
            }
        });
    },
    true,
);
