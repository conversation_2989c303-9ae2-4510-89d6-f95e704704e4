import React, { FC } from 'react';
import { Box, Image, SimpleGrid } from '@mantine/core';

import { Component } from '@repo/dcide-component-models';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { useFile } from 'hooks/use-file';

import cx from './ComponentImages.module.css';
import { useDraggable } from '@dnd-kit/core';
import { useElementSize } from '@mantine/hooks';

const ComponentImages: FC<{
    component: Component;
}> = ({ component }) => {
    const images = component.images.filter((image) => !!image.file);

    return (
        <DiagramSidebar.Section
            title="Images"
            collapsable={{
                enabled: true,
                key: 'diagram.component-images',
                initial: false,
            }}
        >
            <SimpleGrid cols={3} spacing="xs">
                {images.map((image) => (
                    <ComponentImage image={image} key={image.type} />
                ))}
            </SimpleGrid>
        </DiagramSidebar.Section>
    );
};

const ComponentImage: FC<{ image: any }> = ({ image }) => {
    const { ref: wrapper, width, height } = useElementSize();
    const { file } = useFile(image.file);

    const { listeners, attributes, transform, setNodeRef } = useDraggable({
        id: `component-image-${image.type}`,
        data: {
            type: 'component-image',
            file: file,
        },
    });

    const wrapperBoundingClientRect = wrapper.current?.getBoundingClientRect();

    return (
        <Box className={cx.imageContainer} ref={wrapper}>
            {file && (
                <Image
                    className={cx.image}
                    style={{
                        position: transform ? 'fixed' : 'absolute',
                        left: transform ? wrapperBoundingClientRect?.left - 1 : undefined,
                        top: transform ? wrapperBoundingClientRect?.top - 1 : undefined,

                        zIndex: transform ? 9999 : undefined,

                        display: width && height ? 'flex' : 'none',

                        width: width + 2, // 2 = border width
                        height: height + 2, // 2 = border width

                        transform: transform ? `translate(${transform.x}px, ${transform.y}px)` : 'none',
                    }}
                    src={file.url}
                    width={file.width}
                    height={file.height}
                    alt={image.type}
                    {...attributes}
                    {...listeners}
                    ref={setNodeRef}
                />
            )}
        </Box>
    );
};

export { ComponentImages };
