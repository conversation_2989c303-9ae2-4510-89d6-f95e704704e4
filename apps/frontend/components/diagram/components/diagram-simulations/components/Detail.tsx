import React, {FC, useEffect} from 'react';

import {Button, Text, Stack, Paper, Table} from '@mantine/core';

import {DiagramSidebar} from 'components/diagram';

import {CreateModal} from './CreateModal';

import {ModalService} from 'services/ModalService';
import {DateService} from 'services/DateService';
import {SimulationProfileService, SimulationProfile} from 'components/diagram/services/SimulationProfileService';

import {useSimulationProfiles} from 'components/diagram/hooks/use-simulation-profiles';
import {SidebarService} from 'components/diagram/services/SidebarService';
import {useSidebarWidth} from 'components/diagram/hooks/use-sidebar-width';

import cx from '../SimulationProfiles.module.css';

const Detail: FC<{
    simulationProfile: SimulationProfile;
}> = ({ simulationProfile }) => {
    useSidebarWidth(window.innerWidth / 2);

    const headers: string[] = [];
    const data: string[][] = [];

    simulationProfile.data.split('\n').forEach((row, index) => {
        console.log('row', row);
        if (row && index === 0) {
            row.split(',').forEach((header) => {
                headers.push(header.replaceAll('"', ''));
            });
        }

        if (row && index > 0) {
            data.push(row.split(','));
        }
    });

    return (
        <DiagramSidebar.Section>
            <Table.ScrollContainer minWidth="100%">
                <Table className={cx.table}>
                    <Table.Thead>
                        <Table.Tr>
                            {headers.map((header) => (
                                <Table.Th key={header}>
                                    {header}
                                </Table.Th>
                            ))}
                        </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                        {data.map((row, index) => (
                            <Table.Tr key={index}>
                                {row.map((cell, index) => (
                                    <Table.Td key={index}>{cell}</Table.Td>
                                ))}
                            </Table.Tr>
                        ))}
                    </Table.Tbody>
                </Table>
            </Table.ScrollContainer>
            <button onClick={SimulationProfileService.deactivate}>
                Back
            </button>
        </DiagramSidebar.Section>
    );
};

const DetailFetcher: FC<{
    id: string;
}> = ({ id }) => {
    const { simulationProfiles } = useSimulationProfiles();
    const simulationProfile = simulationProfiles.find((profile) => profile.id === id);

    return simulationProfile ? (
        <Detail simulationProfile={simulationProfile} />
    ) : (
        <div>Missing</div>
    );
};

function csvToJson(csvString: string): [string[], Record<string, string>[]] {
    // Handle empty or null input string
    if (!csvString || csvString.trim() === '') {
        console.warn("Input CSV string is empty or whitespace. Returning empty headers and data arrays.");
        return [[], []];
    }

    // Split the CSV string into individual lines
    const lines = csvString.trim().split('\n');

    // If there are no lines or only an empty line, return empty headers and data arrays
    if (lines.length === 0 || (lines.length === 1 && lines[0].trim() === '')) {
        console.warn("Input CSV contains no data rows. Returning empty headers and data arrays.");
        return [[], []];
    }

    // The first line contains the headers.
    // Trim each header to remove leading/trailing whitespace.
    const headers = lines[0].split(',').map(header => header.trim());

    // Initialize an empty array to store the JSON objects
    const result: Record<string, string>[] = [];

    // Iterate over the remaining lines (data rows)
    for (let i = 1; i < lines.length; i++) {
        const currentLine = lines[i].trim();

        // Skip empty lines in the data
        if (currentLine === '') {
            continue;
        }

        // Split the current line into values
        const values = currentLine.split(',');

        // Create a new object for the current row
        const rowObject: Record<string, string> = {};

        // Populate the object with header-value pairs
        // Ensure we don't go out of bounds if a row has fewer values than headers
        for (let j = 0; j < headers.length; j++) {
            rowObject[headers[j]] = (values[j] || '').trim(); // Use empty string if value is missing, then trim
        }

        // Add the populated object to the result array
        result.push(rowObject);
    }

    return [headers, result];
}

export { DetailFetcher as Detail };
