import React, { FC } from 'react';

import { Box } from '@mantine/core';

import { Actions } from './components/Actions';
import { Header } from './components/Header';
import { Content } from './components/Content';
import { RightSide } from './components/RightSide';
import { LeftSide } from './components/LeftSide';
import { Title } from './components/Title';

import cx from './styles.module.scss';

export type SignupLayoutProps = {
    title: string;
    nbSteps: number;
    active: number;
    leftActions?: React.ReactNode;
    rightActions?: React.ReactNode;
};

type Props = {
    children: React.ReactNode;
};

const SignupLayout: FC<Props> & {
    Content: typeof Content;
    Header: typeof Header;
    Actions: typeof Actions;
    LeftSide: typeof LeftSide;
    RightSide: typeof RightSide;
    Title: typeof Title;
} = ({ children }) => {
    return <Box className={cx.root}>{children}</Box>;
};

SignupLayout.Content = Content;
SignupLayout.Header = Header;
SignupLayout.Actions = Actions;
SignupLayout.LeftSide = LeftSide;
SignupLayout.RightSide = RightSide;
SignupLayout.Title = Title;

export { SignupLayout };
