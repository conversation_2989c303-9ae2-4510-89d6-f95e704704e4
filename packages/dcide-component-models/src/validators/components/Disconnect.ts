import { z } from 'zod';

import { ComponentValidator, ComponentPayloadValidator } from '../Component';

import { AbstractPort, Port } from '../fields/AbstractPort';

import { synchronize, synchronizePowerFlowDirection } from '../helpers';

import { ComponentDefinition } from '../../types';

const port = AbstractPort.extend({
    AC: Port.AC.extend({
        controlMethods: Port.controlMethods.validator,
    }).default({}),
    DC: Port.DC.extend({
        controlMethods: Port.controlMethods.validator,
    }).default({}),
}).default({
    powerFlowDirection: 'bidirectional',
});

const electrical = z.object({
    ports: z.tuple([port, port]).default([undefined, undefined]),
    features: z
        .object({
            feedbackSignal: z.boolean().optional().default(false),
            fuse: z.boolean().optional().default(false),
        })
        .default({}),
}).default({});

const DisconnectBase = z.object({
    type: z.literal('disconnect').default('disconnect'),
    electrical: synchronize(synchronizePowerFlowDirection(electrical), {
        'ports.0': ['ports.1'],
    }),
});

export const DisconnectValidator = ComponentValidator.merge(DisconnectBase);
export const DisconnectPayloadValidator = ComponentPayloadValidator.merge(DisconnectBase);

export const DisconnectDefinition: ComponentDefinition = {
    type: 'disconnect',
    name: 'Disconnect',
    plural: 'Disconnects',
    indicator: 'Q',
    hasDynamicNumberOfPorts: false,
    ports: {
        min: 2,
        max: 2,
        powerFlowDirections: ['input', 'output', 'bidirectional'],
        displayAsSinglePort: true,
    },
    validators: {
        component: DisconnectValidator,
        payload: DisconnectPayloadValidator,
    },
};

export type Disconnect = z.infer<typeof DisconnectValidator>;
export type DisconnectPayload = z.infer<typeof DisconnectPayloadValidator>;
