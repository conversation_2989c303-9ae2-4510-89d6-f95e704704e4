import React, { FC } from 'react';

import type { DiagramComponentInstance } from '@repo/dcide-component-models';

import { SimpleGrid, UnstyledButton } from '@mantine/core';
import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';

import cx from '../Validations.module.css';

const ComponentInstanceSelector: FC<{
    componentInstances: DiagramComponentInstance[];
    selected: DiagramComponentInstance['id'] | null;
    select: (id: DiagramComponentInstance['id'] | null) => void;
}> = ({ componentInstances, selected, select }) => (
    <SimpleGrid cols={4} spacing="xs">
        {componentInstances.map((componentInstance) => (
            <UnstyledButton
                className={cx.componentInstanceSelectorItem}
                onClick={() => {
                    select(componentInstance.id === selected ? null : componentInstance.id);
                }}
                onMouseEnter={() => {
                    ComponentInstanceService.highlight(componentInstance.id);
                }}
                data-selected={selected === componentInstance.id}
                key={componentInstance.id}
            >
                <ComponentIcon type={componentInstance.componentType} />
                {componentInstance.designator}
            </UnstyledButton>
        ))}
    </SimpleGrid>
);

export default ComponentInstanceSelector;
