.root {
    --users-height: 300px;

    display: grid;
    grid-template-areas: 'content notifications' 'content users';
    grid-template-rows: auto var(--users-height);
    grid-template-columns: 3fr 280px;

    flex-grow: 1;

    @media (max-width: var(--mantine-breakpoint-md)) {
        grid-template-areas: 'content';
        grid-template-rows: auto;
        grid-template-columns: auto;
    }
}

.content {
    grid-area: content;

/*  make footer sticky */
    :global(.mantine-ScrollArea-viewport) {
        > div {
            display: flex !important;
            flex-direction: column;

            height: 100%;
        }
    }

    footer {
        margin-top: auto;
    }

    @media (min-width: var(--mantine-breakpoint-md)) {
        border-right: 1px solid var(--mantine-color-gray-2);
    }
}

.notifications {
    grid-area: notifications;

    background-color: rgba(0, 0, 0, 0.01);

    border-bottom: 1px solid var(--mantine-color-gray-2);
    margin-bottom: -1px; /*  offset the border */

    @media (max-width: var(--mantine-breakpoint-md)) {
        display: none;
    }
}

.users {
    grid-area: users;

    background-color: rgba(0, 0, 0, 0.01);

    @media (max-width: var(--mantine-breakpoint-md)) {
        display: none;
    }
}
