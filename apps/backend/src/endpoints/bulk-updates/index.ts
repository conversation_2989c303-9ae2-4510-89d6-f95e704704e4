import { Endpoint } from 'payload';

import { updateAbbBreakers } from './abb-breakers';
import { updateCarlingBreakers } from './carling-breakers';
import { removeDisabledPorts } from './remove-disabled-ports';
import { validateComponents } from './validate-components';
import { splitMeasurementMetaIntoIndividualFields } from './split-measurement-meta-into-individual-fields';
import { addDiagramIdToTransactions } from './add-diagram-id-to-transactions';
import { removeDuplicateDocuments } from './remove-duplicate-documents';
import { addBreakerTotalVoltage } from './add-breaker-total-voltage';
import { calculateAllProductCompleteness } from './calculate-all-product-completeness';
import { calculateAllProfileCompleteness } from './calculate-all-profile-completeness';
import { ensureCompatibleWithCrossReference } from './ensure-compatible-with-cross-reference';
import { convertRelationshipIds } from './convert-relationship-ids';

import { NoContentResponse } from '@/responses/NoContentResponse';
import { ForbiddenError, NotFoundError } from '@/responses/errors';

const bulkUpdate: Endpoint = {
    path: '/bulk-update',
    method: 'get',
    handler: async (request) => {
        const token = request.headers.get('authorization');

        if (token !== process.env.DEVOPS_TOKEN) {
            return new ForbiddenError();
        }

        const script = request.query.script;

        switch (script) {
            case 'abb_breakers':
                await updateAbbBreakers(request.payload);
                break;
            case 'carling_breakers':
                await updateCarlingBreakers(request.payload);
                break;
            case 'remove-disabled-ports':
                await removeDisabledPorts(request.payload);
                break;
            case 'split-measurement-meta-into-individual-fields':
                await splitMeasurementMetaIntoIndividualFields(request.payload);
                break;
            case 'add-diagram-id-to-transactions':
                await addDiagramIdToTransactions(request.payload);
                break;
            case 'remove-duplicate-documents':
                await removeDuplicateDocuments(request.payload);
                break;
            case 'add-breaker-total-voltage':
                await addBreakerTotalVoltage(request.payload);
                break;
            case 'calculate-all-product-completeness':
                await calculateAllProductCompleteness(request.payload);
                break;
            case 'calculate-all-profile-completeness':
                await calculateAllProfileCompleteness(request);
                break;
            case 'ensure-compatible-with-cross-reference':
                await ensureCompatibleWithCrossReference(request.payload);
                break;
            case 'convert-relationship-string-ids-to-object-ids':
                await convertRelationshipIds(request.payload);
                break;

            case 'validate-components':
                return validateComponents(request.payload).then((errors) => Response.json(errors));

            default:
                return new NotFoundError();
        }

        return new NoContentResponse();
    },
};

export { bulkUpdate };
