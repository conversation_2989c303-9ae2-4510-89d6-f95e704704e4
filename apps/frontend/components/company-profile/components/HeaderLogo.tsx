import { FC } from 'react';

import { BsPencil } from 'react-icons/bs';
import { Flex, Stack } from '@mantine/core';

import { CompanyProfile, PermissionCompany } from '@repo/dcide-component-models';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { AutoSave } from 'components/forms/AutoSave';
import { ImageField } from 'components/forms/fields/ImageField';
import { CompanyForm } from 'components/company-form/CompanyForm';

import { CompanyLogo } from 'components/company-logo';
import { BoothNumber } from 'components/booth-number/BoothNumber';
import { CompanyLogoFallback } from 'components/company-logo/CompanyLogoFallback';

import classes from './Header.module.css';

const HeaderLogo: FC<{ company: CompanyProfile }> = ({ company }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    return (
        <Stack gap={4} className={classes.logoBoothWrapper}>
            <Stack gap={8} className={classes.logoWrapper}>
                <Flex align="center" justify="center" className={classes.logo}>
                    <CompanyLogo
                        logos={company.logos}
                        width={130}
                        fallback={<CompanyLogoFallback name={company.name} color={company.color} />}
                    />
                </Flex>

                {canEdit && (
                    <Flex align="end" justify="end" className={classes.editLogo}>
                        <CompanyForm
                            companyId={company.id}
                            defaultValues={{
                                logos: {
                                    small:
                                        typeof company.logos.small === 'string'
                                            ? company.logos.small
                                            : company.logos.small?.id,
                                },
                            }}
                        >
                            <AutoSave instant={['logos.small']} />
                            <ImageField
                                name="logos.small"
                                group="manufacturers:images"
                                buttonLabel="Edit"
                                buttonProps={{
                                    leftSection: <BsPencil />,
                                    size: 'compact-xs',
                                    variant: 'default',
                                }}
                                showPreview={false}
                            />
                        </CompanyForm>
                    </Flex>
                )}
            </Stack>

            <BoothNumber company={company} showEventName />
        </Stack>
    );
};

export { HeaderLogo };
