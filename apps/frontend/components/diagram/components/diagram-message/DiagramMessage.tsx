import React, { FC } from 'react';

import { ActionIcon, Box, Flex, Transition } from '@mantine/core';
import { TbX } from 'react-icons/tb';

import { Z_INDEX } from 'components/diagram/diagram-z-index';

import { DiagramMessageShortcut } from './DiagramMessage.Shortcut';

import classes from './DiagramMessage.module.css';

const DiagramMessage: FC<{
    open: boolean;
    handleClose?: () => void;
    withCloseButton?: boolean;
    children: React.ReactNode;
}> & {
    Shortcut: typeof DiagramMessageShortcut;
} = ({ open, children, handleClose = () => {}, withCloseButton = false }) => {
    return (
        <Transition keepMounted mounted={open} transition="slide-down">
            {(styles) => (
                <Box
                    size="xs"
                    className={classes.wrapper}
                    style={{ ...styles, zIndex: Z_INDEX.MESSAGE, top: 'var(--diagram-header-height)' }}
                >
                    <Box className={classes.message}>
                        <Flex align="center" gap={4}>
                            {children}
                            {withCloseButton && (
                                <ActionIcon className={classes.close} size="xs" onClick={handleClose}>
                                    <TbX size={10} />
                                </ActionIcon>
                            )}
                        </Flex>
                    </Box>
                </Box>
            )}
        </Transition>
    );
};

DiagramMessage.Shortcut = DiagramMessageShortcut;

export { DiagramMessage };
