import React, { FC, useEffect, useState } from 'react';
import { uid } from 'radash';

import { Loader, Flex, Alert, Text, Stack, Divider, ActionIcon } from '@mantine/core';
import { IoCloseCircleOutline, IoCloseSharp } from 'react-icons/io5';

import { AIService } from 'services/AIService';
import { Component, FeatureLimit } from '@repo/dcide-component-models';
import { useComponentChat } from 'components/component-chat/hooks/useComponentChat';
import { ComponentChatMessage as ComponentChatMessageType } from '@repo/dcide-component-models';

import {
    ComponentChatAnswer,
    ComponentChatInput,
    ComponentChatMessage,
    ComponentChatScrollArea,
    ComponentChatHistory,
} from 'components/component-chat';
import { FeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';

import classes from './ComponentChat.module.css';
import { useHistory } from '../hooks/useHistory';
import { useCheckFeatureLimit } from 'hooks/use-check-feature-limit';
import { InternalTrackingService } from 'services/InternalTrackingService';

const ComponentChat: FC<{
    component?: Component;
    projectId?: string;
    includePrivateFiles?: boolean;
}> = ({ component, projectId, includePrivateFiles }) => {
    const {
        messages,
        addMessage,
        updateMessage,
        setActiveComponentId,
        setActiveProjectId,
        clearMessages,
        toggleChatOpen,
    } = useComponentChat();

    const [userInput, setUserInput] = useState('');
    const [error, setError] = useState('');

    const [streamingAnswer, setStreamingAnswer] = useState('');

    const [isThinking, setIsThinking] = useState(false);
    const [isStreaming, setIsStreaming] = useState(false);

    const { status } = useCheckFeatureLimit(FeatureLimit.AI_REQUESTS);

    useEffect(() => {
        if (component) {
            setActiveComponentId(component.id);
        }
    }, [component?.id, setActiveComponentId]);

    useEffect(() => {
        if (projectId) {
            setActiveProjectId(projectId);
        }
    }, [projectId, setActiveProjectId]);

    // clear messages on unmount
    useEffect(() => {
        return () => {
            clearMessages();
        };
    }, []);

    const { history } = useHistory(component?.id, projectId);

    const handleSubmit = async (overrideQuestion?: string) => {
        const question = overrideQuestion || userInput;

        setError('');
        setIsThinking(true);
        setIsStreaming(true);

        if (!overrideQuestion) {
            setUserInput('');
        }

        const tempMessageId = uid(8);
        let messageId = '';

        const tempMessage = {
            id: tempMessageId,
            question,
            component: component?.id,
            project: projectId,
        };

        addMessage(tempMessage);

        try {
            InternalTrackingService.track('ai.product.chat', {
                question,
            });

            const response: Response = await AIService.getAnswer({
                componentId: component?.id,
                projectId: projectId,
                question,
                includePrivateFiles,
                history,
            });

            if (!response.ok) {
                throw new Error();
            }

            const reader = response.body?.getReader();
            const decoder = new TextDecoder();

            let answer = '';

            new ReadableStream({
                start(controller) {
                    let references: ComponentChatMessageType['references'];

                    const push: any = () => {
                        reader?.read().then(({ done, value }) => {
                            setIsThinking(false);

                            if (done) {
                                const newMessage = {
                                    ...tempMessage,
                                    id: messageId,
                                    answer,
                                    references,
                                };
                                updateMessage(tempMessageId, newMessage);
                                AIService.saveComponentMessage(newMessage).then();

                                setStreamingAnswer('');
                                setIsStreaming(false);

                                controller.close();

                                return;
                            }

                            const decoded = decoder.decode(value);

                            // ReadableStream value can contain multiple objects
                            const splitValue = decoded.split('}{');

                            splitValue.forEach((dataString) => {
                                let cleanString = dataString;

                                // the starting { might be removed by split
                                if (!cleanString.startsWith('{')) {
                                    cleanString = `{${cleanString}`;
                                }

                                // the ending } might be removed by split
                                if (!cleanString.endsWith('}')) {
                                    cleanString = `${cleanString}}`;
                                }

                                let parsedString: {
                                    message_id?: string;
                                    error?: string;
                                    data?: string;
                                    references?: ComponentChatMessageType['references'];
                                } = {};

                                try {
                                    parsedString = JSON.parse(cleanString) ?? {};
                                } catch (err) {
                                    // ignore json parse errors
                                    // when the stream is not returning json yet
                                    // eslint-disable-next-line no-console
                                    console.log(err);
                                }

                                const {
                                    message_id,
                                    error: errorMessage,
                                    data,
                                    references: incomingReferences,
                                } = parsedString;

                                if (errorMessage) {
                                    setError(errorMessage);
                                }

                                if (message_id) {
                                    messageId = message_id;
                                }

                                if (data) {
                                    setStreamingAnswer(data);
                                    answer = data;
                                }

                                if (incomingReferences) {
                                    references = incomingReferences;
                                }
                            });

                            push();
                        });
                    };

                    push();
                },
            });
        } catch (err) {
            console.log(err);
            setIsThinking(false);
            setIsStreaming(false);
            !error && setError('Something went wrong, please try again later.');
        }
    };

    let title: React.ReactNode = 'Ask a question and we will search the project files for an answer!';
    if (component) {
        title = (
            <>
                Ask a question about the{' '}
                <Text span fw={600} fz="md">
                    {component.name}
                </Text>{' '}
                and we will search the product documentation for an answer!
            </>
        );
    }

    return (
        <Flex className={classes.chat} direction="column" justify="space-between">
            <ComponentChatScrollArea>
                <Stack gap={0}>
                    <ActionIcon size="lg" radius={99} color="brand" onClick={toggleChatOpen} className={classes.close}>
                        <IoCloseSharp size={14} />
                    </ActionIcon>

                    <Alert
                        color="brand"
                        variant="light"
                        p="xs"
                        m="xs"
                        title={
                            <Text fw={400} fz="md">
                                {title}
                            </Text>
                        }
                    ></Alert>

                    <FeatureLimitTracker feature={FeatureLimit.AI_REQUESTS} />

                    <ComponentChatHistory componentId={component?.id} projectId={projectId} />

                    {!!messages.length && <Divider label="Current questions" labelPosition="left" m="xs" mb={0} />}

                    {messages.map((message, index) => (
                        <ComponentChatMessage key={message.id} {...message}>
                            {index === messages.length - 1 && (
                                <>
                                    {streamingAnswer && <ComponentChatAnswer answer={streamingAnswer} isStreaming />}
                                    {isThinking && <ComponentChatAnswer answer={<Loader size="xs" type="dots" />} />}
                                </>
                            )}
                        </ComponentChatMessage>
                    ))}

                    {error && (
                        <Alert
                            color="red"
                            variant="light"
                            p="xs"
                            mt="xs"
                            icon={<IoCloseCircleOutline size={16} />}
                            radius={0}
                            title={
                                <Text fw={400} fz="sm">
                                    {error}
                                </Text>
                            }
                        />
                    )}
                </Stack>
            </ComponentChatScrollArea>

            {status?.pass && (
                <ComponentChatInput
                    componentType={component?.type}
                    userInput={userInput}
                    setUserInput={setUserInput}
                    handleSubmit={handleSubmit}
                    disabled={isThinking || isStreaming}
                />
            )}
        </Flex>
    );
};

export { ComponentChat };
