import React from 'react';

import { Box } from '@mantine/core';

import { Component, ComponentSection } from '@repo/dcide-component-models';

import { useProductsByIds } from 'hooks/use-products-by-ids';
import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { GridSection } from 'components/section/GridSection';
import { CompatibleWithField } from 'components/forms/fields/compatible-with/CompatibleWithField';
import { ComponentDatasheetSectionProgress } from 'components/component-datasheet/components/ComponentDatasheetSectionProgress';
import { ComponentOverviewHitSimple } from 'components/component-overview/ComponentOverviewHit.Simple';
import { CompatiblePlaceholder } from 'components/forms/fields/compatible-with/components/CompatibleWithPlaceholders';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import cx from './CompatibleWith.module.css';
import { useWatch } from 'react-hook-form';

const CompatibleWith = () => {
    const [compatibleWith, compatibleWithPlaceholders] = useWatch({
        name: ['compatibleWith', 'compatibleWithPlaceholders'],
    }) as [Component['compatibleWith'], Component['compatibleWithPlaceholders']];

    const mode = useDatasheetMode();

    const { components: compatibleComponents } = useProductsByIds(compatibleWith ?? []);

    if (mode !== DatasheetMode.VIEW) {
        return (
            <ComponentDatasheetSection
                id={ComponentSection.COMPATIBLE_WITH}
                title="Compatible with"
                afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.COMPATIBLE_WITH} />}
                alwaysShow
            >
                <CompatibleWithField />
            </ComponentDatasheetSection>
        );
    }

    if (!compatibleComponents.length && !compatibleWithPlaceholders.length) {
        return null;
    }

    return (
        <ComponentDatasheetSection
            alwaysShow
            title="Compatible with"
            id={ComponentSection.COMPATIBLE_WITH}
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.COMPATIBLE_WITH} />}
        >
            <GridSection nbCols={4}>
                {compatibleComponents.map((component) => (
                    <Box className={cx.compatibleWith} key={component.id}>
                        <ComponentOverviewHitSimple
                            key={component.id}
                            component={component}
                            href={ComponentHelpers.urls.view(component.id)}
                        />
                    </Box>
                ))}
                {compatibleWithPlaceholders.map(({ manufacturer, productIdentifier, componentType }) => (
                    <Box className={cx.compatibleWith} key={`${manufacturer}-${productIdentifier}`}>
                        <CompatiblePlaceholder
                            manufacturerName={manufacturer}
                            productIdentifier={productIdentifier}
                            componentType={componentType}
                        />
                    </Box>
                ))}
            </GridSection>
        </ComponentDatasheetSection>
    );
};

export { CompatibleWith };
