import React from 'react';
import { PermissionDiagramElements } from '@repo/dcide-component-models';

import { Box, Tooltip, UnstyledButton } from '@mantine/core';

import { ModalService } from 'services/ModalService';
import { ProjectService } from 'services/ProjectService';

import { useCurrentProject } from 'hooks/use-current-project';
import { useCurrentUser } from 'hooks/use-current-user';
import { useDiagramEmpty } from 'components/diagram/hooks/use-diagram-empty';
import { usePermission } from 'hooks/use-permission';

import cx from './DiagramBadges.module.css';

const DiagramBadgeViewOnly = () => {
    const isEmptyDiagram = useDiagramEmpty();
    const user = useCurrentUser();
    const project = useCurrentProject();
    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const tooltip = `
        You don't have the required permissions to edit this diagram
        but you can duplicate it or copy & paste the components into your own diagrams.
    `;

    const loginOrRegister = () => {
        ModalService.openLoginModal({
            redirect: window.location.pathname,
        });
    };

    const duplicate = () => {
        if (project) {
            ProjectService.navigate.duplicate(project.id, project.name, project.isReferenceDesign);
        }
    };

    return !canEdit && !isEmptyDiagram ? (
        <React.Fragment>
            <Tooltip label={tooltip} multiline position="bottom" w={320} withArrow>
                <Box className={cx.badge}>View only mode</Box>
            </Tooltip>
            {project && (
                <UnstyledButton className={cx.badge} onClick={user ? duplicate : loginOrRegister}>
                    Duplicate
                </UnstyledButton>
            )}
        </React.Fragment>
    ) : null;
};

export { DiagramBadgeViewOnly };
