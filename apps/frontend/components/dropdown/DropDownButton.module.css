.root {
    background-color: transparent;

    transition-property: background-color, width, border-color;
    transition-duration: 100ms;
    transition-timing-function: ease;
    outline: none;

    height: 30px;
    padding: 0px 6px;

    font-size: var(--mantine-font-size-md);
    font-weight: 500;

    border: 1px solid transparent;

    &:hover,
    &:active {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    &:focus {
        outline: none;
    }

    &:active {
        transform: none;
    }

    [data-position="left"] {
        svg {
            margin-right: calc(var(--mantine-spacing-xs) / 2);
        }
    }

    &[data-dropdown="true"] {
        padding-right: 6px;
    }

    &[data-variant="icon"] {
        width: 30px;
        padding: 0;

        svg {
            margin: 0;
        }
    }

    &[data-variant="outline"] {
        border: 1px solid rgba(255, 255, 255, 1);
        padding: 0 12px;
    }

    &[data-variant="filled"] {
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    &[data-cursor="text"] {
        cursor: text;
    }

    max-width: 250px;
}

.label {
    display: inline-block;

    min-width: 0;
    height: fit-content;
    line-height: 1rem;

    white-space: nowrap;
    overflow: hidden;

    text-overflow: ellipsis;

    > svg {
        display: block;
    }
}

.section {
    margin-left: 0.25rem;

    /*  Prevent space after icon */
    margin-inline-end: 0;
}
