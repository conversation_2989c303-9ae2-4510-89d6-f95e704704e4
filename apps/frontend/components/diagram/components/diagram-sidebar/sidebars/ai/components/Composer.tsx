import React from 'react';

import { Badge, Box, Group, Tooltip, UnstyledButton } from '@mantine/core';
import { openContextModal } from '@mantine/modals';

import { TbSend } from 'react-icons/tb';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { MultilineTextField } from 'components/forms/fields/MultilineTextField';

import { AIConversationService } from 'components/diagram/services/AIConversationService';

import { useDiagram } from 'components/diagram/hooks';
import { useAIConversation } from 'components/diagram/components/diagram-sidebar/sidebars/ai/hooks/use-ai-conversation';

import cx from './Composer.module.css';

type Values = {
    question: string;
};

const Composer = () => {
    const diagram = useDiagram();
    const { prefilledQuestion } = useAIConversation();

    const openInspirationModal = () => {
        openContextModal({
            modal: 'aiInspiration',
            innerProps: {
                p: 'xl',
            },
            withCloseButton: false,
            size: '1024px',
        });
    };

    const submit = async (values: Values, api: any) => {
        if (values.question.trim()) {
            await AIConversationService.ask(diagram, values.question);
            AIConversationService.clearPrefilledQuestion();

            api.setValue('question', '');
        }
    };

    return (
        <Box className={cx.composer}>
            <Form<Values>
                defaultValues={{
                    question: prefilledQuestion,
                }}
                onSubmit={submit}
            >
                <MultilineTextField
                    name="question"
                    placeholder="Ask a question"
                    rows={3}
                    onKeyDown={(event) => {
                        if (event.key === 'Enter' && !event.shiftKey) {
                            event.preventDefault();
                            event.currentTarget.closest('form')?.querySelector('button')?.click();
                        }
                    }}
                />
                <Group className={cx.actions} gap="xs">
                    <FormSubmit size="xs">
                        <TbSend />
                    </FormSubmit>
                    <UnstyledButton className={cx.inspiration} onClick={openInspirationModal} type="button">
                        Need some inspiration?
                    </UnstyledButton>
                </Group>
                <Tooltip
                    label={
                        <Box>
                            Our AI Assistant is currently in active development.
                            <br />
                            Please note that it can make mistakes.
                        </Box>
                    }
                    position="left"
                    withArrow
                >
                    <Badge className={cx.beta} variant="gradient" size="xs" radius="xs">
                        Beta
                    </Badge>
                </Tooltip>
            </Form>
        </Box>
    );
};

export { Composer };
