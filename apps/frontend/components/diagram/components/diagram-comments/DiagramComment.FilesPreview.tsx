import React, { FC } from 'react';

import { Box, Loader, Tooltip } from '@mantine/core';

import { LocalFile } from '@repo/dcide-component-models';

import { useFile } from 'hooks/use-file';
import { FileIcon } from 'components/file-icon/FileIcon';

import cx from './DiagramComment.module.css';

const DiagramCommentFilesPreview: FC<{
    files: LocalFile[];
}> = ({ files = [] }) => (
    <Box className={cx.filesPreview}>
        {files.map((file, index) => {
            return file ? (
                <FilePreview file={file.file} key={index} />
            ) : (
                <Box className={cx.filePreview} data-loading>
                    <Loader size="xs" />
                </Box>
            );
        })}
    </Box>
);

const FilePreview: FC<{
    file: string;
}> = ({ file: fileId }) => {
    const { file, isLoading } = useFile(fileId);
    const fileIsImage = file?.mimeType?.startsWith('image');

    return isLoading || !file ? (
        <Box className={cx.filePreview} data-loading>
            <Loader size="xs" />
        </Box>
    ) : (
        <Tooltip label={file.name} withArrow>
            <Box className={cx.filePreview} component="a" href={file.url} target="_blank" data-loading={isLoading}>
                {fileIsImage ? <img src={file.url} alt={file.name} /> : <FileIcon mimeType={file.mimeType} />}
            </Box>
        </Tooltip>
    );
};

export { DiagramCommentFilesPreview };
