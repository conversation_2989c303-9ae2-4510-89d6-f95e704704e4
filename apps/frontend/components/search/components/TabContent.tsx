import { Box } from '@mantine/core';

import { useTabs } from 'components/search/hooks/useTabs';

import { HorizontalTabs } from 'components/horizontal-tabs';

import cx from './TabContent.module.css';

const TabContent = ({ initialContent }: { initialContent?: React.ReactNode }) => {
    const tabs = useTabs(initialContent);

    return (
        <Box className={cx.root}>
            <HorizontalTabs tabs={tabs} />
        </Box>
    );
};

export { TabContent };
