import React from 'react';
import { max, min } from 'radash';

import { DiagramComponentInstance, DiagramConnection, Simulation } from '@repo/dcide-component-models';

import { ConnectionService } from 'components/diagram/services';
import { ConnectionLabel } from 'components/diagram/components/diagram-connections/components/ConnectionLabel';

import { useDiagram } from 'components/diagram/hooks';
import { useSimulations } from 'components/diagram/hooks/use-simulations';

import { FormatHelpers } from 'helpers/formatters';
import { SimulationService } from 'components/diagram/services/SimulationService';
import { DisplayMode } from 'components/diagram/state/simulation';
import { NumberHelpers } from 'helpers/NumberHelpers';
import { voltageConverter } from 'units/voltage';
import { currentConverter } from 'units/current';
import { powerConverter } from 'units/power';

const getLimitFromSpecs = (specs: any[], field: 'voltage' | 'power' | 'current', type: 'min' | 'max' | 'nom') => {
    const minOrMax = type === 'max' ? max : min;

    const defaultValue = type === 'max' ? 0 : Infinity;

    return (
        minOrMax([
            ...specs.map((spec) => spec?.[field]?.[type] || defaultValue),
            ...(type === 'nom' ? [] : specs.map((spec) => spec?.[field]?.nom || defaultValue)),
        ]) ?? 0
    );
};

const LOWER_LIMIT_COLOR = '#1864AB'; // blue
const UPPER_LIMIT_COLOR = '#f03e3e'; // red

const ConnectionLabelsSimulation = ({
    connection,
    simulationConnection,
    fromComponentInstance,
    toComponentInstance,
}: {
    connection: DiagramConnection;
    simulationConnection?: Simulation['result']['connections']['number'];
    fromComponentInstance: DiagramComponentInstance;
    toComponentInstance: DiagramComponentInstance;
}) => {
    const diagram = useDiagram();
    const { activeTimestamp, displayMode } = useSimulations();

    const { from, to } = connection;
    const { points, length } = ConnectionService.getPath(from, to, diagram) || {};

    if (from.port === null || to.port === null) return null;
    if (!points || !length) return null;
    if (!simulationConnection) return null;

    const voltageType = ConnectionService.getVoltageType(connection);

    if (!voltageType) return null;

    const {
        fromVoltage: fromVoltageValue,
        toVoltage: toVoltageValue,
        connectionCurrent: connectionCurrentValue,
        connectionPower: connectionPowerValue,
    } = SimulationService.formatConnectionResult(simulationConnection, voltageType, activeTimestamp);

    const fromVoltage = FormatHelpers.formatValue(fromVoltageValue, voltageConverter);
    const toVoltage = FormatHelpers.formatValue(toVoltageValue, voltageConverter);
    const connectionCurrent = FormatHelpers.formatValue(connectionCurrentValue, currentConverter);
    const connectionPower = FormatHelpers.formatValue(connectionPowerValue, powerConverter);

    const formattedConnectionCurrent = connectionCurrentValue === 0 ? '0 A' : connectionCurrent;
    const formattedConnectionPower = connectionPowerValue === 0 ? '0 W' : connectionPower;

    const getMiddleLabel = () => {
        switch (displayMode) {
            case DisplayMode.CURRENT:
                return formattedConnectionCurrent;
            case DisplayMode.POWER:
                return formattedConnectionPower;
        }
    };

    const consolidateVoltageLabels = fromVoltage && toVoltage && fromVoltage === toVoltage && length < 100;

    // @ts-ignore this exists
    const fromPortSpecs = fromComponentInstance.specifications.electrical.ports[from.port][voltageType];
    // @ts-ignore this exists
    const toPortSpecs = toComponentInstance.specifications.electrical.ports[to.port][voltageType];

    const fromVoltagePercentage = NumberHelpers.getPercentageBetweenLimits({
        value: fromVoltageValue ?? 0,
        upperLimit: getLimitFromSpecs([fromPortSpecs, connection], 'voltage', 'max'),
        lowerLimit: getLimitFromSpecs([fromPortSpecs, connection], 'voltage', 'min'),
    });

    const toVoltagePercentage = NumberHelpers.getPercentageBetweenLimits({
        value: toVoltageValue ?? 0,
        upperLimit: getLimitFromSpecs([toPortSpecs, connection], 'voltage', 'max'),
        lowerLimit: getLimitFromSpecs([toPortSpecs, connection], 'voltage', 'min'),
    });

    const fromIsRightOrBottom = from.edge === 'right' || from.edge === 'bottom';
    const showFromLabel = !consolidateVoltageLabels || fromIsRightOrBottom;
    const showToLabel = !consolidateVoltageLabels || !fromIsRightOrBottom;

    const powerPercentage = NumberHelpers.getPercentageBetweenLimits({
        value: connectionPowerValue ?? 0,
        upperLimit: getLimitFromSpecs([fromPortSpecs, toPortSpecs], 'power', 'max'),
        lowerLimit: getLimitFromSpecs([fromPortSpecs, toPortSpecs], 'power', 'nom'),
    });

    const currentPercentage = NumberHelpers.getPercentageBetweenLimits({
        value: connectionCurrentValue ?? 0,
        upperLimit: getLimitFromSpecs([fromPortSpecs, toPortSpecs, connection], 'current', 'max'),
        lowerLimit: getLimitFromSpecs([fromPortSpecs, toPortSpecs, connection], 'current', 'nom'),
    });

    return (
        <>
            <ConnectionLabel
                points={points}
                edge={from.edge}
                labels={[{ label: fromVoltage }]}
                position={'start'}
                bgColor={LOWER_LIMIT_COLOR}
                bgOverlay={{
                    color: UPPER_LIMIT_COLOR,
                    opacity: fromVoltagePercentage,
                }}
                hide={!showFromLabel}
            />
            <ConnectionLabel
                points={points}
                edge={to.edge}
                labels={[{ label: toVoltage }]}
                position={'end'}
                bgColor={LOWER_LIMIT_COLOR}
                bgOverlay={{
                    color: UPPER_LIMIT_COLOR,
                    opacity: toVoltagePercentage,
                }}
                hide={!showToLabel}
            />

            <ConnectionLabel
                points={points}
                edge={from.edge}
                labels={[{ label: getMiddleLabel() }]}
                bgColor={LOWER_LIMIT_COLOR}
                bgOverlay={{
                    color: UPPER_LIMIT_COLOR,
                    opacity: displayMode === DisplayMode.CURRENT ? currentPercentage : powerPercentage,
                }}
            />
        </>
    );
};

export { ConnectionLabelsSimulation };
