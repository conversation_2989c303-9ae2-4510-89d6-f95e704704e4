import React, { FC, useEffect } from 'react';
import { Box, Popover, SimpleGrid, Stack, Group, UnstyledButton } from '@mantine/core';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { useFieldArray, useWatch } from 'react-hook-form';
import { last, list } from 'radash';

import cx from '../Chart.module.scss';

type ChartFormPoint = { x: { value: number | null }; y: { value: number | null } };

type ChartFormProps = {
    fields: {
        x: {
            label: string;
            Component: React.ComponentType<any>;
        };
        y: {
            label: string;
            Component: React.ComponentType<any>;
        };
    };
    opened: boolean;
    close: () => void;
    handleSubmit: (values: ChartFormPoint[]) => void;
    values: ChartFormPoint[];
};

const ChartForm = ({ fields, opened, close, handleSubmit, values }: ChartFormProps) => {
    const submit = (values: { data: ChartFormPoint[] }) => {
        handleSubmit(values.data);
        close();
    };

    const data = values;

    data.push({
        x: { value: null },
        y: { value: null },
    });

    return (
        <Popover
            width={300}
            position="bottom"
            offset={2}
            opened={opened}
            onChange={close}
            transitionProps={{ duration: 0 }}
            shadow="xs"
        >
            <Popover.Target>
                <Box className={cx.formTarget} />
            </Popover.Target>
            <Popover.Dropdown className={cx.form}>
                <Form onSubmit={submit} defaultValues={{ data }}>
                    <InnerForm fields={fields} />
                </Form>
            </Popover.Dropdown>
        </Popover>
    );
};

const InnerForm: FC<{
    fields: ChartFormProps['fields'];
}> = ({ fields }) => {
    const {
        fields: array,
        append,
        remove,
    } = useFieldArray({
        name: 'data',
    });

    const values = useWatch();
    const lastValues = last(values.data) as any;

    useEffect(() => {
        if (array.length === 0) {
            append({
                x: { value: null },
                y: { value: null },
            });
        }
    }, [array.length]);

    useEffect(() => {
        // Check if the last row has either x or y value filled
        if (lastValues?.x.value || lastValues?.y.value) {
            // Check if this is the last row in the array
            if (array.length === values.data.length) {
                // Add a new empty row
                append({
                    x: { value: null },
                    y: { value: null },
                });
            }
        }
    }, [lastValues?.x.value, lastValues?.y.value, array.length, values.data.length]);

    const clear = () => {
        remove(list(array.length - 1));
    };

    return (
        <Stack gap={4}>
            <SimpleGrid cols={2} spacing={4}>
                <Box fz="xs" fw={700}>
                    {fields.x.label}
                </Box>
                <Box fz="xs" fw={700}>
                    {fields.y.label}
                </Box>
            </SimpleGrid>
            {array.map((field, index) => (
                <Row key={field.id} index={index} fields={fields} />
            ))}
            <Group mt="xs" gap="xs">
                <FormSubmit disableIfClean size="xs" variant="gradient" radius={2} style={{ flexShrink: 0 }}>
                    Save
                </FormSubmit>
                <UnstyledButton fz="xs" onClick={clear}>
                    Clear data
                </UnstyledButton>
            </Group>
        </Stack>
    );
};

const Row: FC<{
    index: number;
    fields: ChartFormProps['fields'];
}> = ({ index, fields }) => {
    const XField = fields.x.Component;
    const YField = fields.y.Component;

    return (
        <SimpleGrid cols={2} spacing={4}>
            <XField name={`data.${index}.x`} label="" fields={['value']} />
            <YField name={`data.${index}.y`} label="" fields={['value']} />
        </SimpleGrid>
    );
};

export type { ChartFormProps, ChartFormPoint };
export { ChartForm };
