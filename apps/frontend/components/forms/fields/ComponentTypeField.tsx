import { pick } from 'radash';

import { useController } from 'react-hook-form';

import { Box, Input, InputWrapperProps, Text, Tooltip, UnstyledButton } from '@mantine/core';

import { ComponentDefinition, ComponentType, all } from '@repo/dcide-component-models';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';
import { GridSection, GridSectionProps } from 'components/section/GridSection';

import cx from './GridItemField.module.css';

type ComponentTypeFieldProps = InputWrapperProps & {
    name: string;
    onClick?: (value: string) => void;
    componentTypes?: ComponentType[];
    readOnly?: boolean;
    disabled?: boolean;
} & GridSectionProps;

const ComponentTypeField = ({
    name,
    onClick = () => {},
    componentTypes,
    readOnly,
    disabled,
    cols = { xs: 4, sm: 5, md: 6, lg: 8 },
    nbCols = 8,
    ...props
}: ComponentTypeFieldProps) => {
    const {
        field: { onChange, ...field },
        fieldState: { error },
    } = useController({ name });

    let shownTypes: {
        [type in ComponentType]?: ComponentDefinition;
    } = componentTypes?.length ? pick(all, componentTypes) : all;

    if (readOnly) {
        shownTypes = pick(all, [field.value]);
    }

    return (
        <Input.Wrapper {...props}>
            <GridSection nbCols={nbCols} cols={cols} spacing={8}>
                {Object.values(shownTypes).map(({ name, description, type }) => (
                    <UnstyledButton
                        key={type}
                        onClick={() => {
                            onChange(type);
                            onClick(type);
                        }}
                        disabled={readOnly || disabled}
                        data-selected={type === field?.value}
                        data-readOnly={readOnly}
                        data-disabled={disabled}
                        className={`${cx.root} ${cx.square}`}
                    >
                        <Tooltip
                            label={description}
                            position={'bottom-start'}
                            disabled={!description || readOnly}
                            maw={{
                                base: '200px',
                                sm: '300px',
                                md: '400px',
                                lg: '500px',
                            }}
                            style={{
                                textWrap: 'wrap',
                            }}
                        >
                            <Box className={cx.inner}>
                                <div style={{ width: '24px', height: '24px', fill: 'currentcolor' }}>
                                    <ComponentIcon type={type} />
                                </div>
                                <Text className={cx.label}>{name}</Text>
                            </Box>
                        </Tooltip>
                    </UnstyledButton>
                ))}
            </GridSection>
            {error && <Input.Error mt={5}>{error.message}</Input.Error>}
        </Input.Wrapper>
    );
};

export { ComponentTypeField };
