import React from 'react';

import { TextInput } from '@mantine/core';
import { IoSearch } from 'react-icons/io5';

import { TextHelpers } from 'helpers/TextHelpers';

import { InlineFilterSectionProps } from './InlineFilters.Section';
import { InlineFiltersSectionWithIcon } from 'components/inline-filters/InlineFilters.SectionWithIcon';
import { InlineFiltersMeasurementWrapper } from 'components/inline-filters/InlineFilters.MeasurementWrapper';

const TEXT_LENGTH = 10;

type Props = Partial<InlineFilterSectionProps> & {
    onChange: (value: string) => void;
    search?: string;
};

const InlineFiltersSearch = ({ search, onRemove, onChange = () => {}, ...props }: Props) => {
    const shortLabel = TextHelpers.getTextWithEllipsis(search ?? '', TEXT_LENGTH);

    return (
        <InlineFiltersSectionWithIcon
            icon={<IoSearch />}
            body={
                <InlineFiltersMeasurementWrapper placeholder="Search" label={shortLabel}>
                    <TextInput
                        defaultValue={search}
                        placeholder="Search"
                        onChange={(event) => onChange(event.target.value)}
                        styles={{
                            input: {
                                minWidth: `${search?.length ?? TEXT_LENGTH}ch`,
                            },
                        }}
                    />
                </InlineFiltersMeasurementWrapper>
            }
            active={!!search?.length}
            onRemove={search?.length ? onRemove : undefined}
            {...props}
        />
    );
};
export { InlineFiltersSearch };
