import type { FC, ReactNode } from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box, FloatingPosition, Popover } from '@mantine/core';

import { DiagramToolbarCurrent } from './DiagramToolbar.Current';
import { DiagramToolbarDivider } from './DiagramToolbar.Divider';
import { DiagramToolbarIconButton } from './DiagramToolbar.IconButton';
import { DiagramToolbarInput } from './DiagramToolbar.Input';
import { DiagramToolbarMenuItem } from './DiagramToolbar.MenuItem';
import { DiagramToolbarPower } from './DiagramToolbar.Power';
import { DiagramToolbarSpacer } from './DiagramToolbar.Spacer';
import { DiagramToolbarText } from './DiagramToolbar.Text';
import { DiagramToolbarTextButton } from './DiagramToolbar.TextButton';
import { DiagramToolbarVoltage } from './DiagramToolbar.Voltage';
import { DiagramToolbarMenu, DiagramToolbarMenuInput } from './DiagramToolbar.Menu';
import { DiagramToolbarColor } from './DiagramToolbar.Color';
import { DiagramToolbarBorderStyle } from './DiagramToolbar.BorderStyle';
import { DiagramToolbarFixed } from './DiagramToolbar.Fixed';

import { useClickOutside } from '@mantine/hooks';

import { Z_INDEX } from 'components/diagram/diagram-z-index';

import { state as contextMenuState } from 'components/diagram/state/context-menu';

import cx from './DiagramToolbar.module.css';

const DiagramToolbar: FC<{
    position?: FloatingPosition;
    offset?: number;
    opened: boolean;
    onClose?: () => void;
    target?: ReactNode;
    children: ReactNode;
    withinPortal?: boolean;
}> & {
    Current: typeof DiagramToolbarCurrent;
    Divider: typeof DiagramToolbarDivider;
    IconButton: typeof DiagramToolbarIconButton;
    Input: typeof DiagramToolbarInput;
    MenuItem: typeof DiagramToolbarMenuItem;
    Power: typeof DiagramToolbarPower;
    Spacer: typeof DiagramToolbarSpacer;
    Text: typeof DiagramToolbarText;
    TextButton: typeof DiagramToolbarTextButton;
    Voltage: typeof DiagramToolbarVoltage;
    Menu: typeof DiagramToolbarMenu;
    MenuInput: typeof DiagramToolbarMenuInput;
    Color: typeof DiagramToolbarColor;
    BorderStyle: typeof DiagramToolbarBorderStyle;
    Fixed: typeof DiagramToolbarFixed;
} = ({ position = 'top', offset = 12, withinPortal = true, opened, onClose, children, target }) => {
    const { position: contextMenuOpen } = useSnapshot(contextMenuState);

    const clickOutsideRef = useClickOutside(() => {
        onClose?.();
    });

    return (
        <Popover
            classNames={{
                dropdown: cx.toolbar,
            }}
            opened={opened && !contextMenuOpen}
            position={position}
            offset={offset}
            withArrow
            withinPortal={withinPortal}
            zIndex={Z_INDEX.CANVAS.POPOVER}
        >
            <Popover.Target>
                {/* The toolbar will be absolute positioned to the closest relative parent element */}
                {target ?? <Box className={cx.target} />}
            </Popover.Target>
            <Popover.Dropdown
                data-keep-selection
                onClick={(event) => {
                    // Make sure that the click doesn't trigger a click event on our canvas
                    // This will make the sidebar return to its default behaviour.
                    event.stopPropagation();
                }}
                ref={clickOutsideRef}
            >
                {children}
            </Popover.Dropdown>
        </Popover>
    );
};

DiagramToolbar.Current = DiagramToolbarCurrent;
DiagramToolbar.Divider = DiagramToolbarDivider;
DiagramToolbar.IconButton = DiagramToolbarIconButton;
DiagramToolbar.Input = DiagramToolbarInput;
DiagramToolbar.MenuItem = DiagramToolbarMenuItem;
DiagramToolbar.Power = DiagramToolbarPower;
DiagramToolbar.Spacer = DiagramToolbarSpacer;
DiagramToolbar.Text = DiagramToolbarText;
DiagramToolbar.TextButton = DiagramToolbarTextButton;
DiagramToolbar.Voltage = DiagramToolbarVoltage;
DiagramToolbar.Menu = DiagramToolbarMenu;
DiagramToolbar.MenuInput = DiagramToolbarMenuInput;
DiagramToolbar.Color = DiagramToolbarColor;
DiagramToolbar.BorderStyle = DiagramToolbarBorderStyle;
DiagramToolbar.Fixed = DiagramToolbarFixed;

export { DiagramToolbar };
