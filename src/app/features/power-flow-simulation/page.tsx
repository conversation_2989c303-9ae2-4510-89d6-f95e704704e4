import React from 'react';

import { Box } from '@mantine/core';

import { ContactSection } from '@/components/contact-section/ContactSection';
import { Features } from '@/components/features/Features';
import { PageHeader } from '@/components/page-header/PageHeader';
import { Section } from '@/components/section/Section';
import { Block } from '@/components/blocks/Block';

import { Columns } from '@/components/blocks/Columns';
import { TextBlock } from '@/components/blocks/TextBlock';

import { SectionSpacer } from '@/components/section/SectionSpacer';

import { VideoBlock } from '@/components/blocks/VideoBlock';
import { VideoBackground } from '@/components/video-background/VideoBackground';

import { features } from '@/data/features';

const Page = () => (
    <>
        <VideoBackground src="/videos/features/power-flow-simulation.mp4">
            <PageHeader
                title="Power Flow Simulation"
                titleGradient="blue"
                description={<>Simulate the power flow in your design to ensure that it meets your requirements.</>}
            />
        </VideoBackground>

        <Section>
            <Block size="lg">
                <Columns>
                    <TextBlock title="Advanced Simulation Engine" titleGradient="blue">
                        <Box>
                            Our powerful simulation engine accurately models complex electrical systems, calculating
                            voltage drops, power losses, and load distribution throughout your design.
                        </Box>
                        <Box>
                            Analyze both AC and DC systems with support for different frequencies, phases, and power
                            factors to ensure your design performs as expected under various conditions.
                        </Box>
                    </TextBlock>
                    <VideoBlock
                        video={{
                            src: '/videos/features/power-flow-simulation.mp4',
                            width: 3024,
                            height: 1714,
                            playbackRate: 1.5,
                        }}
                    />
                </Columns>
            </Block>
            <SectionSpacer size="lg" />
            <Block size="lg">
                <Columns switchDesktopOrder>
                    <TextBlock title="Real-world Data Integration" titleGradient="green">
                        <Box>
                            Import real-world load profiles, generation data, and usage patterns to simulate how your
                            system will perform under actual operating conditions over time.
                        </Box>
                        <Box>
                            Test your design against various scenarios including peak demand, minimum load, and seasonal
                            variations to ensure reliable performance year-round.
                        </Box>
                    </TextBlock>
                    <TextBlock title="Visual Results" titleGradient="purple">
                        <Box>
                            View simulation results directly on your design with intuitive color coding and overlays
                            that highlight power flow directions, voltage levels, and potential issues.
                        </Box>
                        <Box>
                            Analyze detailed graphs and charts showing system performance over time, helping you
                            identify patterns, bottlenecks, or inefficiencies that might not be immediately obvious.
                        </Box>
                    </TextBlock>
                </Columns>
            </Block>
        </Section>

        <Features title="There's More to Discover" titleGradient="blue" variant="light">
            {[features.batteryCharging, features.wireSizing, features.validations]}
        </Features>

        <ContactSection />
    </>
);

export default Page;
