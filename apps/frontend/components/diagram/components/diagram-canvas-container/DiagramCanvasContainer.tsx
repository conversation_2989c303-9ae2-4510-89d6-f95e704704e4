import React, { FC, useRef, useCallback, useEffect } from 'react';

import { Box, Stack } from '@mantine/core';

import {
    DiagramSelect,
    DiagramControls,
    DIAGRAM_WIDTH,
    DIAGRAM_HEIGHT,
    CELL_WIDTH,
    CELL_HEIGHT,
    Z_INDEX,
    SCALE_MIN,
    SCALE_MAX,
    DIAGRAM_PADDING,
} from 'components/diagram';

import { useDiagramMode } from 'components/diagram/hooks';

import { useDndMonitor } from '@dnd-kit/core';

import { PinchState, createUseGesture, dragAction, moveAction, pinchAction, wheelAction } from '@use-gesture/react';

import { isHotkeyPressed, useHotkeys } from 'react-hotkeys-hook';

import { ContextMenuService } from 'components/diagram/services/ContextMenuService';
import { SelectionService } from 'components/diagram/services/SelectionService';

import { PermissionDiagramElements } from '@repo/dcide-component-models';

import { DiagramToolbar } from 'components/diagram/components/diagram-controls/DiagramToolbar';
import { DiagramTips } from 'components/diagram/components/diagram-tips/DiagramTips';
import { throttle } from 'radash';
import { ViewportService } from 'components/diagram/services/ViewportService';
import { useCurrentUser } from 'hooks/use-current-user';
import { usePermission } from 'hooks/use-permission';
import { DiagramService } from 'components/diagram/services/DiagramService';

import { state as modeState } from 'components/diagram/state/mode';

import cx from './DiagramCanvasContainer.module.css';

interface Props {
    children?: React.ReactNode;
    overlay?: React.ReactNode;
    background?: React.ReactNode;
}

export const DiagramCanvasContainer: FC<Props> = ({ children, background, overlay }) => {
    const user = useCurrentUser();

    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const containerTarget = useRef<HTMLDivElement>(null);
    const canvasTarget = useRef<HTMLDivElement>(null);

    const draggingOrResizing = useRef(false);
    const setDraggingOrResizing = (value: boolean) => {
        draggingOrResizing.current = value;
    };

    useDndMonitor({
        onDragStart: () => {
            setDraggingOrResizing(true);
        },
        onDragEnd: () => {
            setDraggingOrResizing(false);
        },
    });

    useHotkeys(['esc'], () => {
        setDraggingOrResizing(false);
    });

    useEffect(() => {
        ViewportService.container = containerTarget.current;
        ViewportService.canvas = canvasTarget.current;
    }, []);

    useEffect(() => {
        document.addEventListener('gesturestart', (e) => e.preventDefault());
        document.addEventListener('gesturechange', (e) => e.preventDefault());
    }, []);

    const throttledSetTransform = throttle({ interval: 100 }, ({ scaleOffset, x, y }) => {
        ViewportService.setTransform(
            {
                scale: scaleOffset,
                translation: {
                    x,
                    y,
                },
            },
            {
                smooth: true,
            },
        );
    });

    const zoom = useCallback(
        (props: PinchState) => {
            // Example: https://codesandbox.io/s/github/pmndrs/use-gesture/tree/main/demo/src/sandboxes/card-zoom
            const {
                origin: [originX, originY],
                first,
                movement: [scaleMovement],
                offset: [scaleOffset],
            } = props;

            let memo = props.memo ?? [ViewportService.transform.translation.x, ViewportService.transform.translation.y];

            if (draggingOrResizing.current) return [...memo, 0, 0];

            if (first) {
                const { width, height, x, y } = canvasTarget.current!.getBoundingClientRect();

                const tx = originX - (x + width / 2);
                const ty = originY - (y + height / 2);
                memo = [...memo, tx, ty];
            }

            const x = memo[0] - (scaleMovement - 1) * memo[2];
            const y = memo[1] - (scaleMovement - 1) * memo[3];

            throttledSetTransform({ scaleOffset, x, y });

            return memo;
        },
        [throttledSetTransform],
    );

    const pan = useCallback(({ delta, disabled = false }: GestureProps) => {
        if (disabled || draggingOrResizing.current) return;

        ViewportService.pan({
            x: ViewportService.transform.translation.x + delta[0],
            y: ViewportService.transform.translation.y + delta[1],
        });
    }, []);

    const useGesture = createUseGesture([dragAction, pinchAction, wheelAction, moveAction]);

    useGesture(
        {
            onDragStart: (state) => {
                if (
                    state.ctrlKey ||
                    state.metaKey ||
                    (state.target as HTMLElement).classList.contains('react-resizable-handle') ||
                    (state.target as HTMLElement).closest('[data-group]') ||
                    ((state.target as HTMLElement).closest('[data-selectable]') &&
                        !(state.target as HTMLElement).closest('[data-selectable-inner]')) ||
                    (state.target as HTMLElement).closest('[data-connection-anchor]') ||
                    (state.target as HTMLElement).closest('[data-selection-block]')
                ) {
                    return state.cancel();
                }

                if (modeState.mode === 'edit' && !draggingOrResizing.current) {
                    SelectionService.setStart({
                        x: state.xy[0],
                        y: state.xy[1],
                    });
                }
            },
            onDrag: (state) => {
                if (
                    state.pinching ||
                    state.ctrlKey ||
                    state.metaKey ||
                    (state.target as HTMLElement).classList.contains('react-resizable-handle') ||
                    (state.target as HTMLElement).closest('[data-diagram-comment-popover]')
                ) {
                    return state.cancel();
                }

                if (modeState.mode === 'navigate') {
                    pan({
                        delta: state.delta,
                        velocity: state.velocity,
                    });
                } else if (modeState.mode === 'edit' && !draggingOrResizing.current) {
                    SelectionService.setEnd({
                        x: state.xy[0],
                        y: state.xy[1],
                    });
                }
            },
            onDragEnd: (state) => {
                if ((state.target as HTMLElement).classList.contains('react-resizable-handle')) {
                    return state.cancel();
                }

                if (modeState.mode === 'edit' && !draggingOrResizing.current) {
                    SelectionService.setStart(null);
                    SelectionService.setEnd(null);
                }
            },
            onPinch: (state) => {
                return zoom(state);
            },
            onWheel: (state) => {
                if (state.pinching) return;
                if (state.ctrlKey) return; // = pinching

                pan({
                    delta: state.shiftKey ? state.delta : state.delta.map((x) => -x),
                    velocity: state.velocity,
                    acceleration: -0.6,
                });

                document.body.setAttribute('data-panning', 'true');
            },
            onWheelEnd: () => {
                document.body.removeAttribute('data-panning');
            },
            onMove: (state) => {
                const MIDDLE_MOUSE_BUTTON = 4;

                if (state.buttons === MIDDLE_MOUSE_BUTTON || isHotkeyPressed('space')) {
                    pan({
                        delta: state.delta,
                        velocity: state.velocity,
                    });
                }
            },
        },
        {
            target: containerTarget,
            drag: {
                // this is needed for our selecting logic to work, don't remove ;)
                threshold: 1,
            },
            pinch: {
                scaleBounds: { min: SCALE_MIN, max: SCALE_MAX },
                from: () => [ViewportService.transform.scale, 0],
            },
            eventOptions: { passive: false },
        },
    );

    useHotkeys('ArrowLeft', () => {
        ViewportService.pan(
            {
                ...ViewportService.transform.translation,
                x: ViewportService.transform.translation.x + CELL_WIDTH,
            },
            {
                smooth: true,
            },
        );
    });

    useHotkeys('ArrowRight', () => {
        ViewportService.pan(
            {
                ...ViewportService.transform.translation,
                x: ViewportService.transform.translation.x - CELL_WIDTH,
            },
            {
                smooth: true,
            },
        );
    });

    useHotkeys('ArrowUp', () => {
        ViewportService.pan(
            {
                ...ViewportService.transform.translation,
                y: ViewportService.transform.translation.y + CELL_HEIGHT,
            },
            {
                smooth: true,
            },
        );
    });

    useHotkeys('ArrowDown', () => {
        ViewportService.pan(
            {
                ...ViewportService.transform.translation,
                y: ViewportService.transform.translation.y - CELL_HEIGHT,
            },
            {
                smooth: true,
            },
        );
    });

    return (
        <>
            <DiagramSelect />
            <DiagramModeEffects />
            <Box
                id="diagram-canvas-container"
                ref={containerTarget}
                className={cx.container}
                style={{
                    zIndex: Z_INDEX.CANVAS.ROOT,
                }}
                onContextMenu={(event) => {
                    event.preventDefault();

                    if (!canEdit) {
                        return;
                    }

                    const target = event.target;
                    if (target instanceof SVGElement || target instanceof HTMLElement) {
                        if (target.closest('[data-no-context-menu]')) {
                            return;
                        }
                    }

                    ContextMenuService.openGenericContextMenu({
                        x: event.clientX,
                        y: event.clientY,
                    });
                }}
                onDoubleClick={() => {
                    ViewportService.resetZoom();
                }}
                data-canvas-container
                data-mode="edit"
            >
                <Box
                    id="diagram-canvas"
                    ref={canvasTarget}
                    style={(theme) => ({
                        'position': 'absolute',
                        'top': 0,
                        'left': 0,
                        'width': `${DIAGRAM_WIDTH}px`,
                        'height': `${DIAGRAM_HEIGHT}px`,

                        'transformOrigin': 'center',
                        'transform': ViewportService.transformCSS(),
                        '--viewport-reverse-transform': ViewportService.reverseTransformCSS(),

                        'border': `${DIAGRAM_PADDING}px solid ${theme.colors.gray[0]}`,
                    })}
                >
                    <Box id="diagram-canvas-background">{background}</Box>
                    <Box id="diagram-canvas-content">{children}</Box>
                    <Box id="diagram-canvas-overlay">{overlay}</Box>
                </Box>
            </Box>
            <Box
                style={() => ({
                    position: 'absolute',
                    zIndex: Z_INDEX.CONTROLS,
                    left: 'var(--mantine-spacing-xs)',
                    bottom: 'var(--mantine-spacing-xs)',
                })}
            >
                <Stack>
                    <DiagramControls.Layers />
                    {canEdit && <DiagramControls.History />}
                    <DiagramControls.Zoom />
                </Stack>
            </Box>
            <Box
                style={{
                    position: 'absolute',
                    zIndex: Z_INDEX.SIDEBAR,
                    right: 'var(--mantine-spacing-xs)',
                    top: 'var(--mantine-spacing-xs)',
                    left: 'calc(var(--mantine-spacing-xs) * 2 + 32px)',
                    display: 'flex',
                    alignItems: 'flex-start',
                    justifyContent: 'flex-end',
                    gap: 'var(--mantine-spacing-xs)',
                    pointerEvents: 'none',
                }}
            >
                {user && <DiagramTips />}
            </Box>
            <Box
                style={{
                    position: 'absolute',
                    zIndex: Z_INDEX.CONTROLS,
                    left: 'var(--mantine-spacing-xs)',
                    top: 'calc(var(--header-height-floating) + var(--mantine-spacing-xs) * 2)',
                }}
                data-diagram-toolbar
            >
                <DiagramToolbar />
            </Box>
        </>
    );
};

const DiagramModeEffects: FC = () => {
    const { mode } = useDiagramMode();

    const prevModeRef = useRef(mode);

    useEffect(() => {
        const handleMouseDown = (event: MouseEvent) => {
            if (event.button === 1) {
                prevModeRef.current = mode;
                DiagramService.setMode('navigate');
            }
        };

        const handleMouseUp = (event: MouseEvent) => {
            if (event.button === 1) {
                DiagramService.setMode(prevModeRef.current);
            }
        };

        document.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mouseup', handleMouseUp);

        return () => {
            document.removeEventListener('mousedown', handleMouseDown);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [mode]);

    useEffect(() => {
        const element = document.getElementById('diagram-canvas-container');

        if (!element) return;

        element.setAttribute('data-mode', mode);
    }, [mode]);

    return null;
};

type GestureProps = {
    delta: number[];
    disabled?: boolean;
    velocity?: [number, number];
    acceleration?: number;
    xMin?: number;
    xMax?: number;
    yMin?: number;
    yMax?: number;
    scaleMin?: number;
    scaleMax?: number;
    origin?: number[];
};
