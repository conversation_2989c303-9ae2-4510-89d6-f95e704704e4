import { <PERSON>, MouseEventHandler } from 'react';

import { ActionIcon, Box, Group } from '@mantine/core';
import { IoEyeOutline } from 'react-icons/io5';

import { isNumber } from 'radash';

import { CurrentOSZoneType } from '@repo/dcide-component-models';

import cx from './Validations.module.css';

const ValidationTeaser: FC<{
    name: string;
    errors: any[];
    warnings: any[];
    activate: () => void;
    highlight?: () => void;
    lowlight?: () => void;
    moveIntoView: MouseEventHandler;
    currentOSZoneType?: CurrentOSZoneType;
}> = ({ name, errors, warnings, activate, highlight, lowlight, moveIntoView, currentOSZoneType }) => {
    return (
        <Box className={cx.teaser} onClick={activate} onMouseEnter={highlight} onMouseLeave={lowlight} data-compact>
            <Box className={cx.teaserContent}>
                <Group justify="space-between">
                    <Box className={cx.teaserTitle}>{name}</Box>
                    <Group gap={4}>
                        {isNumber(currentOSZoneType) && (
                            <Box className={cx.teaserBadge} data-type="current-os">
                                {currentOSZoneType}
                            </Box>
                        )}
                        {warnings.length > 0 && (
                            <Box className={cx.teaserBadge} data-type="warning">
                                {warnings.length} {warnings.length === 1 ? 'warning' : 'warnings'}
                            </Box>
                        )}
                        {errors.length > 0 && (
                            <Box className={cx.teaserBadge} data-type="error">
                                {errors.length} {errors.length === 1 ? 'error' : 'errors'}
                            </Box>
                        )}
                    </Group>
                </Group>
            </Box>
            <ActionIcon onClick={moveIntoView} variant="transparent">
                <IoEyeOutline />
            </ActionIcon>
        </Box>
    );
};

export { ValidationTeaser };
