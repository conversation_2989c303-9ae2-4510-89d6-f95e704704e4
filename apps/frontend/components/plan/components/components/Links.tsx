import { Anchor, Group, GroupProps } from '@mantine/core';

import Link from 'next/link';

import { RouterHelpers } from 'helpers/RouterHelpers';
import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { useCurrentUser } from 'hooks/use-current-user';

import cx from './Links.module.css';

export const Links = (props: GroupProps) => {
    const user = useCurrentUser();

    return (
        <Group gap="md" className="gradient-text-default" {...props}>
            <Anchor
                component={Link}
                className={cx.link}
                href={user ? ProjectHelpers.urls.create() : '/login?redirect=/projects/create'}
                c="white"
                target="_blank"
                visibleFrom="sm"
            >
                Try our Design Editor
            </Anchor>
            <Anchor component={Link} href={RouterHelpers.urls.search()} c="white" className={cx.link} target="_blank">
                Browse our Product Catalog
            </Anchor>
            <Anchor
                component={Link}
                className={cx.link}
                href={CompanyProfileHelpers.urls.create()}
                c="white"
                target="_blank"
            >
                Create Company Profile
            </Anchor>
        </Group>
    );
};
