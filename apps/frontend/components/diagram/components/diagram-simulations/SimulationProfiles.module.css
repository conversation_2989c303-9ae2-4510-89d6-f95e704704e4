.dropzone {
    position: relative;

    background: hotpink;
}

.tableScrollContainer {
    &::before,
    &::after {
        content: '';

        position: absolute;
        top: 0;

        z-index: 10;

        width: 1px;
        height: 100%;

        background-color: var(--mantine-color-gray-2);
    }

    &::before {
        left: 0;
    }

    &::after {
        right: 0;
    }
}

.table {
    position: relative;

    th {
        white-space: nowrap;
    }

    td:has(input) {
        padding: 0;

        input {
            padding: var(--table-vertical-spacing) var(--table-horizontal-spacing, var(--mantine-spacing-xs));
            border: none;
            border-radius: 0;

            &:hover {
                background: var(--mantine-color-gray-0);
            }

            &:focus {
                outline: none;
            }
        }
    }
}
