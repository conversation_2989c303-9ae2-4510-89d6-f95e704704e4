import React, { FC } from 'react';

import { Stack } from '@mantine/core';

import { DiagramComponentInstance } from '@repo/dcide-component-models';

import { FormatHelpers } from 'helpers/formatters';

import { SpecificationsRow } from '../sections/Specifications.Row';
import { SimulationPortWrapper } from '../sections/Simulation.Port';
import { PortHelpers } from 'components/diagram/helpers/PortHelpers';

import { resistanceConverter } from 'units/resistance';
import { powerConverter } from 'units/power';
import { currentConverter } from 'units/current';
import { voltageConverter } from 'units/voltage';

const PortFieldsSimulationView: FC<{
    componentInstance: DiagramComponentInstance;
    port: number;
}> = ({ componentInstance, port }) => {
    const portConfiguration = PortHelpers.getConfiguration(componentInstance, port);

    const powerFlowDirection = FormatHelpers.formatPowerFlowDirection(portConfiguration.powerFlowDirection!);
    const controlMethod = FormatHelpers.formatControlMethod(portConfiguration.controlMethod);
    const voltage = FormatHelpers.formatMeasurement(portConfiguration.voltage, voltageConverter);
    const current = FormatHelpers.formatMeasurement(portConfiguration.current, currentConverter);
    const power = FormatHelpers.formatMeasurement(portConfiguration.power, powerConverter);
    const resistance = FormatHelpers.formatMeasurement(portConfiguration.droopResistance, resistanceConverter);

    const items = [
        { label: 'Power Flow', value: powerFlowDirection },
        { label: 'Control method', value: controlMethod },
        { label: 'Voltage', value: voltage },
        { label: 'Current', value: current },
        { label: 'Power', value: power },
        { label: 'Droop resistance', value: resistance },
    ].filter((item) => !!item.value);

    return items.length ? (
        <SimulationPortWrapper componentInstance={componentInstance} port={port}>
            <Stack gap={6}>
                {items.map((item) => (
                    <SpecificationsRow label={item.label} key={item.label}>
                        {item.value}
                    </SpecificationsRow>
                ))}
            </Stack>
        </SimulationPortWrapper>
    ) : null;
};

export { PortFieldsSimulationView };
