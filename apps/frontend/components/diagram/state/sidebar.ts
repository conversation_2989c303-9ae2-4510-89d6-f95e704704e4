import { proxy } from 'valtio';

import { OpenConfirmModal } from '@mantine/modals/lib/context';

import { DIAGRAM_SIDEBAR_WIDTH } from 'components/diagram/diagram-dimensions';

export enum SidebarType {
    'AI' = 'ai',
    'ADD' = 'add',
    'EDIT' = 'edit',
    'COMMENTS' = 'comments',
    'CHAT' = 'chat',
    'BILL_OF_MATERIALS' = 'billOfMaterials',
    'FILES' = 'files',
    'TIPS' = 'tips',
    'SHARE' = 'share',
    'PROJECT' = 'project',
    'SIMULATIONS' = 'simulations',
    'VALIDATIONS' = 'validations',
    'WIRE_SIZING' = 'wireSizing',
}

export enum SidebarTab {
    SPECIFICATIONS = 'specifications',
    SIMULATION = 'simulation',
    NOTES = 'notes',
    SETIINGS = 'settings',
    INSTALLATION = 'installation',
    WIRE_SIZING_ASSISTANT = 'wireSizingAssistant',
    CONNECT_CABLE = 'connectCable',
    COMMENTS = 'comments',
    FILES = 'files',
    DATASHEET = 'datasheet',
    COMPONENTS = 'components',
    QUOTES = 'quotes',
    AI = 'ai',
    ADD_GENERIC = 'addGeneric',
    ADD_TEAM = 'addTeam',
    ADD_CATALOG = 'addCatalog',
}

export enum SidebarEditSubtype {
    'EDIT_COMPONENT' = 'editComponent',
    'EDIT_COMPONENT_SEARCH' = 'editComponentSearch',
    'EDIT_CONNECTION' = 'editConnection',
    'EDIT_CONNECTIONS' = 'editConnections',
}

export type SidebarShareData = {
    email?: string;
    hideCollaborators?: boolean;
    hidePublicLink?: boolean;
};

export type SidebarConfirmModal = Omit<OpenConfirmModal, 'onConfirm' | 'onCancel'> & {
    onConfirm?: () => Promise<void>;
    onCancel?: () => Promise<void>;
};

const defaultValues = {
    collapsed: false,
    type: SidebarType.ADD,
    addSidebar: SidebarTab.ADD_GENERIC,
    editSidebar: null,
    shareSidebarData: null,
    sidebarTab: null,
    editComponentInstanceTab: null,
    width: DIAGRAM_SIDEBAR_WIDTH,
    confirmModal: null,
};

const state = proxy<{
    collapsed: boolean;
    type: SidebarType;
    addSidebar: SidebarTab | null;
    editSidebar: SidebarEditSubtype | null;
    shareSidebarData: SidebarShareData | null;
    sidebarTab: SidebarTab | null;
    editComponentInstanceTab: SidebarTab | null;
    width: number;
    confirmModal: SidebarConfirmModal | null;
}>(defaultValues);

const reset = () => {
    Object.keys(state).forEach((key) => {
        // @ts-ignore
        state[key] = defaultValues[key];
    });
};

export { state, reset };
