import { z } from 'zod';

import {
    ConductorMaterial,
    Current,
    Length,
    PowerFactor,
    Resistance,
    Temperature,
    Voltage,
    WireSize,
} from '../validators';

import { EdgeSchema } from './Edge.schema';
import { ComponentInstanceSchema } from './DiagramComponentInstance.schema';
import { IECInstallationMethod, NECInstallationMethod } from './InstallationType';
import { ConnectionCores } from './ConnectionCores';
import { isNumber } from 'radash';

export const DiagramConnectionAnchorSchema = z.object({
    componentInstanceId: ComponentInstanceSchema.shape.id,
    edge: EdgeSchema,
    offset: z.number().gte(0).default(0),
    port: z.preprocess((value) => {
        if (typeof value === 'number' && value < 0) {
            return null;
        }

        return value;
    }, z.number().gte(0).nullable().default(null)),
});

// TODO: refactor in specifications and configuration
export const DiagramConnectionSchema = z.object({
    id: z.string(),
    label: z.string().default(''),
    notes: z.string().default(''),
    from: DiagramConnectionAnchorSchema,
    to: DiagramConnectionAnchorSchema,
    voltage: Voltage.validator, // TODO: remove, requirements.voltage
    current: Current.validator, // TODO: remove
    lines: z
        .object({
            AC: z
                .object({
                    L1: z.boolean().default(true),
                    L2: z.boolean().default(true),
                    L3: z.boolean().default(true),
                    N: z.boolean().default(true),
                    PE: z.boolean().default(false),
                })
                .default({}),
            DC: z
                .object({
                    'L+': z.boolean().default(true),
                    'M': z.boolean().default(false),
                    'L-': z.boolean().default(true),
                    'PE': z.boolean().default(false),
                })
                .default({}),
        })
        .default({}),
    multiple: z
        .object({
            parallel: z.preprocess((value) => {
                if (!isNumber(value)) {
                    return 1;
                }

                return value < 1 ? 1 : value;
            }, z.number().int().min(1)),
        })
        .default({}),
    wireSize: WireSize.validator,
    length: z.preprocess((value) => {
        // config
        return value ? value : undefined;
    }, Length.validator.default({})),
    resistance: z.preprocess((value) => {
        // config
        return value ? value : undefined;
    }, Resistance.validator.default({})),
    material: ConductorMaterial.validator.default('copper'), // todo: rename to conductorMaterial

    conductorTemperature: Temperature.validators.value, // TODO: remove
    installationMethod: z // config
        .union([z.nativeEnum(NECInstallationMethod), z.nativeEnum(IECInstallationMethod)])
        .nullable()
        .default(NECInstallationMethod.FREE_AIR),
    ambientTemperature: Temperature.validators.value, // config
    cores: z.nativeEnum(ConnectionCores).default(ConnectionCores.SINGLE),
    powerFactor: PowerFactor.validator, // config
    requirements: z // config
        .object({
            voltage: Voltage.validators.value,
            current: z
                .object({
                    'L+': Current.validators.value,
                    'M': Current.validators.value,
                    'L-': Current.validators.value,
                    'PE': Current.validators.value,
                })
                .default({}),
        })
        .default({}),
});
