import { FC } from 'react';

import { Box } from '@mantine/core';

import { DiagramConnectionAnchor } from '@repo/dcide-component-models';

import classes from './Anchor.module.css';

export const AnchorArea: FC<{
    center: { x: number; y: number };
    edge: DiagramConnectionAnchor['edge'];
    radius: number;
    onPointerUp: (event: React.PointerEvent<SVGCircleElement>) => void;
    onPointerDown: () => void;
    onMouseEnter: () => void;
    onMouseLeave: () => void;
}> = ({ center, edge, radius, ...eventHandlers }) => {
    const tb = edge === 'top' || edge === 'bottom';
    const tr = edge === 'top' || edge === 'right';

    return (
        <Box
            component="path"
            className={classes.activeArea}
            d={`
            M ${center.x - (tb ? radius : 0)},${center.y - (tb ? 0 : radius)}
            A ${radius},${radius}
            0 0,${tr ? 1 : 0}
            ${center.x + (tb ? radius : 0)},${center.y + (tb ? 0 : radius)}
        `}
            {...eventHandlers}
            data-selection-block
        />
    );
};
