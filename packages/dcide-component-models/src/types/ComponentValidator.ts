import {
    BatteryValidator,
    BusValidator,
    CapacitorValidator,
    ChargerValidator,
    CircuitBreakerValidator,
    CombinerBoxValidator,
    ContactorValidator,
    ConverterValidator,
    CustomValidator,
    DisconnectValidator,
    FuseValidator,
    GeneratorValidator,
    GroundingValidator,
    HVACValidator,
    HydroValidator,
    LoadValidator,
    LightValidator,
    MeterValidator,
    MotorValidator,
    PanelValidator,
    PowerDistributionUnitValidator,
    RapidShutdownDeviceValidator,
    SolarValidator,
    SolutionValidator,
    TransferSwitchValidator,
    TransformerValidator,
    UtilityValidator,
    WindValidator,
    OtherValidator,
} from '../validators/components';

export type ComponentValidator =
    | typeof BatteryValidator
    | typeof BusValidator
    | typeof CapacitorValidator
    | typeof ChargerValidator
    | typeof CircuitBreakerValidator
    | typeof CombinerBoxValidator
    | typeof ContactorValidator
    | typeof ConverterValidator
    | typeof CustomValidator
    | typeof DisconnectValidator
    | typeof FuseValidator
    | typeof GeneratorValidator
    | typeof GroundingValidator
    | typeof HVACValidator
    | typeof HydroValidator
    | typeof LoadValidator
    | typeof LightValidator
    | typeof MeterValidator
    | typeof MotorValidator
    | typeof OtherValidator
    | typeof PanelValidator
    | typeof PowerDistributionUnitValidator
    | typeof RapidShutdownDeviceValidator
    | typeof SolarValidator
    | typeof SolutionValidator
    | typeof TransferSwitchValidator
    | typeof TransformerValidator
    | typeof UtilityValidator
    | typeof WindValidator;
