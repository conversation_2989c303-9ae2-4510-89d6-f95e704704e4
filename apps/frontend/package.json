{"name": "@repo/frontend", "version": "0.3.0", "private": true, "scripts": {"dev": "next dev -p 3001 --turbopack", "build": "next build", "start": "next start", "lint": "biome lint --write", "cypress:open": "cypress open", "cypress:loadtest": "cypress run --spec cypress/e2e/load_test_local.cy.ts --record", "cypress:loadteststaging": "cypress run --spec cypress/e2e/load_test_staging.cy.ts --record", "cypress:loadtestproduction": "cypress run --spec cypress/e2e/load_test_production.cy.ts --record", "test:unit": "jest"}, "dependencies": {"@ably/spaces": "^0.4.0", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^7.0.0", "@fireworks-js/react": "^2.10.5", "@hookform/resolvers": "^3.3.4", "@mantine/carousel": "8.1.1", "@mantine/charts": "8.1.1", "@mantine/core": "8.1.1", "@mantine/dates": "8.1.1", "@mantine/dropzone": "8.1.1", "@mantine/form": "8.1.1", "@mantine/hooks": "8.1.1", "@mantine/modals": "8.1.1", "@mantine/notifications": "8.1.1", "@mantine/nprogress": "8.1.1", "@mantine/spotlight": "8.1.1", "@mantine/tiptap": "8.1.1", "@mapbox/search-js-react": "1.1.0", "@payloadcms/live-preview-react": "3.43.0", "@repo/dcide-component-models": "*", "@repo/dcide-sync-engine": "*", "@svgdotjs/svg.js": "^3.2.0", "@tiptap/extension-character-count": "2.14.0", "@tiptap/extension-link": "2.14.0", "@tiptap/extension-mention": "2.14.0", "@tiptap/extension-placeholder": "2.14.0", "@tiptap/extension-task-item": "2.14.0", "@tiptap/extension-task-list": "2.14.0", "@tiptap/extension-text-align": "2.14.0", "@tiptap/pm": "2.14.0", "@tiptap/react": "2.14.0", "@tiptap/starter-kit": "2.14.0", "@tiptap/suggestion": "2.14.0", "@use-gesture/react": "^10.3.1", "@usersnap/browser": "^1.0.2", "@vercel/analytics": "^1.2.2", "@vercel/functions": "^1.5.2", "@vercel/speed-insights": "^1.0.7", "dayjs": "^1.11.13", "derive-valtio": "^0.1.0", "embla-carousel-auto-height": "8.6", "embla-carousel-autoplay": "8.6", "embla-carousel-react": "8.6", "export-to-csv": "^1.2.2", "fflate": "^0.8.2", "fireworks-js": "^2.10.7", "framer-motion": "^11.2.3", "fuse.js": "^7.1.0", "html2canvas": "^1.4.1", "iron-session": "^8.0.1", "js-cookie": "^3.0.5", "mongodb": "^6.11.0", "patch-package": "^8.0.0", "react-dropzone": "^14.2.3", "react-dropzone-esm": "^15.0.1", "react-error-boundary": "^4.0.13", "react-gtm-module": "^2.0.11", "react-hook-form": "^7.51.3", "react-hotkeys-hook": "^4.5.0", "react-icons": "^5.5.0", "react-international-phone": "^4.5.0", "react-markdown": "^9.0.0", "react-resizable": "^3.0.5", "react-simple-typewriter": "^5.0.1", "recharts": "^2.15.3", "remark-gfm": "^4.0.0", "sonner": "^1.5.0", "swr": "^2.2.5", "tiptap-markdown": "^0.8.10", "tiptap-unique-id": "^1.2.1", "valtio": "^2.1.3"}, "devDependencies": {"@types/cookie": "^0.6.0", "@types/jsonwebtoken": "^9.0.7", "@types/react-gtm-module": "^2.0.2", "@types/react-resizable": "^3.0.4", "cypress": "^13.5.1", "husky": "^9.0.11", "postcss": "^8.5.3", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "smtp-tester": "^2.1.0"}, "resolutions": {"markdown-it": "13.0.2"}, "overrides": {"react-is": "19.0.0"}}