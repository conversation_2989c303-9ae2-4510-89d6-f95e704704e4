.suggestion {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 100%;

    padding: var(--mantine-spacing-sm);

    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--mantine-radius-sm);

    text-align: center;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);

    transition: background-color 0.3s ease;

    &:hover {
        color: rgba(255, 255, 255, 1);
        background-color: rgba(255, 255, 255, 0.1);
    }

    @media (min-width: var(--mantine-breakpoint-sm)) {
        padding: var(--mantine-spacing-md);
    }
}
