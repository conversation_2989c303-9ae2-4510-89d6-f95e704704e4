import { useEffect, useState } from 'react';

import { useInterval } from '@mantine/hooks';
import { Slider } from '@mantine/core';
import { IoPauseSharp, IoPlayBackSharp, IoPlayForwardSharp, IoPlaySharp } from 'react-icons/io5';

import { parseCSV } from 'helpers/parseCSV';

import { useFile } from 'hooks/use-file';
import { useSimulations } from 'components/diagram/hooks/use-simulations';
import { useSimulation } from 'components/diagram/hooks/use-simulation';
import { SimulationService } from 'components/diagram/services/SimulationService';
import { simulationState } from 'components/diagram/state/simulation';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';

import cx from './DiagramSimulationSlider.module.css';

const SPEED_STEPS = [1, 2, 4, 0.5];
const BASE_SPEED = 2000; // in milliseconds

const DiagramSimulationSlider = () => {
    const { active: activeSimulationId, activeTimestamp } = useSimulations();
    const activeSimulation = useSimulation(activeSimulationId);

    const [activeSpeed, setActiveSpeed] = useState(0);
    const speed = SPEED_STEPS[activeSpeed];

    const playState = useInterval(() => setNextTimestamp(), BASE_SPEED / speed);

    useEffect(() => {
        return () => {
            playState.stop();
        };
    }, [activeSimulationId]);

    useEffect(() => {
        if (!playState.active) return;

        playState.stop();
        playState.start();
    }, [activeSpeed]);

    const { file: csvFile } = useFile(activeSimulation?.file);

    const [timeStamps, setTimeStamps] = useState<string[]>([]);

    useEffect(() => {
        fetch(csvFile.url)
            .then((response) => response.text())
            .then((text) => {
                const data = parseCSV(text);

                if (data) {
                    setTimeStamps(data.map(({ time }) => time));
                }
            })
            .catch((err) => console.error('Error parsing CSV file', err));
    }, [csvFile.url]);

    if (!activeSimulation) return null;

    const cycleSpeed = () => {
        if (activeSpeed === SPEED_STEPS.length - 1) {
            setActiveSpeed(0);
        } else {
            setActiveSpeed((prevSpeed) => prevSpeed + 1);
        }
    };

    const setNextTimestamp = () => {
        const currentTimestamp = simulationState.activeTimestamp;

        if (currentTimestamp < activeSimulation.nbTimestamps - 1) {
            SimulationService.setActiveTimestamp(currentTimestamp + 1);
            return;
        }

        SimulationService.setActiveTimestamp(0);
    };

    const setPreviousTimestamp = () => {
        const currentTimestamp = simulationState.activeTimestamp;

        if (currentTimestamp > 0) {
            SimulationService.setActiveTimestamp(currentTimestamp - 1);
            return;
        }

        SimulationService.setActiveTimestamp(activeSimulation.nbTimestamps - 1);
    };

    const getlLabel = (ts: number) => timeStamps?.[ts] || `${ts + 1} / ${activeSimulation.nbTimestamps}`;

    return (
        <>
            <DiagramToolbar.IconButton onClick={setPreviousTimestamp} tooltip="Previous">
                <IoPlayBackSharp />
            </DiagramToolbar.IconButton>
            <DiagramToolbar.IconButton onClick={playState.toggle} tooltip="Play">
                {playState.active ? <IoPauseSharp /> : <IoPlaySharp />}
            </DiagramToolbar.IconButton>
            <DiagramToolbar.IconButton onClick={() => setNextTimestamp()} tooltip="Next">
                <IoPlayForwardSharp />
            </DiagramToolbar.IconButton>
            <DiagramToolbar.Divider />
            <DiagramToolbar.IconButton onClick={cycleSpeed} tooltip="Speed">
                ×{speed}
            </DiagramToolbar.IconButton>
            <DiagramToolbar.Divider />
            <DiagramToolbar.Text pr={0}>{getlLabel(activeTimestamp)}</DiagramToolbar.Text>
            <Slider
                classNames={cx}
                color="primary"
                value={activeTimestamp + 1}
                onChange={(value) => SimulationService.setActiveTimestamp(value - 1)}
                min={1}
                max={activeSimulation.nbTimestamps}
                size={2}
                thumbSize={10}
                label={(value) => getlLabel(value - 1)}
            />
        </>
    );
};

export { DiagramSimulationSlider };
