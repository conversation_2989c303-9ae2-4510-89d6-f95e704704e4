// @ts-nocheck
import React from 'react';

import { DiagramConnection, MeasurementSystem, CurrentConverter } from '@repo/dcide-component-models';

import { ConnectionService } from 'components/diagram/services';
import { ConnectionLabel } from 'components/diagram/components/diagram-connections/components/ConnectionLabel';

import { useDiagram } from 'components/diagram/hooks';

import { useWireSizings } from 'components/diagram/hooks/use-wire-sizings';
import { WireSizingService } from 'components/diagram/services/WireSizingService';
import { FormatHelpers } from 'helpers/formatters';

import { useDefaultMeasurementSystem } from 'hooks/use-default-measurement-system';
import { WireSizingHelpers } from 'components/diagram/helpers/WireSizingHelpers';
import { MeasurementSystemHelpers } from 'helpers/measurement-system';

const ConnectionLabelsWireSizing = ({ connection }: { connection: DiagramConnection }) => {
    return null;

    const measurementSystem = useDefaultMeasurementSystem();

    const diagram = useDiagram();
    const { wireSizing } = useWireSizings();

    const { from, to } = connection;
    const { points } = ConnectionService.getPath(from, to, diagram) || {};

    if (!points) return null;
    if (!wireSizing) return null;

    const { maxCurrent, minWireSize, maxLength } =
        WireSizingService.transformResult(wireSizing.result)[connection.id] ?? {};

    const connectionCurrent = FormatHelpers.formatValue(maxCurrent, CurrentConverter);

    const formattedConnectionCurrent = maxCurrent === 0 ? '0 A' : connectionCurrent;

    let formattedWireSize = '';

    if (minWireSize) {
        if (measurementSystem === MeasurementSystem.METRIC) {
            formattedWireSize = `${minWireSize} mm²`;
        }

        if (measurementSystem === MeasurementSystem.IMPERIAL) {
            const awg = WireSizingHelpers.toAWG(minWireSize);

            if (awg) {
                formattedWireSize = `${awg} AWG`;
            }
        }
    }

    const formattedLength = MeasurementSystemHelpers.formatLengthValue(measurementSystem, maxLength) || '';

    const labels = [{ label: formattedConnectionCurrent }];

    if (formattedWireSize) {
        labels.push({ label: formattedWireSize });
    }

    if (formattedLength) {
        labels.push({ label: formattedLength });
    }

    return <ConnectionLabel points={points} edge={from.edge} labels={labels} />;
};

export { ConnectionLabelsWireSizing };
