import { useEffect, useState } from 'react';

import { <PERSON><PERSON><PERSON>, Drawer, Flex, Stack, Text, Tooltip, UnstyledButton, Loader } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { modals } from '@mantine/modals';
import { IoTimeOutline, IoTrashOutline } from 'react-icons/io5';

import { useSearchThread } from 'components/plan/hooks/use-search-thread';
import { useSearchThreads } from 'components/plan/hooks/use-search-threads';

import { TextHelpers } from 'helpers/TextHelpers';

import { DateService } from 'services/DateService';
import { SearchAgentService } from 'services/AISearchAgentService';

import cx from './ThreadHistory.module.css';

const ThreadHistory = () => {
    const { threads } = useSearchThreads();
    const { searchThreadId, setSearchThreadId, create } = useSearchThread();

    const [opened, handlers] = useDisclosure();
    const [deletingThreadId, setDeletingThreadId] = useState<string | null>(null);

    const handleDelete = async (threadId: string) => {
        modals.openConfirmModal({
            title: 'Delete Chat',
            children: 'Are you sure you want to delete this chat? This action cannot be undone.',
            labels: { confirm: 'Yes, delete', cancel: 'No, cancel' },
            confirmProps: { color: 'red' },
            onConfirm: async () => {
                setDeletingThreadId(threadId);
                await SearchAgentService.delete(threadId);

                if (searchThreadId === threadId) {
                    await create();
                    handlers.close();
                }

                setDeletingThreadId(null);
            },
        });
    };

    const handleHistoryItemClick = async (threadId: string) => {
        await SearchAgentService.refresh(threadId);
        setSearchThreadId(threadId);
        handlers.close();
    };

    useEffect(() => {
        if (opened) {
            SearchAgentService.refreshHistory();
        }
    }, [opened]);

    if (!threads?.length) return null;

    return (
        <>
            <Tooltip label="Older chats">
                <ActionIcon variant="outline" size={36} data-thread-history onClick={handlers.open}>
                    <IoTimeOutline />
                </ActionIcon>
            </Tooltip>

            <Drawer
                size="sm"
                position="right"
                opened={opened}
                onClose={handlers.close}
                withCloseButton={false}
                classNames={cx}
            >
                <Stack gap={4}>
                    {threads?.map((thread) => (
                        <UnstyledButton
                            key={thread.id}
                            className={cx.item}
                            onClick={() => handleHistoryItemClick(thread.id)}
                            data-active={searchThreadId === thread.id}
                        >
                            <Flex justify="space-between" align="flex-start" gap="sm">
                                <Text>
                                    {TextHelpers.getTextWithEllipsis(
                                        thread.thread?.input.find((msg) => msg.role === 'user')?.content ?? 'New chat',
                                        100,
                                    )}
                                </Text>
                                <ActionIcon
                                    size="md"
                                    variant="subtle"
                                    c="dimmed"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        handleDelete(thread.id);
                                    }}
                                >
                                    {deletingThreadId === thread.id ? (
                                        <Loader size={14} color="dimmed" />
                                    ) : (
                                        <IoTrashOutline />
                                    )}
                                </ActionIcon>
                            </Flex>
                            <Text c="dimmed" fz="xs">
                                {DateService.formatDistanceToNow(thread.createdAt)}
                            </Text>
                        </UnstyledButton>
                    ))}
                </Stack>
            </Drawer>
        </>
    );
};

export { ThreadHistory };
