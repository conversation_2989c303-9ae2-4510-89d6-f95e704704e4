import React, { FC } from 'react';

import { Box, Table } from '@mantine/core';
import { ContextModalProps } from '@mantine/modals';

import { ModalTitle } from 'components/modals/ModalTitle';

const InstallationMethodInfoModal: FC<ContextModalProps> = () => {
    const data = [
        {
            method: 'A1',
            description: 'Insulated conductors or single-core cables in conduit in a thermally insulated wall',
        },
        { method: 'A2', description: 'Multi-core cables in conduit in a thermally insulated wall' },
        {
            method: 'B1',
            description:
                'Insulated conductors or single-core cables in conduit on a wooden, or masonry wall or spaced less than 0,3 x conduit diameter from it',
        },
        {
            method: 'B2',
            description:
                'Multi-core cable in conduit on a wooden, or mansonry wall or spaced less than 0,3 x conduit diameter from it',
        },
        {
            method: 'C',
            description:
                'Single-core or multi-core cables: - fixed on, or sapced less than 0.3 x cable diameter from a wooden wall',
        },
        { method: 'D1', description: 'Multi-core cables in conduit or in cable ducting in the ground' },
        { method: 'D2', description: 'Single-core cable in conduit or in cable ducting in the ground' },
        {
            method: 'E',
            description: 'Single-core or multi-core cables: On perforated tray run horizontally or vertically',
        },
    ];

    return (
        <Box>
            <ModalTitle mt="md">Installation Methods</ModalTitle>
            <Table mt="lg" withTableBorder withColumnBorders>
                <Table.Tbody>
                    {data.map((item) => (
                        <Table.Tr key={item.method}>
                            <Table.Td>
                                <strong>{item.method}</strong>
                            </Table.Td>
                            <Table.Td>{item.description}</Table.Td>
                            <Table.Td>
                                <img
                                    src={`/images/installation-methods/${item.method}.svg`}
                                    style={{ width: 160, height: 'auto' }}
                                    alt=""
                                />
                            </Table.Td>
                        </Table.Tr>
                    ))}
                </Table.Tbody>
            </Table>
        </Box>
    );
};

export { InstallationMethodInfoModal };
