import Link from 'next/link';

import { Box, Text, UnstyledButton } from '@mantine/core';
import { IoAddCircleOutline } from 'react-icons/io5';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import cx from './ComponentLanding.AddProduct.module.css';
import iconCx from 'components/page/Hero.module.css';

const ComponentLandingAddProduct = () => {
    return (
        <Box className={`${cx.animationWrapper} animation-border-spin`}>
            <UnstyledButton
                component={Link}
                href={ComponentHelpers.urls.create()}
                className={`${iconCx.componentIcon} ${cx.root}`}
            >
                <Box className={`${iconCx.icon} ${cx.icon}`}>
                    <IoAddCircleOutline size={30} />
                </Box>
                <Text className={iconCx.title}>Add new product</Text>
            </UnstyledButton>
        </Box>
    );
};

export { ComponentLandingAddProduct };
