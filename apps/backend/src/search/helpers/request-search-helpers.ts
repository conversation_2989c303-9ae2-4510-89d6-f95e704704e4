import { parseCookies, PayloadRequest } from 'payload';
import { User } from '@/payload-types';
import { SearchState, TextSearchState } from '@/search/types';
import { cleanObject, SortType } from '@repo/dcide-component-models';

import { getId } from '@/helpers/get-id';

const PAGE_SIZE = 48;

export const getBaseSearchStateFromRequest = (
    request: PayloadRequest,
    user: User | null,
): Omit<SearchState, 'collection'> => {
    const { page: pageAsString, sort, limit } = request.query;
    const page = parseInt(pageAsString as string);
    const pageSize = limit ? parseInt(limit as string) : PAGE_SIZE;

    return {
        stages: [],
        user,
        teamId: getId(user?.team),
        page,
        pageSize,
        sort: sort ? [sort as SortType] : [],
    };
};

export const getTextSearchStateFromRequest = (
    request: PayloadRequest,
): Omit<TextSearchState, keyof SearchState | 'query'> => {
    const { hasTextSearch } = request.query;

    return {
        hasTextSearch: hasTextSearch == 'true',
        hasVectorSearch: false,
    };
};

export const getQueryObjectFromRequest = (request: PayloadRequest) => {
    const { query: queryAsString = '{}' } = request.query;
    return cleanObject(JSON.parse(queryAsString as string));
};

export const getJwt = (request: PayloadRequest) => parseCookies(request.headers).get('dcide-jwt') ?? '';
