import { forwardRef, useEffect, useRef, useState } from 'react';

import { useHotkeys } from 'react-hotkeys-hook';
import { Box, Loader } from '@mantine/core';

import cx from './DropDownInput.module.css';

type Props = {
    placeholder: string;
    onSubmit?: (value: string) => Promise<void>;
    onBlur?: () => void;
    timeout?: number;
};

const DropDownInput = forwardRef<HTMLDivElement, Props>((props, forwardedRef) => {
    const { placeholder, onSubmit, onBlur } = props;

    const [value, setValue] = useState(placeholder);

    const [loading, setLoading] = useState(false);

    const onChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setValue(event.target.value);
    };

    const onBlur_ = () => {
        if (value !== placeholder) {
            submit();
        }

        onBlur?.();
    };

    const submit = async () => {
        setLoading(true);

        await onSubmit?.(value);

        setLoading(false);
    };

    useHotkeys('esc', () => onBlur?.(), {
        enableOnFormTags: ['input'],
    });

    useHotkeys('enter', submit, {
        enableOnFormTags: ['input'],
    });

    const ref = useRef<HTMLInputElement>(null);

    useEffect(() => {
        ref.current?.focus();
        ref.current?.select();
    }, []);

    return (
        <Box className={cx.input} ref={forwardedRef}>
            <input
                ref={ref}
                autoComplete="off"
                autoFocus
                className={cx.autoInputInput}
                value={value}
                placeholder={placeholder}
                onChange={onChange}
                onBlur={onBlur_}
            />
            <span className={cx.autoInputSizer}>{value || placeholder}</span>
            {loading && <Loader size={14} color="gray.6" className={cx.autoInputLoader} />}
        </Box>
    );
});

DropDownInput.displayName = 'DropDownInput';

export { DropDownInput };
