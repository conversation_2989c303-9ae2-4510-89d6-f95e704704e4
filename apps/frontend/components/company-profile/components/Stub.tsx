import React, { useState, FC } from 'react';

import { Anchor, Box, Button, ButtonProps, Card, Group, Modal, Stack, Title } from '@mantine/core';
import { IoImageOutline } from 'react-icons/io5';

import { ImageCrop, Stub as StubType } from '@repo/dcide-component-models';
import { RTEContent } from 'components/rte-content/RTEContent';
import { IKImage } from 'components/ik-image/IKImage';
import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';
import { Files } from 'components/tiptap/TipTapViewer.Files';
import { getId } from 'helpers/getId';

import cx from './Stub.module.css';

const Stub: FC<
    Omit<StubType, 'name'> & {
        name?: string | null;
        handleEdit?: (id: string) => void;
        handleDelete?: (id: string) => void;
        children?: React.ReactNode;
        orientation?: 'horizontal' | 'vertical';
        buttonProps?: ButtonProps;
        showEmptyImage?: boolean;
    }
> = ({
    id,
    name,
    description,
    image,
    imageCrop,
    files,
    button,
    handleEdit,
    handleDelete,
    orientation = 'vertical',
    buttonProps,
    showEmptyImage,
    children,
}) => {
    const withUrl = !!button?.url;

    const [descriptionLength, setDescriptionLength] = useState(0);
    const [readMore, setReadMore] = useState(false);

    const showReadMore = descriptionLength > 240;

    return (
        <>
            <Card withBorder className={cx.root} radius="sm" data-orientation={orientation}>
                {image && (
                    <Box
                        className={cx.imageWrapper}
                        component={withUrl ? 'a' : 'div'}
                        href={button?.url ? button.url : undefined}
                        target="_blank"
                    >
                        <IKImage
                            disableCrop={imageCrop === ImageCrop.CONTAIN}
                            fileOrId={image as any}
                            width={600}
                            height={340}
                            alt={name || ''}
                        />
                    </Box>
                )}

                {!image && showEmptyImage && (
                    <Box className={`${cx.imageWrapper} ${cx.imagePreview}`}>
                        <IoImageOutline size={20} />
                    </Box>
                )}

                <Stack className={cx.content} gap="sm">
                    {name && (
                        <Anchor
                            component={withUrl ? 'a' : 'div'}
                            href={button?.url ? button.url : undefined}
                            td={withUrl ? undefined : 'none'}
                            target="_blank"
                            c="brand"
                            style={{
                                cursor: withUrl ? 'pointer' : 'text',
                            }}
                        >
                            <Title order={3} fz="lg" fw={700} c="brand">
                                {name}
                            </Title>
                        </Anchor>
                    )}

                    {description && (
                        <Box mah={showReadMore ? 101 : undefined} style={{ overflow: 'hidden' }}>
                            <RTEContent content={description} updateCharacterCount={setDescriptionLength} />
                        </Box>
                    )}

                    {!!files?.length && <Files files={files.map(({ file }) => ({ file: getId(file) as string }))} />}

                    {showReadMore && (
                        <Button
                            style={{ alignSelf: 'flex-start' }}
                            variant="outline"
                            size="compact-sm"
                            mt="auto"
                            onClick={() => setReadMore(true)}
                            {...buttonProps}
                        >
                            Read more
                        </Button>
                    )}

                    {button?.label && button?.url && !showReadMore && (
                        <Button
                            component="a"
                            href={button.url}
                            target="_blank"
                            style={{ alignSelf: 'flex-start' }}
                            variant="outline"
                            size="compact-sm"
                            mt="auto"
                            {...buttonProps}
                        >
                            {button.label}
                        </Button>
                    )}

                    {children}

                    {(handleEdit || handleDelete) && id && (
                        <Box className={cx.actions}>
                            <SettingsDropdown>
                                {handleEdit && (
                                    <SettingsDropdown.Edit onClick={() => handleEdit(id)}>Edit</SettingsDropdown.Edit>
                                )}
                                {handleDelete && (
                                    <SettingsDropdown.Delete onClick={() => handleDelete(id)}>
                                        Delete
                                    </SettingsDropdown.Delete>
                                )}
                            </SettingsDropdown>
                        </Box>
                    )}
                </Stack>
            </Card>
            <Modal
                opened={readMore}
                onClose={() => {
                    setReadMore(false);
                }}
                title={name}
                size="xl"
            >
                <Stack>
                    {image && <IKImage fileOrId={image as any} width={600} height={340} alt={name || ''} />}

                    {description && <RTEContent content={description} />}

                    {button && button.url && (
                        <Group>
                            <Anchor href={button.url} target="_blank" fw={600}>
                                {button.label}
                            </Anchor>
                        </Group>
                    )}
                </Stack>
            </Modal>
        </>
    );
};

export { Stub };
