import React, { FC } from 'react';

import { DiagramComponentInstance, type ComponentInstanceValidationError } from '@repo/dcide-component-models';

import { Box } from '@mantine/core';

import { useWatch } from 'react-hook-form';

import { ComponentInstanceForm } from 'components/diagram/components/diagram-component-instance/ComponentInstanceForm';
import { SimulationPorts } from 'components/diagram/components/diagram-sidebar/sidebars/edit-component-instance/sections/Simulation.Ports';
import { ExtraSimulationFields } from 'components/diagram/components/diagram-sidebar/sidebars/edit-component-instance/components/ExtraSimulationFields';
import { DiagramSidebarMantineProvider } from 'components/diagram/components/diagram-sidebar/DiagramSidebarMantineProvider';

import { SubmitButton } from '../SubmitButton';

import { useComponentInstance } from 'components/diagram/hooks';

import cx from '../../Validations.module.css';

const Configuration: FC<{ error: ComponentInstanceValidationError }> = ({ error }) => {
    const componentInstance = useComponentInstance(error.payload.componentInstance.id);

    return componentInstance ? (
        <DiagramSidebarMantineProvider>
            <ComponentInstanceForm componentInstance={componentInstance} autosave={false}>
                <Box className={cx.quickfixComponentInstanceConfiguration}>
                    <Fields />
                </Box>
                <SubmitButton />
            </ComponentInstanceForm>
        </DiagramSidebarMantineProvider>
    ) : null;
};

const Fields = () => {
    const componentInstance = useWatch() as DiagramComponentInstance;

    return (
        <>
            <SimulationPorts componentInstance={componentInstance} />
            <ExtraSimulationFields componentInstance={componentInstance} />
        </>
    );
};

export { Configuration };
