import Router from 'next/router';

import { uid } from 'radash';
import { snapshot } from 'valtio';

import { ApiService } from 'services/ApiService';
import { LocalStorageService } from 'services/LocalStorageService';
import { UserHelpers } from 'helpers/UserHelpers';

import { state as currentUser } from 'state/current-user';
import { state as currentTeam } from 'state/current-team';
import { state as currentProject } from 'state/current-project';
import { geoState } from 'state/geo';
import { SessionStorageService } from 'services/SessionStorageService';

const InternalTrackingService = {
    started: false,
    sessionId: null as string | null,
    queue: [] as any[],

    track: (name: string, data: object = {}) => {
        if (typeof window === 'undefined') {
            return;
        }

        InternalTrackingService.startSession();

        const now = new Date();
        const event = {
            name,
            data,
            metadata: InternalTrackingService.metadata(),
            timestamp: now.getTime(),
            date: now.toISOString(),
        };

        if (LocalStorageService.get('internalTrackingServiceLogging') === 'debug') {
            console.log('Tracking Event', event);
        }

        InternalTrackingService.queue.push(event);
    },

    metadata: () => {
        const { pathname, asPath, query } = Router;

        const referrer = LocalStorageService.get(UserHelpers.localStorageKey.referrer);
        const email = LocalStorageService.get(UserHelpers.localStorageKey.email);

        return {
            title: document.title,
            path: asPath,
            pathname,
            query,
            search: window.location.hash,
            session: InternalTrackingService.sessionId,
            user: currentUser.user?.id || null,
            goofy: currentUser.user?.goofy || null,
            internal: currentUser.user?.internal || false,
            userAgent: navigator.userAgent,
            language: navigator.language,
            languages: navigator.languages,
            team: currentTeam.team?.id || null,
            project: currentProject.project?.id || null,
            // component: currentComponentState.component?.id || null,
            geo: snapshot(geoState).geo,
            localStorage: {
                referrer,
                email,
            },
        };
    },

    startSession: () => {
        if (InternalTrackingService.sessionId) {
            return;
        }

        let id = SessionStorageService.get('sessionId');

        if (!id) {
            id = uid(12);
            SessionStorageService.store('sessionId', id);
        }

        InternalTrackingService.sessionId = id;
    },

    start: () => {
        if (InternalTrackingService.started) {
            return;
        }

        window.addEventListener('beforeunload', InternalTrackingService.push);
        window.setInterval(InternalTrackingService.push, 10_000);

        InternalTrackingService.started = true;
    },

    push: () => {
        if (InternalTrackingService.queue.length) {
            ApiService.post('/api/secret', {
                events: InternalTrackingService.queue,
            }).then();

            InternalTrackingService.queue = [];
        }
    },
};

export type TrackingEvent<T = object> = {
    name: string;
    data: T;
};

export { InternalTrackingService };
