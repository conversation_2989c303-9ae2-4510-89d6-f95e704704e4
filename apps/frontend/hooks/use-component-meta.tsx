import Link from 'next/link';
import { Anchor } from '@mantine/core';

import { Manufacturer, all } from '@repo/dcide-component-models';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { Component } from '@repo/dcide-component-models';

import { DateService } from 'services/DateService';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { NumberHelpers } from 'helpers/NumberHelpers';

const useComponentMeta = (component: Component) => {
    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    const { identifier, name: uniqueName, slug } = ComponentHelpers.generateMetadata(component);

    const { typeName, subtitle } = useComponentMetaSubtitle(component.type, manufacturer);

    const price = component.msrp ? NumberHelpers.formatPriceShort(component.msrp) : '';
    const leadTime = component.leadTime ? DateService.formatNumberOfDays(component.leadTime) : '';

    return { uniqueName, identifier, typeName, subtitle, price, leadTime, slug, manufacturerName: manufacturer?.name };
};

const useComponentMetaSubtitle = (type: Component['type'], manufacturer?: Pick<Manufacturer, 'name' | 'id'>) => {
    const typeName = all[type].name;

    const subtitle = manufacturer ? (
        <span>
            {typeName} •{' '}
            <Anchor
                component={Link}
                href={CompanyProfileHelpers.urls.view(manufacturer.id)}
                underline="hover"
                inherit
                style={{ color: 'inherit' }}
            >
                {manufacturer.name}
            </Anchor>
        </span>
    ) : (
        typeName
    );

    return { subtitle, typeName };
};

export { useComponentMeta, useComponentMetaSubtitle };
