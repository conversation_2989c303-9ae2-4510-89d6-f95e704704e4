{% extends 'layout/app' %}

{% block content %}

    {% include 'components/header' %}

    {% for section in entry.sections.all %}
        {% set colors = section.backgroundColor == 'black' ? '!bg-brand-black text-white' %}

        <div
            class="relative {{ containerClasses() }} {{ colors }}"
            style="background: linear-gradient(180deg, rgba(42, 42, 42, 0) 0%, rgba(42, 42, 42, 0.25) 100%)"
        >

            <div
                class="absolute top-0 left-0 z-[-1] w-[100vw] | bg-brand-black"
                data-opening-section
                data-percentage="{{ section.openingOverlap ?? '0' }}"
            ></div>

            {% set proseContent = null %}

            {% for block in section.vizy.all %}
                {% if block.type == 'heading' %}
                    {% set proseContent %}
                        {{ proseContent }}
                        {% if block.attrs.level == 2 %}
                            <h2 class="font-display font-bold text-balance">
                                {% for node in block.content %}
                                    {{ node.renderHtml() }}
                                {% endfor %}
                            </h2>
                        {% elseif block.attrs.level == 3 %}
                            <h3 class="font-display font-bold text-balance">
                                {% for node in block.content %}
                                    {{ node.renderHtml() }}
                                {% endfor %}
                            </h3>
                        {% endif %}
                    {% endset %}
                {% elseif block.type == 'vizyBlock' %}

                    {% if proseContent %}
                        <div class="max-w-4xl mx-auto | prose lg:prose-lg">
                            {{ proseContent }}
                            {% set proseContent = null %}
                        </div>
                    {% endif %}

                    {% include 'content/vizy/' ~ block.handle %}

                {% else %}
                    {% set proseContent %}
                        {{ proseContent }}
                        {{ block.renderHtml() }}
                    {% endset %}
                {% endif %}
            {% endfor %}

            {% if proseContent %}
                <div class="max-w-4xl mx-auto | prose lg:prose-lg">
                    {{ proseContent }}
                    {% set proseContent = false %}
                </div>
            {% endif %}

            <div
                class="absolute bottom-0 left-0 z-[-1] w-[100vw] | bg-brand-black"
                data-closing-section
                data-percentage="{{ section.closingOverlap ?? '0' }}"
            ></div>

        </div>
    {% endfor %}

{% endblock %}
