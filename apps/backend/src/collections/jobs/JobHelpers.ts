import { CollectionAfterChangeHook, CollectionSlug, Payload } from 'payload';
import { Simulation, WireSizing } from '@/payload-types';

import { JobStatus, JobErrorResponseSchema, JobErrorResponse } from '@repo/dcide-component-models';
import { config } from '../../config';
import { JobsCollection } from './types';

export const JOB_UNKNOWN_ERROR_MESSAGE = 'Something went wrong.';

const JobHelpers = {
    handleSyncronous: async (result: any, collection: CollectionSlug, id: string, payload: Payload) => {
        await payload.update({
            collection: collection,
            id,
            data: {
                status: JobStatus.DONE,
                result,
            },
        });
    },
    handleError: async (error: JobErrorResponse, collection: CollectionSlug, id: string, payload: Payload) => {
        payload.update({
            collection,
            id: id,
            data: { status: JobStatus.ERROR, ...error },
        });
    },
    updateJobStatusToProcessing: async (id: string, collection: CollectionSlug, payload: Payload) => {
        await payload.update({
            collection,
            id: id,
            data: {
                status: JobStatus.PROCESSING,
            },
        });
    },

    createRunner: (
        collection: JobsCollection,
        handle: (doc: WireSizing | Simulation, isAsync: boolean) => Promise<Response>,
    ): CollectionAfterChangeHook<WireSizing | Simulation> => {
        return async ({ req, doc, operation, previousDoc }) => {
            const { payload } = req;
            const statusChangedToPending = previousDoc.status !== JobStatus.PENDING && doc.status === JobStatus.PENDING;
            const isCreateOperation = operation === 'create';
            const isDevelopment = config.environment === 'development';
            const isAsync = !isDevelopment;

            if (isCreateOperation || statusChangedToPending) {
                try {
                    await JobHelpers.updateJobStatusToProcessing(doc.id, collection, payload);

                    const response = await handle(doc, isAsync);

                    const data = await response.json();
                    const { success: hasError, data: result } = JobErrorResponseSchema.safeParse(data);
                    if (hasError) {
                        console.error(`Error requesting: ${collection} - `, result);
                        JobHelpers.handleError(result, collection, doc.id, payload);
                        return;
                    }

                    const hasResult = data !== undefined;

                    if (hasResult) {
                        await JobHelpers.handleSyncronous(data, collection, doc.id, payload);
                    }
                } catch (error) {
                    console.error(`Error requesting: ${collection} - `, error);
                    JobHelpers.handleError(
                        { error: { message: JOB_UNKNOWN_ERROR_MESSAGE }, execution: {} },
                        collection,
                        doc.id,
                        payload,
                    );
                }
            }
        };
    },
};

export { JobHelpers };
