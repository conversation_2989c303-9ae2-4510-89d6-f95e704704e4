.root {
    --offset: var(--mantine-spacing-xl);

    position: absolute;
    left: calc(50% - (var(--diagram-sidebar-width) - var(--diagram-sidebar-nav-width)) / 2);
    top: 50%;

    transform: translate(-50%, -50%);

    z-index: 98;

    width: 360px;
    max-height: calc(100vh - var(--mantine-spacing-xl));
}

.card {
    display: flex;
    flex-direction: column;
    gap: var(--mantine-spacing-md);

    box-shadow: var(--mantine-shadow-xl);
    border-color: var(--mantine-color-gray-1);

    overflow-y: auto;

    > * {
        flex-shrink: 0;
    }

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);

        background-color: var(--mantine-color-gray-0);

        h2 {
            font-size: 13px;
        }
    }

    .closeButton {
        margin-right: calc((var(--mantine-spacing-md) - var(--mantine-spacing-xs)) * -1);
    }
}

.component {
    display: flex;
    flex-direction: column;

    aspect-ratio: 1 / 1;

    align-items: center;
    justify-content: center;

    background-color: #ffffff;
    color: var(--mantine-color-gray-8);
    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);
    padding-left: var(--mantine-spacing-xs);
    padding-right: var(--mantine-spacing-xs);

    &[data-selected="true"] {
        border: 2px solid black;
    }
}

.componentIcon {
    width: 24px;
    height: 24px;
    fill: currentcolor;
}

.componentName {
    margin-top: 6px;

    color: var(--mantine-color-gray-7);
    font-size: var(--mantine-font-size-xs);
    line-height: 1.2;
    text-align: center;
}

.browseButton {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;

    font-weight: 500;
    font-size: var(--mantine-font-size-sm);

    .icon {
        transition: transform 200ms;
    }

    &:hover {
        .icon {
            transform: translateX(4px);
        }

        .thumbnails,
        .roundIcon {
            transform: translateY(-4px);
        }

        .thumbnail,
        .roundIcon {
            box-shadow: 0 0 20px var(--mantine-color-gray-3);
        }
    }
}

.thumbnails {
    position: relative;

    width: 60px;
    height: 30px;

    transition: transform 200ms;

    .thumbnail {
        position: absolute;
        left: 0;
        top: 0;

        &:first-child {
            background-color: var(--mantine-color-primary-5);

            transform: rotate(-10deg);
        }

        &:nth-child(2) {
            background-color: var(--mantine-color-primary-3);

            left: 8px;
        }

        &:last-child {
            left: 16px;
            transform: rotate(10deg);

            background-color: var(--mantine-color-primary-1);
        }
    }
}

.thumbnail {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 36px;
    height: 24px;

    border-radius: var(--mantine-radius-xs);
    border: 2px solid white;

    background-color: var(--mantine-color-gray-2);
    box-shadow: 1px 1px 3px var(--mantine-color-gray-3);

    transition: box-shadow 200ms;
}

.roundIcon {
    display: flex;

    padding: 4px;

    border: 2px solid white;
    border-radius: 99px;

    background-color: var(--mantine-color-yellow-2);
    box-shadow: 1px 1px 3px var(--mantine-color-gray-3);

    transition: transform 200ms, box-shadow 200ms;
}

.product {
    position: relative;

    padding: var(--mantine-spacing-xs);
    padding-right: var(--mantine-spacing-xl);

    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);

    .selectedIcon {
        position: absolute;
        top: var(--mantine-spacing-xs);
        right: var(--mantine-spacing-xs);

        flex: auto 0 0;

        display: none;
        margin-left: auto;

        color: var(--mantine-color-primary-6);

        width: 16px;
        height: 16px;
    }

    &[data-selected="true"] {
        background-color: var(--mantine-color-gray-0);

        .selectedIcon {
            display: block;
        }
    }
}
