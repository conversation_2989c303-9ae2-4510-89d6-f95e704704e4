import React from 'react';

import { MeasurementFieldType, efficiencyConverter } from '@repo/dcide-component-models';

import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    UnitWithTooltip,
    useRenderVariableMultiInput,
    MeasurementInputWrapper,
} from 'components/forms/fields/measurement';

export type EfficiencyFieldProps = {
    name: string;
    fallbackBaseUnit?: string;
    fields?: MeasurementFieldType[];
    hideIcons?: boolean;
} & MeasurementInputWrapperProps;

const EfficiencyField = ({ name, fields = ['nom', 'max'], hideIcons, ...wrapperProps }: EfficiencyFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: fieldsRecord,
    } = useMultiUnitMeasurementController({ name, converter: efficiencyConverter });

    const Unit = <UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>;
    const Fields = useRenderVariableMultiInput({
        fields,
        fieldsRecord,
        Unit,
        extraProps: {
            hideIcon: hideIcons,
            size: wrapperProps.size,
        },
    });

    return <MeasurementInputWrapper {...wrapperProps}>{Fields}</MeasurementInputWrapper>;
};

export { EfficiencyField };
