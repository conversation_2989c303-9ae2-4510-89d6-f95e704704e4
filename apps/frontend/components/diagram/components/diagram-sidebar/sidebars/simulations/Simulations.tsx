import React, { FC } from 'react';

import { Box, Loader, Stack, Text, Tooltip } from '@mantine/core';
import { IoAddSharp } from 'react-icons/io5';

import { FeatureLimit, getSubscriptionForUnlimitedFeature } from '@repo/dcide-component-models';

import { DiagramSidebar } from 'components/diagram';
import { useSimulations, useSimulationsData } from 'components/diagram/hooks/use-simulations';
import { useSyncing } from 'components/diagram/hooks/use-syncing';

import { JobTeaser } from 'components/diagram/components/diagram-jobs/JobTeaser';
import { UploadCsv } from './components/UploadCsv';
import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';
import { FeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';

import { useCreateSimulation } from './hooks/useCreateSimulation';
import { useDiagramValidationErrorCount } from 'components/diagram/hooks/use-diagram-validation-error-count';
import { useCurrentTeamSubscription } from 'hooks/use-current-team-subscription';

const Simulations: FC & {
    UploadCsv: typeof UploadCsv;
} = () => {
    const plan = useCurrentTeamSubscription();
    const unlimitedPlan = getSubscriptionForUnlimitedFeature(FeatureLimit.SIMULATIONS);

    const { active } = useSimulations();
    const { all: simulations, isLoading } = useSimulationsData();
    const { canCreate, simulationFeatureAccess, creating, handleCreateSimulation } = useCreateSimulation();

    const hasZoneErrors = Boolean(useDiagramValidationErrorCount());

    const { status: syncingStatus } = useSyncing();
    const isSyncing = syncingStatus === 'syncing';

    const pastSimulations = simulations.slice(1);

    return (
        <>
            <DiagramSidebar.SimpleHeader
                rightSection={
                    canCreate && simulationFeatureAccess ? (
                        <DiagramSidebar.ButtonGroup>
                            <Tooltip
                                label={
                                    isSyncing ? (
                                        <Box>You cannot start a simulation while we are saving your project.</Box>
                                    ) : (
                                        <Box>
                                            Attention, your diagram has validation errors.
                                            <br />
                                            You can still create a simulation, but it might fail.
                                        </Box>
                                    )
                                }
                                disabled={!hasZoneErrors && !isSyncing}
                                position="bottom"
                                withArrow
                            >
                                <DiagramSidebar.Button
                                    onClick={() => handleCreateSimulation()}
                                    loading={creating}
                                    disabled={isSyncing}
                                >
                                    <IoAddSharp />
                                    <span>New simulation</span>
                                </DiagramSidebar.Button>
                            </Tooltip>
                            <Simulations.UploadCsv />
                        </DiagramSidebar.ButtonGroup>
                    ) : null
                }
            >
                Simulations
            </DiagramSidebar.SimpleHeader>

            {plan !== unlimitedPlan && (
                <>
                    <DiagramSidebar.Section>
                        <FeatureLimitTracker feature={FeatureLimit.SIMULATIONS} p={0} />
                    </DiagramSidebar.Section>
                    <DiagramSidebar.Divider />
                </>
            )}

            {simulationFeatureAccess ? (
                simulations.length === 0 ? (
                    isLoading ? (
                        <DiagramSidebar.Section>
                            <Loader size="sm" />
                        </DiagramSidebar.Section>
                    ) : (
                        <DiagramSidebar.Section>
                            <Text c="dimmed">No simulations yet</Text>
                        </DiagramSidebar.Section>
                    )
                ) : (
                    <>
                        <DiagramSidebar.Section title="Latest simulation">
                            <JobTeaser
                                type="simulation"
                                job={simulations[0]}
                                active={simulations[0].id === active}
                                isLatest={true}
                            />
                        </DiagramSidebar.Section>

                        {!!pastSimulations.length && (
                            <>
                                <DiagramSidebar.Divider />
                                <DiagramSidebar.Section title="Past simulations">
                                    <Stack gap="lg">
                                        {pastSimulations.map((simulation) => (
                                            <JobTeaser
                                                type="simulation"
                                                job={simulation}
                                                active={simulation.id === active}
                                                key={simulation.id}
                                                isLatest={false}
                                            />
                                        ))}
                                    </Stack>
                                </DiagramSidebar.Section>
                            </>
                        )}
                    </>
                )
            ) : (
                <DiagramSidebar.Section>
                    <SubscriptionOnboardingMessage>
                        Want to run simulations on your diagrams?
                    </SubscriptionOnboardingMessage>
                </DiagramSidebar.Section>
            )}
        </>
    );
};

Simulations.UploadCsv = UploadCsv;

export { Simulations };
