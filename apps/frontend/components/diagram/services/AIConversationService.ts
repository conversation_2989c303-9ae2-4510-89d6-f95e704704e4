import { Diagram } from '@repo/dcide-component-models';

import { mutate } from 'swr';

import { ApiService } from 'services/ApiService';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { ValidationService } from 'components/diagram/services/ValidationService';

import { AIConversationHelpers } from 'components/diagram/helpers/AIConversationHelpers';
import { StringHelpers } from 'helpers/StringHelpers';

import { AIConversationState } from 'components/diagram/state/ai-conversation';

import { config } from 'config';

export type DiagramAIMessage = {
    id: string;
    question: string;
    feedback: string;
    answer: string;
    changes: {
        applied: boolean;
        reverted: boolean;
        failed: boolean;
        transaction: string;
    };
    action: {
        executed: boolean;
        failed: boolean;
        result: any;
    };
    status: 'pending' | 'processing' | 'done' | 'cancelled' | 'error';
    diagram: string;
};

const AIConversationService = {
    openSidebar: () => {
        SidebarService.openAIConversationSidebar();
    },

    get: async (diagramId: Diagram['id']) => {
        const search = new URLSearchParams();
        search.set('where[diagram][equals]', diagramId);
        search.set('pagination', 'false');
        search.set('sort', 'createdAt');

        return ApiService.get<{
            docs: DiagramAIMessage[];
        }>(`${config.api.backend}/diagramAIMessages?${search}`);
    },

    ask: async (diagram: Diagram, question: string) => {
        InternalTrackingService.track('diagram.ai.ask', {
            diagram: diagram.id,
            question,
        });

        const validationReport = ValidationService.getValidationReport();

        await ApiService.post(`${config.api.backend}/diagramAIMessages`, {
            question,
            diagram: diagram.id,
            diagramValidations: validationReport,
            project: diagram.project,
        });
    },

    applyChanges: async (diagram: Diagram, message: DiagramAIMessage) => {
        InternalTrackingService.track('diagram.ai.applyChanges', {
            diagram: diagram.id,
            message,
        });

        await ApiService.patch(`${config.api.backend}/diagramAIMessages/${message.id}`, {
            changes: {
                applied: true,
            },
        });
    },

    undoChanges: async (diagram: Diagram, message: DiagramAIMessage) => {
        InternalTrackingService.track('diagram.ai.applyChanges', {
            diagram: diagram.id,
            message,
        });

        await ApiService.patch(`${config.api.backend}/diagramAIMessages/${message.id}`, {
            applied: false,
        });
    },

    markChangesAsFailed: async (diagram: Diagram, message: DiagramAIMessage) => {
        InternalTrackingService.track('diagram.ai.markChangesAsFailed', {
            diagram: diagram.id,
            message,
        });

        await ApiService.patch(`${config.api.backend}/diagramAIMessages/${message.id}`, {
            changes: {
                applied: true,
                failed: true,
            },
        });
    },

    execute: async (message: DiagramAIMessage, result: any) => {
        InternalTrackingService.track('diagram.ai.execute', {
            message,
        });

        await ApiService.patch(`${config.api.backend}/diagramAIMessages/${message.id}`, {
            action: {
                executed: true,
                result,
            },
        });
    },

    markExecutionAsFailed: async (message: DiagramAIMessage) => {
        await ApiService.patch(`${config.api.backend}/diagramAIMessages/${message.id}`, {
            action: {
                failed: true,
            },
        });
    },

    refresh: async (diagramId: Diagram['id']) => {
        await mutate(AIConversationHelpers.swr.get(diagramId));
    },

    setPrefilledQuestion: (question: string) => {
        AIConversationState.prefilledQuestion = StringHelpers.trim(question);
    },

    clearPrefilledQuestion: () => {
        AIConversationState.prefilledQuestion = '';
    },
};

export { AIConversationService };
