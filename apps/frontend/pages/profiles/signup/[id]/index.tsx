import React, { FC } from 'react';

import { GetServerSideProps } from 'next';

import { CompanyProfile, PermissionCompany } from '@repo/dcide-component-models';

import { getParam } from 'helpers/getParam';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { CompanySignup } from 'components/company-signup/CompanySignup';

const ManufacturerSignupPage: FC<{ manufacturer: CompanyProfile }> = ({ manufacturer }) => {
<<<<<<< Updated upstream:apps/frontend/pages/profiles/signup/[id]/index.tsx
    return <CompanySignup company={manufacturer} title="Welcome To Your Profile!" />;
=======
    return <CompanySignup company={manufacturer} title="Your Profile" />;
>>>>>>> Stashed changes:apps/frontend/pages/profile/signup/[id]/index.tsx
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const manufacturerId = getParam(params, 'id')!;
    const manufacturer = await CompanyProfileService.get(manufacturerId);

    if (!manufacturer || manufacturer.errors) {
        return {
            notFound: true,
        };
    }

    if (!manufacturer.permissions.includes(PermissionCompany.EDIT)) {
        return {
            notFound: true,
        };
    }

    return {
        props: {
            manufacturer,
        },
    };
};

export default ManufacturerSignupPage;
