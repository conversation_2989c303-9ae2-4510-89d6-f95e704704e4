import {
    DiagramComponentInstance,
    DiagramConnection,
    PowerFlowDirection,
    VoltageType,
} from '@repo/dcide-component-models';

type PortIndex = DiagramConnection['from']['port'];

const PortHelpers = {
    getLabel: (index: number) => {
        return `Port ${index + 1}`;
    },

    getConfiguration: (componentInstance: DiagramComponentInstance, port: number) => {
        return componentInstance.configuration.ports[port];
    },

    getSpecifications: (componentInstance: DiagramComponentInstance, port: number) => {
        return componentInstance.specifications.electrical.ports[port];
    },

    getPower: (componentInstance: DiagramComponentInstance, port: number): number => {
        const voltageType = PortHelpers.getVoltageTypeConfiguration(componentInstance, port);

        if (!voltageType) {
            return 0;
        }

        // @ts-ignore this exists.
        const power = componentInstance.specifications.electrical.ports[port][voltageType].power;

        return power?.nom ?? power?.max ?? 0;
    },

    getPowerFlowDirectionConfiguration: (componentInstance: DiagramComponentInstance, port: number) => {
        const { powerFlowDirection } = componentInstance.configuration.ports[port];

        return powerFlowDirection;
    },

    getPowerFlowDirectionOptions: (componentInstance: DiagramComponentInstance, index: PortIndex) => {
        const options: PowerFlowDirection[] = [];

        if (index === null) {
            return options;
        }

        const port = PortHelpers.getSpecifications(componentInstance, index);
        const powerFlowDirection = port.powerFlowDirection;

        if (powerFlowDirection === 'bidirectional') {
            options.push('bidirectional', 'input', 'output');
        } else if (powerFlowDirection === 'input') {
            options.push('input');
        } else if (powerFlowDirection === 'output') {
            options.push('output');
        }

        return options;
    },

    getVoltageTypeConfiguration: (componentInstance: DiagramComponentInstance, port: number) => {
        const { voltageType } = componentInstance.configuration.ports[port];

        return voltageType;
    },

    getVoltageTypeOptions: (componentInstance: DiagramComponentInstance, port: PortIndex) => {
        const voltageTypes: VoltageType[] = [];

        if (port === null) {
            return voltageTypes;
        }

        const portSpecifications = componentInstance.specifications.electrical.ports[port];

        if (!portSpecifications) {
            // TODO Remove this in a few weeks..
            console.error(`Cannot get PortSpecifications for port ${port}`, JSON.stringify(componentInstance));
        }

        if (portSpecifications && 'AC' in portSpecifications && portSpecifications.AC.enabled) {
            voltageTypes.push('AC');
        }

        if (portSpecifications && 'DC' in portSpecifications && portSpecifications.DC.enabled) {
            voltageTypes.push('DC');
        }

        return voltageTypes;
    },

    isVoltageTypeAllowed: (
        componentInstance: DiagramComponentInstance,
        portIndex: PortIndex,
        voltageType: VoltageType,
    ) => {
        return PortHelpers.getVoltageTypeOptions(componentInstance, portIndex).includes(voltageType);
    },
};

export { PortHelpers };
