import React, { <PERSON> } from 'react';

import Link from 'next/link';

import { ActionIcon, Anchor, Group, Menu, Tabs } from '@mantine/core';
import { IoEllipsisHorizontalSharp } from 'react-icons/io5';

import { DiagramSidebarTabItem } from 'components/diagram/hooks';

import cx from './SidebarComponents.module.css';
import { useSidebar } from 'components/diagram/hooks/use-sidebar';

const SidebarTabs: FC<{
    tabs: DiagramSidebarTabItem[];
}> = ({ tabs }) => {
    const { width } = useSidebar();

    const nbShownTabs = Math.floor(width / 105);

    const shownTabs = tabs.slice(0, nbShownTabs);
    const overflowTabs = tabs.slice(nbShownTabs);

    return (
        <Tabs.List className={cx.list}>
            {shownTabs.map((tab) => (
                <SidebarTab tab={tab} key={tab.value} />
            ))}

            {!!overflowTabs?.length && (
                <Menu
                    classNames={{ dropdown: cx.overflowTabs }}
                    position="bottom-end"
                    trigger="click-hover"
                    shadow="xl"
                    offset={0}
                    closeOnItemClick
                    openDelay={100} // add delay for automatically resizable sidebar, to prevent unexpected menu open
                >
                    <Menu.Target>
                        <ActionIcon size="sm" variant="subtle" my="auto" c="gray.5">
                            <IoEllipsisHorizontalSharp />
                        </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                        {overflowTabs.map((tab) => (
                            <Menu.Item component="div" key={tab.value}>
                                <SidebarTab tab={tab} />
                            </Menu.Item>
                        ))}
                    </Menu.Dropdown>
                </Menu>
            )}
        </Tabs.List>
    );
};

const SidebarTab = ({ tab }: { tab: DiagramSidebarTabItem }) => {
    return tab.href ? (
        <Anchor component={Link} href={tab.href} className={cx.link} target="_blank">
            <Group align="center" gap={4}>
                {tab.leftSection}
                {tab.title}
                {tab.rightSection}
            </Group>
        </Anchor>
    ) : (
        <Tabs.Tab className={cx.tab} value={tab.value} leftSection={tab.leftSection} rightSection={tab.rightSection}>
            {tab.title}
        </Tabs.Tab>
    );
};

export { SidebarTabs };
