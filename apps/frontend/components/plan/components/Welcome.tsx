import { Button, Group, Stack, StackProps, Text, Title, Transition } from '@mantine/core';
import Link from 'next/link';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { useCurrentUser } from 'hooks/use-current-user';

export const Welcome = ({ hidden, ...props }: StackProps) => {
    const user = useCurrentUser();

    return (
        <Transition mounted={!hidden} transition="fade" duration={300} timingFunction="ease">
            {(styles) => (
                <Stack gap={'xs'} align="center" p="xl" style={styles} {...props}>
                    <Title size={36} fw={700} ta="center">
                        What are you looking for?
                    </Title>
                    <Text ta="center" fz={16} c={'rgba(255,255,255,0.5)'}>
                        Get matched with{' '}
                        <Text span inherit fw={600} c="white">
                            companies
                        </Text>{' '}
                        in the electric power industry, their{' '}
                        <Text span inherit fw={600} c="white">
                            services
                        </Text>
                        ,{' '}
                        <Text span inherit fw={600} c="white">
                            products
                        </Text>{' '}
                        and more.
                    </Text>
                    <Group gap="xs" mt="md">
                        <Button
                            component={Link}
                            href={user ? ProjectHelpers.urls.create() : '/login?redirect=/projects/create'}
                            variant="gradient"
                        >
                            Try our Design Editor
                        </Button>
                        <Button component={Link} href={ComponentHelpers.urls.search()} variant="gradient">
                            Browse our Product Catalog
                        </Button>
                    </Group>
                </Stack>
            )}
        </Transition>
    );
};
