import { CompanyProfileSchema, componentQuerySchema, profileQuerySchema } from '@repo/dcide-component-models';
import { z } from 'zod';

export const getProfileSearchFilterSchema = async (companyNames: string) =>
    z.object({
        systemSize: profileQuerySchema.shape.systemSize.optional().nullable(),
        projectBudget: profileQuerySchema.shape.projectBudget.optional().nullable(),
        services: CompanyProfileSchema.shape.services.optional().nullable(),
        compliances: profileQuerySchema.shape.compliances.optional().nullable(),
        location: profileQuerySchema.shape.location.optional().nullable(),
        manufacturer: componentQuerySchema.shape.manufacturer
            .optional()
            .nullable()
            .describe(`
                id of the product manufacturer to filter by. There might be misspelled names in the input.

                List of manufacturers:
                ${companyNames}
            `),
        search: profileQuerySchema.shape.search
            .optional()
            .nullable()
            .describe(
                `Keyword search. Any strings from the SEARCH_INPUT that are not mapped to other fields should be included in the "search" field.`,
            ),
    });
