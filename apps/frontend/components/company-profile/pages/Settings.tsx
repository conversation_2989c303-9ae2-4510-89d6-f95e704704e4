import React, { <PERSON> } from 'react';

import { pick } from 'radash';
import { Card, SimpleGrid, Stack } from '@mantine/core';

import { CompanyProfile, CompanyProfileSchema, Compliance } from '@repo/dcide-component-models';

import { LocalNotificationService } from 'services/LocalNotificationService';

import { addUniqueNameValidator } from 'helpers/add-unique-name-validator';

import { useCompanyProfiles } from 'hooks/use-company-profiles';
import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { Section } from 'components/section/Section';

import { FormSubmit } from 'components/forms/FormSubmit';
import { CompanyForm } from 'components/company-form/CompanyForm';

import { URLField } from 'components/forms/fields/URLField';
import { CompliancesField } from 'components/forms/fields/CompliancesField';
import { PowerLevelField } from 'components/component-fields/PowerLevelField';
import { ProjectBudgetField } from 'components/component-fields/ProjectBudgetField';
import { CompanyFormNameField } from 'components/company-form/CompanyFormNameField';

import cx from './styles.module.css';

const SettingsSchema = CompanyProfileSchema.pick({
    name: true,
    website: true,
    socials: true,
    about: true,
    systemSize: true,
    projectBudget: true,
    compliance: true,
});

const Settings: FC<{
    company: CompanyProfile;
}> = ({ company }) => {
    const { companies: existingCompanies } = useCompanyProfiles();

    const defaultValues = {
        ...pick(company, ['name', 'website', 'socials', 'systemSize', 'projectBudget', 'compliance']),
    };

    const zodSchema = addUniqueNameValidator(
        SettingsSchema,
        existingCompanies
            .filter((existingCompany) => existingCompany.id !== company?.id)
            .map((existingCompany) => existingCompany.name),
        ' ',
    );

    return (
        <Card classNames={cx}>
            <Section title="Settings">
                <CompanyForm
                    companyId={company.id}
                    defaultValues={defaultValues}
                    zodSchema={zodSchema}
                    onSubmitSuccess={() => {
                        LocalNotificationService.showSuccess({
                            message: 'Company info updated',
                        });
                    }}
                >
                    <Stack gap="lg" maw={700}>
                        <CompanyFormNameField
                            savedNameForThisCompany={company?.name}
                            companyIsPublished={Boolean(company?.publishedAt)}
                        />

                        <PowerLevelField
                            name="systemSize"
                            label="System size"
                            description="What power levels are you comfortable working with?"
                        />

                        <ProjectBudgetField
                            name="projectBudget"
                            label="Project Budget"
                            description="What size of projects do you typically work on?"
                        />

                        <URLField name="website" label="Website" />

                        <SimpleGrid cols={{ base: 1, xs: 2 }} spacing="xs">
                            <URLField name="socials.linkedin" label="LinkedIn" />
                            <URLField name="socials.facebook" label="Facebook" />
                            <URLField name="socials.twitter" label="X" />
                            <URLField name="socials.youtube" label="YouTube" />
                        </SimpleGrid>

                        <CompliancesField
                            name="compliance"
                            label="We are a member of the following organizations"
                            description="Check all that apply. Multiple options are allowed."
                            showCompliances={[
                                Compliance.CURRENT_OS,
                                Compliance.EMERGE,
                                Compliance.ODCA,
                                Compliance.OTHER,
                            ]}
                        />

                        <FormSubmit style={{ alignSelf: 'start' }}>Save changes</FormSubmit>
                    </Stack>
                </CompanyForm>
            </Section>
        </Card>
    );
};

const WrappedSettings = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <Settings company={company} />;
};

export { WrappedSettings as Settings };
