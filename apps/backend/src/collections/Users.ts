import { CollectionConfig, Field, IncomingAuthType } from 'payload';

import { JWTStrategy } from '../strategies';

import { hubspotSync } from './users/hooks/hubspot-sync';
import { suggestCompanies } from './users/hooks/suggest-companies';
import { suggestTeams } from './users/hooks/suggest-teams';
import { updateInvitations } from './users/hooks/update-invitations';

import {
    getTeams,
    magicLogin,
    magicLoginVerify,
    magicLoginGetToken,
    switchTeam,
    magicLoginSetName,
    switchOrder,
    logout,
    dismissTip,
    sendNewUserInvites,
    impersonate,
    getProgress,
    completeProgressItem,
    finishSignup,
    heartbeat,
} from './users/endpoints';

import { ping } from './users/endpoints/ping';
import { pingProject } from './users/endpoints/ping-project';
import { pingProduct } from './users/endpoints/ping-product';

import { config } from '../config';
import { DateField, Goofy } from '../fields';
import { getRequestUser } from '../helpers';

import {
    MeasurementSystemOptions,
    UserAccess,
    UserFeatureFlags,
    UserProgressItem,
    UserReferrer,
    UserTip,
    UserType,
} from '@repo/dcide-component-models';

// Keep in sync with https://github.com/Direct-Energy-Partners/dcide-nxt/blob/main/helpers/getSession.ts
const TOKEN_EXPIRATION_TIME = 60 * 60 * 24 * 30;

export const AUTH_CONFIG: IncomingAuthType &
    Required<Pick<IncomingAuthType, 'cookies' | 'tokenExpiration' | 'disableLocalStrategy' | 'strategies'>> = {
    tokenExpiration: TOKEN_EXPIRATION_TIME,
    disableLocalStrategy: true,
    strategies: [JWTStrategy],
    cookies: {
        domain: config.environment === 'development' ? undefined : 'dcide.app',
    },
};

const Users: CollectionConfig = {
    slug: 'users',
    auth: AUTH_CONFIG,
    admin: {
        useAsTitle: 'email',
    },
    access: {
        admin: async ({ req }) => {
            if (config.environment === 'development') {
                return true;
            }

            return (await getRequestUser(req))?.adminPanel ?? false;
        },
        read: ({ req }) => {
            return !!req.user;
        },
        create: () => true,
    },
    fields: [
        {
            name: 'impersonate',
            type: 'ui',
            admin: {
                components: {
                    Field: '/ImpersonateButton',
                },
                position: 'sidebar',
            },
        },
        {
            type: 'tabs',
            tabs: [
                {
                    label: 'General',
                    fields: [
                        {
                            name: 'name',
                            type: 'text',
                        },
                        Goofy,
                        {
                            name: 'email',
                            type: 'email',
                            required: true,
                            hooks: {
                                beforeValidate: [({ value }) => value?.trim()?.toLowerCase()],
                            },
                        },
                        {
                            name: 'profileImage',
                            type: 'upload',
                            relationTo: 'files',
                            maxDepth: 0,
                        },
                        {
                            ...DateField,
                            label: 'Last Active Date',
                            name: 'lastActiveDate',
                            defaultValue: undefined,
                        } as Field,
                        {
                            name: 'dismissedTips',
                            type: 'select',
                            options: Object.values(UserTip).map((tip) => ({
                                value: tip,
                                label: tip,
                            })),
                            hasMany: true,
                        },
                        {
                            name: 'completedProgressItems',
                            type: 'select',
                            options: Object.values(UserProgressItem).map((item) => ({
                                value: item,
                                label: item,
                            })),
                            hasMany: true,
                        },
                        // This team field is special, don't use Team.ts
                        {
                            name: 'team',
                            type: 'relationship',
                            relationTo: 'teams',
                            maxDepth: 0,
                            required: false,
                        },
                        {
                            name: 'recentProjects',
                            type: 'relationship',
                            relationTo: 'projects',
                            hasMany: true,
                            maxDepth: 0,
                        },
                        {
                            name: 'measurementSystem',
                            type: 'select',
                            options: MeasurementSystemOptions,
                        },
                        {
                            name: 'type',
                            type: 'select',
                            options: Object.values(UserType),
                            defaultValue: UserType.DESIGNER,
                        },
                        {
                            name: 'referrer',
                            type: 'select',
                            options: Object.values(UserReferrer),
                        },
                    ],
                },
                {
                    label: 'Access & flags',
                    fields: [
                        {
                            type: 'row',
                            fields: Object.values(UserAccess).map(
                                (flag) =>
                                    ({
                                        name: flag,
                                        label: flag,
                                        type: 'checkbox',
                                    }) as Field,
                            ),
                        },
                        {
                            name: 'flags',
                            label: 'Feature flags',
                            type: 'select',
                            hasMany: true,
                            options: Object.values(UserFeatureFlags).map((flag) => ({
                                value: flag,
                                label: flag,
                            })),
                            defaultValue: [],
                        },
                    ],
                },
                {
                    label: 'Signup info',
                    fields: [
                        {
                            name: 'phone',
                            type: 'text',
                        },
                        {
                            type: 'text',
                            name: 'source',
                        },
                        {
                            name: 'signup',
                            type: 'group',
                            fields: [
                                {
                                    name: 'company',
                                    type: 'text',
                                },
                                {
                                    name: 'job',
                                    type: 'text',
                                },
                                {
                                    name: 'industry',
                                    type: 'text',
                                },
                                {
                                    name: 'industryOther',
                                    type: 'text',
                                    required: false,
                                },
                                {
                                    name: 'referral',
                                    type: 'text',
                                },
                                {
                                    name: 'systemSize',
                                    type: 'group',
                                    fields: [
                                        {
                                            name: 'min',
                                            type: 'number',
                                        },
                                        {
                                            name: 'max',
                                            type: 'number',
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                },
                {
                    label: 'Suggestions',
                    fields: [
                        {
                            label: 'Dismissed Feature Suggestions',
                            name: 'dismissedFeatureSuggestions',
                            type: 'array',
                            fields: [
                                {
                                    name: 'feature',
                                    type: 'text',
                                },
                                {
                                    name: 'data',
                                    type: 'date',
                                },
                            ],
                        },
                        {
                            label: 'Team Suggestions',
                            name: 'teamSuggestions',
                            type: 'array',
                            fields: [
                                {
                                    name: 'team',
                                    type: 'relationship',
                                    relationTo: 'teams',
                                    maxDepth: 0,
                                },
                                {
                                    name: 'reason',
                                    type: 'text',
                                },
                            ],
                        },
                        {
                            label: 'Team Suggestions Dismissed',
                            name: 'teamSuggestionsDismissed',
                            type: 'checkbox',
                            defaultValue: false,
                        },
                        {
                            label: 'Company Suggestions',
                            name: 'companySuggestions',
                            type: 'array',
                            fields: [
                                {
                                    name: 'company',
                                    type: 'relationship',
                                    relationTo: 'manufacturers',
                                    maxDepth: 0,
                                },
                                {
                                    name: 'reason',
                                    type: 'text',
                                },
                            ],
                        },
                        {
                            label: 'Company Suggestions Dismissed',
                            name: 'companySuggestionsDismissed',
                            type: 'checkbox',
                            defaultValue: false,
                        },
                    ],
                },
            ],
        },
    ],
    hooks: {
        afterChange: [hubspotSync, suggestCompanies, suggestTeams, updateInvitations],
    },
    endpoints: [
        magicLogin,
        magicLoginVerify,
        magicLoginGetToken,
        switchTeam,
        switchOrder,
        getTeams,
        magicLoginSetName,
        logout,
        ping,
        pingProject,
        pingProduct,
        dismissTip,
        sendNewUserInvites,
        impersonate,
        getProgress,
        completeProgressItem,
        finishSignup,
        heartbeat,
    ],
};

export default Users;
