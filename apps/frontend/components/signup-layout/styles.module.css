.root {
    width: 100%;

    display: grid;
    grid-template-columns: repeat(4, 1fr);

    min-height: calc(100dvh - var(--header-height));

    [data-actions] {
        position: sticky;
        bottom: 0;
        z-index: 99;
        background-color: rgba(255, 255, 255, 0.75);
    }

    [data-left-side] {
        grid-column: span 3;

        @media (max-width: var(--mantine-breakpoint-sm)) {
            grid-column: span 4;
        }
    }

    [data-content-wrapper] {
        height: 100%;

        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    [data-content] {
        padding: var(--mantine-spacing-xl);
    }

    [data-right-side] {
        grid-column: span 1;

        @media (max-width: var(--mantine-breakpoint-sm)) {
            display: none;
        }
    }
}
