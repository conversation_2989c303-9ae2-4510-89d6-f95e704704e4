import type { FC } from 'react';
import type { DiagramConnection, DiagramComponentInstance } from '@repo/dcide-component-models';

import { get } from 'radash';

import { Box, Popover } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';
import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

import { CurrentField } from 'components/component-fields/CurrentField';

import { FormatHelpers } from 'helpers/formatters';
import { currentConverter } from '@repo/dcide-component-models';

const DiagramToolbarCurrent: FC<{
    source: DiagramConnection | DiagramComponentInstance;
    path: string;
}> = ({ source, path }) => {
    const [opened, handlers] = useDisclosure(false);
    const formattedCurrent = FormatHelpers.formatMinNomMax(get(source, path), currentConverter);

    return (
        <Popover
            classNames={{
                dropdown: cx.dropdown,
            }}
            opened={opened}
            onClose={handlers.close}
            position="top"
            withArrow
            withinPortal={false}
        >
            <Popover.Target>
                <div>
                    <DiagramToolbar.TextButton
                        tooltip={!opened && formattedCurrent ? 'Current' : ''}
                        onClick={() => {
                            handlers.toggle();
                        }}
                        active={!!formattedCurrent}
                    >
                        {formattedCurrent || 'Current'}
                    </DiagramToolbar.TextButton>
                </div>
            </Popover.Target>
            <Popover.Dropdown>
                <Box className={cx.measurement} style={{ '--measurement-fields': 2 }}>
                    <CurrentField name={path} hideIcons />
                </Box>
            </Popover.Dropdown>
        </Popover>
    );
};

export { DiagramToolbarCurrent };
