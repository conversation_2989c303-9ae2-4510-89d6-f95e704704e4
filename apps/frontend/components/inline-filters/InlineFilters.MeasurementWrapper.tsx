import { FC, useRef } from 'react';

import { useFocusWithin } from '@mantine/hooks';
import { Box, UnstyledButton } from '@mantine/core';

import { InlineFiltersMeasurementPlaceholder } from './InlineFilters.MeasurementPlaceholder';

import cx from './InlineFilters.MeasurementWrapper.module.css';

const InlineFiltersMeasurementWrapper: FC<{
    label?: string;
    placeholder?: string;
    dynamicWidth?: boolean;
    children: React.ReactNode;
}> = ({ label, placeholder, dynamicWidth, children }) => {
    const childrenRef = useRef<HTMLDivElement>(null);

    const { focused, ref } = useFocusWithin();

    const changeFocus = () => {
        childrenRef.current?.querySelector('input')?.focus();
    };

    return (
        <Box ref={ref} className={`${cx.root} ${dynamicWidth && cx.dynamicWidth}`} tabIndex={0}>
            <UnstyledButton fz="inherit" onFocus={changeFocus} className={focused ? cx.hidden : ''}>
                {label ? (
                    label
                ) : placeholder ? (
                    <InlineFiltersMeasurementPlaceholder>{placeholder}</InlineFiltersMeasurementPlaceholder>
                ) : null}
            </UnstyledButton>
            {focused && (
                <Box className={cx.input} ref={childrenRef}>
                    {children}
                </Box>
            )}
        </Box>
    );
};

export { InlineFiltersMeasurementWrapper };
