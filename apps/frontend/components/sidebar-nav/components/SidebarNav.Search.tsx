import React, { useEffect, useState } from 'react';

import { Box, UnstyledButton } from '@mantine/core';
import { IoSearchOutline } from 'react-icons/io5';

import { BrowserHelpers } from 'helpers/BrowserHelpers';

import { useCurrentUser } from 'hooks/use-current-user';

import { SpotlightService } from 'services/SpotlightService';

import cx from './SidebarNav.Search.module.css';

const SidebarNavSearch = () => {
    const [actionKey, setActionKey] = useState('Ctrl');

    useEffect(() => {
        if (BrowserHelpers.isApple()) {
            setActionKey('⌘');
        } else {
            setActionKey('Ctrl');
        }
    }, []);

    return (
        <UnstyledButton className={cx.search} onClick={SpotlightService.open}>
            <IoSearchOutline size={12} />
            <Box className={cx.placeholder}>Quick Search</Box>
            <Box className={cx.shortcut}>{actionKey} K</Box>
        </UnstyledButton>
    );
};

const WrappedSidebarNavSearch = () => {
    const user = useCurrentUser();

    if (!user) {
        return null;
    }

    return <SidebarNavSearch />;
};

export { WrappedSidebarNavSearch as SidebarNavSearch };
