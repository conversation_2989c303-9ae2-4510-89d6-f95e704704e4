import React, { <PERSON> } from 'react';
import { UnstyledButton } from '@mantine/core';

import classes from './DiagramMessage.module.css';

const DiagramMessageShortcut: FC<{
    onClick: () => void;
    children: React.ReactNode;
}> = ({ onClick, children }) => (
    <UnstyledButton className={classes.shortcut} onClick={onClick}>
        {children}
    </UnstyledButton>
);

export { DiagramMessageShortcut };
