import React, { FC } from 'react';

import NextImage from 'next/image';

import {Anchor, Image, Space, Stack, Text, Title} from '@mantine/core';
import { Block } from '@/components/blocks/Block';

import image from './battery-simulations.gif';
import {TextBlock} from '@/components/blocks/TextBlock';

const Content: FC = () => (
    <Block size="md">
        <Text>
            We're thrilled to announce a powerful new enhancement to our electrical design tool, DCIDE: Simulation Profiles!
            This new feature empowers you to move beyond static analyses and dive into the dynamic world of time-series based power flow simulations.
        </Text>
        <Space h="xl" />
        <Title order={2}>
            What are Simulation Profiles?
        </Title>
        <Space h="md" />
        <Text>
            In essence, Simulation Profiles allow you to define how power generation and consumption change over time within your electrical designs.
            Instead of just seeing a snapshot of your system at a single moment, you can now model its behavior throughout a day, a week, or even longer.
            This is a game-changer for understanding system performance under varying conditions, optimizing energy management, and identifying potential issues before they arise.
        </Text>
        <Space h="xl" />
        <Title order={2}>
            Time-Series Power Flow at Your Fingertips
        </Title>
        <Space h="md" />
        <Text>
            With Simulation Profiles, DCIDE users can now:
            <ul>
                <li>
                    Run Time-Series Based Power Flow Simulations: Accurately model the ebb and flow of power, observing how voltages, currents, and power factors fluctuate as loads and generation sources change throughout a defined period. This is invaluable for systems with intermittent renewables like solar and wind, or dynamic load demands.
                </li>
                <li>
                    Create and Edit Custom Profiles: Tailor profiles to your specific needs. Whether you're modeling a typical sunny day, a cloudy week, or specific operational schedules, you have the flexibility to define the precise time-series data for various components in your electrical system.
                </li>
                <li>
                    Import and Export Data: We understand that you might have existing data or prefer to work with external tools. That's why DCIDE now supports seamless import and export of simulation profile data, making it easy to integrate with your current workflows and share information across projects.
                </li>
            </ul>
        </Text>
        <Space h="md" />
        <Text>
            This new capability in DCIDE provides unprecedented depth to your electrical simulations, allowing for more robust designs, better operational planning, and a deeper understanding of your system's performance under real-world conditions.
        </Text>
        <Space h="md" />
        <Text>
            Ready to take your electrical design to the next level?
            {' '}
            <Anchor href="https://dcide.app/login" target="_blank">
                Explore Simulation Profiles
            </Anchor>
            {' '}
            in DCIDE today and unlock the power of dynamic insights!
        </Text>
    </Block>
);

export { Content };
