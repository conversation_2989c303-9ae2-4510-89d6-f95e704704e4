import type { FC } from 'react';
import type { DiagramConnection, DiagramComponentInstance, VoltageType } from '@repo/dcide-component-models';

import { get } from 'radash';

import { Box, Popover } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { useDiagramVoltages } from 'components/diagram/hooks/use-diagram-voltages';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';
import { VoltageFieldWithSuggestions } from 'components/component-fields/VoltageFieldWithSuggestions';

import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.scss';

import { FormatHelpers } from 'helpers/formatters';
import { VoltageConverter } from 'units/voltage';

const DiagramToolbarVoltage: FC<{
    source: DiagramConnection | DiagramComponentInstance;
    voltageType?: VoltageType | null;
    path: string;
}> = ({ source, path, voltageType }) => {
    const [opened, handlers] = useDisclosure(false);
    const formattedVoltage = FormatHelpers.formatMinNomMax(get(source, path), VoltageConverter);

    const recentVoltages = useDiagramVoltages();

    return (
        <Popover
            classNames={{
                dropdown: cx.dropdown,
            }}
            opened={opened}
            onClose={handlers.close}
            position="top"
            withArrow
            withinPortal={false}
        >
            <Popover.Target>
                <div>
                    <DiagramToolbar.TextButton
                        tooltip={!opened && formattedVoltage ? 'Voltage' : ''}
                        onClick={() => {
                            handlers.toggle();
                        }}
                        active={!!formattedVoltage}
                    >
                        {formattedVoltage || 'Voltage'}
                    </DiagramToolbar.TextButton>
                </div>
            </Popover.Target>
            <Popover.Dropdown>
                <Box className={cx.measurement} style={{ '--measurement-fields': 3 }}>
                    <VoltageFieldWithSuggestions
                        name={path}
                        hideIcons
                        withinPortal={false}
                        voltageType={voltageType}
                        recentVoltages={recentVoltages}
                        onOptionSubmit={() => {
                            handlers.close();
                        }}
                    />
                </Box>
            </Popover.Dropdown>
        </Popover>
    );
};

export { DiagramToolbarVoltage };
