import type { FC } from 'react';

import { Box, Flex, UnstyledButton } from '@mantine/core';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';

import cx from './InlineFilters.ClearButton.module.css';

const InlineFiltersClearButton: FC<{ onClick: () => void }> = ({ onClick }) => {
    return (
        <Flex className={cx.clearButtonWrapper}>
            <Box data-clear-gradient className={cx.clearGradient} />
            <InlineFilters.Section
                wrapperProps={{
                    className: cx.clearButton,
                }}
                body={
                    <UnstyledButton onClick={onClick} fz="inherit">
                        Clear filters
                    </UnstyledButton>
                }
                active={false}
            />
        </Flex>
    );
};

export { InlineFiltersClearButton };
