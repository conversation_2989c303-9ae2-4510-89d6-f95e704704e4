import { Box, Divider, Indicator, Popover, Stack, Text } from '@mantine/core';
import { TbCurrencyDollar, TbLayoutSidebarRightCollapse, TbLayoutSidebarRightExpand, TbSpark<PERSON> } from 'react-icons/tb';

import {
    IoAddSharp,
    IoChatbubbleOutline,
    IoChatbubblesOutline,
    IoPlayCircleOutline,
    IoSettingsOutline,
    IoShieldCheckmarkOutline,
    IoResizeOutline,
    IoBulbOutline,
    IoDocumentOutline,
} from 'react-icons/io5';
import { BsPencil } from 'react-icons/bs';
import { useDiagramComments } from 'components/diagram/hooks';
import { useChatChannels } from 'components/diagram/hooks/use-chat-channels';
import { useSidebar } from 'components/diagram/hooks/use-sidebar';
import { useCurrentProject } from 'hooks/use-current-project';
import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar';
import { TipService } from 'components/diagram/services/TipService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { RouterService } from 'services/RouterService';
import { SidebarType } from 'components/diagram/state/sidebar';

import cx from './DiagramSidebar.Nav.module.css';
import {
    PermissionDiagramElements,
    PermissionDiagramFiles,
    PermissionDiagramGeneral,
    PermissionDiagramSimulations,
    PermissionDigramComments,
    UserFeatureFlags,
} from '@repo/dcide-component-models';
import { usePermission } from 'hooks/use-permission';
import { useDiagramValidationErrorCount } from 'components/diagram/hooks/use-diagram-validation-error-count';
import { useCurrentUserFlag } from 'hooks/use-current-user-flag';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useRouterQuery } from 'hooks/use-router-query';

const DiagramSidebarNav = () => {
    const { collapsed } = useSidebar();
    const { startedFromReferenceDesign } = useRouterQuery<{
        startedFromReferenceDesign?: boolean;
    }>();

    const userHasWireSizing = useCurrentUserFlag(UserFeatureFlags.WIRE_SIZING);

    const project = useCurrentProject();
    const canCreate = usePermission(PermissionDiagramElements.CREATE);
    const canEdit = usePermission(PermissionDiagramElements.EDIT);
    const canEditProject = usePermission(PermissionDiagramGeneral.EDIT);
    const canViewComments = usePermission(PermissionDigramComments.VIEW);
    const canBOM = usePermission(PermissionDiagramGeneral.BOM);
    const canViewFiles = usePermission(PermissionDiagramFiles.VIEW);
    const canDownload = usePermission(PermissionDiagramGeneral.DOWNLOAD);
    const canViewSimulations = usePermission(PermissionDiagramSimulations.VIEW);

    const comments = useDiagramComments();
    const unseenComments = comments.filter((comment) => !comment.seen);

    const { channels } = useChatChannels();
    const unseenChannels = channels.filter((channel) => !channel.viewed);

    const tips = TipService.getAll();

    const validationErrorCount = useDiagramValidationErrorCount();

    const { company: referenceDesignCompany } = useCompanyProfile(project?.reference?.profile);

    const closeReferenceCompanyPopover = async () => {
        await RouterService.removeQuery('startedFromReferenceDesign');
    };

    return (
        <Stack className={cx.nav}>
            {/* TOP */}
            <Stack>
                {(canCreate || canEdit || canViewComments) && (
                    <>
                        <DiagramSidebar.NavAction
                            label={collapsed ? 'Show sidebar' : 'Collapse sidebar'}
                            onClick={() => {
                                SidebarService.toggleCollapsed();
                            }}
                        >
                            {collapsed ? (
                                <TbLayoutSidebarRightExpand size={16} strokeWidth={1.5} />
                            ) : (
                                <TbLayoutSidebarRightCollapse size={16} strokeWidth={1.5} />
                            )}
                        </DiagramSidebar.NavAction>
                        <Divider />
                    </>
                )}
                {canCreate && (
                    <DiagramSidebar.NavItem type={SidebarType.ADD} label="Add component">
                        <IoAddSharp size={16} strokeWidth={1.5} />
                    </DiagramSidebar.NavItem>
                )}

                {canEdit && (
                    <DiagramSidebar.NavItem type={SidebarType.EDIT} label="Edit">
                        <BsPencil size={16} />
                    </DiagramSidebar.NavItem>
                )}

                {canViewComments && (
                    <DiagramSidebar.NavItem type={SidebarType.COMMENTS} label="Comments">
                        <Indicator
                            inline
                            size={14}
                            color="red"
                            label={unseenComments.length}
                            disabled={!unseenComments.length}
                            styles={{ indicator: { fontSize: 8 } }}
                        >
                            <IoChatbubbleOutline size={16} strokeWidth={1.5} />
                        </Indicator>
                    </DiagramSidebar.NavItem>
                )}

                {canViewComments && (
                    <DiagramSidebar.NavItem type={SidebarType.CHAT} label={startedFromReferenceDesign ? '' : 'Chat'}>
                        {startedFromReferenceDesign && referenceDesignCompany ? (
                            <Box data-mantine-color-scheme="dark" onClick={closeReferenceCompanyPopover}>
                                <Popover opened position="left" withinPortal={false} withArrow>
                                    <Popover.Target>
                                        <Indicator
                                            inline
                                            size={14}
                                            color="red"
                                            label={unseenChannels.length}
                                            disabled={!unseenChannels.length}
                                            styles={{ indicator: { fontSize: 8 } }}
                                        >
                                            <IoChatbubblesOutline size={16} strokeWidth={1.5} />
                                        </Indicator>
                                    </Popover.Target>
                                    <Popover.Dropdown>
                                        <Text c="white">
                                            Need some assistance from <strong>{referenceDesignCompany?.name}</strong>?
                                            <br />
                                            Click here to start your conversation.
                                        </Text>
                                    </Popover.Dropdown>
                                </Popover>
                            </Box>
                        ) : (
                            <Indicator
                                inline
                                size={14}
                                color="red"
                                label={unseenChannels.length}
                                disabled={!unseenChannels.length}
                                styles={{ indicator: { fontSize: 8 } }}
                            >
                                <IoChatbubblesOutline size={16} strokeWidth={1.5} />
                            </Indicator>
                        )}
                    </DiagramSidebar.NavItem>
                )}

                {canViewSimulations && (
                    <DiagramSidebar.NavItem type={SidebarType.VALIDATIONS} label="Validations">
                        <Indicator
                            inline
                            size={14}
                            color="red"
                            label={validationErrorCount > 99 ? '99+' : validationErrorCount}
                            disabled={validationErrorCount === 0}
                            styles={{ indicator: { fontSize: 8 } }}
                        >
                            <IoShieldCheckmarkOutline size={16} strokeWidth={1.5} />
                        </Indicator>
                    </DiagramSidebar.NavItem>
                )}

                {canViewSimulations && (
                    <DiagramSidebar.NavItem type={SidebarType.SIMULATIONS} label="Simulations">
                        <IoPlayCircleOutline size={16} strokeWidth={1.5} />
                    </DiagramSidebar.NavItem>
                )}

                {userHasWireSizing && canViewSimulations && (
                    <DiagramSidebar.NavItem type={SidebarType.WIRE_SIZING} label="Wire Sizing">
                        <IoResizeOutline size={16} strokeWidth={1.5} />
                    </DiagramSidebar.NavItem>
                )}

                {project && canBOM && (
                    <DiagramSidebar.NavItem type={SidebarType.BILL_OF_MATERIALS} label="Bill of Materials">
                        <TbCurrencyDollar size={16} strokeWidth={1.5} />
                    </DiagramSidebar.NavItem>
                )}
                {project && canViewFiles && (
                    <DiagramSidebar.NavItem type={SidebarType.FILES} label="Files">
                        <IoDocumentOutline size={16} />
                    </DiagramSidebar.NavItem>
                )}
                {project && canEditProject && (
                    <DiagramSidebar.NavItem type={SidebarType.PROJECT} label="Project">
                        <IoSettingsOutline size={16} strokeWidth={1.5} />
                    </DiagramSidebar.NavItem>
                )}
                {canEdit && (
                    <DiagramSidebar.NavItem type={SidebarType.AI} label="AI Assistant">
                        <TbSparkles size={16} strokeWidth={1.5} />
                    </DiagramSidebar.NavItem>
                )}
            </Stack>

            {/* BOTTOM */}
            <Stack gap={8}>
                {canDownload && <DiagramSidebar.Export />}

                <DiagramSidebar.Share />

                {canEditProject && tips.length && (
                    <>
                        <Divider />
                        <DiagramSidebar.NavItem type={SidebarType.TIPS} label="Tips">
                            {/* <Indicator inline offset={3} size={6} color="red" disabled={!hasNewTips}>
                                <IoInformationCircleOutline size={16} />
                            </Indicator> */}
                            <IoBulbOutline size={16} />
                        </DiagramSidebar.NavItem>
                    </>
                )}
            </Stack>
        </Stack>
    );
};

export { DiagramSidebarNav };
