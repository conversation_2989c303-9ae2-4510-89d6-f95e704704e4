import { FC, useState } from 'react';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@mantine/core';

import { Component } from '@repo/dcide-component-models';

import { AIService } from 'services/AIService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { useSnapshot } from 'hooks/use-safe-snapshot';
import { useComponentContext } from '../hooks/use-component-context';

import { componentSignupState } from 'components/component-signup/state';

import { DatasheetMode, useDatasheetMode } from 'components/datasheet';

import cx from './AiAssistantProductComponent.module.css';

type AiAssistantProductComponentProps = {
    name: string;
};

const AiAssistantProductComponent: FC<AiAssistantProductComponentProps> = ({ name }) => {
    const mode = useDatasheetMode();
    const [error, setError] = useState(false);
    const [loading, setLoading] = useState(false);
    const [key] = useState(name);
    const { component } = useComponentContext();
    const componentState = useSnapshot(componentSignupState);

    const editMode = mode !== DatasheetMode.VIEW;

    const onClick = async () => {
        const hasResponse = componentState.aiResponses.has(key);

        try {
            componentSignupState.key = key;

            if (!hasResponse) {
                await getAssistantFact(component.id, component.productIdentifier, component.type, key);
            }

            if (key === componentSignupState.key) {
                const aiResponse = componentState.aiResponses.get(key);
                componentSignupState.selectedResponse = aiResponse;
                componentSignupState.aiOpened = true;
            }
        } catch (error) {
            console.log(error);
            setLoading(false);
            setError(true);
        }
    };

    const getAssistantFact = async (
        componentId: Component['id'],
        productIdentifier: Component['productIdentifier'],
        componentType: Component['type'],
        key: string,
    ) => {
        setLoading(true);

        InternalTrackingService.track('product.aiDatasheet.verify', {
            componentId,
            componentType,
        });

        await AIService.getAssistantFact(componentId, productIdentifier, componentType, key, 'datasheet')
            .then((response) => {
                if (response.status !== 200) {
                    throw new Error('AI service - something went wrong');
                }
                return response.json();
            })
            .then((response) => {
                setLoading(false);
                componentSignupState.aiResponses.set(key, response);
            });
    };

    return (
        editMode && (
            <div className={cx.aiResponse}>
                <Tooltip fz="xs" label={error ? 'Retry' : 'Verify with AI'} position="top" disabled={loading}>
                    <Button c="primary" variant="subtle" size="compact-xs" onClick={onClick} loading={loading}>
                        {error ? 'Something Went Wrong' : 'Verify'}
                    </Button>
                </Tooltip>
            </div>
        )
    );
};

export { AiAssistantProductComponent };
