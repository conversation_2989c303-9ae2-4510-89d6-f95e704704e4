.icon {
    width: 40px;
    height: 40px;

    margin: 0 auto;
    margin-top: var(--mantine-spacing-xs);

    stroke-width: 1.25px;
}

.title {
    margin: 0;
    margin-bottom: var(--mantine-spacing-xs);

    font-size: 20px;
    font-weight: 700;

    text-align: center;
}

.aiClose {
    position: absolute;
    right: 15px;
    top: 15px;
}

.card {
    position: relative;
    padding: var(--mantine-spacing-sm);
    transition: background-color 0.3s ease, border-color 0.3s ease;

    &:hover {
        background-color: var(--mantine-color-gray-0);
    }

    &[disabled] {
        opacity: 0.5;

        &:hover {
            background-color: transparent;
        }
    }

    &[data-checked] {
        border-color: var(--mantine-color-gray-5);
    }
}

.label {
    font-size: 14px;
    font-weight: 600;

    line-height: 20px;
}
