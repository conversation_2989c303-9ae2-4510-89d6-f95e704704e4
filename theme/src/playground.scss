@import "~/scaffolding/index";

.flickity-page-dots {
    position: relative !important;
    top: 0 !important;
    bottom: 0 !important;

    margin-top: $spacing-small !important;

    .dot {
        background-color: transparent !important;

        border: 0.2rem solid var(--accent) !important;
        border-radius: 50% !important;

        opacity: 1 !important;

        transition: background-color $basic-ease, transform $basic-ease !important;

        &.is-selected {
            background-color: var(--accent) !important;

            transform: scale(1.4) !important;
        }
    }

    .flickity-prev-next-button[disabled]
        + .flickity-prev-next-button[disabled]
        + & {
        display: none !important;
    }

    @media (min-width: $breakpoint-tablet-small) {
        margin-top: $spacing-medium !important;
    }
}

.flickity-prev-next-button {
    top: calc(50% - 2.5rem - 2.5rem) !important; // pagedots space - arrow space

    width: 5rem !important;
    height: 5rem !important;

    transform: none !important;

    background-color: var(--button-background) !important;
    color: var(--button-color) !important;

    transition: background-color $basic-ease, color $basic-ease !important;

    .flickity-button-icon {
        position: absolute !important;
        left: 50% !important;
        top: 50% !important;

        width: 1.6rem !important;

        transform: translate(-50%, -50%) !important;
    }

    &[disabled] {
        opacity: 0 !important;
    }

    &:hover,
    &:focus {
        background-color: var(--button-hover-background) !important;
        color: var(--button-hover-color) !important;
    }

    &.previous {
        left: auto !important;
        right: calc(100% - 2.5rem) !important;
    }

    &.next {
        left: calc(100% - 2.5rem) !important;
        right: auto !important;
    }

    @media (max-width: #{$breakpoint-desktop-small - 1}) {
        display: none !important;
    }
}

.separator {
    display: block;
    width: 100%;
    height: 0.1rem;

    border: none;

    background-color: currentColor;
    opacity: 0.1;
}

.site-container {
    display: flex;
    flex-direction: column;

    max-width: 100%;
    min-height: 100vh;

    overflow-x: hidden;

    &__notice {
        position: relative;
        z-index: 5;

        padding: 0.5em 0;

        text-align: center;

        background-color: $body-color;
        color: #ffffff;
    }

    &__navigation {
        position: relative;
        z-index: 3;
    }

    &__content {
        flex-grow: 1;
        flex-shrink: 0;

        position: relative;
        z-index: 2;
    }

    &__footer {
        flex-grow: 0;
        flex-shrink: 0;

        position: relative;
        z-index: 1;
    }
}

.contact {
    background-color: #ffffff;

    &__content {
        padding: $spacing-small;

        .headline-3 {
            margin-bottom: 0.5em;

            &:not(:first-child) {
                margin-top: 1.5em;
            }
        }
    }

    &__map {
        position: relative;

        &::after {
            content: "";

            display: block;
            padding-top: 56.26%;
        }

        iframe {
            position: absolute;
            left: 0;
            top: 0;

            display: block;
            width: 100%;
            height: 100%;
        }
    }

    @media (min-width: $breakpoint-tablet-small) {
        &__content {
            padding: $spacing-medium;
        }
    }

    @media (min-width: $breakpoint-desktop-small) {
        display: flex;
        flex-direction: row;

        &__content {
            flex-grow: 1;
            flex-shrink: 1;

            padding: $spacing-large;
        }

        &__map {
            flex-grow: 0;
            flex-shrink: 0;

            width: calc(50% - #{$spacing-large * 0.5});

            &::after {
                display: none;
            }
        }

        &.is-inversed & {
            &__content {
                order: 2;
            }

            &__map {
                order: 1;
            }
        }
    }
}

.footer-newsletter {
    .form-label {
        display: none;
    }
}

.loading-icon {
    width: 20%;
    max-width: 6rem;

    margin: 0 auto;

    color: var(--accent);

    animation: spin 1.2s linear infinite;
}

@supports (-webkit-appearance: none) {
    input[type="checkbox"] {
        position: relative;

        -webkit-appearance: none;

        display: block;
        width: 1.8rem;
        height: 1.8rem;

        background: #ffffff;
        color: var(--accent);

        border: 1px solid $border-color;
        outline: none !important;

        transition: border $basic-ease;

        &:hover,
        &:focus,
        &:checked {
            border: 1px solid darken($border-color, 15);
        }

        &:checked::after {
            content: "";

            position: absolute;
            left: calc(50% - 0.5rem);
            top: calc(50% - 0.5rem);

            width: 1rem;
            height: 0.6rem;

            border-left: 0.2rem solid currentColor;
            border-bottom: 0.2rem solid currentColor;

            transform: rotate(-45deg);
        }
    }
}

.fui-btn,
.fui-alert,
.fui-tabs,
.fui-label,
.fui-legend,
.fui-instructions,
.fui-error-message,
.fui-checkbox,
.fui-radio,
.fui-input,
.fui-select {
    font-size: 1.6rem !important;
}

.order-overview {
    position: relative;
    z-index: 20;
}

.order-overview-filters {
    display: grid;

    grid-template-columns: 1fr;
    grid-gap: $spacing-small * 0.5 $spacing-small;

    @media (min-width: $breakpoint-tablet-small) {
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: $spacing-small;
    }
}

.order-overview-overview-table {
    width: 100%;

    font-size: 1.6rem;

    border-collapse: collapse;
    border-radius: $border-radius;

    th,
    td {
        padding: .5em 1em;
        border: 1px solid $border-color;
    }
}

@media print {
    html.page-orderOverview {
        font-size: 8px;
    }

    .page-orderOverview {
        .site-container__navigation,
        .site-container__footer,
        .header {
            display: none;
        }

        .order-teaser {
            padding: 0;

            border: none;
            box-shadow: none;

            page-break-inside: avoid;
            page-break-after: always;
        }

        .order-teaser__actions {
            display: none;
        }
    }

    .print-hidden {
        display: none;
    }

    .print-avoid-break-inside {
        page-break-inside: avoid;
    }

    .print-break-after {
        page-break-after: always;
    }
}

.order-table {
    width: 100%;
    border-collapse: collapse;

    td,
    th {
        padding: .5em;

        text-align: left;

        border: 1px solid $border-color;
    }
}

.table-of-contents {
    &__content {

    }
}
