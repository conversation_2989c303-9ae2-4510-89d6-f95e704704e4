{% set teaserStyle = block.teaserStyle.one %}
{% set gridStyle = craft.entries.section('gridStyle').one %}

<div class="{{ cx(gridStyle) }}">

    {% set textAlign = {
        start: '',
        center: 'text-center',
        right: 'text-right',
    }[teaserStyle.horizontalAlign.value] %}

    {% for teaser in block.teasers.all %}

        <div
            class="group relative {{ cx([teaserStyle.cardStyle, teaserStyle.padding]) }} {{ textAlign }}"

        >
            <div
                class="
                    {{ cx([teaserStyle.titleFont, teaserStyle.titleFontSize]) }}
                    text-(--card-title-color)
                    group-hover:text-(--card-title-color-hover)
                "
                style="
                    --card-title-color: {{ var(teaserStyle.titleColor) }};
                    --card-title-color-hover: {{ var(teaserStyle.titleColorHover) }};
                "
            >
                {{ teaser.title }}
            </div>

            {% if teaser.teaserDescription %}
                <div class="{{ cx(teaserStyle.descriptionSpacing) }}"></div>
                <div
                    class="
                        {{ cx([teaserStyle.descriptionFont, teaserStyle.descriptionFontSize]) }}
                        text-(--card-description-color)
                        group-hover:text-(--card-description-color-hover)
                    "
                    style="
                        --card-description-color: {{ var(teaserStyle.titleColor) }};
                        --card-description-color-hover: {{ var(teaserStyle.titleColorHover) }};
                    "
                >
                    {{ teaser.teaserDescription }}
                </div>
            {% endif %}

            {% for buttonStyle in teaserStyle.buttonStyle.all %}
                <div
                    class="
                        absolute right-0 bottom-0
                        flex items-center justify-center
                        w-10 h-10
                        bg-(--button-background) group-hover:bg-(--button-background-hover)
                        text-(--button-color) group-hover:text-(--button-color-hover)
                        border-(--button-border) rounded-(--button-border-radius)
                        transition-all
                    "
                    style="
                        --button-background: {{ var(buttonStyle.background) }};
                        --button-background-hover: {{ var(buttonStyle.backgroundHover) }};
                        --button-color: {{ var(buttonStyle.color) }};
                        --button-color-hover: {{ var(buttonStyle.colorHover) }};
                        --button-border: {{ var(buttonStyle.border) }};
                        --button-border-radius: {{ borderRadius(buttonStyle.border) }};
                    "
                >
                    <svg class="w-4 aspect-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3">
                        <path d="M5 12h14M12 5l7 7-7 7"></path>
                    </svg>
                </div>
            {% endfor %}

        </div>

    {% endfor %}
</div>
