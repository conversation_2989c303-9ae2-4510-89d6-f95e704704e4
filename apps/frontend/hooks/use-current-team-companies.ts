import { CompanyService, CompanyProfile, ManufacturerProfile, Team } from '@repo/dcide-component-models';

import {useSnapshot} from 'hooks/use-safe-snapshot';

import { SWRResponse } from 'swr';
import useSWRImmutable from 'swr/immutable';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { prepareConditionalSwrArgs } from './prepare-conditional-swr-args';

import {currentTeamCompaniesState} from '../state/current-team-companies';


const filter = (companies: CompanyProfile[], service: CompanyService) => {
    return companies.filter((company) => company.services.includes(service));
};

const useTeamCompanies = (
    teamId: Team['id'] | undefined,
): SWRResponse & {
    companies: ManufacturerProfile[];
    inAppSupportCompanies: ManufacturerProfile[];
    manufacturers: ManufacturerProfile[];
    distrbutors: ManufacturerProfile[];
} => {
    const swr = useSWRImmutable<{ docs: ManufacturerProfile[] }>(
        ...prepareConditionalSwrArgs({
            key: `/teams/${teamId}/manufacturers`,
            fetcher: () => CompanyProfileService.getByTeam(teamId ?? ''),
            condition: Boolean(teamId),
        }),
    );

    const companies = swr?.data?.docs || [];



    return {
        ...swr,
        companies,
        inAppSupportCompanies: [], // filter(companies, CompanyService.IN_APP_SUPPORT),
        manufacturers: [], // filter(companies, CompanyService.MANUFACTURING),
        distrbutors: [], // filter(companies, CompanyService.DISTRIBUTION),
    };
};

const useCurrentTeamCompanies = () => {
    const { companies } = useSnapshot(currentTeamCompaniesState);

    console.log('✅ compies', companies);

    return {
        companies,
        inAppSupportCompanies: filter(companies, CompanyService.IN_APP_SUPPORT),
        manufacturers: filter(companies, CompanyService.MANUFACTURING),
        distrbutors: filter(companies, CompanyService.DISTRIBUTION),
    };
};

export { useTeamCompanies, useCurrentTeamCompanies };
