import React, { useEffect } from 'react';

import { useController, useWatch, useFormContext } from 'react-hook-form';
import { isNumber } from 'radash';

import { Tooltip } from '@mantine/core';
import { IoCalculatorSharp } from 'react-icons/io5';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';

import { safeRound } from 'helpers/safe-round';

type CalculateProps = {
    name: string;
    dependencies: string[];
    calculate: (...args: number[]) => number;
    size?: 'xs' | 'sm' | 'md' | 'lg';
};

const useCalculate = ({
    name,
    dependencies,
    calculate,
    overrideExistingValue,
}: CalculateProps & { overrideExistingValue?: boolean }) => {
    const { setValue } = useFormContext();
    const { field } = useController({
        name,
    });

    const subfields = ['value', 'min', 'nom', 'max'].filter((subfield) => {
        return field.value && field.value[subfield] !== undefined;
    });

    const dependencyValues = useWatch({
        name: dependencies,
    });

    const doAutoFill = subfields.some((subfield) => {
        // Don't autofill if the field already has a value
        if (field.value[subfield] && !overrideExistingValue) {
            return false;
        }

        // Make sure all dependencies have values
        if (dependencyValues.some((value) => !value?.[subfield])) {
            return false;
        }

        return true;
    });

    const autofill = () => {
        const currentValue = field.value;

        subfields.forEach((subfield) => {
            if (!field.value[subfield] || overrideExistingValue) {
                const args = dependencies.map((_, index) => dependencyValues[index][subfield]);

                if (args.every((arg) => isNumber(arg))) {
                    currentValue[subfield] = safeRound(calculate(...args));
                }
            }
        });

        setValue(`${name}`, currentValue);
    };

    return {
        dependencyValues,
        doAutoFill,
        autofill,
    };
};

const Calculate = ({ size = 'xs', ...props }: CalculateProps) => {
    const { doAutoFill, autofill } = useCalculate(props);

    return doAutoFill ? (
        <Tooltip label="Autofill these fields" openDelay={500} withArrow>
            <DiagramSidebar.Button onClick={autofill} size={size}>
                <IoCalculatorSharp strokeWidth={0.5} />
            </DiagramSidebar.Button>
        </Tooltip>
    ) : null;
};

const AutoCalculate = (props: CalculateProps) => {
    const { dependencyValues, doAutoFill, autofill } = useCalculate({
        ...props,
        overrideExistingValue: true,
    });

    useEffect(() => {
        if (doAutoFill) {
            autofill();
        }
    }, [JSON.stringify(dependencyValues)]);

    return null;
};

Calculate.Auto = AutoCalculate;

export { Calculate };
