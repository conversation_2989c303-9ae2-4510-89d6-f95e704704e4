<div class="grid gap-4 lg:grid-cols-10 lg:gap-10 | max-w-7xl mx-auto">
    <div class="lg:col-span-4">
        <h2 class="{{ h2Classes() }}">
            {{ block.internaltitle }}
        </h2>
        {% if block.textLeft %}
            <div class="lg:w-4/5 mt-4 lg:text-lg">
                {{ block.textLeft }}
            </div>
        {% endif %}
    </div>
    <div class="lg:col-span-6 lg:text-lg">
        {% if block.textLeft %}
            <div class="hidden lg:block {{ h2Classes() }} mb-4">&nbsp;</div>
        {% else %}
            <div class="hidden lg:block h-2"></div>
        {% endif %}
        {{ block.textRight }}
    </div>
</div>
