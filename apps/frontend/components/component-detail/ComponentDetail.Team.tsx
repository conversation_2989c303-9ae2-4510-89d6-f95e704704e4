import React, { FC, useState } from 'react';

import { Bad<PERSON>, Card, Stack, Text } from '@mantine/core';

import { Component } from '@repo/dcide-component-models';

import { useCurrentTeams } from 'hooks/use-current-teams';

import { Page } from 'components/page/Page';
import { useDatasheetMeta } from 'components/component-datasheet/hooks/use-datasheet-meta';
import { ComponentForm } from 'components/component-form/ComponentForm';
import { ComponentDatasheet } from 'components/component-datasheet/components/ComponentDatasheet.Team';
import { ComponentDatasheetActions } from 'components/component-datasheet/components/ComponentDatasheetActions.Team';
import { CurrentComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import cx from './ComponentDetail.Team.module.css';

interface Props {
    component: Component;
}

const ComponentDetail: FC<Props> = ({ component }) => {
    const { subtitle } = useDatasheetMeta(component);

    const teams = useCurrentTeams();
    const componentTeam = teams.find((team) => team.id === component.team);

    if (!componentTeam) return null;

    return (
        <Page showBackground title={component.name}>
            <Page.Content
                title={component.name}
                subtitle={subtitle}
                afterTitle={
                    <Stack gap={4}>
                        <Badge size="sm" color="orange" variant="light">
                            Private
                        </Badge>
                        <Text size="xs" c="dimmed">
                            Everyone in team{' '}
                            <Text span inherit fw={500}>
                                {componentTeam?.name}
                            </Text>{' '}
                            can view and modify
                        </Text>
                    </Stack>
                }
                rightSection={<ComponentDatasheetActions />}
            >
                <Card className={cx.root}>
                    <ComponentDatasheet component={component} />
                </Card>
            </Page.Content>
        </Page>
    );
};

const WrappedComponentDetail: FC<Props> = (props) => {
    const [currentComponent, setCurrentComponent] = useState<Component>(props.component);

    return (
        <CurrentComponentContext.Provider
            value={{
                component: currentComponent,
                setComponent: setCurrentComponent,
            }}
        >
            <ComponentForm initialValues={props.component}>
                <ComponentDetail {...props} />
            </ComponentForm>
        </CurrentComponentContext.Provider>
    );
};

export { WrappedComponentDetail as ComponentDetail };
