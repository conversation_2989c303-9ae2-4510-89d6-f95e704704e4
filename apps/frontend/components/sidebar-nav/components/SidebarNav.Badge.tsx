import { Badge, BadgeProps, Tooltip } from '@mantine/core';
import Link from 'next/link';

import cx from './SidebarNav.Badge.module.css';
import cxTooltip from './SidebarNav.Tooltip.module.css';

const SidebarNavBadge = ({
    href,
    tooltip,
    children,
    ...props
}: { href?: string; tooltip?: string; children: React.ReactNode } & BadgeProps) => {
    const badgeProps: BadgeProps = {
        size: 'xs',
        color: 'primary',
        variant: 'light',
        ...props,
    };

    if (href) {
        return (
            <Tooltip classNames={cxTooltip} label={tooltip} disabled={!tooltip}>
                <Badge className={cx.badge} component={Link} href={href} {...badgeProps}>
                    {children}
                </Badge>
            </Tooltip>
        );
    }

    return (
        <Tooltip classNames={cxTooltip} label={tooltip} disabled={!tooltip}>
            <Badge className={cx.badge} {...badgeProps}>
                {children}
            </Badge>
        </Tooltip>
    );
};

export { SidebarNavBadge };
