import React, { FC, useEffect, useState } from 'react';
import { Editor } from '@tiptap/react';

import { Box } from '@mantine/core';

import { TipTapComposer } from 'components/tiptap/TipTapComposer';

import cx from './Intercom.module.css';

declare global {
    interface Window {
        dangerouslySetIntercomContent?: (content: string) => void;
    }
}

const IntercomComposer: FC<{
    placeholder?: string;
    onSubmit: any;
}> = ({ placeholder = 'Your question', onSubmit }) => {
    const [editor, setEditor] = useState<Editor | null>(null);

    useEffect(() => {
        window.dangerouslySetIntercomContent = (content: string) => {
            if (editor) {
                editor.commands.setContent(content);
                editor.commands.focus();
            }
        };

        return () => {
            window.dangerouslySetIntercomContent = undefined;
        };
    });

    return (
        <Box className={cx.composer}>
            <TipTapComposer
                placeholder={placeholder}
                onInit={(editor) => {
                    setEditor(editor);
                }}
                onSubmit={onSubmit}
            >
                <TipTapComposer.Content />
                <TipTapComposer.Footer>
                    <TipTapComposer.FilesButton />
                </TipTapComposer.Footer>
            </TipTapComposer>
        </Box>
    );
};

export { IntercomComposer };
