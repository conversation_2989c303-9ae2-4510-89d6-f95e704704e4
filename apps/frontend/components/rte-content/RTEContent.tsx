import React, { FC, useEffect } from 'react';

import { Box, BoxProps } from '@mantine/core';

import { EditorContent, JSONContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import CharacterCount from '@tiptap/extension-character-count';
import Link from '@tiptap/extension-link';

import cx from './RTEContent.module.css';

const RTEContent: FC<
    {
        content?: JSONContent | null;
        emptyMessage?: React.ReactNode;
        updateCharacterCount?: (count: number) => void;
        updateWordCount?: (count: number) => void;
    } & BoxProps
> = ({ content, emptyMessage, updateCharacterCount, updateWordCount, ...props }) => {
    const editor = useEditor(
        {
            // CharacterCount.extend is needed: https://github.com/ueberdosis/tiptap/issues/4664#issuecomment-2200878320
            extensions: [StarterKit, CharacterCount.extend(), Link],
            content,
            editable: false,
            immediatelyRender: false,
        },
        [content],
    );

    useEffect(() => {
        if (editor && editor.getText()) {
            updateCharacterCount?.(editor.storage.characterCount.characters());
            updateWordCount?.(editor.storage.characterCount.words());
        }
    }, [editor]);

    if (!editor) return null;
    if (!editor.getText()) return emptyMessage ?? null;

    return (
        <Box {...props}>
            <EditorContent editor={editor} className={cx.root} />
        </Box>
    );
};

export { RTEContent };
