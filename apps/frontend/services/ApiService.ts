import { config } from 'config';

type FetchOptions = Omit<RequestInit, 'method'> & { throwError?: boolean; hideConsoleError?: boolean };
type FetchOptionsNoBody = Omit<FetchOptions, 'body'>;

type ApiLogger = {
    warn: (...messages: any[]) => void;
    error: (...messages: any[]) => void;
};

const MutedLogger: ApiLogger = {
    warn: () => {},
    error: () => {},
};

class ApiService {
    static token = '';

    static fetch = async (
        endpoint: string,
        method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'GET',
        options: FetchOptions | undefined,
    ) => {
        const logger: ApiLogger = options?.hideConsoleError ? MutedLogger : console;

        const headers = new Headers({
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            ...options?.headers,
        });

        if (config.environmentLockAccess) {
            headers.set('environmentLockAccess', config.environmentLockAccess);
        }

        if (ApiService.token) {
            headers.set('Authorization', ApiService.token);
        }

        let errorToThrow;
        let errorLoggerPayload: any = {};

        try {
            const response = await fetch(endpoint, {
                credentials: 'include',
                ...options,
                method,
                headers,
            });

            if (response.status === 204 /* No content */) {
                return null;
            }

            if (response.ok) {
                switch (headers.get('Accept')) {
                    case 'text/event-stream':
                        return response;
                    case 'text/markdown':
                        return response.text();
                    default:
                        return response.json();
                }
            }

            const body = await response.text();

            try {
                if (body.startsWith('{')) {
                    const { error, errors, message } = JSON.parse(body);
                    const errorMessage = message || error || (errors ? errors?.[0]?.message : body);

                    errorToThrow = new Error(errorMessage);
                    errorLoggerPayload = {
                        name: 'API',
                        message: errorMessage,
                    };
                } else {
                    errorToThrow = new Error(body);
                    errorLoggerPayload = {
                        name: 'API',
                        message: body,
                    };
                }
            } catch (error) {
                errorToThrow = error;
                errorLoggerPayload = {
                    name: 'Parsing',
                    error,
                    body,
                };
            }
        } catch (error) {
            errorToThrow = error;
            errorLoggerPayload = {
                name: 'Fetching',
                error,
            };

            let reason = "Unknown fetching error";
            const isOffline = !navigator.onLine;

            if (isOffline) {
                reason = "Browser reported as offline";
            } else if (error instanceof TypeError) {
                // TypeError is common for network errors, CORS, potentially mixed content, etc.
                // It's hard to distinguish further without more context (like checking console for specific CORS messages)
                reason = "TypeError (Likely network error, CORS issue, or invalid URL/protocol)";
            } else if (error instanceof DOMException) {
                // DOMExceptions can be more specific
                reason = `DOMException: ${error.name}`; // e.g., AbortError

                if (error.message) {
                    reason += ` (${error.message})`;
                }
            } else if (error instanceof Error) {
                // Catch other generic errors
                reason = `Generic Error: ${error.name} (${error.message})`;
            } else {
                // Handle cases where the caught object might not be a standard Error
                reason = "Caught a non-standard error object";
            }

            console.error({
                reason,
                error,
            });
        }

        logger.error(`Failed to fetch (${errorLoggerPayload.name}): ${endpoint}`, JSON.stringify(errorLoggerPayload));

        if (options?.throwError) {
            throw errorToThrow;
        }
    };

    static get = async <T = any>(endpoint: string, options: FetchOptions = {}) =>
        ApiService.fetch(endpoint, 'GET', options) as Promise<T>;

    static post = async <T = any>(endpoint: string, json: any = {}, options: FetchOptionsNoBody = {}) =>
        ApiService.fetch(endpoint, 'POST', { body: JSON.stringify(json), ...options }) as Promise<T>;

    static patch = async <T = any>(endpoint: string, json: any = {}, options: FetchOptionsNoBody = {}) =>
        ApiService.fetch(endpoint, 'PATCH', { body: JSON.stringify(json), ...options }) as Promise<T>;

    static delete = async (endpoint: string, options: FetchOptions = {}) =>
        ApiService.fetch(endpoint, 'DELETE', options);
}

export { ApiService };
