import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { ActionIcon, ActionIconProps, Button, ButtonProps, Tooltip } from '@mantine/core';

import { IoBookmark, IoBookmarkOutline, IoClose } from 'react-icons/io5';

import { SavedItemType } from '@repo/dcide-component-models';

import { ModalService } from 'services/ModalService';

import { useAction } from 'hooks/use-action';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentSavedItems } from 'hooks/use-current-saved-items';

import { SavedItemsService } from 'services/SavedItemsService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import cx from './SaveButton.module.css';

const COPY = {
    default: {
        default: {
            label: 'Bookmark',
            message: null,
            icon: <IoBookmarkOutline />,
        },
        save: {
            label: 'Bookmarked',
            message: 'Saved to My Bookmarks',
            icon: <IoBookmark />,
        },
        remove: {
            label: 'Remove',
            message: 'Removed from My Bookmarks',
            icon: <IoClose />,
        },
    },
    [SavedItemType.PROFILE]: {
        default: {
            label: 'Bookmark',
            message: null,
            icon: <IoBookmarkOutline />,
        },
        save: {
            label: 'Bookmarked',
            message: 'Profile saved to My Bookmarks',
            icon: <IoBookmark />,
        },
        remove: {
            label: 'Remove',
            message: 'Profile removed from My Bookmarks',
            icon: <IoClose />,
        },
    },
};

const SaveButton = ({
    id,
    name,
    type,
    iconOnly,
    buttonProps,
    actionIconProps,
}: {
    id: string;
    name: string;
    type: string;
    iconOnly?: boolean;
    buttonProps?: ButtonProps;
    actionIconProps?: ActionIconProps;
}) => {
    const copy = type in COPY ? COPY[type as keyof typeof COPY] : COPY.default;

    const router = useRouter();
    const user = useCurrentUser();

    const {
        lookup: { [id]: savedItemId },
    } = useCurrentSavedItems();

    const [justSaved, setJustSaved] = useState(false);

    const state = savedItemId ? 'save' : 'default';

    const saveItem = useCallback(async () => {
        if (!user) return;

        const payload = {
            user: user.id,
            item: {
                value: id,
                relationTo: type,
            },
        };

        if (savedItemId) {
            await SavedItemsService.delete(savedItemId, payload);

            LocalNotificationService.showSuccess({
                message: `${copy.remove.message}`,
            });

            setJustSaved(false);
        } else {
            await SavedItemsService.create(payload);

            LocalNotificationService.showSuccess({
                message: `${copy.save.message}`,
                actions: [
                    {
                        label: 'View all',
                        onClick: SavedItemsService.navigate.overview,
                    },
                ],
            });

            setJustSaved(true);
        }
    }, [id, type, user, state, savedItemId, copy.save.message, copy.remove.message]);

    const openLogin = () => {
        let redirect = router.pathname;

        const searchParams = new URLSearchParams(window.location.search);

        searchParams.set('action', 'save-item');
        searchParams.set('item', id);

        redirect += '?' + searchParams.toString();

        if (window.location.hash) {
            redirect += window.location.hash;
        }

        ModalService.openLoginModal({
            title: `Add "${name}" to My Bookmarks`,
            message: '',
            redirect,
        });
    };

    const [toggleSave, saving] = useAction(async () => {
        if (!user) {
            openLogin();
            return;
        }

        saveItem();
    });

    useEffect(() => {
        const { action, item, ...updatedQuery } = router.query;

        if (action !== 'save-item' || item !== id) {
            return;
        }

        saveItem();

        const newPath = {
            pathname: router.pathname,
            query: updatedQuery,
        };

        router.replace(newPath, undefined, { shallow: true }).then();
    }, [router.query, id, router, saveItem]);

    const getColor = () => {
        if (savedItemId) {
            return 'green';
        }

        return 'gray';
    };

    const props = {
        'color': getColor(),
        'variant': justSaved ? 'filled' : 'outline',
        'onClick': toggleSave,
        'disabled': saving,
        'data-saved': !!savedItemId,
        'style': {
            transition: 'background-color 100ms ease',
        },
    };

    const { label, icon } = copy[state];

    if (iconOnly) {
        return (
            <Tooltip label={label} withArrow>
                <ActionIcon className={cx.root} size="sm" {...props} {...actionIconProps}>
                    {icon}
                </ActionIcon>
            </Tooltip>
        );
    }

    return (
        <Button className={cx.root} size="compact-xs" leftSection={icon} {...props} {...buttonProps}>
            {label}
        </Button>
    );
};

export { SaveButton };
