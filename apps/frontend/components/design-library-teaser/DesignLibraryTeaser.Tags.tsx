import { Group, Pill } from '@mantine/core';

import { Project } from '@repo/dcide-component-models';

import cx from './DesignLibraryTeaser.module.css';

const DesignLibraryTeaserTags = ({
    project,
    handleTagClick,
}: {
    project: Project;
    handleTagClick?: (tag: string) => void;
}) => {
    if (!project.tags.length) {
        return null;
    }

    return (
        <Group gap={4} className={cx.tags}>
            {project.tags.map((tag) => (
                <Pill
                    key={tag}
                    className={cx.tag}
                    size="xs"
                    onClick={handleTagClick ? () => handleTagClick(tag) : undefined}
                    data-clickable={Boolean(handleTagClick)}
                >
                    {tag}
                </Pill>
            ))}
        </Group>
    );
};

export { DesignLibraryTeaserTags };
