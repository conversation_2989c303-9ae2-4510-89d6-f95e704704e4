import { CollectionConfig, FieldAccess } from 'payload';

import {
    CreatedBy,
    Name,
    Subscribers,
    TeamFromUser,
    User,
    FilesField,
    getSubscribeEndpoint,
    getUnsubscribeEndpoint,
    subscribeRequestUser,
} from '../fields';

import { addComponent, removeComponent, updateComponent, addEvent, addMessage } from './orders/endpoints';
import { getByDesign } from './orders/endpoints/getByDesign';

import { addCreateEvent } from './orders/hooks';
import { subscribeSupportUsers } from './orders/hooks/subscribeSupportUsers';
import { triggerCreateNotifications } from './orders/hooks/triggerCreateNotifications';
import { StatusField, TotalPriceField } from './orders/fields';
import { enhanceOrderWithPermissions, userHasReviewOrderPermission } from '../access/orders';
import { OrderAvailabilities, OrderStatuses } from '@repo/dcide-component-models';
import { getRequestUser } from '../helpers';

const reviewFieldAccess: Record<string, FieldAccess> = {
    create: () => false,
    update: async ({ req, id }) => {
        const user = await getRequestUser(req);

        if (!user) {
            return false;
        }

        return await userHasReviewOrderPermission(user, id);
    },
};

const Orders: CollectionConfig = {
    slug: 'orders',
    access: {
        create: ({ req }) => !!req.user,
        read: async ({ req, id }) => {
            const user = await getRequestUser(req);

            if (!user) return false;

            const userHasReviewPermission = await userHasReviewOrderPermission(user, id);
            if (userHasReviewPermission) return true;

            const conditions: any[] = [{ createdBy: { equals: user.id } }];

            if (user.team) {
                conditions.push({ team: { equals: user.team } });
            }

            return { or: conditions };
        },
        update: async ({ req, id }) => {
            const user = await getRequestUser(req);

            if (!user) return false;

            const userHasReviewPermission = await userHasReviewOrderPermission(user, id);
            if (userHasReviewPermission) return true;

            return { createdBy: { equals: user.id } };
        },
        delete: () => false,
    },
    fields: [
        Name,
        {
            name: 'design',
            type: 'relationship',
            relationTo: 'projectDesigns',
        },
        {
            name: 'components',
            type: 'array',
            fields: [
                {
                    name: 'component',
                    type: 'relationship',
                    relationTo: 'components',
                    required: true,
                },
                {
                    name: 'quantity',
                    type: 'number',
                    min: 0,
                    required: true,
                },
                {
                    name: 'price',
                    type: 'number',
                    access: reviewFieldAccess,
                },
                {
                    name: 'leadTime',
                    type: 'number',
                    access: reviewFieldAccess,
                },
                {
                    name: 'availability',
                    type: 'select',
                    options: [...OrderAvailabilities],
                    access: reviewFieldAccess,
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    access: reviewFieldAccess,
                },
            ],
            defaultValue: [],
        },
        CreatedBy,
        TeamFromUser,
        StatusField,
        TotalPriceField,
        {
            name: 'events',
            type: 'array',
            fields: [
                {
                    name: 'type',
                    type: 'select',
                    options: OrderStatuses,
                    required: true,
                },
                {
                    name: 'date',
                    type: 'date',
                    required: true,
                },
                User,
            ],
            access: {
                create: () => false,
                update: () => false,
            },
            defaultValue: [],
        },
        {
            type: 'array',
            name: 'messages',
            fields: [
                {
                    type: 'text',
                    name: 'message',
                    required: true,
                },
                {
                    type: 'date',
                    name: 'date',
                    required: true,
                },
                {
                    type: 'relationship',
                    name: 'user',
                    relationTo: 'users',
                    required: true,
                },
            ],
            defaultValue: [],
        },
        FilesField(),
        Subscribers,
        {
            name: 'reviewers',
            type: 'group',
            fields: [
                {
                    name: 'manufacturers',
                    type: 'relationship',
                    relationTo: 'manufacturers',
                    hasMany: true,
                    defaultValue: () => [],
                    maxDepth: 0,
                },
            ],
        },
    ],
    hooks: {
        afterRead: [
            async ({ req, doc }) => {
                const user = await getRequestUser(req);

                return enhanceOrderWithPermissions({ user, order: doc });
            },
        ],
        beforeChange: [addCreateEvent, subscribeRequestUser, subscribeSupportUsers],
        afterChange: [triggerCreateNotifications],
    },
    endpoints: [
        addComponent,
        removeComponent,
        updateComponent,
        addEvent,
        addMessage,
        getSubscribeEndpoint('orders'),
        getUnsubscribeEndpoint('orders'),
        getByDesign,
    ],
};

export default Orders;
