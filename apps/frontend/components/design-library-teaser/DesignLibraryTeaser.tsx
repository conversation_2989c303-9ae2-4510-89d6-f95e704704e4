import { FC } from 'react';

import Link from 'next/link';

import { Anchor, Box, Card, Spoiler, Stack } from '@mantine/core';
import { IKImage } from 'components/ik-image/IKImage';

import { Project, PublishedStatus } from '@repo/dcide-component-models';

import { PublishedBadge } from 'components/published-badge/PublishedBadge';
import { DesignLibraryTeaserCreatedBy } from './DesignLibraryTeaserCreatedBy';
import { DesignLibraryTeaserComponents } from 'components/design-library-teaser/DesignLibraryTeaserComponents';
import { DesignLibraryTeaserTags } from 'components/design-library-teaser/DesignLibraryTeaser.Tags';
import { DesignLibraryTeaserIndicators } from 'components/design-library-teaser/DesignLibraryTeaser.Indicators';

import { useElementSize } from '@mantine/hooks';

import { ProjectHelpers } from 'helpers/ProjectHelpers';

import cx from './DesignLibraryTeaser.module.css';

const DesignLibraryTeaser: FC<{
    project: Project;
    handleTagClick?: (tag: string) => void;
    showPublishedStatus?: boolean;
    isLight?: boolean;
}> = ({ project, handleTagClick, showPublishedStatus, isLight }) => {
    const { ref, width } = useElementSize();
    const projectUrl = ProjectHelpers.urls.editor(project.id);

    const thumbnailSize = {
        width: 400,
        height: 200,
    };

    if (width >= 500) {
        thumbnailSize.width = 400;
        thumbnailSize.height = 300;
    }

    return (
        <Box className={cx.container} data-light-teaser={isLight} ref={ref}>
            <Card withBorder className={cx.card}>
                <Box className={cx.inner}>
                    {project.thumbnail && (
                        <Link href={projectUrl} className={cx.image}>
                            <IKImage
                                fileOrId={project.thumbnail}
                                width={thumbnailSize.width}
                                height={thumbnailSize.height}
                                alt={project.name}
                            />
                        </Link>
                    )}

                    <Stack className={cx.main}>
                        <Box>
                            <Anchor component={Link} href={projectUrl} className={cx.title}>
                                {project.name}
                                <br />
                            </Anchor>

                            <Box className={cx.author} c="dimmed">
                                <DesignLibraryTeaserCreatedBy project={project} />
                            </Box>
                        </Box>

                        {project.description && (
                            <Box className={cx.description}>
                                <Spoiler
                                    className={cx.spoiler}
                                    maxHeight={65}
                                    showLabel="read more"
                                    hideLabel="read less"
                                >
                                    {project.description}
                                </Spoiler>
                            </Box>
                        )}

                        <DesignLibraryTeaserTags project={project} handleTagClick={handleTagClick} />

                        {showPublishedStatus && project.visibility !== 'marketplace' && (
                            <PublishedBadge variant="light" status={PublishedStatus.DRAFT} />
                        )}
                    </Stack>

                    <Stack className={cx.secondary}>
                        <DesignLibraryTeaserIndicators project={project} />
                        <DesignLibraryTeaserComponents components={project.template.components as any} />
                    </Stack>
                </Box>
            </Card>
        </Box>
    );
};

export { DesignLibraryTeaser };
