import { useFormContext, useWatch } from 'react-hook-form';

import { crush, isNumber, uid } from 'radash';

import { Component, getComponentPayloadValidator } from '@repo/dcide-component-models';

import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';
import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { componentTypeHasElectrical } from 'helpers/componentTypeHasElectrical';

const useDatasheetPorts = () => {
    const [ports, type] = useWatch({
        name: ['electrical.ports', 'type'],
    }) as [Component['electrical']['ports'], Component['type']];

    const { setValue } = useFormContext();
    const { addOverride } = useComponentBulkFields();
    const mode = useDatasheetMode();

    const addPort = (copyPortIndex?: number) => {
        if (!componentTypeHasElectrical(type)) return;

        const componentValidator = getComponentPayloadValidator(type);

        const newComponent = componentValidator.parse({ name: '' });

        if (!newComponent.electrical) {
            return;
        }

        const newPort = isNumber(copyPortIndex) ? ports[copyPortIndex] : newComponent.electrical.ports[0];
        newPort.id = uid(8);

        // save the new port when bulk editing
        if (mode === DatasheetMode.BULK) {
            const newPortValues = crush(newPort);

            Object.keys(newPortValues).forEach((key) => {
                addOverride(`electrical.ports.${ports.length}.${key}`);
            });
        }

        setValue('electrical.ports', [...ports, newPort]);
    };

    const deletePort = (index: number) => {
        const newPorts = ports.filter((_, i) => i !== index);

        setValue('electrical.ports', newPorts);
    };

    return { addPort, deletePort };
};

export { useDatasheetPorts };
