import React, { FC, useState } from 'react';

import { Anchor, Box, Skeleton, Stack, UnstyledButton } from '@mantine/core';

import { BsRobot } from 'react-icons/bs';

import { AIMarkdown } from 'components/ai-markdown/AIMarkdown';

import { useCompanyProfile } from 'hooks/use-company-profile';
import { useUser } from 'hooks/use-user';

import { DateService } from 'services/DateService';
import { AICommentError, DiagramComment } from '@repo/dcide-component-models';
import { DiagramCommentDisplay } from '../diagram-comments';
import { DiagramCommentFilesPreview } from 'components/diagram/components/diagram-comments/DiagramComment.FilesPreview';
import { Avatar } from 'components/avatar/Avatar';

import cx from '../diagram-comments/DiagramComment.module.css';
import { useLinkProduct } from 'components/diagram/hooks/use-link-product';
import { SidebarService } from 'components/diagram/services/SidebarService';

const DiagramCommentThread: FC<{
    comment: DiagramComment;
    teaser?: boolean;
}> = ({ comment, teaser = false }) => {
    const lastMessage = comment.thread.findLast((item) => {
        return item.blockType === 'message' || item.blockType === 'ai';
    });

    const showCompanyMentionNotice =
        lastMessage && lastMessage.blockType === 'message' && lastMessage.content.includes('"id":"manufacturer:');

    return (
        <Stack gap="md" data-comment-teaser={teaser}>
            {comment.thread.slice(0, teaser ? 1 : 9999).map((item) => (
                <React.Fragment key={item.id}>
                    {item.blockType === 'message' && <Message comment={comment} message={item} />}
                    {item.blockType === 'ai' && <AI comment={comment} ai={item} key={item.id} />}
                </React.Fragment>
            ))}
            {showCompanyMentionNotice && <CompanyMentionNotice />}
        </Stack>
    );
};

const Message: FC<{
    comment: DiagramComment;
    message: Extract<DiagramComment['thread'][number], { blockType: 'message' }>;
}> = ({ comment, message }) => {
    const { company } = useCompanyProfile(message.manufacturer);

    const { user } = useUser(message.user);

    return (
        <Box className={cx.thread} data-indicator={!comment.seen}>
            <Box className={cx.threadAvatar}>
                <Avatar company={company} user={message.user} size={20} />
            </Box>
            <Box className={cx.threadContent}>
                <Box className={cx.threadItemHeader}>
                    {company?.name || user?.name}
                    <small>{DateService.formatDistanceToNow(message.date)}</small>
                </Box>
                <Box className={cx.threadItemBody}>
                    <DiagramCommentDisplay content={message.content} />
                </Box>
                {message.files.length > 0 && <DiagramCommentFilesPreview files={message.files} />}
            </Box>
        </Box>
    );
};

const AI: FC<{
    comment: DiagramComment;
    ai: Extract<DiagramComment['thread'][number], { blockType: 'ai' }>;
}> = ({ comment, ai }) => {
    const [cutoff, setCutoff] = useState(25);

    const { linkProduct } = useLinkProduct();

    // @ts-ignore temp.
    const aiJsonResponse = ai?.json;
    const content = aiJsonResponse?.data || ai.content || '';
    const words = content.split(' ');
    const teaserOrContent = words.slice(0, cutoff).join(' ');
    const showReadMore = words.length > cutoff;

    return (
        <Box className={cx.thread}>
            <Box className={cx.threadAvatar}>
                <BsRobot size={16} style={{ margin: '0 2px' }} />
            </Box>
            <Box className={cx.threadItemBody}>
                <Box className={cx.threadItemHeader}>
                    <Box ml={0} className={cx.threadItemHeaderLabel}>
                        AI assistant
                    </Box>
                </Box>
                {ai.error ? (
                    <Box>
                        {ai.error === AICommentError.NO_LINKED_COMPONENT_INSTANCE && (
                            <Box>
                                Your comment is not linked to a component. Move it close to a component to link it.
                            </Box>
                        )}
                        {ai.error === AICommentError.NO_LINKED_COMPONENT && (
                            <Box>
                                There is no product linked to this component.{' '}
                                <Anchor
                                    fz="sm"
                                    underline="always"
                                    onClick={() => linkProduct(comment.componentInstances[0])}
                                >
                                    Link a product
                                </Anchor>
                            </Box>
                        )}
                        {ai.error === AICommentError.NO_LINKED_COMPONENT_FILES && (
                            <Box>
                                Unfortunately, there is no documentation the AI assistant can consult for this
                                component.
                            </Box>
                        )}
                        {ai.error === AICommentError.AI_ERROR && (
                            <Box>Sorry, something went wrong with the AI assistant. Please try again later.</Box>
                        )}
                    </Box>
                ) : (
                    <Box>
                        {ai.generating ? (
                            <Box>
                                <Skeleton height={8} width="80%" radius="xl" />
                                <Skeleton height={8} width="60%" mt={6} radius="xl" />
                            </Box>
                        ) : (
                            <AIMarkdown id={aiJsonResponse?.id} references={aiJsonResponse?.references}>
                                {teaserOrContent}
                            </AIMarkdown>
                        )}
                        {showReadMore && ' '}
                        {showReadMore && (
                            <UnstyledButton
                                className={cx.threadItemBodyReadMore}
                                onClick={() => {
                                    setCutoff(Infinity);
                                }}
                            >
                                Read more...
                            </UnstyledButton>
                        )}
                    </Box>
                )}
            </Box>
        </Box>
    );
};

const CompanyMentionNotice = () => {
    return (
        <Box className={cx.threadNotice}>
            <p>
                <strong>Attention</strong>
            </p>
            <p>
                The manufacturer you requested help from, will be able to <strong>view the entire diagram</strong> and
                respond to the comments they are mentioned in.
            </p>
            <p>
                <UnstyledButton onClick={() => SidebarService.openShareSidebar()}>
                    Change access settings?
                </UnstyledButton>
            </p>
        </Box>
    );
};

export { DiagramCommentThread };
