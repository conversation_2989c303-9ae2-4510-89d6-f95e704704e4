import { after } from 'next/server';
import { MongoClient } from 'mongodb';

import { config as envConfig } from 'config';

export const POST = async (request: Request) => {
    const connectionString = envConfig.tracking.mongodb;

    if (!connectionString) {
        return Response.json({
            success: false,
        });
    }

    after(
        (async () => {
            const client = new MongoClient(connectionString);
            const { events } = await request.json();

            try {
                const database = client.db('tracking');
                const collection = database.collection('events');

                await collection.insertMany(events);
            } catch (error) {
                console.error('Tracking Error:', error);
            } finally {
                await client.close();
            }
        })(),
    );

    return Response.json({
        success: false,
    });
};
