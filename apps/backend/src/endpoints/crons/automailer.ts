import { EmailService } from '@/services';

import { designUserReminderXDays } from '@/collections/emails/reminders/design-user-reminder-x-days';
import { generateCronEndpoint } from '@/endpoints/crons/generate-cron-endpoint';

const automailer = generateCronEndpoint({
    subPath: '/automailer',
    method: 'get',
    handler: async (request) => {
        const { payload } = request;

        const emails = [
            designUserReminderXDays(1), // today for testing purpose
            designUserReminderXDays(2 * 7), // 2 weeks
            designUserReminderXDays(4 * 7), // 4 weeks
            designUserReminderXDays(8 * 7), // 8 weeks
            designUserReminderXDays(26 * 7), // 1/2 year
        ];

        let sendCount = 0;

        for (const mail of emails) {
            for (const candidate of await mail.candidates()) {
                const {
                    docs: [harassed],
                } = await payload.find({
                    collection: 'emails',
                    where: {
                        type: { equals: mail.key },
                        user: { equals: candidate.id },
                    },
                    limit: 1,
                });

                if (!harassed) {
                    sendCount += 1;

                    const send = EmailService.send({
                        to: candidate.email,
                        replyTo: '<EMAIL>',
                        data: await mail.email(candidate),
                    });

                    const create = payload.create({
                        collection: 'emails',
                        data: {
                            type: mail.key,
                            user: candidate.id,
                        },
                    });

                    await Promise.all([send, create]);
                }
            }
        }

        return Response.json({
            success: true,
            sendCount,
        });
    },
});

export { automailer };
