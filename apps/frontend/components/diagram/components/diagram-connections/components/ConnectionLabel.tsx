import { useState, SVGProps, useEffect } from 'react';

import { Edge, Point } from '@repo/dcide-component-models';
import { CELL_HEIGHT } from 'components/diagram/diagram-dimensions';
import { useElementSize, useMergedRef } from '@mantine/hooks';

import cx from './ConnectionLabel.module.css';

type ConnectionLabelType = {
    label?: string | null;
    underline?: boolean;
    isIcon?: boolean;
};

type Props = {
    points: Point[];
    edge: Edge;
    labels?: ConnectionLabelType[];
    position?: 'start' | 'center' | 'end';
    color?: string;
    hide?: boolean;
    fontWeight?: number;
    bgColor?: string;
    bgOverlay?: {
        color: string;
        opacity: number;
    };
};

const FONTSIZE = 10;
const OFFSET = 15;
const SMALL_OFFSET = 5;
const TEXT_OFFSET = 12;

export const ConnectionLabel = ({
    points,
    edge,
    labels,
    position = 'center',
    color = '#212529',
    hide = false,
    fontWeight = 500,
    bgColor,
    bgOverlay,
}: Props) => {
    const [, setRefresh] = useState(0);

    const { width: labelWidth, height: labelHeight, ref: sizeRef } = useElementSize();
    const [positionRef, setPositionRef] = useState<SVGTextElement | null>(null);
    const mergedRef = useMergedRef(sizeRef, setPositionRef);

    const shownLabels = labels?.filter(({ label }) => Boolean(label)) ?? [];

    const pointsAsString = JSON.stringify(points);

    useEffect(() => {
        setRefresh((val) => val + 1);
    }, [pointsAsString]);

    if (hide || !shownLabels?.length) return null;

    const first = points[0];
    const last = points[points.length - 1];

    let start = points[1];
    let end = points[2];

    const xPointsAligned = points.filter((point) => points[0].x === point.x);
    const yPointsAligned = points.filter((point) => points[0].y === point.y);
    const isStraightLine = xPointsAligned.length === points.length || yPointsAligned.length === points.length;

    if (isStraightLine) {
        start = first;
        end = last;
    }

    const positionAlongPath = getPositionAlongPath([start, end], 0.5);

    const x = position === 'start' ? first.x : position === 'center' ? positionAlongPath.x : last.x;

    const y = position === 'start' ? first.y : position === 'center' ? positionAlongPath.y : last.y;

    const isVertical = start.y === end.y && position === 'center';

    const textOptions = {
        x: isVertical ? x + FONTSIZE / 2 - 1 : x + OFFSET,
        y: isVertical ? y - OFFSET : y + FONTSIZE / 2 - 1,
        textAnchor: isVertical ? 'start' : 'start',
        fill: bgColor ? 'white' : color,
        fontSize: FONTSIZE,
        fontWeight,
        transform: ``,
    };

    if (isVertical) {
        textOptions.transform = `rotate(-90 ${textOptions.x} ${textOptions.y})`;
    }

    const isMultiLine = isStraightLine && getLength(points) > CELL_HEIGHT;
    const textYOffset = isMultiLine ? (shownLabels.length - 1) * TEXT_OFFSET : 0;

    if (position === 'start' && !isStraightLine) {
        if (edge === 'left') {
            textOptions.x = x - SMALL_OFFSET;
            textOptions.y = y + OFFSET;
            textOptions.textAnchor = 'end';
        } else if (edge === 'right') {
            textOptions.x = x + SMALL_OFFSET;
            textOptions.y = y + OFFSET;
            textOptions.textAnchor = 'start';
        } else if (edge === 'top') {
            textOptions.x = x - SMALL_OFFSET;
            textOptions.y = y - SMALL_OFFSET - textYOffset;
            textOptions.textAnchor = 'end';
        } else if (edge === 'bottom') {
            textOptions.x = x - SMALL_OFFSET;
            textOptions.y = y + OFFSET;
            textOptions.textAnchor = 'end';
        }
    } else if ((position === 'start' && isStraightLine) || position === 'end') {
        if (edge === 'left') {
            textOptions.x = x - SMALL_OFFSET;
            textOptions.y = y + OFFSET;
            textOptions.textAnchor = 'end';
        } else if (edge === 'right') {
            textOptions.x = x + SMALL_OFFSET;
            textOptions.y = y - SMALL_OFFSET;
            textOptions.textAnchor = 'start';
        } else if (edge === 'top') {
            textOptions.x = x - SMALL_OFFSET;
            textOptions.y = y - SMALL_OFFSET - textYOffset;
            textOptions.textAnchor = 'end';
        } else if (edge === 'bottom') {
            textOptions.x = x - SMALL_OFFSET;
            textOptions.y = y + OFFSET;
            textOptions.textAnchor = 'end';
        }
    }

    let background = null;

    if (positionRef) {
        const bbox = positionRef.getBBox();

        background = {
            x: bbox.x - 2,
            y: bbox.y - 1,
            width: labelWidth + 4,
            height: labelHeight + 2,
            fill: bgColor,
            transform: textOptions.transform,
            rx: 2,
        };
    }

    return (
        <>
            {bgColor && background && <rect {...background} />}
            {bgColor && background && bgOverlay && (
                <rect {...background} fill={bgOverlay.color} opacity={bgOverlay.opacity} />
            )}
            <text {...textOptions} ref={mergedRef} className={cx.text} data-with-stroke={!bgColor}>
                {shownLabels.map(({ label, isIcon, underline }, index) => {
                    const props: SVGProps<SVGTSpanElement> = {};
                    const showComma = !isMultiLine && !isIcon && index !== shownLabels.length - 1;

                    if (isMultiLine) {
                        props.x = textOptions.x;
                        props.dy = index === 0 ? 0 : TEXT_OFFSET;
                    }

                    if (underline) {
                        props.textDecoration = 'underline';
                    }

                    return (
                        <tspan {...props} key={index}>
                            {label}
                            {showComma ? ', ' : ' '}
                        </tspan>
                    );
                })}
            </text>
        </>
    );
};

const getLineLength = (p1: Point, p2: Point) => Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));

const getLength = (points: Point[]) => {
    let length = 0;

    for (let i = 1; i < points.length; i++) {
        const p1 = points[i - 1];
        const p2 = points[i];
        length += getLineLength(p1, p2);
    }

    return length;
};

const getPositionAlongPath = (points: Point[], percent: number) => {
    const length = getLength(points);

    const targetLength = length * percent;
    let currentLength = 0;
    for (let i = 1; i < points.length; i++) {
        const p1 = points[i - 1];
        const p2 = points[i];

        const segmentLength = getLineLength(p1, p2);

        if (currentLength + segmentLength >= targetLength) {
            const percent = (targetLength - currentLength) / segmentLength;

            return {
                x: p1.x + (p2.x - p1.x) * percent,
                y: p1.y + (p2.y - p1.y) * percent,
            };
        }

        currentLength += segmentLength;
    }

    const p = [...points].pop()!;

    return {
        x: p.x,
        y: p.y,
    };
};
