import { closeModal, openContextModal } from '@mantine/modals';
import { CompanyProfile, CompanyService } from '@repo/dcide-component-models';
import { RouterHelpers } from 'helpers/RouterHelpers';

const CompanyProfileHelpers = {
    swr: {
        profile: (profileId: string) => `/api/profiles/${profileId}`,
    },

    urls: {
        create: (options?: { id?: string; redirect?: string; step?: number; partner?: string }) => {
            const { id, redirect, step, partner } = options || {};

            const query = [];

            if (redirect) {
                query.push(`redirect=${redirect}`);
            }

            if (step) {
                query.push(`step=${step}`);
            }

            if (partner) {
                query.push(`partner=${partner}`);
            }

            let baseUrl = '/profiles/signup';

            if (id) {
                baseUrl += `/${id}`;
            }

            return `${baseUrl}${query.length ? `?${query.join('&')}` : ''}`;
        },
        view: (slug: string, hash?: string) => `/profiles/${slug}${hash ? `#${hash}` : ''}`,
        requestAccess: (slug: string) => `/profiles/${slug}/request-access`,
        overview: () => RouterHelpers.urls.searchTab('profiles'),
        manage: () => '/profiles/manage',
    },

    offersInAppSupport: (company?: CompanyProfile | null) => {
        return company?.services.includes(CompanyService.IN_APP_SUPPORT) || false;
    },

    modals: {
        choosePartner: 'companyProfileChoosePartner',
        createPartner: 'companyProfileCreatePartner',
    },

    openChoosePartnerModal: (company: CompanyProfile) => {
        openContextModal({
            modal: CompanyProfileHelpers.modals.choosePartner,
            modalId: CompanyProfileHelpers.modals.choosePartner,
            size: 'lg',
            withCloseButton: false,
            innerProps: {
                company,
            },
        });
    },

    openCreatePartnerModal: (company: CompanyProfile) => {
        openContextModal({
            modal: CompanyProfileHelpers.modals.createPartner,
            modalId: CompanyProfileHelpers.modals.createPartner,
            size: 'lg',
            withCloseButton: false,
            innerProps: {
                company,
            },
        });
    },

    closeModal: (key: string) => {
        closeModal(key);
    },
};

export { CompanyProfileHelpers };
