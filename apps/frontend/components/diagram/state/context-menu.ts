import { proxy } from 'valtio';

import { DiagramComponentInstance } from '@repo/dcide-component-models';

export enum DiagramContextMenuType {
    GENERIC = 'generic',
    COMPONENT_INSTANCE = 'component-instance',
}

const createInitialState = () => ({
    position: null,
    type: null,
    componentInstance: undefined,
});

const state = proxy<{
    position: { x: number; y: number } | null;
    type: DiagramContextMenuType | null;
    componentInstance?: DiagramComponentInstance;
}>(createInitialState());

const reset = () => {
    const initialState = createInitialState();

    state.position = initialState.position;
    state.type = initialState.type;
    state.componentInstance = initialState.componentInstance;
};

export { state, reset };
