import React, { useState } from 'react';

import type { FC } from 'react';

import type { ResizableProps, ResizeHandle } from 'react-resizable';
import type { DiagramComponentInstance } from '@repo/dcide-component-models';

import { Resizable } from 'react-resizable';

import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { ViewportService } from 'components/diagram/services/ViewportService';
import { SelectionService } from 'components/diagram/services/SelectionService';

import { useDiagramScale } from 'components/diagram/hooks';

import { CELL_HEIGHT, CELL_WIDTH, ICON_HEIGHT, ICON_WIDTH, CELL_PADDING } from 'components/diagram';

import 'react-resizable/css/styles.css';

import cx from './ComponentInstanceResize.module.css';

const calculateX = (width: number) => 1 + Math.ceil((width - CELL_WIDTH) / CELL_WIDTH);
const calculateY = (height: number) => 1 + Math.ceil((height - CELL_HEIGHT) / CELL_HEIGHT);

const calculateWidth = (colSpan: number, dw = 0) => ICON_WIDTH + (colSpan + dw - 1) * CELL_WIDTH;
const calculateHeight = (rowSpan: number, dh = 0) => ICON_HEIGHT + (rowSpan + dh - 1) * CELL_HEIGHT;

const ComponentInstanceResize: FC<{
    componentInstance: DiagramComponentInstance;
    enabled: boolean;
    children: (
        width: number,
        height: number,
        top: number,
        left: number,
        shadow: {
            width: number;
            height: number;
            left: number;
            top: number;
        },
        resizing: boolean,
    ) => React.ReactNode;
}> = ({ componentInstance, enabled, children }) => {
    const scale = useDiagramScale();

    const [delta, setDelta] = useState({ x: 0, y: 0, width: 0, height: 0 });
    const [resizing, setResizing] = useState(false);

    const original = {
        width: calculateWidth(componentInstance.colSpan),
        height: calculateHeight(componentInstance.rowSpan),
    };

    const handleStart: ResizableProps['onResizeStart'] = () => {
        document.body.setAttribute('data-resizing', 'true');

        setResizing(true);
    };

    const handleResize: ResizableProps['onResize'] = (_event, data) => {
        const { size, handle } = data;

        const delta = {
            x: 0,
            y: 0,
            width: calculateX(size.width - original.width),
            height: calculateY(size.height - original.height),
        };

        // Scaling on the north side yields negative delta y
        if (handle.includes('n')) {
            delta.y = -delta.height;
        }

        // Scaling on the west side yields negative delta x
        if (handle.includes('w')) {
            delta.x = -delta.width;
        }

        if (ComponentInstanceService.validToResize(componentInstance, delta)) {
            setDelta(delta);
        }
    };

    const handleStop: ResizableProps['onResizeStop'] = () => {
        document.body.removeAttribute('data-resizing');

        setResizing(false);

        ComponentInstanceService.resize(componentInstance, delta);
        DiagramSyncService.save();

        setDelta({ x: 0, y: 0, width: 0, height: 0 });

        SelectionService.clear();
    };

    const resizeHandles: ResizeHandle[] = ['ne', 'se', 'sw', 'nw'];

    if (componentInstance.colSpan % 2 == 0) {
        resizeHandles.push('n');
        resizeHandles.push('s');
    }

    if (componentInstance.rowSpan % 2 == 0) {
        resizeHandles.push('e');
        resizeHandles.push('w');
    }

    const width = calculateWidth(componentInstance.colSpan, delta.width);
    const height = calculateHeight(componentInstance.rowSpan, delta.height);

    const left = CELL_WIDTH - CELL_PADDING - ICON_WIDTH + delta.x * CELL_WIDTH;
    const top = CELL_PADDING + delta.y * CELL_HEIGHT;

    const shadow = {
        width: 0,
        height: 0,
        left,
        top,
    };

    return (
        <Resizable
            className={cx.root}
            width={width}
            height={height}
            resizeHandles={enabled ? resizeHandles : []}
            handleSize={[CELL_PADDING, CELL_PADDING]}
            onResizeStart={handleStart}
            onResize={handleResize}
            onResizeStop={handleStop}
            data-component-instance={componentInstance.id}
            transformScale={scale}
            draggableOpts={{
                grid: [CELL_WIDTH * ViewportService.transform.scale, CELL_HEIGHT * ViewportService.transform.scale],
            }}
        >
            {children(width, height, top, left, shadow, resizing) as ResizableProps['children']}
        </Resizable>
    );
};

export { ComponentInstanceResize };
