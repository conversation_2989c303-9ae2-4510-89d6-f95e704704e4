import { AppShell } from '@mantine/core';
import { useSessionStorage } from '@mantine/hooks';

import { ShowtimeHelpers } from 'helpers/ShowtimeHelpers';

import { usePlanPage } from 'components/plan/hooks/use-plan-page';

import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { Body } from './components/Body';
import { ReplusNav } from 'components/replus-nav/ReplusNav';

import classNames from './AppShell.module.css';

export const PlanPage = () => {
    const { isInitial } = usePlanPage();

    const [showShowtimeNav] = useSessionStorage({
        key: ShowtimeHelpers.localStorageKey.showShowtimeNav,
        defaultValue: false,
    });

    return (
        <AppShell
            layout="alt"
            header={{ height: 72 }}
            footer={{ height: isInitial ? 72 : 'var(--mantine-spacing-xl)' }}
            navbar={{ width: 300, breakpoint: 'sm', collapsed: { desktop: true, mobile: true } }}
            aside={{ width: 300, breakpoint: 'md', collapsed: { desktop: true, mobile: true } }}
            classNames={classNames}
            withBorder={false}
        >
            <AppShell.Navbar />
            <AppShell.Header>
                <Header />
            </AppShell.Header>
            <AppShell.Main>
                <Body withBottomNav={showShowtimeNav} />
                {showShowtimeNav && <ReplusNav withSpace={false} />}
            </AppShell.Main>
            {isInitial && (
                <AppShell.Footer>
                    <Footer />
                </AppShell.Footer>
            )}
        </AppShell>
    );
};
