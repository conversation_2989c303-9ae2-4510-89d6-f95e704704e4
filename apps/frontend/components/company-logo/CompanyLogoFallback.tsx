import { Box } from '@mantine/core';

import cx from './CompanyLogoFallback.module.css';

const CompanyLogoFallback = ({ name, color, size = 'lg' }: { name: string; color?: string; size?: 'sm' | 'lg' }) => {
    return (
        <Box
            component="span"
            className={cx.root}
            style={{
                '--brand-color': color ?? 'var(--mantine-color-brand-5)',
            }}
            data-size={size}
        >
            <span className={cx.letter}>{name.charAt(0).toUpperCase()}</span>
        </Box>
    );
};

export { CompanyLogoFallback };
