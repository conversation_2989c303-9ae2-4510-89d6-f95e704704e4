import React, { FC } from 'react';

import { PermissionDigramComments } from '@repo/dcide-component-models';

import { UnstyledButton } from '@mantine/core';

import { IoHelpCircleOutline } from 'react-icons/io5';

import { SupportService } from 'components/diagram/services/SupportService';

import { useCurrentProject } from 'hooks/use-current-project';
import { usePermission } from 'hooks/use-permission';

import cx from './DiagramBadges.module.css';

const DiagramBadgeEngineeringSupport: FC = () => {
    const canViewComments = usePermission(PermissionDigramComments.VIEW);
    const project = useCurrentProject();

    return canViewComments && project ? (
        <UnstyledButton
            className={cx.badge}
            onClick={() => {
                SupportService.openRequestSupportModal();
            }}
        >
            <IoHelpCircleOutline />
            Request Support
        </UnstyledButton>
    ) : null;
};

export { DiagramBadgeEngineeringSupport };
