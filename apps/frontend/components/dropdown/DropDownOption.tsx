import { FC } from 'react';

import Link from 'next/link';
import { Button, ButtonProps } from '@mantine/core';

import optionClasses from './DropDownOption.module.css';

type PlaceholderProps = {
    iconSize?: number;
    indent?: number;
    children?: React.ReactNode;
};

type DropDownOption = {
    onClick?: () => void;
    href?: string;
    highlight?: boolean;
};

export const DropDownOption: FC<ButtonProps & DropDownOption & PlaceholderProps> = ({
    children,
    highlight,
    leftSection,
    rightSection,
    iconSize,
    indent,
    href,
    onClick,
    ...props
}) => {
    const buttonProps: ButtonProps = {
        classNames: optionClasses,
        leftSection: leftSection ? (
            <Placeholder iconSize={iconSize} indent={indent}>
                {leftSection}
            </Placeholder>
        ) : undefined,
        rightSection: rightSection ?? <Placeholder iconSize={iconSize} />,
        fw: highlight ? 500 : 'normal',
        ...props,
    };

    if (href) {
        return (
            <Button component={Link} href={href} {...buttonProps}>
                {children}
            </Button>
        );
    }

    return (
        <Button {...buttonProps} onClick={onClick}>
            {/* {indent && <Placeholder iconSize={iconSize}  />} */}
            {children}
        </Button>
    );
};

const Placeholder: FC<PlaceholderProps> = ({ iconSize = 12, indent = 0, children }) => (
    <div
        style={{
            minWidth: iconSize * (1 + indent),
            minHeight: iconSize,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
        }}
    >
        {children}
    </div>
);
