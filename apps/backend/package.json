{"name": "@repo/backend", "version": "1.0.0", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "dev:email": "email dev --port 3004 --dir src/emails/preview", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint --fix", "payload": "cross-env NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "patch": "cd ../../ && patch-package --patch-dir=./patches", "test:unit": "jest", "postinstall": "npm run patch"}, "dependencies": {"@aws-sdk/client-s3": "^3.709.0", "@payloadcms/db-mongodb": "^3.28.1", "@payloadcms/email-nodemailer": "^3.28.1", "@payloadcms/next": "^3.28.1", "@payloadcms/plugin-sentry": "^3.28.1", "@payloadcms/richtext-lexical": "^3.28.1", "@payloadcms/richtext-slate": "^3.28.1", "@payloadcms/storage-s3": "^3.28.1", "@payloadcms/ui": "^3.28.1", "@react-email/components": "^0.0.31", "@repo/dcide-component-models": "*", "@repo/dcide-sync-engine": "*", "@tiptap/extension-bold": "^2.3.0", "@tiptap/extension-bullet-list": "^2.3.0", "@tiptap/extension-document": "^2.3.0", "@tiptap/extension-italic": "^2.3.0", "@tiptap/extension-link": "^2.3.0", "@tiptap/extension-list-item": "^2.3.0", "@tiptap/extension-mention": "^2.3.0", "@tiptap/extension-ordered-list": "^2.3.0", "@tiptap/extension-paragraph": "^2.3.0", "@tiptap/extension-text": "^2.3.0", "@tiptap/react": "^2.3.0", "@tiptap/starter-kit": "^2.3.0", "@vercel/speed-insights": "^1.1.0", "ably": "^2.5.0", "cross-env": "^7.0.3", "fflate": "^0.8.2", "file-type": "^20.4.1", "graphql": "^16.8.1", "json-2-csv": "^5.5.6", "jsonwebtoken": "^9.0.2", "nodemailer-sendgrid": "^1.0.3", "openai": "^4.94.0", "patch-package": "^8.0.0", "payload": "^3.28.1", "react-email": "^3.0.4", "react-icons": "^5.4.0", "sharp": "^0.33.5", "unique-names-generator": "^4.7.1", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/babel__template": "^7.4.4", "@types/graceful-fs": "^4.1.9", "@types/istanbul-lib-coverage": "^2.0.6", "@types/istanbul-lib-report": "^3.0.3", "@types/istanbul-reports": "^3.0.4", "@types/json5": "^0.0.30", "@types/node": "^22.5.4", "@types/nodemailer-sendgrid": "^1.0.3", "eslint": "^8", "eslint-config-next": "15.0.0"}}