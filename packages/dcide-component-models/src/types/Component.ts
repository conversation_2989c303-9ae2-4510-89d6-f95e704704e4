import { ComponentType } from '../validators/fields/ComponentType';
import { FileUpload } from './File';

import { Battery } from '../validators/components/Battery';
import { Bus } from '../validators/components/Bus';
import { Capacitor } from '../validators/components/Capacitor';
import { Charger } from '../validators/components/Charger';
import { CircuitBreaker } from '../validators/components/CircuitBreaker';
import { CombinerBox } from '../validators/components/CombinerBox';
import { Contactor } from '../validators/components/Contactor';
import { Converter } from '../validators/components/Converter';
import { Custom } from '../validators/components/Custom';
import { Disconnect } from '../validators/components/Disconnect';
import { Fuse } from '../validators/components/Fuse';
import { Generator } from '../validators/components/Generator';
import { Grounding } from '../validators/components/Grounding';
import { HVAC } from '../validators/components/HVAC';
import { Hydro } from '../validators/components/Hydro';
import { Load } from '../validators/components/Load';
import { Light } from '../validators/components/Light';
import { Meter } from '../validators/components/Meter';
import { Motor } from '../validators/components/Motor';
import { Other } from '../validators/components/Other';
import { Panel } from '../validators/components/Panel';
import { PowerDistributionUnit } from '../validators/components/PowerDistributionUnit';
import { RapidShutdownDevice } from '../validators/components/RapidShutdownDevice';
import { Solar } from '../validators/components/Solar';
import { Display, FileVisibility, Solution } from '../validators';
import { TransferSwitch } from '../validators/components/TransferSwitch';
import { Transformer } from '../validators/components/Transformer';
import { Utility } from '../validators/components/Utility';
import { Wind } from '../validators/components/Wind';
import { Cable } from '../validators/components/Cable';

export type Component =
    | Battery
    | Bus
    | Capacitor
    | Charger
    | CircuitBreaker
    | CombinerBox
    | Contactor
    | Converter
    | Custom
    | Disconnect
    | Fuse
    | Generator
    | Grounding
    | HVAC
    | Hydro
    | Load
    | Light
    | Meter
    | Motor
    | Other
    | Panel
    | PowerDistributionUnit
    | RapidShutdownDevice
    | Solar
    | Solution
    | TransferSwitch
    | Transformer
    | Utility
    | Wind
    | Cable;

export {
    Display as ComponentFieldDisplay,
    DisplayLabels as ComponentFieldDisplayLabels,
} from '../validators/component-sections/metadata';

export type { FieldMeta as ComponentFieldMeta } from '../validators/component-sections/metadata';

export type ComponentTeaser = Component;
export type ComponentCount = { [key in ComponentType]: number };
export type ComponentFileUpload = FileUpload & {
    type: string;
    visibility?: FileVisibility;
};

export const isAlternativeDisplay = (
    display?: Display,
): display is Display.NOT_APPLICABLE | Display.HIDDEN | Display.UNKNOWN => {
    switch (display) {
        case Display.NOT_APPLICABLE:
        case Display.HIDDEN:
        case Display.UNKNOWN:
            return true;

        default:
            return false;
    }
};
