import React, { FC } from 'react';
import { Box, InputProps, Menu, Tooltip } from '@mantine/core';
import { TbSparkles } from 'react-icons/tb';

import { useTypewriter } from 'react-simple-typewriter';

import { SearchBox } from 'components/search-box';

import { useIsMobile } from 'hooks/use-is-mobile';
import { useComponentSearchSubmit } from '../hooks/use-component-search-submit';
import { useComponentSearchQuery } from '../hooks/use-component-search-query';
import { useFocusWithin } from '@mantine/hooks';

import cx from './SearchBar.module.css';
import cxTooltip from 'components/sidebar-nav/components/SidebarNav.Tooltip.module.css';
import { SearchBarAutocomplete } from './SearchBarAutocomplete';
import { SearchSuggestions } from './SearchSuggestions';

const SearchBar: FC<{
    size?: InputProps['size'];
    searchSuggestions?: boolean;
    placeholder?: string;
}> = ({ size = 'lg', searchSuggestions = true, placeholder: overridePlaceholder }) => {
    const { ref, focused } = useFocusWithin();

    const isMobile = useIsMobile();

    const defaultPlaceholder = 'Search for companies, their products, services, or reference designs…';
    const [cyclingPlaceholder] = useTypewriter({
        words: ['Search products', 'Search companies', 'Search designs', 'Search case studies'],
        loop: true,
    });
    const placeholder =
        overridePlaceholder || (isMobile ? (focused ? 'Search' : cyclingPlaceholder) : defaultPlaceholder);

    const { query, setQuery, resetAll } = useComponentSearchQuery();
    const { submitting, submit } = useComponentSearchSubmit();

    return (
        <>
            <Menu opened={focused} position="bottom-start" offset={0} width="target" trapFocus={false} shadow="xl">
                <Menu.Target>
                    <Box ref={ref}>
                        <SearchBox
                            value={query ?? ''}
                            placeholder={placeholder}
                            leftSection={
                                <Tooltip classNames={cxTooltip} label="Try our AI powered search" position="top-start">
                                    <TbSparkles className={cx.indicator} size={20} strokeWidth={1.5} />
                                </Tooltip>
                            }
                            size={size}
                            autoComplete="off"
                            onChange={(event) => {
                                setQuery(event.target.value);
                            }}
                            isLoading={submitting}
                            submit={() => {
                                (document.activeElement as HTMLElement)?.blur();
                                submit(query);
                            }}
                            handleReset={resetAll}
                            autocompleteOpen={focused}
                        />
                    </Box>
                </Menu.Target>
                <SearchBarAutocomplete
                    query={query}
                    setQuery={setQuery}
                    handleSearch={(_query) => {
                        setQuery(_query);
                        submit(_query);
                    }}
                />
            </Menu>
            {searchSuggestions && (
                <SearchSuggestions
                    handleSearch={(suggestion) => {
                        setQuery(suggestion);
                        submit(suggestion);
                    }}
                />
            )}
        </>
    );
};

export { SearchBar };
