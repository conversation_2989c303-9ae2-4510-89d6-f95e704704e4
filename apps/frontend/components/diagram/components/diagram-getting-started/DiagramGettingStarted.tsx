import React, { useEffect } from 'react';
import Link from 'next/link';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { ActionIcon, Box, Card, Divider, Group, HoverCard, Stack, Text, Title, UnstyledButton } from '@mantine/core';
import { IoChevronForward, IoCloseSharp, IoHelpCircleOutline, IoSettingsOutline } from 'react-icons/io5';
import { BsPencil } from 'react-icons/bs';

import { gettingStartedState } from 'components/diagram/state/getting-started';

import { useCurrentProject } from 'hooks/use-current-project';
import { useDiagramEmpty } from 'components/diagram/hooks/use-diagram-empty';

import { TourService } from 'components/diagram/services/TourService';

import { ProjectInfoForm } from 'components/project/components/ProjectInfoForm';

import { DiagramGettingStartedProducts } from './DiagramGettingStarted.Products';
import { DiagramGettingStartedComponents } from './DiagramGettingStarted.Components';

import cx from './DiagramGettingStarted.module.css';

const DiagramGettingStarted = () => {
    const { open } = useSnapshot(gettingStartedState);

    const project = useCurrentProject();
    const isEmptyDiagram = useDiagramEmpty();

    const showProducts = !!project?.template?.components?.length;

    const close = () => {
        gettingStartedState.open = false;
    };

    useEffect(() => {
        if (!isEmptyDiagram) {
            gettingStartedState.open = false;
        }

        return () => {
            gettingStartedState.open = false;
        };
    }, [isEmptyDiagram]);

    const handleTourStart = () => {
        TourService.start();
        gettingStartedState.open = false;
    };

    if (!open) return null;

    return (
        <Stack className={cx.root} visibleFrom="md">
            <Card withBorder className={cx.card}>
                <Card.Section className={cx.header}>
                    <Title order={2}>Getting started</Title>

                    <HoverCard withArrow shadow="xl" position="right" offset={16} width={240}>
                        <HoverCard.Target>
                            <ActionIcon size="sm" variant="subtle" className={cx.closeButton} onClick={close}>
                                <IoCloseSharp />
                            </ActionIcon>
                        </HoverCard.Target>

                        <HoverCard.Dropdown>
                            <Group wrap="nowrap" gap="xs">
                                <ActionIcon variant="light">
                                    <IoSettingsOutline />
                                </ActionIcon>
                                <Text fz="sm">You can access the project settings in the sidebar</Text>
                            </Group>
                        </HoverCard.Dropdown>
                    </HoverCard>
                </Card.Section>

                <ProjectInfoForm autosave />

                {showProducts ? (
                    <DiagramGettingStartedProducts onClose={close} />
                ) : (
                    <DiagramGettingStartedComponents onClose={close} />
                )}
            </Card>

            <Divider label="or" />

            <UnstyledButton className={cx.browseButton} component={Link} href="/designs" target="_blank">
                <Box className={cx.thumbnails}>
                    <Box className={cx.thumbnail} />
                    <Box className={cx.thumbnail} />
                    <Box className={cx.thumbnail}>
                        <BsPencil />
                    </Box>
                </Box>
                Browse reference designs
                <IoChevronForward className={cx.icon} />
            </UnstyledButton>

            <UnstyledButton className={cx.browseButton} onClick={handleTourStart}>
                <Box className={cx.roundIcon} mr={8}>
                    <IoHelpCircleOutline size={14} />
                </Box>
                Follow the getting started tour
                <IoChevronForward className={cx.icon} />
            </UnstyledButton>
        </Stack>
    );
};

export { DiagramGettingStarted };
