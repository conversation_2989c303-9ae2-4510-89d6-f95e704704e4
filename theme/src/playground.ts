/**
 * Defines a point with x and y coordinates.
 */
interface Point {
    x: number;
    y: number;
}

/**
 * Creates an SVG polygon string from an array of points.
 *
 * @param points An array of Point objects (x, y coordinates).
 * Example: [{ x: 10, y: 10 }, { x: 50, y: 10 }, { x: 30, y: 50 }]
 * @param fill The fill color of the polygon (CSS color name or hex code).
 * Defaults to "blue".
 * @param stroke The stroke (border) color of the polygon.
 * Defaults to "black".
 * @param strokeWidth The width of the stroke.
 * Defaults to 1.
 * @returns An SVG <polygon> element string.
 */
function createSvgPolygon(
    points: Point[],
): string {
    if (points.length === 0) {
        return ""; // Return empty string if no points are provided
    }

    // Format points into the "x,y x,y ..." string required by SVG
    const pointsStr = points.map(p => `${p.x},${p.y}`).join(" ");

    return `<polygon points="${pointsStr}" />`;
}

// --- Example Usage ---

// Function to create a full SVG wrapper for display
function createFullSvg(content: string, width: number = 200, height: number = 200): string {
    return `<svg width="${width}" height="${height}" viewport="0 0 ${width} ${height}" version="1.1" xmlns="http://www.w3.org/2000/svg">${content}</svg>`;
}

(() => {
    const images = document.querySelectorAll('[data-funky-image]');

    Array.from(images).forEach((image) => {
        const generate = (offsetXX: number, offsetYY: number) => {
            const { width, height } = image.getBoundingClientRect();
            const arm = {
                length: width / 3,
                thickness: width / 3,
            };

            const offsetX = offsetXX - arm.length - (arm.thickness / 2);
            const offsetY = offsetYY - arm.length - (arm.thickness / 2);

            const polygon = createSvgPolygon([
                { x: offsetX + arm.length, y: offsetY },
                { x: offsetX + arm.length + arm.thickness, y: offsetY },
                { x: offsetX + arm.length + arm.thickness, y: offsetY + arm.length },
                { x: offsetX + arm.length + arm.thickness + arm.length, y: offsetY + arm.length },
                { x: offsetX + arm.length + arm.thickness + arm.length, y: offsetY + arm.length + arm.thickness },
                { x: offsetX + arm.length + arm.thickness, y: offsetY + arm.length + arm.thickness },
                { x: offsetX + arm.length + arm.thickness, y: offsetY + arm.length + arm.thickness + arm.length },
                { x: offsetX + arm.length, y: offsetY + arm.length + arm.thickness + arm.length },
                { x: offsetX + arm.length, y: offsetY + arm.length + arm.thickness },
                { x: offsetX, y: offsetY + arm.length + arm.thickness },
                { x: offsetX, y: offsetY + arm.length },
                { x: offsetX + arm.length, y: offsetY + arm.length },
            ]);

            const svg = createFullSvg(polygon, width, height);

            image.style.maskImage = `url('data:image/svg+xml;utf8,${svg}')`;
            image.style.maskSize = 'auto 100%';
            image.style.maskRepeat = 'no-repeat';
        };

        image.addEventListener('mousemove', (event) => {
            const { offsetX, offsetY } = event;
            generate(offsetX, offsetY);
        });
    });
})();
