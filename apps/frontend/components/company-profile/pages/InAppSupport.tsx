import { FC } from 'react';

import { Card } from '@mantine/core';

import { CompanyProfile } from '@repo/dcide-component-models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';

import { InAppSupport as InAppSupportComponent } from 'components/company-profile/components/InAppSupport';

import cx from './styles.module.css';

const InAppSupport: FC<{ company: CompanyProfile }> = ({ company }) => {
    return (
        <Card classNames={cx}>
            <InAppSupportComponent company={company} />
        </Card>
    );
};

const WrappedInAppSupport = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <InAppSupport company={company} />;
};

export { WrappedInAppSupport as InAppSupport };
