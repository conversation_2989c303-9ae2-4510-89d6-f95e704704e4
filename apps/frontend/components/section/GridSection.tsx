import React, { FC } from 'react';

import { SimpleGrid, SimpleGridProps } from '@mantine/core';

import { Breakpoint } from 'hooks/use-breakpoint';
import { useResponsiveNbCols } from 'components/section/useResponsiveNbCols';

import cx from './GridSection.module.css';

export type GridSectionProps = {
    nbCols?: number;
    cols?: Record<Breakpoint, number>;
};

const GridSection: FC<
    SimpleGridProps &
        GridSectionProps & {
            children: React.ReactNode;
        }
> = ({ children, nbCols = 4, cols, ...props }) => {
    const responsiveNbCols = useResponsiveNbCols(nbCols, cols);

    if (!responsiveNbCols) return null;

    return (
        <SimpleGrid maw="100%" spacing="sm" pos="relative" cols={responsiveNbCols} classNames={cx} {...props}>
            {children}
        </SimpleGrid>
    );
};

export { GridSection };
