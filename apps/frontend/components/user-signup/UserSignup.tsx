import React, { FC, useEffect, useState } from 'react';

import { Alert } from '@mantine/core';

import { UserReferrer, UserType } from '@repo/dcide-component-models';

import { LoginLayout } from 'components/login-layout';
import { SimpleButton, SimpleButtonGroup } from 'elements/buttons';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { NavigationService } from 'services/NavigationService';
import { UserService } from 'services/UserService';
import { RouterService } from 'services/RouterService';

import { FeedbackButton } from 'components/feedback-button';
import { Step1 } from 'components/user-signup/components/Step1';
import { Step2 } from 'components/user-signup/components/Step2';
import { Step3 } from 'components/user-signup/components/Step3';

import { ReplusLogo } from 'components/logo/ReplusLogo';

import { range } from 'radash';

const NB_STEPS = 3;

const UserSignup: FC<{ magicToken: string }> = ({ magicToken }) => {
    const [step, setStep] = useState(1);
    const [error, setError] = useState<boolean>(false);

    const user = useCurrentUser();
    const currentTeam = useCurrentTeam();

    const teamIsCreatedByUser = currentTeam?.createdBy === user?.id;
    const isReplusVisitor = user?.referrer === UserReferrer.REPLUS;
    const isManufacturer = user?.type === UserType.MANUFACTURER;

    let nbSteps = teamIsCreatedByUser ? NB_STEPS : 1;

    const redirect = user?.source ?? '';
    const isClaimProfile = redirect.includes('/profiles/') && redirect.includes('/request-access');

    if (isReplusVisitor && !isManufacturer) {
        nbSteps = 1;
    }

    if (isClaimProfile) {
        nbSteps = 2;
    }

    const handleNext = async (redirect?: string) => {
        setStep((s) => (s === nbSteps ? s : s + 1));

        if (step === nbSteps) {
            try {
                await UserService.finishSignup();
                await RouterService.replace(`/token/${magicToken}${redirect ? `?redirect=${redirect}` : ''}`);

                setError(false);
                NavigationService.enable();
            } catch (error) {
                setError(true);
            }
        }
    };

    useEffect(() => {
        NavigationService.disable();
    }, []);

    return (
        <LoginLayout title={isReplusVisitor ? <ReplusLogo width={100} /> : <></>}>
            {nbSteps > 1 && (
                <SimpleButtonGroup mb="lg" style={{ justifyContent: 'center' }}>
                    {[...range(nbSteps - 1)].map((i) => (
                        <SimpleButton key={i} disabled opacity={step === i + 1 ? 1 : 0.5}>
                            Step {i + 1}
                        </SimpleButton>
                    ))}
                </SimpleButtonGroup>
            )}

            {step === 1 && <Step1 handleNext={handleNext} setError={setError} />}

            {step === 2 && <Step2 handleNext={handleNext} setError={setError} />}

            {step === 3 && <Step3 handleNext={handleNext} />}

            {error && (
                <Alert p="xs" mt="xs" color="red" title="Something went wrong, please try again">
                    Does this error persist? Try refreshing the page or contact us via <FeedbackButton isInline />
                </Alert>
            )}
        </LoginLayout>
    );
};

export { UserSignup };
