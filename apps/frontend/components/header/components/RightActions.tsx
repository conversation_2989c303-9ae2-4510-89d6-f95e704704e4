import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';

import { ButtonProps } from '@mantine/core';
import { IoChatbubbleOutline, IoGlobe, IoInfiniteSharp, IoLogoYoutube, IoStarSharp } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { HeaderButton, HeaderLink } from 'components/header/components/HeaderButton';

const RIGHT_LINKS: (ButtonProps & {
    component: any;
    href: string;
    target?: string;
    children: React.ReactNode;
})[] = [
    {
        component: 'a',
        color: 'red.4',
        href: 'https://www.youtube.com/@dcmicrogriddesignandengine5189/videos',
        target: '_blank',
        leftSection: <IoLogoYoutube size={16} />,
        children: 'Learn',
    },
    {
        component: Link,
        color: 'primary.3',
        href: '/map',
        leftSection: <IoGlobe size={16} />,
        children: 'Map',
    },
    {
        component: 'a',
        color: 'white',
        href: 'https://about.dcide.app/changelog',
        target: '_blank',
        leftSection: <IoInfiniteSharp size={16} />,
        children: 'Changelog',
    },
];

const RightActions = () => {
    const user = useCurrentUser();
    const team = useCurrentTeam();

    const { asPath } = useRouter();

    const showSubscriptionButton = user && team;

    return (
        <>
            {RIGHT_LINKS.map((props, index) => (
                <HeaderLink key={index} {...props} />
            ))}

            {user && (
                <HeaderButton
                    // @ts-ignore
                    onClick={() => window.Usersnap.logEvent('custom_button_click')}
                    leftSection={<IoChatbubbleOutline size={16} />}
                >
                    Feedback
                </HeaderButton>
            )}

            {showSubscriptionButton && (
                <HeaderLink
                    color="primary.3"
                    component={Link}
                    href={`/upgrade?redirect=${asPath}`}
                    leftSection={<IoStarSharp size={16} />}
                >
                    Upgrade
                </HeaderLink>
            )}
        </>
    );
};

export { RightActions };
