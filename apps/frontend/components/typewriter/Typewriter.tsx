import { FC } from 'react';
import { Box } from '@mantine/core';

import cx from './Typewriter.module.css';

const Typewriter: FC<{
    enabled?: boolean;
    children: string;
}> = ({ enabled = true, children = '' }) => (
    <Box
        className={cx.typewriter}
        style={{
            '--typewriter-steps': children.length,
        }}
        data-disabled={!enabled}
    >
        {children}
    </Box>
);

export { Typewriter };
