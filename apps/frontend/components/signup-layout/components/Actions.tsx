import { Box, Flex, Group } from '@mantine/core';

import { SignupLayoutProps } from '../SignupLayout';

import cx from './styles.module.css';

type Props = Pick<SignupLayoutProps, 'leftActions' | 'rightActions'>;

const Actions = ({ leftActions, rightActions }: Props) => {
    return (
        <Box className={cx.actions} data-actions>
            <Flex justify="space-between" gap={8}>
                {leftActions ? <Group gap={8}>{leftActions}</Group> : <span />}

                {rightActions && <Group gap={8}>{rightActions}</Group>}
            </Flex>
        </Box>
    );
};

export { Actions };
