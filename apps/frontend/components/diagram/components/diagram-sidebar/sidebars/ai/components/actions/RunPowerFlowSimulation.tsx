import React, { FC, useEffect } from 'react';

import { Group, Loader, Paper } from '@mantine/core';
import { JobTeaser } from 'components/diagram/components/diagram-jobs/JobTeaser';

import { useSimulationsData } from 'components/diagram/hooks/use-simulations';

import { useCreateSimulation } from 'components/diagram/components/diagram-sidebar/sidebars/simulations/hooks/useCreateSimulation';

import { AIConversationService } from 'components/diagram/services/AIConversationService';
import { useCheckFeatureLimit } from 'hooks/use-check-feature-limit';
import { FeatureLimit } from '@repo/dcide-component-models';
import { FeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';

import { ActionProps } from './ActionProps';

const RunPowerFlowSimulation: FC<ActionProps> = ({ message, isLastMessage }) => {
    const { all } = useSimulationsData();
    const createdSimulation = all.find((simulation) => {
        // @ts-ignore
        return simulation.id === message.action?.result?.createdSimulation;
    });

    const { handleCreateSimulation, creating } = useCreateSimulation();

    useEffect(() => {
        if (!message.action.executed && !message.action.failed && isLastMessage) {
            const execute = async () => {
                const { doc: simulation } = await handleCreateSimulation();

                await AIConversationService.execute(message, {
                    createdSimulation: simulation.id,
                });
            };

            execute()
                .then()
                .catch((error) => {
                    AIConversationService.markExecutionAsFailed(message).then();

                    console.error('Error Executing Diagram AI Simulation', error);
                });
        }
    }, [message.action.executed, isLastMessage]);

    return (
        <Paper mt="xs" p="sm" radius="md" shadow="md">
            {message.action.failed ? (
                <Group gap="xs">
                    <Loader size={16} color="blue" />
                    Something went wrong
                </Group>
            ) : creating ? (
                <Group gap="xs">
                    <Loader size={16} color="blue" />
                    Running your simulation
                </Group>
            ) : createdSimulation ? (
                <JobTeaser type="simulation" job={createdSimulation} active={false} isLatest />
            ) : (
                <Group gap="xs">
                    <Loader size={16} color="blue" />
                    Loading your simulation
                </Group>
            )}
        </Paper>
    );
};

const FeatureLimitWrapper: FC<ActionProps> = (props) => {
    const { status: limitCheck, isLoading } = useCheckFeatureLimit(FeatureLimit.SIMULATIONS);

    if (limitCheck?.pass) {
        return <RunPowerFlowSimulation {...props} />;
    }

    return (
        <Paper mt="xs" p="sm" radius="md" shadow="md">
            {isLoading ? (
                <Group gap="xs">
                    <Loader size={16} color="blue" />
                    Checking simulation access
                </Group>
            ) : (
                <FeatureLimitTracker feature={FeatureLimit.SIMULATIONS} p={0} shadow="none" />
            )}
        </Paper>
    );
};

export { FeatureLimitWrapper as RunPowerFlowSimulation };
