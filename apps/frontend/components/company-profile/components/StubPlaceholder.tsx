import React, { FC } from 'react';

import { Badge, Card, Stack, Text, Title, Tooltip } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import {
    CompanyProfile,
    CompanyProfileStubFields,
    StubPlaceholder as StubPlaceholderType,
    StubWithLocalFiles,
} from '@repo/dcide-component-models';

import { CompanyProfileService } from 'services/CompanyProfileService';

import { StubModal } from 'components/company-profile/components/StubModal';

import { stubConfig } from 'components/company-profile/sections/Stubs';

import cx from './StubPlaceholder.module.css';

const StubPlaceholder: FC<
    StubPlaceholderType & {
        company: CompanyProfile;
        field: CompanyProfileStubFields;
    }
> = ({ name, tag, description, placeholder, company, field }) => {
    const copy = stubConfig[field];

    const [opened, handlers] = useDisclosure();

    const onCreate = async (data: StubWithLocalFiles) => {
        await CompanyProfileService.addStub({
            companyId: company.id,
            field,
            data: {
                ...data,
                tags: tag ? [tag] : [],
            },
        });

        handlers.close();
    };

    return (
        <>
            <Tooltip label={`Click to add more info about ${name}`}>
                <Card withBorder className={cx.root} radius="sm" onClick={handlers.open}>
                    <Badge size="xs" variant="light" className={cx.badge}>
                        Preview
                    </Badge>

                    <Badge size="xs" variant="filled" color="brand" className={cx.addBadge}>
                        Click to add
                    </Badge>

                    <Stack gap="sm">
                        <Title order={3} fz="lg" fw={700} className={cx.title}>
                            {name}
                        </Title>

                        <Text fz="sm" className={cx.description}>
                            {description}
                        </Text>
                    </Stack>
                </Card>
            </Tooltip>

            {opened && (
                <StubModal
                    company={company}
                    opened={opened}
                    onClose={handlers.close}
                    title={copy.addTitle}
                    submitLabel={copy.addSubmitLabel}
                    onSumbit={onCreate}
                    defaultStub={{ name: tag ?? name }}
                    placeholder={placeholder}
                />
            )}
        </>
    );
};

export { StubPlaceholder };
