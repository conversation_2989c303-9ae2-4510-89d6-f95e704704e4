import React from 'react';

import { Group, Stack } from '@mantine/core';

import { DiagramBadgeCollaborators } from 'components/diagram/components/diagram-badges/DiagramBadge.Collaborators';
import { DiagramBadgeEngineeringSupport } from 'components/diagram/components/diagram-badges/DiagramBadge.EngineeringSupport';
import { DiagramBadgeViewOnly } from 'components/diagram/components/diagram-badges/DiagramBadge.ViewOnly';
import { DiagramBadgePublish } from 'components/diagram/components/diagram-badges/DiagramBadge.Publish';
import { DiagramBadgesDesignBy } from 'components/diagram/components/diagram-badges/DiagramBadge.DesignBy';

import { useAblyConnectable } from 'hooks/use-ably-connectable';

const DiagramBadges = () => {
    const connectable = useAblyConnectable();

    const content = (
        <>
            <DiagramBadgeEngineeringSupport />
            {connectable && <DiagramBadgeCollaborators />}
            <DiagramBadgeViewOnly />
            <DiagramBadgesDesignBy />
            <DiagramBadgePublish />
        </>
    );

    return (
        <>
            <Stack gap={4} align="start" hiddenFrom="md">
                {content}
            </Stack>
            <Group gap={4} justify="end" visibleFrom="md">
                {content}
            </Group>
        </>
    );
};

export { DiagramBadges };
