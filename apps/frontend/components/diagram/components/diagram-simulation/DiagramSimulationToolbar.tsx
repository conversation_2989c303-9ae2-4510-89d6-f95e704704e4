import React from 'react';

import { Box, Text } from '@mantine/core';
import { IoCameraOutline, IoCloseCircleOutline, IoCloudDownloadOutline } from 'react-icons/io5';

import { SimulationType } from '@repo/dcide-component-models';

import { useSimulations } from 'components/diagram/hooks/use-simulations';
import { useSimulation } from 'components/diagram/hooks/use-simulation';
import { DisplayMode } from 'components/diagram/state/simulation';

import { DiagramService } from 'components/diagram/services';
import { SimulationService } from 'components/diagram/services/SimulationService';
import { SimulationCsvService } from 'components/diagram/services/SimulationCsvService';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';

import { DiagramSimulationSlider } from './DiagramSimulationSlider';

import cx from './DiagramSimulationToolbar.module.css';

const DiagramSimulationToolbar = () => {
    const { active: activeSimulationId, displayMode } = useSimulations();
    const activeSimulation = useSimulation(activeSimulationId);

    if (!activeSimulation) return null;

    const isBatchSimulation = activeSimulation.type === SimulationType.MULTIPLE;

    return (
        <Box className={cx.root}>
            <DiagramToolbar.Fixed className={isBatchSimulation ? cx.fullWidthToolbar : ''}>
                <DiagramToolbar.Text>{activeSimulation?.name}</DiagramToolbar.Text>
                <DiagramToolbar.Divider />

                {isBatchSimulation ? (
                    <>
                        <DiagramSimulationSlider />
                        <DiagramToolbar.Divider />
                        <DiagramToolbar.IconButton
                            tooltip="Save snapshot image"
                            onClick={() => DiagramService.downloadAsPng({ filename: activeSimulation.name })}
                        >
                            <IoCameraOutline />
                        </DiagramToolbar.IconButton>
                        <DiagramToolbar.IconButton
                            tooltip="Download CSV"
                            onClick={() => SimulationCsvService.downloadSimulationCsv(activeSimulation)}
                        >
                            <IoCloudDownloadOutline />
                        </DiagramToolbar.IconButton>
                    </>
                ) : (
                    <DiagramToolbar.TextButton
                        onClick={() => DiagramService.downloadAsPng({ filename: activeSimulation.name })}
                    >
                        <IoCloudDownloadOutline />
                        <Text inherit ml={6}>
                            Save as image
                        </Text>
                    </DiagramToolbar.TextButton>
                )}

                <DiagramToolbar.Divider />

                <DiagramToolbar.TextButton onClick={() => SimulationService.toggleDisplayMode()}>
                    <Text inherit>{getNextDisplayModeLabel(displayMode)}</Text>
                </DiagramToolbar.TextButton>

                <DiagramToolbar.Divider />

                <DiagramToolbar.TextButton onClick={() => SimulationService.deactivateSimulation()}>
                    <IoCloseCircleOutline size={14} />
                    <Text inherit ml={6}>
                        Exit
                    </Text>
                </DiagramToolbar.TextButton>
            </DiagramToolbar.Fixed>
        </Box>
    );
};

const getNextDisplayModeLabel = (displayMode: DisplayMode) => {
    switch (displayMode) {
        case DisplayMode.CURRENT:
            return 'Show Power';
        case DisplayMode.POWER:
            return 'Show Current';
    }
};

export { DiagramSimulationToolbar };
