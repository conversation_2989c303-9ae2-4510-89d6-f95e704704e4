import type { Payload } from 'payload';
import { Change, Transaction } from '@repo/dcide-sync-engine';
import { User } from '@/payload-types';

export const applyDiagramTransaction = async (
    payload: Payload,
    diagramId: string,
    { id, ...transaction }: Transaction,
    saveTransaction = true,
    user?: User,
) => {
    if (saveTransaction) {
        await payload.db.collections.transactions.create({
            ...transaction,
            _id: id,
            diagram: diagramId,
            createdBy: user?.id,
        });
    }

    for (const change of transaction.changes) {
        await payload.db.collections.projectDesignDiagrams.updateOne(
            ...getTransactionMongoUpdateObject(diagramId, change),
        );

        await payload.db.collections.projectDesignDiagrams.updateOne(
            { _id: diagramId },
            {
                $pull: {
                    groups: null,
                    textareas: null,
                    images: null,
                    ...getPullNullsFromChange(change),
                },
            },
        );
    }
};

type TransactionMongoUpdateObject = Parameters<Payload['db']['collections']['projectDesignDiagrams']['updateOne']>;

const isArrayKey = (key: string): boolean => ['groups', 'textareas', 'images'].includes(key);

const getTransactionMongoUpdateObject = (diagramId: string, change: Change): TransactionMongoUpdateObject => {
    switch (change.type) {
        case 'create': {
            const [key, value] = Object.entries(change.data)[0];

            const [firstKey] = key.split('.');
            const isBaseKey = key.split('.').length === 2;

            if (isArrayKey(firstKey) && isBaseKey) {
                return [
                    { _id: diagramId },
                    {
                        $push: {
                            [firstKey]: value,
                        },
                    },
                ];
            }

            return [
                { _id: diagramId },
                {
                    $set: change.data as { [key: string]: any },
                },
            ];
        }

        case 'update': {
            const [key, value] = Object.entries(change.data)[0];
            const [firstKey, id, ...rest] = key.split('.');

            if (isArrayKey(firstKey)) {
                // Check if we're updating by array index or by element id
                if (!Number.isNaN(+id)) {
                    // Array index
                    return [
                        { _id: diagramId },
                        {
                            $set: {
                                [key]: value,
                            },
                        },
                    ];
                } else {
                    // Update by matching element id
                    return [
                        { _id: diagramId },
                        {
                            $set: {
                                [`${firstKey}.$[match]${['', ...rest].join('.')}`]: value,
                            },
                        },
                        {
                            arrayFilters: [{ 'match.id': { $eq: id } }],
                        },
                    ];
                }
            }

            return [
                { _id: diagramId },
                {
                    $set: change.data as { [key: string]: any },
                },
            ];
        }

        case 'delete': {
            const [key] = change.data as string[];

            const [type, index] = key.split('.');

            const deleteByArrayIndex = !isNaN(Number(index));

            if (!deleteByArrayIndex && isArrayKey(type)) {
                const ids = (change.data as string[]).map((key) => {
                    const [_type, id] = key.split('.');

                    if (_type !== type) {
                        throw new Error('Cannot delete keys of different types');
                    }

                    return id;
                });

                return [
                    { _id: diagramId },
                    {
                        $pull: {
                            [type]: { id: { $in: ids } },
                        },
                    },
                ];
            }

            return [
                { _id: diagramId },
                {
                    $unset: Object.fromEntries((change.data as string[]).map((key) => [key, ''])),
                },
            ];
        }
    }
};

/**
 * Generates a record of paths mapped to null for deletion in MongoDB.
 * Pulls nulls for all dot-separated keys that end with an array index.
 *
 * For example, given a key like `'componentInstances.component-c1gP7w3C.specifications.electrical.ports.1'`,
 * it extracts the path `'componentInstances.component-c1gP7w3C.specifications.electrical.ports'` and maps it to `null`.
 *
 * @param change - The change object containing create, update or delete details.
 * @returns A record where each key is a path to be nullified, and each value is `null`.
 */
const getPullNullsFromChange = (change: Change): Record<string, null> => {
    let paths: string[] = [];

    if (change.type === 'delete') {
        paths = (change.data as string[])
            .filter((key) => !isNaN(+key.split('.').pop()!))
            .map((key) => key.split('.').slice(0, -1).join('.'));
    }

    return Object.fromEntries(paths.map((key) => [key, null]));
};
