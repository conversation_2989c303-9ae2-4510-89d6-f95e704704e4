import React, { useState, FC } from 'react';

import { Box, Stack, Radio, UnstyledButton, Loader } from '@mantine/core';
import { openContextModal } from '@mantine/modals';

import { LocalNotificationService } from 'services/LocalNotificationService';
import { ProjectService } from 'services/ProjectService';

import { useCurrentProject } from 'hooks/use-current-project';

import cx from './GeneralAccess.module.css';

const GeneralAccess: FC = () => {
    const project = useCurrentProject()!;
    const visibility = project.visibility || 'private';

    const [loading, setLoading] = useState<'private' | 'public' | 'marketplace' | null>(null);

    const openPublishModal = () => {
        openContextModal({
            modal: 'publishDesign',
            innerProps: {},
            size: 'lg',
            withCloseButton: false,
        });
    };

    const setVisibility = async (visibility: 'private' | 'public') => {
        setLoading(visibility);

        try {
            await ProjectService.update(project.id, {
                visibility,
            });

            LocalNotificationService.showSuccess({
                message: 'Project updated',
            });
        } catch (error) {
            LocalNotificationService.showError({
                message: 'The project could not be updated.',
            });

            console.error('Error updating project visibility', error);
        } finally {
            setLoading(null);
        }
    };

    const options = [
        {
            value: 'private',
            label: 'Private',
            description: 'Only your team and collaborators can access this project',
            action: () => {
                setVisibility('private').then();
            },
        },
        {
            value: 'public',
            label: 'Public',
            description: 'Everybody with the link can view this project',
            action: () => {
                setVisibility('public').then();
            },
        },
        {
            value: 'marketplace',
            label: 'Publish to Reference Designs',
            description: 'Show your project to the world and share it publicly in the Reference Designs',
            action: () => {
                openPublishModal();
            },
        },
    ];

    return (
        <Stack gap="xs">
            {options.map((option) => (
                <UnstyledButton className={cx.button} onClick={option.action} key={option.value}>
                    {loading === option.value ? (
                        <Loader size={18} />
                    ) : (
                        <Radio className={cx.buttonRadio} checked={visibility === option.value} readOnly />
                    )}
                    <Box>
                        <Box className={cx.buttonTitle}>{option.label}</Box>
                        <Box className={cx.buttonDescription}>{option.description}</Box>
                    </Box>
                </UnstyledButton>
            ))}
        </Stack>
    );
};

export { GeneralAccess };
