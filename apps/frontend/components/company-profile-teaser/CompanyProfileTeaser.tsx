import { FC } from 'react';

import Link from 'next/link';

import {
    Anchor,
    Badge,
    Box,
    Card,
    CardProps,
    Flex,
    Group,
    Progress,
    Stack,
    Table,
    Text,
    Tooltip,
    UnstyledButton,
} from '@mantine/core';
import { IKImage } from 'components/ik-image/IKImage';

import {
    CompanyProfile,
    CompanySubscription,
    Event,
    POWER_LEVELS,
    SavedItemType,
    TipTapHelpers,
    powerConverter,
} from '@repo/dcide-component-models';

import { useCompanyMeta } from 'hooks/use-company-meta';

import { TextHelpers } from 'helpers/TextHelpers';
import { FormatHelpers } from 'helpers/formatters';
import { NumberHelpers } from 'helpers/NumberHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { SaveButton } from 'components/save-button/SaveButton';
import { BoothNumber } from 'components/booth-number/BoothNumber';
import { InAppSupportBadge } from 'elements/badge/InAppSupportBadge';
import { CompanyLogoFallback } from 'components/company-logo/CompanyLogoFallback';

import cx from './CompanyProfileTeaser.module.css';
import { VerifiedBadge } from 'components/verified-badge/VerifiedBadge';
import { InternalScoreBadge } from 'components/internal-score-badge/InternalScoreBadge';
import { useProductsByCompany } from 'components/company-profile/hooks/use-products-by-company';
import { ComponentOverviewHitSimple } from 'components/component-overview/ComponentOverviewHit.Simple';

const CompanyProfileTeaser: FC<{
    company: CompanyProfile;
    event?: Event;
    isExternalLink?: boolean;
    showIntercom?: boolean;
    showProductsPreview?: boolean;
    showActions?: boolean;
    children?: React.ReactNode;
    cardProps?: CardProps;
}> = ({
    company,
    event,
    isExternalLink,
    showIntercom,
    showProductsPreview,
    showActions = true,
    children,
    cardProps,
}) => {
    const companyUrl = CompanyProfileHelpers.urls.view(company.slug);
    const companyLogo = company.logos.small || company.logos.large;

    const { subtitle } = useCompanyMeta(company);

    const description = company.about && TipTapHelpers.getText(company.about);

    const minSize =
        company.systemSize?.min &&
        FormatHelpers.formatMeasurement({ value: company.systemSize.min, unit: 'W' }, powerConverter);
    const maxSize =
        company.systemSize?.max &&
        FormatHelpers.formatMeasurement({ value: company.systemSize.max, unit: 'W' }, powerConverter);

    const completeness = company.completeness ?? 0;

    const isPremiumCompany = company.subscription === CompanySubscription.PREMIUM && !company.internal;

    return (
        <Card radius="sm" withBorder className={cx.root} {...cardProps}>
            <Flex gap="xs">
                <Stack gap={4}>
                    <UnstyledButton
                        component={Link}
                        href={companyUrl}
                        className={cx.imageWrapper}
                        target={isExternalLink ? '_blank' : undefined}
                    >
                        {companyLogo ? (
                            <IKImage fileOrId={companyLogo as any} width={200} alt={company.name} />
                        ) : (
                            <CompanyLogoFallback name={company.name} color={company.color} />
                        )}
                    </UnstyledButton>

                    <BoothNumber company={company} event={event} size="xs" radius="sm" />

                    {isPremiumCompany && (
                        <VerifiedBadge withTooltip companyName={company.name} size="xs" radius="sm" fullWidth />
                    )}
                    {completeness > 80 && (
                        <Tooltip label="Profiles with high completeness feature the most products, services and case studies">
                            <Badge color="yellow" size="xs" radius="sm" fullWidth>
                                Top Profile
                            </Badge>
                        </Tooltip>
                    )}
                </Stack>

                <Stack className={cx.body} gap={'xs'}>
                    <Stack gap={'xs'} justify="space-between">
                        <Stack gap={0}>
                            <Anchor
                                component={Link}
                                href={companyUrl}
                                size="lg"
                                lh={1.2}
                                fw={700}
                                c="brand"
                                target={isExternalLink ? '_blank' : undefined}
                            >
                                {company.name}
                            </Anchor>
                            {subtitle.length > 0 && (
                                <Text fz="sm" c="dimmed">
                                    {subtitle}
                                </Text>
                            )}
                        </Stack>

                        {company.systemSize?.min && company.systemSize?.max && (
                            <Tooltip label={`Operating in the ${minSize} - ${maxSize} power range`}>
                                <Group grow gap={3} w="100%">
                                    {POWER_LEVELS.map((level) => {
                                        const isSelected =
                                            level >= company.systemSize!.min! && level <= company.systemSize!.max!;

                                        return (
                                            <Stack gap={4} key={level}>
                                                <Text
                                                    fz={8}
                                                    c={isSelected ? 'gray.6' : 'gray.5'}
                                                    fw={isSelected ? 600 : 400}
                                                >
                                                    {FormatHelpers.formatMeasurement(
                                                        { value: level, unit: 'W' },
                                                        powerConverter,
                                                    )}
                                                </Text>

                                                <Progress value={isSelected ? 100 : 0} size="xs" color="primary" />
                                            </Stack>
                                        );
                                    })}
                                </Group>
                            </Tooltip>
                        )}

                        {company.projectBudget?.min && company.projectBudget?.max && (
                            <Text fz="sm" c="dimmed">
                                Project Budget: {NumberHelpers.formatPriceShort(company.projectBudget.min)} -{' '}
                                {NumberHelpers.formatPriceShort(company.projectBudget.max)}
                            </Text>
                        )}
                    </Stack>

                    {!!description && <Box fz="xs">{TextHelpers.getTextWithEllipsis(description, 200, true)}</Box>}

                    {showActions && (
                        <Group mt="auto" gap={8}>
                            <InternalScoreBadge item={company as any} />
                            <SaveButton id={company.id} name={company.name} type={SavedItemType.PROFILE} />
                            {showIntercom && CompanyProfileHelpers.offersInAppSupport(company) && (
                                <InAppSupportBadge company={company} isExternalLink={isExternalLink} />
                            )}
                        </Group>
                    )}
                </Stack>
            </Flex>

            {showProductsPreview && <CompanyProfileProducts company={company} />}

            {children}
        </Card>
    );
};

const CompanyProfileProducts = ({ company }: { company: CompanyProfile }) => {
    const { products } = useProductsByCompany(company);

    if (!products.length) {
        return null;
    }

    return (
        <Stack gap={4} align="start">
            <Text fz="xs" fw={500} c="dimmed">
                Product preview
            </Text>
            <Table withTableBorder striped>
                <Table.Tbody>
                    {products.slice(0, 3).map((product) => (
                        <Table.Tr>
                            <Table.Td>
                                <ComponentOverviewHitSimple key={product.id} component={product} />
                            </Table.Td>
                        </Table.Tr>
                    ))}
                </Table.Tbody>
            </Table>
        </Stack>
    );
};

export { CompanyProfileTeaser };
