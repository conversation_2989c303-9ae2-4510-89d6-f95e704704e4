.notification {
    display: flex;
    align-items: stretch;

    gap: var(--mantine-spacing-xs);
    padding: var(--mantine-spacing-md);

    text-decoration: none;

    &[data-read="false"] {
        background-color: var(--mantine-color-white);
    }

    &[data-compact="true"] {
        padding: 0;
        background-color: transparent;
    }
}

.notification + .notification {
    border-top: 1px solid var(--mantine-color-gray-2);
}

.notificationContent {
    display: flex;
    flex-grow: 1;

    gap: var(--mantine-spacing-xs);

    font-size: 12px;
    text-decoration: none;

    color: var(--mantine-color-gray-7);
    transition: color 0.15s ease;

    &:hover,
    &:focus {
        color: var(--mantine-color-gray-9);
    }
}

.notificationAvatar {
    flex-grow: 0;
    flex-shrink: 0;
}

.notificationText {
    flex-grow: 1;
    flex-shrink: 1;

    word-break: break-word;

    strong {
        font-weight: 500;
    }
}

.notificationInner {
    max-width: 800px;
}

.notificationDate {
    color: var(--mantine-color-gray-6);
}

.notificationReadBubbleContainer {
    flex-grow: 0;
    flex-shrink: 0;

    display: flex;

    justify-content: center;
    align-items: center;

    width: 28px;
}

.notificationReadBubble {
    display: block;

    width: 8px;
    height: 8px;

    background-color: var(--mantine-color-primary-7) !important;
    border-radius: 9999px;
}

.notificationActions {
    margin-top: calc(var(--mantine-spacing-xs) / 2);

    &[data-success] {
        font-weight: 500;
        color: var(--mantine-color-green-6);
    }
}
