import React, { FC } from 'react';

import { IoCaretDown } from 'react-icons/io5';
import { Box, Group, GroupProps, Menu, ScrollArea, Text, UnstyledButton } from '@mantine/core';

import cx from './InlineFilters.Section.module.css';

import { InlineFiltersSectionRemove } from './InlineFilters.SectionRemove';

export type InlineFilterSectionProps = {
    icon?: React.ReactNode;
    label?: React.ReactNode;
    body?: React.ReactNode;
    rightSection?: React.ReactNode;
    onRemove?: () => void;
    popoverContent?: React.ReactNode;
    isViewOnly?: boolean;
    wrapperProps?: GroupProps;
    active: boolean;
};

const InlineFiltersSection: FC<InlineFilterSectionProps> = ({
    onRemove,
    popoverContent,
    isViewOnly,
    wrapperProps = {},
    active,
    ...props
}) => {
    if (popoverContent) {
        return (
            <Wrapper onRemove={onRemove} isViewOnly={isViewOnly} active={active} {...wrapperProps}>
                <InlineFiltersSectionWithPopover
                    target={
                        <UnstyledButton className={cx.target} fz="inherit">
                            <Content {...props} />
                            <Box c="dimmed" data-section-popover-arrow>
                                <IoCaretDown size={8} />
                            </Box>
                        </UnstyledButton>
                    }
                >
                    {popoverContent}
                </InlineFiltersSectionWithPopover>
            </Wrapper>
        );
    }

    return (
        <Wrapper onRemove={onRemove} active={active} {...wrapperProps}>
            <Content {...props} />
        </Wrapper>
    );
};

const InlineFiltersSectionWithPopover: FC<{
    target: React.ReactNode;
    children: React.ReactNode;
}> = ({ children, target }) => {
    return (
        <Menu trigger="click" position="bottom-start" transitionProps={{ transition: 'fade' }} offset={4}>
            <Menu.Target>{target}</Menu.Target>
            <Menu.Dropdown>
                <ScrollArea.Autosize mah={300} scrollbarSize={8}>
                    {children}
                </ScrollArea.Autosize>
            </Menu.Dropdown>
        </Menu>
    );
};

const Wrapper: FC<
    {
        onRemove?: () => void;
        isViewOnly?: boolean;
        children: React.ReactNode;
        active?: boolean;
    } & GroupProps
> = ({ onRemove, isViewOnly, children, active, ...rest }) => {
    return (
        <Group
            classNames={cx}
            gap={4}
            data-view-only={isViewOnly}
            data-active={active}
            data-inline-filter-section
            {...rest}
        >
            {children}

            {onRemove && <InlineFiltersSectionRemove onRemove={onRemove} />}
        </Group>
    );
};

const Content: FC<{
    icon?: InlineFilterSectionProps['icon'];
    label?: InlineFilterSectionProps['label'];
    body?: InlineFilterSectionProps['body'];
    rightSection?: InlineFilterSectionProps['rightSection'];
}> = ({ icon, label, body, rightSection }) => {
    return (
        <>
            {icon}
            {label && (
                <Text inherit c="dimmed" component="div">
                    {label}
                </Text>
            )}
            {body}
            {rightSection && <Box>{rightSection}</Box>}
        </>
    );
};

export { InlineFiltersSection, InlineFiltersSectionWithPopover };
