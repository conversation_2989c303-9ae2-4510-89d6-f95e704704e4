import { z } from 'zod';

import { UserTip } from './UserTip';
import { MeasurementSystem } from './MeasurementSystem';
import { PowerLevel } from '../validators/fields/PowerLevel';

export enum UserIndustry {
    CONSULTING = 'Consulting',
    DEVELOPER = 'Developer',
    DISTRIBUTOR = 'Distributor',
    ENGINEERING_FIRM = 'Engineering Firm',
    ENERGY_STORAGE = 'Energy Storage',
    EPC_CONTRACTOR = 'EPC Contractor',
    MANUFACTURER = 'Original Equipment Manufacturers',
    SOLAR = 'Solar',
    UNIVERSITY_EDUCATION = 'University/Education',
    UTILITY = 'Utility',
    OTHER = 'Other',
}

export enum UserAccess {
    ADMIN_PANEL = 'adminPanel', // can access Payload backend
    INTERNAL = 'internal',
    DEVELOPER = 'developer', // can see more info in the UI, can see unreleased features
}

export enum UserFeatureFlags {
    AI_PRODUCT_ASSISTANT = 'aiProductAssistant',
    INTERCOM = 'intercom',
    SIMULATION = 'simulation',
    WIRE_SIZING = 'wireSizing',
    BULK_UPLOAD = 'bulkUpload',
    QUOTES = 'quotes',
    OEM_SUBSCRIPTION = 'oemSubscription',
}

export enum UserProgressItem {
    CREATE_PROJECT = 'createProject',
    INVITE_TEAM_USERS = 'inviteTeamUsers',
    CREATE_PROFILE = 'createProfile',
    CREATE_PRODUCT = 'createProduct',
    CREATE_REFERENCE_DESIGN = 'createReferenceDesign',
}

export type UserProgress = {
    key: UserProgressItem;
    label: string;
    show: boolean;
    completed: boolean;
};

export enum UserReferrer {
    REPLUS = 'replus',
}

export enum UserType {
    DESIGNER = 'designer',
    MANUFACTURER = 'manufacturer',
}

export const userSchema = z.object({
    id: z.string(),
    team: z.string(),
    recentProjects: z.string().array(),
    name: z.string().optional(),
    goofy: z.string().optional(),
    email: z.string().email(),
    profileImage: z.string().optional().nullable(),
    source: z.string().optional(),
    developer: z.boolean().optional(),
    internal: z.boolean().optional(),
    adminPanel: z.boolean().optional(),
    order: z.any(), // todo..
    updatedAt: z.string(), // TODO
    createdAt: z.string(), // TODO
    dismissedTips: z.array(z.nativeEnum(UserTip)),
    completedProgressItems: z.array(z.nativeEnum(UserProgressItem)),
    flags: z.array(z.nativeEnum(UserFeatureFlags)),
    phone: z.string().optional(),
    signup: z
        .object({
            company: z.string().optional(),
            job: z.string().optional(),
            industry: z.nativeEnum(UserIndustry).optional().nullable(),
            industryOther: z.string().nullable().optional(),
            referral: z.string().optional(),
            systemSize: PowerLevel.validator.optional(),
        })
        .optional(),
    measurementSystem: z.nativeEnum(MeasurementSystem).optional(),
    type: z.nativeEnum(UserType).optional(),
    referrer: z.nativeEnum(UserReferrer).optional(),
    isManufacturer: z.boolean().optional(),
    hasCompany: z.boolean().optional(),
    isSupportUser: z.boolean().optional(),
    teamSuggestions: z
        .object({
            team: z.string(),
            reason: z.string(),
        })
        .array()
        .optional(),
    teamSuggestionsDismissed: z.boolean().optional(),
    companySuggestions: z
        .object({
            company: z.string(),
            reason: z.string(),
        })
        .array()
        .optional(),
    companySuggestionsDismissed: z.boolean().optional(),
});

export type User = z.infer<typeof userSchema>;
