import { ProfileSearchState, TextSearchState } from '@/search/types';
import { SearchIndex } from '@repo/dcide-component-models';
import { config } from '@/config';

export const setTextSearchFlags = (search: TextSearchState) => {
    const textQuery = search.query?.search;
    if (!textQuery) {
        return;
    }

    const useVectorSearch = config.search.vectorSearch.enabled;
    if (useVectorSearch) {
        search.hasVectorSearch = true;
    }

    if (config.search.textSearch.enabled) {
        search.hasTextSearch = true;
    }
};

export const getAutoCompleteStage = (textQuery: string, fieldName: string, boost: number): object[] => {
    return textQuery
        ? [
              {
                  autocomplete: {
                      query: textQuery,
                      path: fieldName,
                      fuzzy: {
                          maxEdits: 1,
                          prefixLength: 2,
                      },
                      score: { boost: { value: boost } },
                  },
              },
          ]
        : [];
};

export const getLocationStage = (search: ProfileSearchState, boost: number): object[] => {
    if ('location' in search.query && search.query.location) {
        const coordinates = search.query.location.coordinates;
        return [
            {
                near: {
                    path: 'locations.address.coordinates',
                    origin: {
                        type: 'Point',
                        coordinates: [coordinates.longitude, coordinates.latitude],
                    },
                    pivot: config.search.textSearch.locationPivot,
                    score: { boost: { value: boost } },
                },
            },
        ];
    }

    return [];
};

export const getSynonymsStages = (textQuery: string, fieldName: string, boost: number): object[] => {
    return textQuery
        ? [
              {
                  text: {
                      query: textQuery,
                      path: fieldName,
                      fuzzy: { maxEdits: 1, prefixLength: 2 },
                      score: { boost: { value: boost } },
                  },
              },
              {
                  text: {
                      query: textQuery,
                      path: fieldName,
                      synonyms: SearchIndex.SEARCH_SYNONYMS,
                      score: { boost: { value: boost } },
                  },
              },
          ]
        : [];
};

export const addTextSearch = (index: SearchIndex, search: TextSearchState, textSearchStages: any): void => {
    const limit = 96;
    const { query } = search;
    const hasType = 'type' in query;

    const searchConfig = {
        index: index,
        compound: {
            should: textSearchStages,
            ...(hasType && {
                filter: {
                    text: {
                        path: 'type',
                        query: query.type,
                    },
                },
            }),
        },
    };

    const pipeline = [
        {
            $search: searchConfig,
        },
        {
            $limit: limit,
        },
        addSearchScoreField('searchScore'),
    ];

    if (search.hasVectorSearch) {
        search.stages.push(
            {
                // Union vector and text search results
                $unionWith: {
                    coll: search.collection,
                    pipeline: [...pipeline],
                },
            },
            // Remove duplicates, while keeping the search and vector scores
            {
                $group: {
                    _id: '$_id',
                    doc: { $first: '$$ROOT' },
                    searchScore: { $max: '$searchScore' },
                    vectorSearchScore: { $max: '$vectorSearchScore' },
                },
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: [
                            '$doc',
                            {
                                searchScore: '$searchScore',
                                vectorSearchScore: '$vectorSearchScore',
                            },
                        ],
                    },
                },
            },
        );
    } else {
        search.stages.push(...pipeline);
    }
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const addVectorSearch = async (search: TextSearchState, searchIndex: SearchIndex) => {
    try {
        throw new Error('Vector search temporarily disabled');

        /**
        const { query } = search;
        const text = query.search;

        if (!text) {
            return;
        }

        const embedding = await AIService.getEmbedding(text, jwt);
        const hasType = 'type' in query;

        const vectorSearch: PipelineStage.VectorSearch['$vectorSearch'] = {
            index: searchIndex,
            path: 'embedding',
            queryVector: embedding,
            // ENN: Exact Nearest Neighbor Search, ANN: Approximate Nearest Neighbor Search
            limit: search.exactMatch ? config.search.vectorSearch.ennLimit : config.search.vectorSearch.annLimit,
            numCandidates: config.search.vectorSearch.annCandidates,
        };

        if (hasType) {
            vectorSearch.filter = { type: query.type };
        }

        search.stages.push(
            {
                $vectorSearch: vectorSearch,
            },
            addSearchScoreField('vectorSearchScore'),
        );
        */
    } catch (error) {
        console.error('Failed to add vector search', error);
    }
};

const addSearchScoreField = (scoreField: string) => ({
    $addFields: {
        [scoreField]: {
            $meta: scoreField,
        },
    },
});
