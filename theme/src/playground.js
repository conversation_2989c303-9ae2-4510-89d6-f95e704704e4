import { createSvgPolygonRoundedAutoFit } from './generate-polygon';
import { generateRightTrianglePointsV2 } from './generate-triangle';

export const polygon = ({
    width,
    height,
    offset,
    padding,
    radius = 50,
}) => {
    const { pointC: point1 } = generateRightTrianglePointsV2({
        angleB_deg: 20,
        lengthAB: offset,
    });

    const { pointC: point2 }= generateRightTrianglePointsV2({
        angleB_deg: 20,
        lengthAB: height,
    });

    return createSvgPolygonRoundedAutoFit([
        { x: 0, y: 0 },
        { x: width, y: 0, radius },
        { x: width - point1.x, y: offset, radius },
        { x: width - point1.x - padding, y: offset, radius },
        { x: width - point2.x - padding, y: height, radius },
        { x: 0, y: height }
    ]);
};

window.polygon = polygon;

(() => {
    const image = document.querySelector('[data-insights-image]');

    if (image) {
        const {
            width,
            height,
        } = image.getBoundingClientRect();

        const options = {
            width,
            height,
            offset: 240,
            padding: 80,
            radius: 20,
        };

        if (window.innerWidth >= 1280) {
            options.padding = 128;
        }

        const svg = polygon(options);

        image.style.maskImage = `url('data:image/svg+xml;utf8,${svg}')`;
        image.style.maskPosition = 'right';
        image.style.maskSize = 'auto 100%';
        image.style.maskRepeat = 'no-repeat';
        image.style.transform = 'scale(-1)';
    }
})();

(() => {
    const elements = document.querySelectorAll('[data-polygon-wrapper]');

    Array.from(elements).forEach((element) => {
        const mask = element.querySelector('[data-polygon-mask]');
        const content = element.querySelector('[data-polygon-content]');

        const {
            width,
            height,
        } = mask.getBoundingClientRect();

        const {
            width: contentWidth,
            height: contentHeight,
        } = content.getBoundingClientRect();

        const options = {
            width,
            height,
            offset: height / 3 * 2,
            padding: 0,
            radius: 20,
        };

        if (window.innerWidth >= 640) {
            options.padding = 50;
        }

        if (window.innerWidth >= 768) {
            options.offset = height - 24 - contentHeight - 24;
            options.padding = 230;
        }

        if (window.innerWidth >= 1024) {
            options.padding = 320;
        }

        if (window.innerWidth >= 1280) {
            options.offset = height - 40 - contentHeight - 24;
            options.padding = 400;
        }

        const svg = polygon(options);

        mask.style.maskImage = `url('data:image/svg+xml;utf8,${svg}')`;
        mask.style.maskPosition = 'right';
        mask.style.maskSize = 'auto 100%';
        mask.style.maskRepeat = 'no-repeat';
    });
})();

(() => {
    const openingSections = document.querySelectorAll('[data-opening-section]');

    Array.from(openingSections).forEach((section) => {
        const nextElement = section.nextElementSibling;
        const percentage = (+section.dataset.percentage) / 100;

        const {
            height,
        } = nextElement.getBoundingClientRect();

        section.style.height = `${height * percentage}px`;
    });

    const closingSections = document.querySelectorAll('[data-closing-section]');

    Array.from(closingSections).forEach((section) => {
        const nextElement = section.previousElementSibling;
        const percentage = (+section.dataset.percentage) / 100;

        const {
            height,
        } = nextElement.getBoundingClientRect();

        section.style.height = `${height * percentage}px`;
    });
})();

(() => {
    const casesWrappers = document.querySelectorAll('[data-cases-wrapper]');

    const pictures = Array.from(document.querySelectorAll('[data-cases-preview-image]'));
    const triggers = Array.from(document.querySelectorAll('[data-cases-preview-trigger]'));

    const activate = (position) => {
        pictures.forEach((picture, index) => {
            if (position === index) {
                picture.style.setProperty('opacity', '100%');
            } else {
                picture.style.removeProperty('opacity');
            }
        });
    };

    triggers.forEach((trigger, index) => {
        trigger.addEventListener('mouseenter', () => {
            activate(index);
        });
    });

    activate(0);
})();

(() => {
    const blogTeaser = document.querySelectorAll('[data-blog-teaser]');

    Array.from(blogTeaser).forEach((blogTeaser) => {
        const image = blogTeaser.querySelector('[data-blog-teaser-image]');
        const content = blogTeaser.querySelector('[data-blog-teaser-content]');

        const {
            width: imageWidth,
            height: imageHeight,
        } = image.getBoundingClientRect();

        const {
            width: contentWidth,
            height: contentHeight,
        } = content.getBoundingClientRect();

        let radius = 20;
        let percentage = 25 / 100;
        let offset = 20;

        if (window.innerWidth >= 1280) {
            radius = 40;
        }

        const svg = createSvgPolygonRoundedAutoFit([
            { x: 0, y: 0, radius },
            { x: imageWidth, y: 0, radius },
            { x: imageWidth, y: imageHeight, radius },
            { x: contentWidth, y: imageHeight, radius },
            { x: contentWidth - offset, y: imageHeight - contentHeight, radius },
            { x: 0, y: imageHeight - contentHeight, radius },

        ]);

        image.style.maskImage = `url('data:image/svg+xml;utf8,${svg}')`;
        image.style.maskPosition = 'right';
        image.style.maskSize = 'auto 100%';
        image.style.maskRepeat = 'no-repeat';
    });
})();








/**
 * Calculates the coordinates of a point on a 2D line.
 * The line is defined by passing through `lineOrigin` and oriented at an angle
 * relative to the negative Y-axis (downwards direction).
 *
 * The "height from the bottom" is interpreted as the target y-coordinate of the point.
 *
 * @param {number} userInputAngleDegrees The angle of the line in degrees,
 * measured relative to the negative Y-axis (which is 270° in standard
 * Cartesian coordinates, pointing straight down).
 * For example:
 * - An input of 0° means the line goes straight down (effective angle 270°).
 * - An input of 20° means the line is 20° counter-clockwise from the
 * negative Y-axis (effective angle 270 + 20 = 290°).
 * - An input of -90° means the line goes along the positive X-axis
 * (effective angle 270 - 90 = 180°).
 * @param {number} targetY The desired y-coordinate of the point on the line.
 * This is interpreted as the "height from the bottom",
 * assuming "bottom" corresponds to y=0.
 * @param {{x: number, y: number}} [lineOrigin={x: 0, y: 0}] The origin point
 * through which the line passes. Defaults to {x: 0, y: 0}.
 * @returns {{x: number, y: number} | null} The coordinates {x, y} of the point on the line.
 * Returns null if the point cannot be reached on the specified line.
 * For a horizontal line that *does* pass through targetY,
 * it conventionally returns the point (lineOrigin.x, targetY).
 */
function getAngledCoordinates(
    userInputAngleDegrees,
    targetY,
    lineOrigin = { x: 0, y: 0 }
) {
    // Adjust the input angle: it's an offset from the negative Y-axis (270 degrees).
    const effectiveAngleDegrees = userInputAngleDegrees;
    const angleRadians = effectiveAngleDegrees * (Math.PI / 180);

    const { x: ox, y: oy } = lineOrigin;

    // A small number for floating point comparisons
    const epsilon = 1e-9;

    const dy = targetY - oy;

    // Case 1: Line is nearly vertical (effective angle is ~90° or ~270°)
    if (Math.abs(Math.cos(angleRadians)) < epsilon) {
        return { x: ox, y: targetY };
    }

    // Case 2: Line is nearly horizontal (effective angle is ~0° or ~180°)
    if (Math.abs(Math.sin(angleRadians)) < epsilon) {
        if (Math.abs(dy) < epsilon) { // targetY is the same as the line's origin y
            return { x: ox, y: targetY }; // Conventionally return the origin point
        } else {
            // Horizontal line at y = oy, but targetY is different.
            return null;
        }
    }

    // Case 3: Angled line
    const dx_from_origin = dy / Math.tan(angleRadians);
    const calculatedX = ox + dx_from_origin;

    return { x: calculatedX, y: targetY };
}

(() => {
    const caseTeasers = document.querySelectorAll('[data-case-teaser]');

    Array.from(caseTeasers).forEach((caseTeaser) => {
        const variant = caseTeaser.dataset.caseTeaserVariant;
        const swapped = caseTeaser.dataset.caseTeaserSwapped === '1';

        const image = caseTeaser.querySelector('[data-case-teaser-image]');
        const content = caseTeaser.querySelector('[data-case-teaser-content]');

        const {
            width: wrapperWidth,
            height: wrapperHeight,
        } = caseTeaser.getBoundingClientRect();

        const {
            width: imageWidth,
            height: imageHeight,
        } = image.getBoundingClientRect();

        const {
            width: contentWidth,
            height: contentHeight,
        } = content.getBoundingClientRect();

        const margin = (window.innerWidth - contentWidth * 2) / 2;

        const point1 = getAngledCoordinates(290, imageHeight, { x: imageWidth, y: 0 });

        let svg = createSvgPolygonRoundedAutoFit([
            { x: 0, y: 0 },
            { x: imageWidth, y: 0, radius: 20 },
            { ...point1, radius: 20 },
            { x: 0, y: imageHeight },
        ]);

        if (variant === 'angled' && !swapped && window.innerWidth >= 768) {
            const point1 = getAngledCoordinates(290, imageHeight - contentHeight, { x: imageWidth, y: 0 });
            const point2 = {
                x: imageWidth - contentWidth,
                y: point1.y,
            };
            const point3 = getAngledCoordinates(290, imageHeight, { x: point2.x, y: point2.y });

            svg = createSvgPolygonRoundedAutoFit([
                { x: 0, y: 0 },
                { x: imageWidth, y: 0, radius: 20 },
                { x: point1.x, y: point1.y, radius: 20 },
                { x: point2.x, y: point2.y, radius: 20 },
                { x: point3.x, y: point3.y, radius: 20 },
                { x: 0, y: imageHeight },
            ]);
        }

        if (variant === 'angled' && swapped && window.innerWidth >= 768) {
            const point1 = {
                x: imageWidth - contentWidth,
                y: imageHeight,
            };
            const point2 = getAngledCoordinates(250, imageHeight - contentHeight, { x: point1.x, y: point1.y })

            svg = createSvgPolygonRoundedAutoFit([
                { x: 0, y: 0 },
                { x: imageWidth, y: 0, radius: 20 },
                { x: imageWidth, y: imageHeight, radius: 20 },
                { x: point1.x, y: point1.y, radius: 20 },
                { x: point2.x, y: point2.y, radius: 20 },
                { x: 0, y: imageHeight - contentHeight },
            ]);
        }

        if (variant === 'straight' && !swapped && window.innerWidth >= 768) {
            const point1 = {
                x: imageWidth - contentWidth - margin,
                y: imageHeight - contentHeight,
            };
            const point2 = getAngledCoordinates(290, imageHeight, { x: point1.x, y: point1.y })

            svg = createSvgPolygonRoundedAutoFit([
                { x: 0, y: 0, radius: 20 },
                { x: imageWidth, y: 0 },
                { x: imageWidth, y: imageHeight - contentHeight },
                { x: point1.x, y: point1.y, radius: 20 },
                { x: point2.x, y: point2.y, radius: 20 },
                { x: 0, y: imageHeight, radius: 20 },
            ]);
        }

        if (variant === 'straight' && swapped && window.innerWidth >= 768) {
            const point1 = {
                x: imageWidth - contentWidth,
                y: imageHeight,
            };
            const point2 = getAngledCoordinates(250, imageHeight - contentHeight, { x: point1.x, y: point1.y })

            svg = createSvgPolygonRoundedAutoFit([
                { x: 0, y: 0 },
                { x: imageWidth, y: 0, radius: 20 },
                { x: imageWidth, y: imageHeight, radius: 20 },
                { x: point1.x, y: point1.y, radius: 20 },
                { x: point2.x, y: point2.y, radius: 20 },
                { x: 0, y: imageHeight - contentHeight },
            ]);
        }

        image.style.maskImage = `url('data:image/svg+xml;utf8,${svg}')`;
        image.style.maskSize = 'auto 100%';
        image.style.maskRepeat = 'no-repeat';
    });




    return;
    /*
    const angle = -70;
    const offset = 500;

    const point1 = getCoordinatesOnLineWithDownwardBaseAngle(angle, height / 2, { x: width, y: 0 });
    const point2 = { x: point1.x - offset, y: point1.y };
    const point3 = getCoordinatesOnLineWithDownwardBaseAngle(angle, height, { x: point2.x, y: point2.y });

    const svg = createSvgPolygonRoundedAutoFit([
        { x: 0, y: 0, radius: 20 },
        { x: width, y: 0, radius: 20 },
        { x: point1.x, y: point1.y, radius: 20 },
        { x: point2.x, y: point2.y, radius: 20 },
        { x: point3.x, y: point3.y, radius: 20 },
        { x: 0, y: height, radius: 20 },
    ]);
    */

    const angle = -70;
    const offset = 500;

    const point1 = getCoordinatesOnLineWithDownwardBaseAngle(angle, height / 2, { x: width - offset, y: 0 });
    const point2 = { x: point1.x - offset, y: point1.y };
    const point3 = getCoordinatesOnLineWithDownwardBaseAngle(angle, height, { x: point2.x, y: point2.y });

    const svg = createSvgPolygonRoundedAutoFit([
        { x: 0, y: 0, radius: 20 },
        { x: width, y: 0, radius: 20 },
        { x: width, y: height, radius: 20 },
        { x: width - offset, y: height, radius: 20 },
        { x: width - offset - 100, y: height / 2, radius: 20 },
        { x: 50, y: height / 2, radius: 20 },
    ]);

    testje.style.maskImage = `url('data:image/svg+xml;utf8,${svg}')`;
    testje.style.maskPosition = 'right';
    testje.style.maskSize = 'auto 100%';
    testje.style.maskRepeat = 'no-repeat';
})();

