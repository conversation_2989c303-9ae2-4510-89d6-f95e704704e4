import type { Endpoint } from 'payload';

import { handleEndpointError } from '../../../../helpers/handle-endpoint-error';
import { inviteUserHelper } from '../../../../helpers/invite-user';

const inviteUser: Endpoint = {
    path: '/:teamId/users/invite',
    method: 'post',
    handler: async (request) => {
        try {
            const { teamId } = request.routeParams as {
                teamId: string;
            };

            const requestBody = await request.json();

            const { companyId } = requestBody as {
                companyId?: string;
            };

            const team = await inviteUserHelper({
                payload: request.payload,
                requestUserId: request.user.id,
                requestBody,
                teamId,
                companyId,
            });

            return Response.json(team);
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { inviteUser };
