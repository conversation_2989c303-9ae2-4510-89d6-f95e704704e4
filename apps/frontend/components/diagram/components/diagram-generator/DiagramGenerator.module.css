.root {
}

.menu {
    position: absolute;

    left: calc(50% - (var(--diagram-sidebar-width) - var(--diagram-sidebar-nav-width)) / 2);
    top: 50%;

    transform: translate(-50%, -50%);

    z-index: 98;

    padding: var(--mantine-spacing-lg);

    font-size: 12px;

    background-color: #ffffff;
    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);
    box-shadow: var(--mantine-shadow-lg);

    transition: transform 0.3s, opacity 0.3s;

    &[data-generator] {
        opacity: 0;
        transform: translate(-50%, -50%) scale(50%);
        pointer-events: none;
    }
}

.menuTitle {
    font-size: 16px;
    font-weight: 700;

    margin: 0;
    margin-bottom: var(--mantine-spacing-lg);

    text-align: center;
}

.menuButtons {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--mantine-spacing-xs);

    margin-top: 8px;
}

.diagramGeneratorButton {
    display: flex;
    flex-direction: column;

    justify-content: center;
    align-items: center;

    width: 120px;
    height: 120px;

    gap: 10px;

    padding: 8px;

    font-size: 11px;
    text-align: center;

    background-color: #ffffff;
    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);

    &:is(:hover, :focus) {
        svg {
            color: var(--mantine-color-primary-7);
        }
    }

    svg {
        width: 36px;
        height: 36px;

        stroke-width: 1.25;
    }
}

.title {
    font-size: 14px;
    font-weight: 600;

    margin: 0;

    text-align: center;

    &[data-size="large"] {
        font-size: 18px;
    }
}

.componentsContent {
    display: grid;
    gap: var(--mantine-spacing-lg);
}

.productsContent {
    display: grid;
    gap: var(--mantine-spacing-lg);
}

.componentsGrid {
    display: grid;
    gap: var(--mantine-spacing-xs);
    grid-template-columns: 1fr 1fr 1fr 1fr;
}

.productsGrid {
    display: grid;
    gap: var(--mantine-spacing-xs);
    grid-template-columns: 1fr 1fr;
}

.componentsGridItem {
    display: flex;
    flex-direction: column;

    justify-content: center;
    align-items: center;

    gap: var(--mantine-spacing-xs);

    width: 100px;
    height: 100px;

    font-size: 12px;
    font-weight: 500;

    text-align: center;

    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);

    svg {
        flex-grow: 0;
        flex-shrink: 0;

        width: 40px;
        height: 40px;
    }

    &[data-selected="true"] {
        background-color: var(--mantine-color-gray-0);
    }
}

.productsGridItem {
    position: relative;

    padding: var(--mantine-spacing-xs);
    padding-right: var(--mantine-spacing-xl);

    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-sm);

    .selectedIcon {
        position: absolute;
        top: var(--mantine-spacing-xs);
        right: var(--mantine-spacing-xs);

        flex: auto 0 0;

        display: none;
        margin-left: auto;

        color: var(--mantine-color-primary-6);

        width: 16px;
        height: 16px;
    }

    &[data-selected="true"] {
        background-color: var(--mantine-color-gray-0);

        .selectedIcon {
            display: block;
        }
    }
}

.componentsActions {
    display: flex;
    justify-content: center;
}

.projects {
    position: absolute;
    left: 0;
    top: var(--header-height);

    z-index: 9999;

    width: 100%;
    height: calc(100% - var(--header-height));

    background-color: red;
}

.card {
    width: 100%;
    border: 1px solid var(--mantine-color-gray-2);

    &:hover {
        background-color: var(--mantine-color-gray-0);
    }
}

.backButton {
    justify-self: center;
}
