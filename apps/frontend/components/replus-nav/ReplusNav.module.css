.root {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;

    z-index: 201;

    box-shadow: 0px -2px 10px rgba(0, 0, 0, 0.1);

    transition: opacity 200ms;

    &[data-hidden="true"] {
        opacity: 0;
    }
}

.item {
    padding: 10px 4px 8px 4px;

    font-size: var(--mantine-font-size-xs);
    font-weight: 600;

    color: var(--mantine-color-gray-7);

    svg {
        width: 20px;
        height: 20px;
    }

    &[data-active="true"] {
        color: var(--mantine-color-gray-9);
    }
}

.iconWrapper {
    position: relative;

    width: 30px;
    height: 30px;

    display: flex;
    align-items: center;
    justify-content: center;
}

.badge {
    position: absolute;
    top: -4px;
    right: -4px;

    transform: translate(50%, 0) scale(0.8);
}
