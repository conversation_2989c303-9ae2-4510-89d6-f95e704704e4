import { ActionIcon, Collapse, Flex, Text } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import { digestString } from 'helpers/digestString';
import { useGeneralGlobals } from 'hooks/use-general-globals';
import { useEffect, useState } from 'react';
import { LocalStorageService } from 'services/LocalStorageService';

const BANNER_MESSAGE_HASH_KEY = 'banner_message_hash';

const BannerMessage = () => {
    const { data: { bannerMessage } = { bannerMessage: undefined } } = useGeneralGlobals();
    const [showBanner, setShowBanner] = useState(false);
    const storedHash = LocalStorageService.get(BANNER_MESSAGE_HASH_KEY);

    useEffect(() => {
        if (bannerMessage) {
            digestString(bannerMessage).then((hash) => {
                if (hash !== storedHash) {
                    setShowBanner(true);
                } else {
                    setShowBanner(false);
                }
            });
        }
    }, [bannerMessage, storedHash]);

    const setBannerHidden = () => {
        setShowBanner(false);

        digestString(bannerMessage!).then((hash) => {
            LocalStorageService.store(BANNER_MESSAGE_HASH_KEY, hash);
        });
    };

    if (bannerMessage) {
        return (
            <Collapse in={showBanner}>
                <Flex p="sm" justify="space-between" bg="gray.1">
                    <Text>{bannerMessage}</Text>
                    <ActionIcon onClick={setBannerHidden} size="sm" variant="subtle">
                        <IconX />
                    </ActionIcon>
                </Flex>
            </Collapse>
        );
    }

    return null;
};

export { BannerMessage };
