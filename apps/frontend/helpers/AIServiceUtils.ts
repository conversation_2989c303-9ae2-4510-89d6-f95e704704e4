import { construct, get, omit, unique } from 'radash';
import {
    AIConversationMessage,
    ComponentChatMessage,
    AIConversationRole,
    ComponentType,
    crush,
    getComponentValidator,
    Component,
    MeasurementFieldTypes,
    profileQuerySchema,
    referenceDesignQuerySchema,
    componentQuerySchema,
    GeneratedSearchFilters,
} from '@repo/dcide-component-models';
import { SafeParseReturnType, z, ZodError, ZodIssueCode, ZodObject } from 'zod';

const EMPTY_ZOD_ERROR = new ZodError([
    {
        code: ZodIssueCode.custom,
        message: 'Unable to clean input data: object is empty after removal of invalid fields.',
        path: [],
    },
]);

class AIServiceUtils {
    static convertAIMessagesToAIConversationMessages = (history?: ComponentChatMessage[]): AIConversationMessage[] => {
        if (!history) {
            return [];
        }

        return history.flatMap((item) => [
            { role: AIConversationRole.USER, content: item.question },
            { role: AIConversationRole.ASSISTANT, content: item.answer },
        ]);
    };

    static deletePath(obj: any, path: (string | number)[]) {
        let current = obj;

        for (let i = 0; i < path.length - 1; i++) {
            current = current[path[i]];
            if (current === undefined || current === null) return;
        }

        const lastKey = path[path.length - 1];
        delete current[lastKey];
    }

    static cleanSafeParse(data: Record<string, any>, zodSchema: ZodObject<any>): SafeParseReturnType<any, any> {
        const result = zodSchema.safeParse(data);
        if (result.success) {
            return result;
        }

        const cleanedData = { ...data };
        for (const issue of result.error.issues) {
            if (issue.path.length > 0) {
                AIServiceUtils.deletePath(cleanedData, issue.path);
            }
        }

        if (Object.keys(cleanedData).length === 0) {
            return { success: false, error: EMPTY_ZOD_ERROR };
        }

        return zodSchema.safeParse(cleanedData);
    }

    static getCleanResult = (componentType: ComponentType, result: Partial<Component>) => {
        let cleanResult = crush(result);

        try {
            const validator = getComponentValidator(componentType);
            cleanResult = crush(validator.parse(result));
        } catch (err) {
            if (err instanceof z.ZodError) {
                const invalidPaths = err.issues.map((issue) => issue.path.join('.')) as string[];

                cleanResult = Object.fromEntries(
                    Object.entries(cleanResult).filter(([key]) => !invalidPaths.some((path) => key.startsWith(path))),
                );
            }
        }

        const measurementFields = Object.keys(cleanResult).filter((key) => key.includes('.unit'));

        const constructedCleanResult = construct(cleanResult) as any;

        // we don't want to set measurement fields individually
        // but set them as a whole object
        measurementFields.forEach((unitFieldKey) => {
            const key = unitFieldKey.replace('.unit', '');
            const objectValue = get(constructedCleanResult, key) as any;

            // remove individual fields
            cleanResult = omit(cleanResult, [unitFieldKey, ...MeasurementFieldTypes.map((field) => `${key}.${field}`)]);

            // set object field
            cleanResult[key] = objectValue;
        });

        return cleanResult;
    };

    static safelyParseJSON(value: string) {
        if (!value || value === 'None') {
            return { success: false, value: null };
        }

        const cleaned = value.replaceAll("'", '"').replaceAll('None', 'null');

        try {
            return {
                success: true,
                value: JSON.parse(cleaned),
            };
        } catch (e) {
            console.error('JSON parse error:', e);
            return {
                success: false,
                value: cleaned,
            };
        }
    }

    static processResponse(response: string, componentType: ComponentType, key: string) {
        if (!response) {
            return { success: false };
        }

        const parseResult = AIServiceUtils.safelyParseJSON(response);

        if (!parseResult.success) {
            return { success: false };
        }

        if (parseResult.value !== null) {
            const parsed = AIServiceUtils.getCleanResult(componentType, construct({ [key]: parseResult.value }));

            if (parsed && Object.keys(parsed).length > 0) {
                return { success: true, data: parsed };
            }
        }

        return { success: true, data: {} };
    }

    static parseFilters(data: any, query: string): GeneratedSearchFilters {
        const productFilters = AIServiceUtils.cleanSafeParse(data.component, componentQuerySchema);
        const profileFilters = AIServiceUtils.cleanSafeParse(data.profile, profileQuerySchema);
        const referenceDesignFilteres = AIServiceUtils.cleanSafeParse(data.referenceDesign, referenceDesignQuerySchema);

        let tryOnlyProducts = undefined;
        let tryOnlyProfiles = undefined;
        let tryOnlyDesigns = undefined;
        const parsingFailed = !productFilters.success && !referenceDesignFilteres.success && !profileFilters.success;
        if (parsingFailed) {
            tryOnlyProducts = AIServiceUtils.cleanSafeParse(data, componentQuerySchema);
            tryOnlyProfiles = AIServiceUtils.cleanSafeParse(data, profileQuerySchema);
            tryOnlyDesigns = AIServiceUtils.cleanSafeParse(data, referenceDesignQuerySchema);
        }

        if (parsingFailed && !tryOnlyProducts?.success && !tryOnlyProfiles?.success && !tryOnlyDesigns?.success) {
            const error = {
                productFilters: productFilters.error,
                profileFilters: profileFilters.error,
                referenceDesignFilters: referenceDesignFilteres.error,
            };
            console.error(
                `Failed to parse component search, query: "${query}" response:${JSON.stringify(data)} error:${JSON.stringify(error)}`,
            );

            return AIServiceUtils.defaultFilters(query);
        }

        const generatedFilters: GeneratedSearchFilters = {
            productFilters: productFilters.success ? productFilters.data : tryOnlyProducts?.data,
            profileFilters: profileFilters.success ? profileFilters.data : tryOnlyProfiles?.data,
            designFilters: referenceDesignFilteres.success ? referenceDesignFilteres.data : tryOnlyDesigns?.data,
            caseStudyFilters: undefined,
        };

        return generatedFilters;
    }

    static defaultFilters(query: string): GeneratedSearchFilters {
        return {
            productFilters: { search: query },
            designFilters: { search: query },
            profileFilters: { search: query },
            caseStudyFilters: { search: query },
        };
    }

    static parseGeneratedFilters(data: any, query: string): GeneratedSearchFilters {
        const result = AIServiceUtils.parseFilters(data, query);

        const manufacturerFilter = result?.productFilters?.manufacturer || result?.profileFilters?.manufacturer;
        if (manufacturerFilter) {
            result.profileFilters = { ...result.profileFilters, manufacturer: manufacturerFilter };
            result.designFilters = { ...result.designFilters, manufacturer: manufacturerFilter };
            result.caseStudyFilters = { ...result.caseStudyFilters, manufacturer: manufacturerFilter };
        }

        const searchFilter = result?.productFilters?.search || result?.profileFilters?.search;
        if (searchFilter) {
            result.profileFilters = { ...result.profileFilters, search: searchFilter };
            result.designFilters = { ...result.designFilters, search: searchFilter };
        }

        const compliancesFilter = unique([
            ...(result?.productFilters?.compliances ?? []),
            ...(result?.profileFilters?.compliances ?? []),
        ]);
        if (compliancesFilter.length > 0) {
            result.productFilters = { ...result.productFilters, compliances: compliancesFilter };
            result.profileFilters = { ...result.profileFilters, compliances: compliancesFilter };
        }

        if (!manufacturerFilter && (searchFilter || query)) {
            result.caseStudyFilters = {
                ...result.caseStudyFilters,
                search: searchFilter ?? query,
            };
        }

        return result;
    }
}

export { AIServiceUtils };
