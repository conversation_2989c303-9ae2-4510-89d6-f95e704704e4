import { Badge, Box, BoxProps, Image } from '@mantine/core';

import cx from './ImagePreview.module.css';

const ImagePreview = ({
    src,
    alt = '',
    withShadow,
    withOverlay,
    ...rest
}: {
    src: string;
    alt?: string;
    withShadow?: boolean;
    withOverlay?: boolean;
} & BoxProps) => {
    return (
        <Box className={cx.root} data-overlay={withOverlay} data-shadow={withShadow} bg="gray.0" {...rest}>
            <Image src={src} alt={alt} />
            <Badge size="sm" variant="outline" className={cx.badge} bg="white">
                Preview
            </Badge>
        </Box>
    );
};

export { ImagePreview };
