import React, { FC, forwardRef } from 'react';

import { Loader, UnstyledButton, UnstyledButtonProps } from '@mantine/core';

import cx from './styles.module.css';

const SimpleButton: FC<
    {
        onClick?: () => void;
        disabled?: boolean;
        children: React.ReactNode;
        variant?: 'light' | 'dark';
        loading?: boolean;
        fullWidth?: boolean;
    } & UnstyledButtonProps
> = forwardRef(({ onClick, children, variant = 'light', loading, fullWidth, ...props }, ref) => (
    <UnstyledButton
        {...props}
        className={[
            cx.button,
            variant === 'dark' && cx.dark,
            loading && cx.loading,
            fullWidth && cx.fullWidth,
            props.className,
        ]
            .filter(Boolean)
            .join(' ')}
        onClick={onClick}
        loading={loading}
        /* @ts-ignore */
        ref={ref}
    >
        {children}
        {loading && <Loader size={10} className={cx.loader} />}
    </UnstyledButton>
));

SimpleButton.displayName = 'SimpleButton';

export { SimpleButton };
