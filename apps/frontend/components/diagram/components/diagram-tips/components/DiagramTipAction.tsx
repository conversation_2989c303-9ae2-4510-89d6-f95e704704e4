import { createPolymorphicComponent, UnstyledButton, UnstyledButtonProps } from '@mantine/core';

import { IoInformationCircleOutline } from 'react-icons/io5';

import cx from './DiagramTipAction.module.css';

type Props = UnstyledButtonProps & { children: any };

const DiagramTipAction = createPolymorphicComponent<'button', Props>(({ children, ...props }: Props) => {
    return (
        <UnstyledButton {...props} className={cx.root}>
            <IoInformationCircleOutline size={16} />
            {children}
        </UnstyledButton>
    );
});

export { DiagramTipAction };
