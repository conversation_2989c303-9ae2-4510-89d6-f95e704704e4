import React from 'react';

import { Box, Group, Stack, Text } from '@mantine/core';

import cx from './DatasheetTable.module.css';
import { IoChevronForward } from 'react-icons/io5';

const DatasheetTable = ({
    header,
    children,
}: {
    header: {
        prefix?: React.ReactNode[];
        label: React.ReactNode;
        rightSection?: React.ReactNode;
    };
    children: React.ReactNode;
}) => {
    if (!React.Children.toArray(children).length) {
        return null;
    }

    return (
        <Stack className={cx.table} gap={0}>
            {header && (
                <Group className={cx.header} gap={4}>
                    {header.prefix?.map((prefix) => (
                        <>
                            <span>{prefix}</span>
                            <IoChevronForward
                                style={{
                                    color: 'var(--mantine-color-gray-5)',
                                }}
                            />
                        </>
                    ))}
                    <Text span inherit className={cx.headerTitle}>
                        {header.label}
                    </Text>
                    {header.rightSection && <Box ml="auto">{header.rightSection}</Box>}
                </Group>
            )}
            {children}
        </Stack>
    );
};

export { DatasheetTable };
