import { FC } from 'react';

import { <PERSON>, Button } from '@mantine/core';

import { ComponentSection, getComponentDefinition } from '@repo/dcide-component-models';

import { Datasheet, DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { ComponentDatasheetSection } from './ComponentDatasheetSection';

import { ComponentDatasheetPort } from '../ComponentDatasheetPort';
import { ComponentDatasheetSectionProgress } from '../ComponentDatasheetSectionProgress';

import { CapacityField } from 'components/component-fields/CapacityField';
import { IsolationVoltageField } from 'components/component-fields/IsolationVoltageField';
import { CheckboxField } from 'components/forms/fields/CheckboxField';
import { I2tRatingField } from 'components/component-fields/I2tRatingField';
import { ChargerConnectorsField } from 'components/component-fields/ChargerConnectorsField';
import { PowerField } from 'components/component-fields/PowerField';
import { VoltageField } from 'components/component-fields/VoltageField';
import { CurrentField } from 'components/component-fields/CurrentField';
import { IntegerField } from 'components/forms/fields/IntegerField';
import { ChargeField } from 'components/component-fields/ChargeField';
import { BreakerTypeField } from 'components/component-fields/BreakerTypeField';
import { CanServeAsField } from 'components/component-fields/CanServeAsField';
import { SolarTechnologiesField } from 'components/component-fields/SolarTechnologiesField';
import { BatteryTechnologiesField } from 'components/component-fields/BatteryTechnologiesField';
import { useComponentContext } from '../../hooks/use-component-context';
import { ElectricalCable } from 'components/component-datasheet/components/sections/Electrical.Cable';

import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { IoAdd } from 'react-icons/io5';

import { useDatasheetPorts } from 'components/component-datasheet/hooks/use-datasheet-ports';
import { useDatasheetElectrical } from 'components/component-datasheet/hooks/use-datasheet-electrical';

const Electrical: FC = () => {
    const { component } = useComponentContext();
    const { addPort } = useDatasheetPorts();
    const electrical = useDatasheetElectrical();
    const mode = useDatasheetMode();

    const componentDefinition = getComponentDefinition(component.type);

    if (!componentDefinition || !ComponentHelpers.componentTypeHasElectrical(component.type)) {
        return null;
    }

    if (component.type === 'cable') {
        return <ElectricalCable />;
    }

    const hasOtherElectricalSpecifications = 'isolationVoltage' in electrical;

    return (
        <ComponentDatasheetSection
            id={ComponentSection.ELECTRICAL}
            title="Electrical"
            afterTitle={<ComponentDatasheetSectionProgress section={ComponentSection.ELECTRICAL} />}
        >
            <Datasheet.Table header={{ prefix: ['Electrical'], label: 'General' }}>
                {'batteryTechnologies' in electrical && (
                    <Datasheet.TableRow label="Battery Technology" name="electrical.batteryTechnologies">
                        <BatteryTechnologiesField name="electrical.batteryTechnologies" />
                    </Datasheet.TableRow>
                )}
                {'energyCapacity' in electrical && (
                    <Datasheet.TableRow name="electrical.energyCapacity" label="Energy Capacity">
                        <CapacityField name={`electrical.energyCapacity`} />
                    </Datasheet.TableRow>
                )}
                {'chargeCapacity' in electrical && (
                    <Datasheet.TableRow name="electrical.chargeCapacity" label="Charge Capacity">
                        <ChargeField name={`electrical.chargeCapacity`} />
                    </Datasheet.TableRow>
                )}
                {'charging' in electrical && <ChargingRows charging={electrical.charging} />}
                {'polaritySensitive' in electrical && (
                    <Datasheet.TableRow label="Polarity Sensitive" name="electrical.polaritySensitive">
                        <CheckboxField name={`electrical.polaritySensitive`} />
                    </Datasheet.TableRow>
                )}
                {'meltingI2t' in electrical && (
                    <Datasheet.TableRow label="Melting i²t Rating" name="electrical.meltingI2t">
                        <I2tRatingField name="electrical.meltingI2t" />
                    </Datasheet.TableRow>
                )}
                {'arcingI2t' in electrical && (
                    <Datasheet.TableRow label="Arcing i²t Rating" name="electrical.arcingI2t">
                        <I2tRatingField name="electrical.arcingI2t" />
                    </Datasheet.TableRow>
                )}
                {'totalClearingI2t' in electrical && (
                    <Datasheet.TableRow label="Total Clearing i²t Rating" name="electrical.totalClearingI2t">
                        <I2tRatingField name="electrical.totalClearingI2t" />
                    </Datasheet.TableRow>
                )}
                {'breakerType' in electrical && (
                    <Datasheet.TableRow label="Breaker Type" name="electrical.breakerType">
                        <BreakerTypeField name="electrical.breakerType" />
                    </Datasheet.TableRow>
                )}
                {'canServeAsOptions' in componentDefinition && componentDefinition.canServeAsOptions && (
                    <Datasheet.TableRow label="Can serve as" name="electrical.breakerType">
                        <CanServeAsField name="canServeAs" componentTypes={componentDefinition.canServeAsOptions} />
                    </Datasheet.TableRow>
                )}
                {'solarTechnologies' in electrical && (
                    <Datasheet.TableRow label="Solar Technology" name="electrical.solarTechnologies">
                        <SolarTechnologiesField name="electrical.solarTechnologies" />
                    </Datasheet.TableRow>
                )}
                {'parallelableCapacity' in electrical && (
                    <Datasheet.TableRow label="Parallelable Capacity" name="electrical.parallelableCapacity">
                        <IntegerField name="electrical.parallelableCapacity" />
                    </Datasheet.TableRow>
                )}
            </Datasheet.Table>
            {electrical.ports.map((port: any, index: number) => (
                <ComponentDatasheetPort index={index} label={`Port ${index + 1}`} key={`${index}-${port.id}`} />
            ))}

            {(mode === DatasheetMode.EDIT || mode === DatasheetMode.CREATE || mode === DatasheetMode.BULK) &&
                electrical.ports.length < componentDefinition.ports.max && (
                    <Box mb="sm">
                        <Button onClick={() => addPort()} size="xs" leftSection={<IoAdd />}>
                            Add Port {electrical.ports.length + 1} Specifications
                        </Button>
                    </Box>
                )}

            {hasOtherElectricalSpecifications && (
                <Datasheet.Table header={{ prefix: ['Electrical'], label: 'Other' }}>
                    {'isolationVoltage' in electrical && (
                        <Datasheet.TableRow name="electrical.isolationVoltage" label="Isolation Voltage">
                            <IsolationVoltageField name={`electrical.isolationVoltage`} />
                        </Datasheet.TableRow>
                    )}
                </Datasheet.Table>
            )}
        </ComponentDatasheetSection>
    );
};

const ChargingRows = ({ charging }: { charging: any }) => {
    return (
        <>
            {'connectors' in charging && (
                <Datasheet.TableRow name="electrical.charging" label="Connector Types">
                    <ChargerConnectorsField name={`electrical.charging.connectors`} />
                </Datasheet.TableRow>
            )}
            {'power' in charging && (
                <Datasheet.TableRow name="electrical.charging.power" label="Charging Power">
                    <PowerField name={'electrical.charging.power'} fields={['min', 'nom', 'max']} />
                </Datasheet.TableRow>
            )}
            {'voltage' in charging && (
                <Datasheet.TableRow name="electrical.charging.voltage" label="Charging Voltage">
                    <VoltageField name={'electrical.charging.voltage'} fields={['min', 'max']} />
                </Datasheet.TableRow>
            )}
            {'current' in charging && (
                <Datasheet.TableRow name="electrical.charging.current" label="Charging Current">
                    <CurrentField name={'electrical.charging.current'} fields={['min', 'max']} />
                </Datasheet.TableRow>
            )}
            {'numberOfConnectors' in charging && (
                <Datasheet.TableRow name="electrical.charging.numberOfConnectors" label="Number of Connectors">
                    <IntegerField name="electrical.charging.numberOfConnectors" min={1} allowNegative={false} />
                </Datasheet.TableRow>
            )}
        </>
    );
};

export { Electrical };
