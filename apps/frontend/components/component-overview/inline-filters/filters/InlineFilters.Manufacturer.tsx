import React, { FC, useState } from 'react';

import { ActionIcon, Box, Group, Menu, Text, TextInput } from '@mantine/core';
import { IoBriefcaseOutline, IoClose } from 'react-icons/io5';

import { PublishedStatus } from '@repo/dcide-component-models';

import { useCompanyProfiles } from 'hooks';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';

import { SearchService } from 'components/component-overview/services/SearchService';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { CompanyLogo } from 'components/company-logo';

import Fuse from 'fuse.js';

import cx from './InlineFilters.module.css';

type Props = {
    isViewOnly?: boolean;
    isRequired?: boolean;
    onSelectOptional?: (id: string) => void;
};

const InlineFiltersManufacturer: FC<Props> = ({ isViewOnly, isRequired, onSelectOptional }) => {
    const [search, setSearch] = useState('');

    const { manufacturer: selectedManufacturerId } = useComponentSearchFilters();

    const { companies: manufacturers, isLoading } = useCompanyProfiles({ status: PublishedStatus.PUBLISHED });

    const selectedManufacturers = manufacturers.filter(({ id }) =>
        typeof selectedManufacturerId === 'string'
            ? id === selectedManufacturerId
            : selectedManufacturerId?.includes(id),
    );

    const onSelect = (id: string) => {
        SearchService.setFilter('manufacturer', id);
        if (onSelectOptional) onSelectOptional(id);
    };

    const onRemove = () => {
        SearchService.setFilter('manufacturer', undefined);
    };

    if (!isLoading && !manufacturers.length) return null;

    const fuzzy = new Fuse(manufacturers, {
        keys: ['name'],
        threshold: 0.5,
    });

    const filteredManufacturers = search.length ? fuzzy.search(search).map((result) => result.item) : manufacturers;

    return (
        <InlineFilters.Section
            isViewOnly={isViewOnly}
            active={!!selectedManufacturers.length}
            label={
                selectedManufacturers.length ? undefined : (
                    <Group gap={8}>
                        <IoBriefcaseOutline size={12} />
                        Manufacturer
                    </Group>
                )
            }
            body={selectedManufacturers ? selectedManufacturers.map(({ name }) => name).join(', ') : undefined}
            onRemove={selectedManufacturers.length && !isRequired ? onRemove : undefined}
            popoverContent={
                isLoading ? (
                    <Menu.Item disabled>
                        <Text c="dimmed">Loading...</Text>
                    </Menu.Item>
                ) : (
                    <>
                        <TextInput
                            value={search}
                            className={cx.search}
                            placeholder="Search manufacturer"
                            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                                setSearch(event.currentTarget.value)
                            }
                            rightSection={
                                search && (
                                    <ActionIcon onClick={() => setSearch('')} size="xs" variant="light">
                                        <IoClose />
                                    </ActionIcon>
                                )
                            }
                        />

                        {filteredManufacturers.map((manufacturer) => (
                            <Menu.Item
                                key={manufacturer.id}
                                onClick={() => onSelect(manufacturer.id)}
                                leftSection={
                                    <CompanyLogo
                                        logos={manufacturer.logos}
                                        width={20}
                                        fallback={<Box w={20} h={20} bg="gray.1" />}
                                    />
                                }
                            >
                                {manufacturer.name}
                            </Menu.Item>
                        ))}

                        {manufacturers.length && !filteredManufacturers.length && (
                            <Menu.Item disabled>
                                <Text c="dimmed">No manufacturers found</Text>
                            </Menu.Item>
                        )}
                    </>
                )
            }
        />
    );
};

export { InlineFiltersManufacturer };
