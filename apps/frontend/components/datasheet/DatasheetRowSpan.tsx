import { Box, Group, MantineStyleProps, Stack, Text } from '@mantine/core';

import cx from './DatasheetTable.module.css';
import { IoChevronForward } from 'react-icons/io5';

type Props = MantineStyleProps & {
    leftSection?: React.ReactNode | string;
    color?: string;
    children?: React.ReactNode;
    header: {
        prefix?: React.ReactNode[];
        label: React.ReactNode;
    };
};

const DatasheetRowSpan = ({ header, children, color }: Props) => {
    if (!children) {
        return null;
    }

    if (!header) {
        return children;
    }

    return (
        <Box className={cx.rowSpan} style={{ '--color': color }}>
            {header && (
                <Group className={cx.subheader} gap={4}>
                    {header.prefix?.map((prefix) => (
                        <>
                            <span>{prefix}</span>
                            <IoChevronForward
                                style={{
                                    color: 'var(--mantine-color-gray-5)',
                                }}
                            />
                        </>
                    ))}
                    <Text inherit span c={color}>
                        {header.label}
                    </Text>
                </Group>
            )}
            <Stack gap={0} className={cx.content}>
                {children}
            </Stack>
        </Box>
    );
};

export { DatasheetRowSpan };
