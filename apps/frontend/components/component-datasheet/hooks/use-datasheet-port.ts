import { useEffect, useState } from 'react';

import { useWatch } from 'react-hook-form';

import { Component } from '@repo/dcide-component-models';

const useDatasheetPort = (index: number) => {
    const values = useWatch({
        name: `electrical.ports.${index}`,
    }) as Component['electrical']['ports'][number];

    let initVoltageType: 'AC' | 'DC' | null = null;

    if (values && 'AC' in values && values.AC.enabled) {
        initVoltageType = 'AC';
    } else if (values && 'DC' in values && values.DC.enabled) {
        initVoltageType = 'DC';
    }

    const [voltageType, setVoltageType] = useState<'AC' | 'DC' | null>(initVoltageType);

    useEffect(() => {
        if (voltageType !== initVoltageType) {
            setVoltageType(initVoltageType);
        }
    }, [initVoltageType]);

    const hasAC = values && 'AC' in values;
    const hasDC = values && 'DC' in values;

    const showAC = values && hasAC && values.AC.enabled;
    const showDC = values && hasDC && values.DC.enabled;

    return { hasAC, hasDC, showAC, showDC, values, voltageType, setVoltageType };
};

export { useDatasheetPort };
