import React from 'react';

import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    UnitWithTooltip,
    useRenderVariableMultiInput,
    MeasurementInputWrapper,
} from 'components/forms/fields/measurement';
import { MeasurementFieldType, currentConverter } from '@repo/dcide-component-models';

type CurrentFieldProps = {
    name: string;
    fields?: MeasurementFieldType[];
    hideIcons?: boolean;
} & MeasurementInputWrapperProps;

const CurrentField = ({ name, fields = ['nom', 'max'], hideIcons, ...wrapperProps }: CurrentFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: fieldsRecord,
    } = useMultiUnitMeasurementController({ name, converter: currentConverter });

    const Unit = <UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>;

    const Fields = useRenderVariableMultiInput({
        fields,
        fieldsRecord,
        Unit,
        extraProps: {
            hideIcon: hideIcons,
            size: wrapperProps.size,
        },
    });

    return <MeasurementInputWrapper {...wrapperProps}>{Fields}</MeasurementInputWrapper>;
};

export { CurrentField };
