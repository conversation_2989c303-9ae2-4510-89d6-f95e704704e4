import React, { FC, useEffect, useRef } from 'react';
import type { IntercomChannel, IntercomMessage } from '@repo/dcide-component-models';

import { Box, Loader, ScrollArea, UnstyledButton } from '@mantine/core';

import { IntercomContent } from 'components/intercom/Intercom.Content';
import { IntercomComposer } from 'components/intercom/Intercom.Composer';
import { IntercomEmpty } from 'components/intercom/Intercom.Empty';

import { Avatar } from 'components/avatar/Avatar';
import { TipTapViewer } from 'components/tiptap/TipTapViewer';

import { IntercomService } from 'services/IntercomService';

import { useIntercomChannelMessages } from './hooks/use-intercom-channel-messages';
import { useUser } from 'hooks/use-user';

import { DateService } from 'services/DateService';
import { useTeam } from 'hooks/use-team';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useIsUserPartOfCompany } from 'components/intercom/hooks/use-is-user-part-of-company';
import { useCurrentUser } from 'hooks/use-current-user';
import { intercomState } from 'components/intercom/state/intercom';
import { IntercomNavigation } from 'components/intercom/Intercom.Navigation';

import cx from './Intercom.module.css';

const IntercomChannel: FC<{ channel: IntercomChannel }> = ({ channel }) => {
    const viewport = useRef<HTMLDivElement>(null);
    const { messages, isLoading } = useIntercomChannelMessages(channel.id);

    const { team } = useTeam(channel.team);
    const { company } = useCompanyProfile(channel.access.company);

    const userPartOfCompany = useIsUserPartOfCompany(company);

    let title = channel.name || '';

    if (userPartOfCompany && team) {
        title = `Chat with ${team.name}`;
    }

    if (!userPartOfCompany && company) {
        title = `Chat with ${company.name}`;
    }

    const back = () => {
        intercomState.activeChannel = null;
    };

    const reply = async (content: any, files: any) => {
        await IntercomService.reply({
            channelId: channel.id,
            content,
            files,
            type: channel.type,
            userPartOfCompany: Boolean(userPartOfCompany),
        });
    };

    useEffect(() => {
        if (channel.viewed === false) {
            IntercomService.viewChannel(channel.id).then();
        }
    }, [channel.viewed, messages.length]);

    useEffect(() => {
        setTimeout(() => {
            if (viewport.current) {
                viewport.current.scrollTo({
                    top: viewport.current.scrollHeight,
                    behavior: 'smooth',
                });
            }
        }, 250);
    }, [messages.length]);

    return (
        <React.Fragment>
            {team && (
                <IntercomNavigation
                    title={title}
                    handleBackClick={userPartOfCompany ? back : undefined}
                    team={team}
                    company={company}
                />
            )}
            <IntercomContent>
                {isLoading ? (
                    <IntercomEmpty>
                        <Loader />
                    </IntercomEmpty>
                ) : (
                    <ScrollArea type="scroll" h={315} viewportRef={viewport}>
                        <Box className={cx.messages}>
                            {messages.map((message) => (
                                <Message message={message} key={message.id} />
                            ))}
                        </Box>
                    </ScrollArea>
                )}
            </IntercomContent>
            <IntercomComposer onSubmit={reply} />
        </React.Fragment>
    );
};

const Message: FC<{ message: IntercomMessage }> = ({ message }) => {
    const currentUser = useCurrentUser();

    const { user } = useUser(message.createdBy);
    const { company } = useCompanyProfile(message.createdByCompany);

    const { content, files } = message.content[0];

    const unlink = async () => {
        await IntercomService.unlinkCompany(message.id);
    };

    return (
        <Box className={cx.message}>
            {company ? <Avatar company={company} size={32} /> : <Avatar user={message.createdBy} size={32} />}
            <Box>
                <Box className={cx.messageHeader}>
                    {company?.name ?? user?.name}
                    <small>{DateService.formatDistanceToNow(message.createdAt)}</small>
                    {currentUser?.internal && company && (
                        <UnstyledButton onClick={unlink}>Unlink company</UnstyledButton>
                    )}
                </Box>
                <TipTapViewer content={JSON.parse(content)} files={files} key={message.id} />
            </Box>
        </Box>
    );
};

export { IntercomChannel };
