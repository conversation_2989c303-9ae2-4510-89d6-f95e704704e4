import React, { FC } from 'react';

import Link from 'next/link';
import { GetServerSideProps } from 'next';

import { Box, Button, Group, MantineProvider } from '@mantine/core';
import { IoArrowForward } from 'react-icons/io5';

import { ComponentCount, ManufacturerProfile } from '@repo/dcide-component-models';

import { ComponentService } from 'services/ComponentService';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { CompanyProfileService } from 'services/CompanyProfileService';

import { useFile } from 'hooks/use-file';

import { getParam } from 'helpers/getParam';

import { CompanyLogo } from 'components/company-logo';
import { ComponentOverview } from 'components/component-overview';
import { ReplusHome } from 'components/company-profile/pages/ReplusHome';

import cx from './styles.module.css';

const ReplusPage: FC<{
    manufacturer: ManufacturerProfile;
    componentCount?: ComponentCount;
}> = ({ manufacturer, componentCount }) => {
    const { file: cover } = useFile(manufacturer.cover);

    const content = (
        <Box className={cx.root}>
            <ComponentOverview
                heroBg={cover?.url}
                title={
                    <Group align="flex-end" mx="auto" maw={1000}>
                        <CompanyLogo logos={{ large: manufacturer.logos.large }} />
                        <Button
                            c="white"
                            variant="light"
                            rightSection={<IoArrowForward />}
                            style={{ border: '1px solid rgba(255, 255, 255, 0.1)' }}
                            component={Link}
                            href={CompanyProfileHelpers.urls.view(manufacturer.slug)}
                        >
                            Visit profile
                        </Button>
                    </Group>
                }
                componentCount={componentCount}
                initialContent={<ReplusHome company={manufacturer} />}
            />
        </Box>
    );

    if (!manufacturer.color) {
        return content;
    }

    return (
        <MantineProvider
            theme={{
                colors: {
                    brand: Array(10).fill(manufacturer.color) as any,
                },
            }}
        >
            {content}
        </MantineProvider>
    );
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const slug = getParam(params, 'slug');

    if (!slug) {
        return { notFound: true };
    }

    const manufacturers = await CompanyProfileService.getBySlug(slug);
    const manufacturer = manufacturers?.docs?.[0] ?? null;

    if (!manufacturer) {
        return { notFound: true };
    }

    const { success, count } = await ComponentService.getCount();

    return {
        props: {
            manufacturer,
            componentCount: success ? count : undefined,
        },
    };
};

export default ReplusPage;
