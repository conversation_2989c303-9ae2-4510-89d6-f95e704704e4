import React, { FC, useCallback, useState } from 'react';

import { useDiagramMode } from 'components/diagram/hooks';
import { useDrag } from '@use-gesture/react';

import { Point } from '@repo/dcide-component-models';
import { useHotkeys } from 'react-hotkeys-hook';

import { DiagramService } from 'components/diagram/services/DiagramService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { GroupService } from 'components/diagram/services/GroupService';
import { ViewportService } from 'components/diagram/services';

import cx from './DiagramGroups.module.css';

const DiagramGroupOverlay = () => {
    const { mode } = useDiagramMode();

    const [start, setStart] = useState<Point | null>(null);
    const [mousePosition, setMousePosition] = useState<Point | null>(null);

    const reset = useCallback(() => {
        setStart(null);
        setMousePosition(null);
        DiagramService.setMode('edit');
    }, [setStart, setMousePosition]);

    const newGroupDrag = useDrag<MouseEvent>(
        ({ movement: [movementX, movementY], last, tap }) => {
            if (!start) {
                return;
            }

            if (tap && last) {
                reset();
                return;
            }

            const mouseX = start.x + movementX * (1 / ViewportService.transform.scale);
            const mouseY = start.y + movementY * (1 / ViewportService.transform.scale);

            setMousePosition({
                x: mouseX,
                y: mouseY,
            });

            if (last) {
                const position = {
                    x: Math.min(start.x, mouseX),
                    y: Math.min(start.y, mouseY),
                };

                const width = Math.abs(mouseX - start.x);
                const height = Math.abs(mouseY - start.y);

                GroupService.create(position, width, height);
                DiagramSyncService.save();

                reset();
            }
        },
        { filterTaps: true },
    );

    const isAddGroupMode = mode === 'add-group';
    const isDragging = Boolean(start);

    useHotkeys(
        'esc',
        () => {
            reset();
        },
        {
            enabled: isAddGroupMode,
        },
    );

    return (
        <svg
            style={{
                position: 'absolute',
                left: 0,
                top: 0,

                zIndex: isAddGroupMode ? 99 : 82,

                width: '100%',
                height: '100%',

                touchAction: 'none',
                pointerEvents: isAddGroupMode ? 'all' : 'none',
                cursor: isAddGroupMode ? 'crosshair' : undefined,
            }}
            onMouseDown={
                !isDragging
                    ? (event) => {
                          setStart({ x: event.nativeEvent.offsetX, y: event.nativeEvent.offsetY });
                          setMousePosition(null);
                      }
                    : undefined
            }
            {...(isAddGroupMode ? newGroupDrag() : {})}
        >
            {start && mousePosition ? <DrawRect start={start} mousePosition={mousePosition} /> : null}
        </svg>
    );
};

const DrawRect: FC<{ start: Point; mousePosition: Point }> = ({ start, mousePosition }) => {
    const {
        position: { x, y },
        width,
        height,
    } = getPositionAndDimensionsFromTwoPoints(start, mousePosition);

    return <rect className={cx.drawGroup} x={x} y={y} width={width} height={height} />;
};

const getPositionAndDimensionsFromTwoPoints = (a: Point, b: Point) => ({
    position: { x: Math.min(a.x, b.x), y: Math.min(a.y, b.y) },
    width: Math.abs(b.x - a.x),
    height: Math.abs(b.y - a.y),
});

export { DiagramGroupOverlay };
