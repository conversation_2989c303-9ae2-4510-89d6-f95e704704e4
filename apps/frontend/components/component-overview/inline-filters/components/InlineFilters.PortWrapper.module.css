.root {
    --inline-filters-border-radius: var(--mantine-radius-sm);

    border-radius: var(--mantine-radius-sm);

    > * {
        border-radius: 0;

        &:not(:last-child) {
            margin-right: -2px;
        }

        &:first-child {
            border-top-left-radius: var(--inline-filters-border-radius);
            border-bottom-left-radius: var(--inline-filters-border-radius);
        }

        &:last-child {
            border-top-right-radius: var(--inline-filters-border-radius);
            border-bottom-right-radius: var(--inline-filters-border-radius);
        }
    }

    &[data-active="true"] {
        outline: 2px solid var(--inline-filters-active-border-color-secondary);

        > * {
            border-color: var(--inline-filters-active-border-color);
            outline: none;
        }
    }

    align-items: stretch;
}
