import React, { useMemo } from 'react';
import { useWatch } from 'react-hook-form';

import { TbDots } from 'react-icons/tb';
import { IoHelpCircleSharp } from 'react-icons/io5';
import { ActionIcon, Anchor, Box, Flex, Popover, Tooltip } from '@mantine/core';

import { DatasheetMode, useDatasheetMode } from './DatasheetMode';
import { DatasheetRowMeta } from './DatasheetRowMeta';

import {
    ComponentFieldDisplay,
    ComponentFieldDisplayLabels,
    ComponentFieldMeta,
    isAlternativeDisplay,
    VoltageType,
} from '@repo/dcide-component-models';
import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';
import { FieldVisibility, useFieldVisibility } from 'components/component-datasheet/hooks/use-field-visibility';
import { Datasheet } from 'components/datasheet/Datasheet';

import cx from './DatasheetTable.module.css';

type DatasheetTableRowProps = {
    voltageType?: 'AC' | 'DC';
    label: React.ReactNode;
    description?: string;
    children: React.ReactNode;
    name: string;
    noMetadata?: boolean;
    alwaysShow?: boolean;
};

const DatasheetTableRow = (props: DatasheetTableRowProps) => {
    const { diff, overrides } = useComponentBulkFields();

    if (diff.includes(props.name) && !overrides.includes(props.name)) {
        return <DifferingFieldTableRow {...props} />;
    }

    if ('noMetadata' in props) {
        return <SimpleDatasheetTableRow {...props} />;
    }

    return <MetadataDatasheetTableRow {...props} />;
};

const SimpleDatasheetTableRow = ({
    voltageType,
    label,
    description,
    name,
    alwaysShow,
    children,
}: DatasheetTableRowProps) => {
    const mode = useDatasheetMode();
    const visibility = useFieldVisibility(name);

    const Content =
        mode === DatasheetMode.VIEW ? <Datasheet.FormattedValue name={name} fallback={children} /> : children;

    return (
        <Box className={cx.row} data-voltage-type={voltageType}>
            <DatasheetTableRowLabel voltageType={voltageType} label={label} description={description} />
            <DatasheetTableRowContent>
                {visibility === FieldVisibility.VISIBLE || alwaysShow ? Content : 'Not specified'}
            </DatasheetTableRowContent>
            {mode !== DatasheetMode.VIEW && <Flex data-datasheet-actions style={{ width: '20px' }}></Flex>}
        </Box>
    );
};

const MetadataDatasheetTableRow = ({ voltageType, label, name, description, children }: DatasheetTableRowProps) => {
    const mode = useDatasheetMode();
    const meta = (useWatch({ name: `metadata.${name}` }) as ComponentFieldMeta) ?? {};

    const FieldCellContent = useMemo(() => {
        const Content =
            mode === DatasheetMode.VIEW ? <Datasheet.FormattedValue name={name} fallback={children} /> : children;

        if (isAlternativeDisplay(meta.display)) {
            return <Datasheet.TableValue>{ComponentFieldDisplayLabels[meta.display]}</Datasheet.TableValue>;
        }

        return Content;
    }, [mode, meta.display, children]);

    if (mode === DatasheetMode.VIEW && meta.display === ComponentFieldDisplay.HIDDEN) {
        return null;
    }

    return (
        <Box className={cx.row} data-voltage-type={voltageType}>
            <DatasheetTableRowLabel voltageType={voltageType} label={label} description={description} />
            <DatasheetTableRowContent>{FieldCellContent}</DatasheetTableRowContent>
            {mode !== DatasheetMode.VIEW && <DatasheetRowRightSection name={name} />}
        </Box>
    );
};

const DifferingFieldTableRow = ({ voltageType, label, name, description }: DatasheetTableRowProps) => {
    const { addOverride } = useComponentBulkFields();

    return (
        <Box className={cx.row} data-voltage-type={voltageType}>
            <DatasheetTableRowLabel voltageType={voltageType} label={label} description={description} />
            <DatasheetTableRowContent>
                This field is not the same accross all components.{' '}
                <Anchor onClick={() => addOverride(name)}>Override?</Anchor>
            </DatasheetTableRowContent>
            <DatasheetRowRightSection name={name} />
        </Box>
    );
};

const DatasheetTableRowLabel = ({
    label,
    description,
}: {
    voltageType?: VoltageType;
    description?: string;
    label: React.ReactNode;
}) => (
    <Box className={cx.label}>
        {label}
        {description && (
            <Tooltip label={description} multiline maw={200}>
                <ActionIcon size="xs" variant="transparent" my={8}>
                    <IoHelpCircleSharp />
                </ActionIcon>
            </Tooltip>
        )}
    </Box>
);

const DatasheetTableRowContent = ({ children }: { children: React.ReactNode }) => (
    <Box className={cx.content}>{children}</Box>
);

const DatasheetRowRightSection = ({ name }: { name: string }) => (
    <Box className={cx.rightSection} data-datasheet-actions>
        <Popover width={400} position="left-start" withArrow arrowSize={9}>
            <Popover.Target>
                <ActionIcon size="xs" variant="subtle" my={8}>
                    <TbDots />
                </ActionIcon>
            </Popover.Target>
            <Popover.Dropdown>
                <DatasheetRowMeta name={name} />
            </Popover.Dropdown>
        </Popover>
    </Box>
);

export { DatasheetTableRow };
