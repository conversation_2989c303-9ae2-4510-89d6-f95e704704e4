import { useState } from 'react';
import { useController } from 'react-hook-form';

import {
    Box,
    FileButton,
    Input,
    InputWrapperProps,
    Image,
    Button,
    Space,
    Loader,
    UnstyledButton,
    ButtonProps,
    Text,
    Flex,
} from '@mantine/core';
import { IoCloudUploadOutline, IoTrashOutline } from 'react-icons/io5';

import { FileService, FileSizeLimitExceededError } from 'services/FileService';

import { useFile } from 'hooks/use-file';

import cx from './ImageField.module.css';
import { LocalNotificationService } from 'services/LocalNotificationService';

type ImageFieldProps = InputWrapperProps & {
    name: string;
    group: string;
    buttonLabel: string;
    buttonProps?: ButtonProps;
    showPreview?: boolean;
};

const ImageField = ({ name, group, buttonLabel, buttonProps, showPreview = true, ...props }: ImageFieldProps) => {
    const {
        field: { value, onChange },
        fieldState: { error },
    } = useController({ name });

    const [uploading, setUploading] = useState(false);
    const { file } = useFile(value);

    const handleUpload = async (file: File) => {
        try {
            setUploading(true);

            const serverFile = await FileService.create({
                file,
                group,
            });

            onChange(serverFile.id);
        } catch (error) {
            if (error instanceof FileSizeLimitExceededError) {
                LocalNotificationService.showError({ title: error.title, message: error.message });
            }

            console.log(error);
        } finally {
            setUploading(false);
        }
    };

    const handleDelete = () => {
        onChange('');
    };

    const UploadButton = (
        <FileButton data-file-button={name} onChange={(file) => file && handleUpload(file)} accept="image/*">
            {(button) => (
                <Button
                    {...button}
                    variant="default"
                    leftSection={<IoCloudUploadOutline />}
                    loading={uploading}
                    {...buttonProps}
                >
                    {buttonLabel}
                </Button>
            )}
        </FileButton>
    );

    if (!showPreview) {
        return (
            <Flex gap={4}>
                {UploadButton}
                {file && (
                    <Button onClick={handleDelete} {...buttonProps} leftSection="" color="red" c="red">
                        <IoTrashOutline />
                    </Button>
                )}
            </Flex>
        );
    }

    return (
        <Input.Wrapper
            {...props}
            error={
                <Text component="span" inherit mt={4}>
                    {error?.message}
                </Text>
            }
        >
            {uploading || file ? (
                <Box className={cx.wrapper}>
                    {uploading && <Loader size="xs" color="dimmed" />}
                    {file && (
                        <>
                            <Image src={file?.url || ''} fit="cover" alt="" className={cx.image} />
                            <UnstyledButton className={cx.delete} onClick={handleDelete}>
                                <IoTrashOutline size={20} />
                            </UnstyledButton>
                        </>
                    )}
                </Box>
            ) : (
                <Box>
                    <Space h="4" />
                    {UploadButton}
                </Box>
            )}
        </Input.Wrapper>
    );
};

export { ImageField };
