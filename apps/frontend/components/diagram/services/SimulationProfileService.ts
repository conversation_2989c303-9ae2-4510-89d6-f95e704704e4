import {Diagram} from '@repo/dcide-component-models';

import { mutate} from 'swr';
import { ApiService } from 'services/ApiService';

import { SimulationProfileHelpers } from 'components/diagram/helpers/SimulationProfileHelpers';

import { simulationProfilesState  } from 'components/diagram/state/simulation-profiles';

import { config } from 'config';

export type SimulationProfile = {
    id: string;
    name: string;
    type: 'load' | 'generation' | 'combined';
    data: string;
    project: string;
    diagram: string;
    createdAt: string;
    updatedAt: string;
};

const SimulationProfileService = {
    create: async (diagram: Diagram, name: string, type: 'load' | 'generation') => {
        const response = await ApiService.post<{
            doc: SimulationProfile;
        }>(`${config.api.backend}/diagramSimulationProfiles`, {
            name,
            type,
            diagram: diagram.id,
            project: diagram.project,
        });

        await mutate(SimulationProfileHelpers.swr.list(diagram.id));

        return response;
    },

    list: async (diagram: Diagram) => {
        return ApiService.get<{
            docs: SimulationProfile[];
        }>(`${config.api.backend}/diagramSimulationProfiles?where[diagram][equals]=${diagram.id}`);
    },

    get: async (id: string) => {
        return ApiService.get<
            SimulationProfile
        >(`${config.api.backend}/diagramSimulationProfiles/${id}`);
    },

    update: async (id: string, data: Partial<SimulationProfile>) => {
        await ApiService.patch(`${config.api.backend}/diagramSimulationProfiles/${id}`, data);
        await mutate(SimulationProfileHelpers.swr.get(id));
    },

    activate: (id: string) => {
        simulationProfilesState.active = id;
    },

    deactivate: () => {
        simulationProfilesState.active = null;
    },
};

export { SimulationProfileService };
