import React, { FC } from 'react';

import { Stack, Tabs } from '@mantine/core';

import { useActiveTab } from 'components/horizontal-tabs/hooks/useActiveTab';

import { HorizontalTabsTabs } from 'components/horizontal-tabs/HorizontalTabs.Tabs';

export type HorizontalTabType = {
    value: string;
    label: React.ReactNode;
    disabled?: boolean;
    icon?: React.ReactNode;
    content?: React.ReactNode;
};

type Props = {
    tabs: HorizontalTabType[];
    topSection?: React.ReactNode;
    isSticky?: boolean;
    keepMounted?: boolean;
};

const HorizontalTabs: FC<Props> & {
    Tabs: typeof HorizontalTabsTabs;
} = ({ tabs, topSection, isSticky, keepMounted }) => {
    const { activeTab, defaultTab } = useActiveTab(tabs);

    if (!activeTab) {
        return null;
    }

    return (
        <HorizontalTabs.Tabs tabs={tabs} value={activeTab} isSticky={isSticky} keepMounted={keepMounted}>
            <Stack gap={0} w="100%">
                {topSection}

                {tabs.map((tab) => (
                    <Tabs.Panel key={`panel-${tab.value}`} value={tab.value} data-active={tab.value === defaultTab}>
                        {tab.content}
                    </Tabs.Panel>
                ))}
            </Stack>
        </HorizontalTabs.Tabs>
    );
};

HorizontalTabs.Tabs = HorizontalTabsTabs;

export { HorizontalTabs };
