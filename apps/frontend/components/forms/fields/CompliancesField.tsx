import { useC<PERSON>roller, useFormContext, useWatch } from 'react-hook-form';

import { Grid, Input, InputWrapperProps, Text, Tooltip, UnstyledButton } from '@mantine/core';

import { Compliance, Component } from '@repo/dcide-component-models';

import { TextField } from 'components/forms/fields/TextField';
import { GridSectionProps } from 'components/section/GridSection';
import { CE, CurrentOS, EmergeAlliance, ODCA, UL } from 'components/icons';

import cx from './GridItemField.module.css';

type CompliancesFieldProps = InputWrapperProps & {
    name: string;
    showCompliances?: Compliance[];
} & GridSectionProps;

const COMPLIANCES: Record<
    string,
    {
        icon: React.ReactNode;
        tooltip?: string;
        colSpan?: number;
    }
> = {
    CE: {
        icon: <CE />,
        tooltip: 'This product is CE compliant',
    },
    UL: {
        icon: <UL />,
        tooltip: 'This product is UL compliant',
    },
    currentOS: {
        icon: <CurrentOS />,
        tooltip: 'We are a member of CurrentOS',
        colSpan: 2,
    },
    emergeAlliance: {
        icon: <EmergeAlliance />,
        tooltip: 'We are a member of EMerge Alliance',
        colSpan: 2,
    },
    ODCA: {
        icon: <ODCA />,
        tooltip: 'We are a member of ODCA',
        colSpan: 2,
    },
    other: {
        icon: (
            <Text ta="center" fz="sm" fw={600}>
                Other DC Microgrid Organization
            </Text>
        ),
        tooltip: 'Specify other',
        colSpan: 2,
    },
};

const CompliancesField = ({ name, nbCols = 8, showCompliances, ...props }: CompliancesFieldProps) => {
    const compliances = useWatch({
        name,
        defaultValue: {},
    }) as Component['compliance'];

    const { fieldState } = useController({ name });
    const { setValue } = useFormContext();

    const shownCompliances = showCompliances
        ? Object.entries(COMPLIANCES).filter(([key]) => showCompliances?.includes(key as Compliance))
        : Object.entries(COMPLIANCES);

    const showOtherInput = showCompliances?.includes(Compliance.OTHER) && compliances.other;

    return (
        <>
            <Input.Wrapper {...props} error={fieldState?.error?.message}>
                <Grid gutter={8} columns={nbCols} mt={8}>
                    {shownCompliances.map(([key, { icon, tooltip, colSpan = 1 }]) => {
                        const selected = compliances[key as Compliance];

                        return (
                            <Grid.Col
                                key={key}
                                span={{
                                    base: colSpan === 1 ? 2 : 4,
                                    xs: colSpan,
                                }}
                            >
                                <Tooltip
                                    label={tooltip}
                                    disabled={!tooltip}
                                    style={{
                                        textWrap: 'wrap',
                                    }}
                                >
                                    <UnstyledButton
                                        onClick={() => {
                                            setValue(`${name}.${key}`, !selected);
                                        }}
                                        data-selected={selected}
                                        className={cx.root}
                                        p="md"
                                    >
                                        {icon}
                                    </UnstyledButton>
                                </Tooltip>
                            </Grid.Col>
                        );
                    })}
                </Grid>
            </Input.Wrapper>
            {showOtherInput && <TextField name={`${name}.otherInput`} label="Specify other" />}
        </>
    );
};

export { CompliancesField };
