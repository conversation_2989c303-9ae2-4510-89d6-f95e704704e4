import { forwardRef } from 'react';

import { Button, ButtonProps, createPolymorphicComponent } from '@mantine/core';

import cx from './SubtleButton.module.css';

const SubtleButtonInner = forwardRef<HTMLButtonElement, ButtonProps>(({ children, ...props }, ref) => (
    <Button ref={ref} classNames={cx} variant="transparent" {...props}>
        {children}
    </Button>
));

SubtleButtonInner.displayName = 'SubtleButton';

export const SubtleButton = createPolymorphicComponent<'button', ButtonProps>(SubtleButtonInner);
