import React from 'react';

import useSWRImmutable from 'swr/immutable';

import { Stack } from '@mantine/core';
import { useHash } from '@mantine/hooks';

import { Component, ComponentApplication, SortType } from '@repo/dcide-component-models';

import { SearchService } from 'components/component-overview/services/SearchService';

import { ComponentLanding } from 'components/component-landing/ComponentLanding';
import { ComponentService } from 'services/ComponentService';
import { ComponentOverviewHit } from 'components/component-overview';
import { GridSection } from 'components/section/GridSection';

const ComponentLandingDCMicrogridProducts = () => {
    const [, setHash] = useHash();

    const { data: products = [] } = useSWRImmutable<Component[]>('dc-microgrid-products', async () => {
        const result = await ComponentService.search({
            application: [ComponentApplication.DC_MICROGRID],
            sort: SortType.CREATED_AT,
            limit: 12,
        });

        return result?.docs;
    });

    if (!products?.length) {
        return null;
    }

    const setDCMicrogridFilter = () => {
        SearchService.setFilter('application', [ComponentApplication.DC_MICROGRID]);
        setHash('products');
    };

    return (
        <Stack gap="xs">
            <ComponentLanding.Title
                rightSection={<ComponentLanding.Link label="View all" onClick={setDCMicrogridFilter} />}
                onClick={setDCMicrogridFilter}
            >
                Discover DC Products
            </ComponentLanding.Title>

            <GridSection nbCols={4}>
                {products.map((product) => (
                    <ComponentOverviewHit key={product.id} showBooth showEmptyImage component={product} />
                ))}
            </GridSection>
        </Stack>
    );
};

export { ComponentLandingDCMicrogridProducts };
