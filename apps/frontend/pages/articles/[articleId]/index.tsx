import { getParam } from 'helpers';

import { GetServerSideProps } from 'next';

import { ArticleService } from 'services/ArticleService';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

const Article = () => {
    return null;
};

export const getServerSideProps: GetServerSideProps = async ({ params }) => {
    const articleId = getParam(params, 'articleId')!;
    const article = await ArticleService.get(articleId);

    if (!article || !article.company) {
        return {
            notFound: true,
        };
    }

    const hash = article.type === 'caseStudy' ? '#caseStudies' : article.type === 'promotion' ? '#promos' : '';

    return {
        redirect: {
            destination: CompanyProfileHelpers.urls.view(article.company) + `?article=${article.id}${hash}`,
            permanent: true,
        },
    };
};

export default Article;
