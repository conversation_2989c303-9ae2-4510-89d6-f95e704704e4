import React, { FC } from 'react';

import { <PERSON>, <PERSON><PERSON>, Card, Text } from '@mantine/core';
import { IoSettingsOutline } from 'react-icons/io5';

import { EmptyMessage } from 'components/empty-message/EmptyMessage';
import { SupportCenterChannel } from 'components/support-center/SupportCenterChannel';
import { InAppSupportModal } from 'components/company-profile/components/InAppSupportModal';
import { Section } from 'components/section/Section';
import { ExhibitorMatchLeads } from 'components/exhibitor-match-leads/ExhibitorMatchLeads';
import { ShowtimeLeads } from 'components/showtime-leads/ShowtimeLeads';

import { CompanyService } from '@repo/dcide-component-models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useSupportCenter } from 'hooks/use-support-center';
import { useDisclosure } from '@mantine/hooks';
import { useTeam } from 'hooks/use-team';
import { useCurrentUser } from 'hooks/use-current-user';

import cx from './styles.module.css';

const SupportCenter: FC = () => {
    const user = useCurrentUser();

    const company = useCurrentProfile();
    const { team } = useTeam(company?.team);

    const {
        supportCenterChannels: [supportCenterChannel],
    } = useSupportCenter(company || undefined);

    const enabled = company?.services.includes(CompanyService.IN_APP_SUPPORT);

    const [supportOpened, supportHandlers] = useDisclosure();

    return team && company ? (
        <Card classNames={cx}>
            {supportOpened && <InAppSupportModal company={company} onClose={supportHandlers.close} />}

            {user?.developer && <ShowtimeLeads />}

            <ExhibitorMatchLeads company={company} />

            <Section
                title="Lead Management Center"
                titleSection={
                    enabled && (
                        <Button
                            size="compact-xs"
                            variant="transparent"
                            onClick={supportHandlers.open}
                            leftSection={<IoSettingsOutline />}
                        >
                            Manage Support Users
                        </Button>
                    )
                }
            >
                <Text c="dimmed" mb="xs">
                    View and respond to user messages—all from one place. Manage updates and keep every lead moving
                    forward.
                </Text>

                {enabled ? (
                    supportCenterChannel ? (
                        <SupportCenterChannel supportChannel={supportCenterChannel} />
                    ) : (
                        <Box h="100vh">
                            <EmptyMessage>
                                Share your profile to connect with potential leads and start building relationships
                                today.
                                <br />
                                Check back here for messages from your leads or look out for email notifications.
                                <br />
                            </EmptyMessage>
                        </Box>
                    )
                ) : (
                    <EmptyMessage>
                        You haven&apos;t enabled in-app support yet.
                        <br />
                        <Button onClick={supportHandlers.open}>Enable In-App Support</Button>
                    </EmptyMessage>
                )}
            </Section>
        </Card>
    ) : null;
};

export { SupportCenter };
