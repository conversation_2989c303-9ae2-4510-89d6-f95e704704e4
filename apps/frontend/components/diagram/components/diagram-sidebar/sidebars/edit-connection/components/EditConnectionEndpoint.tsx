import React, { FC, useState } from 'react';

import { get } from 'radash';

import {
    Box,
    Button,
    Divider,
    Flex,
    Popover,
    CSSProperties,
    UnstyledButton,
    useMantineTheme,
    Stack,
} from '@mantine/core';

import { TbSelector } from 'react-icons/tb';

import { FormatHelpers } from 'helpers/formatters';
import { VoltageConverter } from 'units/voltage';
import { CurrentConverter } from 'units/current';
import { PowerConverter } from 'units/power';

import { PortOption } from 'components/diagram/services';

import { SelectionService } from 'components/diagram/services/SelectionService';

import { useConnectionPorts } from 'components/diagram/hooks';

import { DiagramComponentInstance, DiagramConnection } from '@repo/dcide-component-models';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import { useEditConnectionContext } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/context';

const EditConnectionEndpoint: FC<{
    endpoint: keyof Pick<DiagramConnection, 'from' | 'to'>;
}> & {
    PortOption: FC<{
        portOption: PortOption;
    }>;
    SpecificationTable: FC<{
        style?: CSSProperties;
        children: React.ReactNode;
    }>;
    SpecificationTableRow: FC<{
        label: string;
        value?: string | React.ReactNode;
        rightSection?: React.ReactNode;
        highlighted?: boolean;
    }>;
} = ({ endpoint }) => {
    const theme = useMantineTheme();
    const { connection, fromComponentInstance, toComponentInstance } = useEditConnectionContext();

    const { port: portKey } = connection[endpoint];
    const componentInstance = {
        from: fromComponentInstance,
        to: toComponentInstance,
    }[endpoint];

    const powerFlowDirection = get(
        componentInstance,
        `configuration.ports.${portKey}.powerFlowDirection`,
    ) as DiagramComponentInstance['configuration']['ports'][number]['powerFlowDirection'];

    const [opened, setOpened] = useState(false);

    const { options, selected, updatePort } = useConnectionPorts({
        componentInstance,
        connection,
        endpoint,
    });

    return (
        <Flex gap={4}>
            <Stack
                gap={0}
                align="center"
                justify="center"
                style={{
                    width: 48,
                    padding: 4,

                    border: `1px solid ${theme.colors.gray[2]}`,
                    borderRadius: theme.radius.xs,
                }}
                onClick={() => {
                    SelectionService.selectComponentInstance(componentInstance.id);
                }}
            >
                <Box style={{ width: 24, height: 24 }}>
                    <ComponentIcon type={componentInstance.componentType} />
                </Box>
                <Box fz="10" style={{ weight: 600, textAlign: 'center', marginTop: '4px' }}>
                    {componentInstance.designator}
                </Box>
            </Stack>
            <Box style={{ flexGrow: 1, flexShrink: 1 }}>
                <Popover
                    width="target"
                    opened={opened}
                    onClose={() => {
                        setOpened(false);
                    }}
                >
                    <Popover.Target>
                        {selected ? (
                            <UnstyledButton
                                style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',

                                    width: '100%',
                                    height: 36,

                                    padding: theme.spacing.sm,

                                    fontSize: 12,
                                    fontWeight: 600,

                                    color: theme.colors.gray[7],

                                    backgroundColor: theme.colors.gray[0],
                                    border: `1px solid ${theme.colors.gray[2]}`,
                                    borderRadius: theme.radius.xs,
                                }}
                                onClick={() => {
                                    if (options.length > 1) {
                                        setOpened(!opened);
                                    }
                                }}
                            >
                                <span>
                                    {selected.port.label}
                                    {selected.port.purpose && (
                                        <span style={{ fontWeight: 400 }}>
                                            {' • '}
                                            {selected.port.purpose}
                                        </span>
                                    )}
                                </span>
                                {options.length > 1 && <TbSelector size={15} />}
                            </UnstyledButton>
                        ) : (
                            <UnstyledButton
                                style={{
                                    display: 'flex',
                                    justifyContent: 'center',
                                    alignItems: 'center',

                                    width: '100%',
                                    height: 48,

                                    fontSize: 12,
                                    fontWeight: 600,

                                    backgroundColor: theme.colors.gray[0],
                                    color: theme.colors.gray[7],

                                    border: `1px dashed ${theme.colors.gray[2]}`,
                                    borderRadius: theme.radius.xs,
                                }}
                                onClick={() => {
                                    setOpened(!opened);
                                }}
                            >
                                Select a port
                            </UnstyledButton>
                        )}
                    </Popover.Target>
                    {selected && (
                        <EditConnectionEndpoint.SpecificationTable
                            style={{
                                padding: theme.spacing.xs,

                                fontSize: theme.fontSizes.xs,

                                border: `1px solid ${theme.colors.gray[2]}`,
                                borderTop: 0,
                                borderRadius: `0 0 ${theme.radius.xs} ${theme.radius.xs}`,
                            }}
                        >
                            <EditConnectionEndpoint.SpecificationTableRow
                                label="Voltage"
                                value={FormatHelpers.formatMinNomMax(
                                    selected.specifications?.voltage,
                                    VoltageConverter,
                                )}
                            />
                            <EditConnectionEndpoint.SpecificationTableRow
                                label="Current"
                                value={FormatHelpers.formatMinNomMax(
                                    selected.specifications?.current,
                                    CurrentConverter,
                                )}
                            />
                            <EditConnectionEndpoint.SpecificationTableRow
                                label="Power"
                                value={FormatHelpers.formatMinNomMax(selected.specifications?.power, PowerConverter)}
                            />
                            <EditConnectionEndpoint.SpecificationTableRow
                                label="Power Flow"
                                value={FormatHelpers.formatPowerFlowDirection(powerFlowDirection || '')}
                            />
                            <Button
                                size="compact-sm"
                                variant="light"
                                color="primary"
                                style={{
                                    marginTop: theme.spacing.xs,

                                    fontSize: theme.fontSizes.xs,
                                }}
                                onClick={() => {
                                    SelectionService.selectComponentInstance(componentInstance.id);
                                }}
                            >
                                Edit specifications
                            </Button>
                        </EditConnectionEndpoint.SpecificationTable>
                    )}
                    <Popover.Dropdown>
                        {options.map((option, index) => (
                            <React.Fragment key={option.port.key}>
                                {index > 0 && <Divider my="xs" />}
                                <UnstyledButton
                                    style={{
                                        width: '100%',
                                        opacity: option.connected ? 0.25 : undefined,
                                    }}
                                    onClick={() => {
                                        if (option.connected) {
                                            return;
                                        }

                                        updatePort(option.port.key);
                                        setOpened(false);
                                    }}
                                >
                                    <EditConnectionEndpoint.PortOption portOption={option} />
                                </UnstyledButton>
                            </React.Fragment>
                        ))}
                    </Popover.Dropdown>
                </Popover>
            </Box>
        </Flex>
    );
};

EditConnectionEndpoint.PortOption = ({ portOption }) => (
    <EditConnectionEndpoint.SpecificationTable>
        <EditConnectionEndpoint.SpecificationTableRow
            label={portOption.port.label}
            value={portOption.port.purpose ?? ' '}
            highlighted
        />
        <EditConnectionEndpoint.SpecificationTableRow
            label="Voltage"
            value={FormatHelpers.formatMinNomMax(portOption.specifications.voltage, VoltageConverter)}
        />
        <EditConnectionEndpoint.SpecificationTableRow
            label="Current"
            value={FormatHelpers.formatMinNomMax(portOption.specifications.current, CurrentConverter)}
        />
        <EditConnectionEndpoint.SpecificationTableRow
            label="Power"
            value={FormatHelpers.formatMinNomMax(portOption.specifications.power, PowerConverter)}
        />
    </EditConnectionEndpoint.SpecificationTable>
);
EditConnectionEndpoint.PortOption.displayName = 'PortOption';

EditConnectionEndpoint.SpecificationTable = ({ style, children }) => {
    return (
        <Flex
            direction="column"
            style={(theme) => ({
                ...style,
                fontSize: theme.fontSizes.xs,
            })}
        >
            {children}
        </Flex>
    );
};

EditConnectionEndpoint.SpecificationTable.displayName = 'SpecificationTable';

EditConnectionEndpoint.SpecificationTableRow = ({ label, value, rightSection, highlighted }) => {
    return (
        <Flex align="center" style={{ minHeight: 18 }}>
            <Box
                style={{
                    flexGrow: 0,
                    flexShrink: 0,

                    width: 72,

                    fontWeight: highlighted ? 800 : 600,
                    lineHeight: 1,

                    whiteSpace: 'nowrap',
                }}
            >
                {label}
            </Box>
            <Box>{value || '-'}</Box>
            {rightSection && <Box style={{ marginLeft: 'auto', marginRight: 0 }}>{rightSection}</Box>}
        </Flex>
    );
};

EditConnectionEndpoint.SpecificationTableRow.displayName = 'SpecificationTableRow';

export { EditConnectionEndpoint };
