import { z } from 'zod';

import type { Endpoint } from 'payload';
import { collectionsAreEqual, handleEndpointError } from '../../../helpers';
import { OrderAvailabilities, OrderStatus } from '@repo/dcide-component-models';
import { BadRequestError, ForbiddenError } from '../../../responses/errors';

const draftModeSchema = z.object({
    componentId: z.string(),
    quantity: z.number().int().optional(),
});

const reviewModeSchema = z.object({
    componentId: z.string(),
    price: z.number().optional(),
    leadTime: z.number().int().optional(),
    availability: z.enum(OrderAvailabilities).optional(),
    notes: z.string().optional(),
});

const updateComponent: Endpoint = {
    path: '/:id/components/update',
    method: 'post',
    handler: async (request) => {
        try {
            if (!request.user) {
                throw new ForbiddenError();
            }

            const order = await request.payload.findByID({
                collection: 'orders',
                id: request.routeParams?.id as string,
                depth: 0,
            });

            const bodyParser = getBodyParser(order.status);

            const { componentId, ...update } = bodyParser.parse(await request.json!());

            const updatedOrder = await request.payload.update({
                collection: 'orders',
                id: order.id,
                data: {
                    components: order.components?.map((component) => {
                        if (collectionsAreEqual(component.component, componentId)) {
                            if ('quantity' in update) {
                                delete component.availability;
                                delete component.leadTime;
                                delete component.price;
                                delete component.notes;
                            }

                            return { ...component, ...update };
                        }

                        return component;
                    }),
                },
            });

            return Response.json(updatedOrder);
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

const getBodyParser = (status: OrderStatus | null | undefined) => {
    switch (status) {
        case 'draft':
            return draftModeSchema;
        case 'quoteRequested':
        case 'orderSubmitted':
        case 'orderAcknowledged':
        case 'orderInProgress':
            return reviewModeSchema;

        default:
            throw new BadRequestError('Order is not editable');
    }
};

export { updateComponent };
