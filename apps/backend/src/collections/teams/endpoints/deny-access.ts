import type { Endpoint } from 'payload';
import { z } from 'zod';

import { ForbiddenError } from '../../../responses/errors';
import { getAuthenticatedRequestUser, handleEndpointError } from '../../../helpers';
import { NotificationKey, PermissionTeam } from '@repo/dcide-component-models';
import { NotificationService } from '../../../services/NotificationService';
import { JsonSuccessResponse } from '../../../responses/JsonSuccessResponse';
import { intersects } from 'radash';
import { TeamService } from '@/services/TeamService';

const denyAccess: Endpoint = {
    path: '/:id/deny-access',
    method: 'post',
    handler: async (request) => {
        try {
            const user = await getAuthenticatedRequestUser(request);

            const { user: deniedUserId, message } = z
                .object({
                    user: z.string(),
                    message: z.string(),
                })
                .parse(await request.json!());

            const team = await TeamService.get(request.routeParams?.id as string);
            const hasPermission = team.users.some((entry) => {
                return (
                    entry.user === user.id &&
                    intersects(entry.newPermissions ?? [], [PermissionTeam.OWNER, PermissionTeam.ADMIN])
                );
            });

            if (!hasPermission) {
                throw new ForbiddenError('You do not have permission to deny access to this team');
            }

            await NotificationService.create({
                team: team.id,
                from: user.id,
                to: deniedUserId,
                notification: {
                    type: NotificationKey.TEAM_DENY_ACCESS,
                    data: {
                        teamId: team.id,
                        userId: user.id,
                        message,
                    },
                },
            });

            return JsonSuccessResponse();
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { denyAccess };
