import React from 'react';
import { FeatureLimit, getSubscriptionForUnlimitedFeature } from '@repo/dcide-component-models';

import { Box, Loader, Stack } from '@mantine/core';

import { FileWithIcon } from 'elements/file-with-icon/FileWithIcon';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar';

import { FileService as FileModalService } from 'components/diagram/services/FileService';
import { FileService } from 'services/FileService';
import { ProjectDesignService } from 'services/ProjectDesignService';
import { DiagramService } from 'components/diagram/services';

import { useDesignFiles } from 'components/diagram/hooks/use-design-files';
import { useCurrentTeamSubscription } from 'hooks/use-current-team-subscription';

import { SidebarService } from 'components/diagram/services/SidebarService';
import { ComponentChatInputComponent } from 'components/component-chat';
import { DiagramFeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';

import cx from './DesignFiles.module.css';
import { SidebarTab } from 'components/diagram/state/sidebar';
import { useCurrentDesign } from 'hooks/use-current-design';

const DesignFiles = () => {
    const diagramId = DiagramService.getId();
    const design = useCurrentDesign();
    const designId = design?.id;

    const { files, isLoading } = useDesignFiles(designId ?? null);

    const plan = useCurrentTeamSubscription();
    const unlimitedPlan = getSubscriptionForUnlimitedFeature(FeatureLimit.DESIGN_FILE_UPLOADS);

    if (!designId) return null;

    const onDeleteFile = async (fileId: string) => {
        await FileService.deleteFile({
            associateWith: 'design',
            associatedId: designId,
            file: fileId,
        });

        FileModalService.mutateDesignFiles(designId);
    };

    const onRenameFile = async (fileId: string, name: string) => {
        await FileService.update(fileId, { name });
        await FileModalService.mutateDesignFiles(designId);
    };

    const onChangeType = async (designFileId: string, type: string) => {
        await ProjectDesignService.updateFile({
            designId,
            designFileId,
            data: { type },
            designFiles: files,
        });
    };

    return (
        <>
            {plan !== unlimitedPlan && (
                <>
                    <DiagramSidebar.Section>
                        <DiagramFeatureLimitTracker
                            feature={FeatureLimit.DESIGN_FILE_UPLOADS}
                            diagramId={diagramId}
                            p={0}
                        />
                    </DiagramSidebar.Section>
                    <DiagramSidebar.Divider />
                </>
            )}
            <DiagramSidebar.Section>
                {isLoading ? (
                    <Loader size="xs" />
                ) : (
                    <Stack gap="xs">
                        {files.map(({ id: designFileId, file, type }, index) => (
                            <FileWithIcon
                                file={file}
                                type={type}
                                key={index}
                                onDelete={() => onDeleteFile(file.id)}
                                onRename={(name: string) => onRenameFile(file.id, name)}
                                onChangeType={(type: string) => onChangeType(designFileId, type)}
                            />
                        ))}
                        {files.length === 0 && <Box>No diagram files yet</Box>}
                    </Stack>
                )}

                {/* add padding for the chat input */}
                <Box h={150} />
            </DiagramSidebar.Section>
            <Box className={cx.chatInput}>
                <ComponentChatInputComponent
                    textareaProps={{ onClick: () => SidebarService.setActiveTab(SidebarTab.AI) }}
                    sendButtonProps={{ disabled: true }}
                />
            </Box>
        </>
    );
};

export { DesignFiles };
