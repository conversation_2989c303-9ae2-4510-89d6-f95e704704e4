import { useWatch } from 'react-hook-form';

import { Text } from '@mantine/core';

import {
    ComponentFieldDisplayLabels,
    ComponentFieldMeta,
    energyConverter,
    isAlternativeDisplay,
    MeasurementSystem,
    powerConverter,
    chargeConverter,
    voltageConverter,
    currentConverter,
    frequencyConverter,
    efficiencyConverter,
    inductanceConverter,
    capacitanceConverter,
    LengthConverter,
    MassConverter,
    TemperatureConverter,
    ResistancePerUnitLengthConverter,
    InductancePerUnitLengthConverter,
} from '@repo/dcide-component-models';

import { FormatHelpers } from 'helpers/formatters';

import { useDefaultMeasurementSystem } from 'hooks/use-default-measurement-system';

import { Datasheet } from 'components/datasheet/Datasheet';

const getConverterByUnit = (unit: any, measurementSystem: MeasurementSystem) => {
    switch (unit) {
        case 'V':
            return voltageConverter;
        case 'A':
            return currentConverter;
        case 'W':
            return powerConverter;
        case 'F':
            return capacitanceConverter;
        case 'kg':
            return new MassConverter(measurementSystem);
        case 'm':
            return new LengthConverter(measurementSystem);
        case 'Hz':
            return frequencyConverter;
        case 'K':
            return new TemperatureConverter(measurementSystem);
        case '%':
            return efficiencyConverter;
        case 'J':
            return energyConverter;
        case 'H':
            return inductanceConverter;
        case 'C':
            return chargeConverter;
        case 'ohm/m':
            return new ResistancePerUnitLengthConverter(measurementSystem);
        case 'H/m':
            return new InductancePerUnitLengthConverter(measurementSystem);
        default:
            return null;
    }
};

const getValueWithConverter = (value: any, converter: any) => {
    if (value.unit === 'K' && ('min' in value || 'max' in value)) {
        return FormatHelpers.formatTemperatureRange(value, converter);
    }

    if (Object.keys(value).some((key) => ['min', 'nom', 'max'].includes(key))) {
        return FormatHelpers.formatMinNomMax(value, converter);
    }

    if (Object.keys(value).some((key) => ['length', 'width', 'height'].includes(key))) {
        return FormatHelpers.formatDimensions(value, converter);
    }

    if ('value' in value && value.unit) {
        return FormatHelpers.formatValue(value.value, converter, value.unit);
    }

    return null;
};

const getFormattedValue = (value: any, measurementSystem: MeasurementSystem) => {
    const converter = value?.unit && getConverterByUnit(value.unit, measurementSystem);

    if (converter) {
        return getValueWithConverter(value, converter);
    }

    return null;
};

const DatasheetFormattedValue = ({ name, fallback }: { name: string; fallback?: React.ReactNode }) => {
    const measurementSystem = useDefaultMeasurementSystem();
    let displayValues;

    const [value, metadata] = useWatch({ name: [name, `metadata.${name}`] });

    const isMeasurementField = typeof value === 'object' && value !== null && 'unit' in value;

    if (isMeasurementField && metadata) {
        if (allValuesAreTheSameAlternativeDisplay(value, metadata)) {
            const display = Object.values(metadata as Record<string, ComponentFieldMeta>)[0]?.display;

            return (
                <Datasheet.TableValue>
                    {display ? ComponentFieldDisplayLabels[display] : 'Not Specified'}
                </Datasheet.TableValue>
            );
        }

        displayValues = getNonAlternativeDisplayValues(value, metadata);
    } else {
        displayValues = value;
    }

    const formattedValue = getFormattedValue(displayValues, measurementSystem);

    if (formattedValue || displayValues?.unit) {
        return (
            <Datasheet.TableValue>
                {formattedValue ? (
                    <Text inherit>{formattedValue}</Text>
                ) : (
                    <Text inherit c="gray.5">
                        Not specified
                    </Text>
                )}
            </Datasheet.TableValue>
        );
    }

    if (typeof value === 'boolean') {
        return <Datasheet.TableValue>{value ? 'Yes' : 'No'}</Datasheet.TableValue>;
    }

    if (name.includes('cpr')) {
        return <Datasheet.TableValue>{Object.values(value).join('')}</Datasheet.TableValue>;
    }

    return fallback ?? null;
};

const allValuesAreTheSameAlternativeDisplay = (values: any, metadata: Record<string, ComponentFieldMeta>) => {
    const nonUnitKeys = Object.keys(values).filter((key) => key !== 'unit');

    if (nonUnitKeys.length === 0) {
        return false;
    }

    const firstAlternativeDisplay = metadata[nonUnitKeys[0]]?.display;

    return nonUnitKeys.slice(1).every((key) => {
        return key in metadata && metadata[key].display === firstAlternativeDisplay;
    });
};

const getNonAlternativeDisplayValues = (value: any, metadata: Record<string, ComponentFieldMeta>) => {
    const fieldsToRemove = Object.entries(metadata)
        .filter(([, meta]) => isAlternativeDisplay(meta.display))
        .map(([key]) => key);

    return Object.fromEntries(Object.entries(value).filter(([key]) => !fieldsToRemove.includes(key)));
};

export { DatasheetFormattedValue };
