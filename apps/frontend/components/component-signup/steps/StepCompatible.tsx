import { FC } from 'react';

import { Box, Title } from '@mantine/core';

import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { ComponentForm } from 'components/component-form';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';

import { Content } from 'components/component-signup/components/Content';
import { CompatibleWithField } from 'components/forms/fields/compatible-with/CompatibleWithField';
import { CONTENT_WIDTH } from 'components/page/Content';

import cx from './StepX.module.css';

const StepCompatible: FC<ComponentSignupProps> = ({ nextStep, ...props }) => {
    const { component, setComponent } = useOptionalComponentContext();

    return (
        <Box className={cx.root}>
            <ComponentForm
                initialValues={component ?? undefined}
                onUpdate={(updatedComponent) => {
                    setComponent(updatedComponent);
                    nextStep();
                }}
            >
                <Content nextStep={nextStep} {...props} wrapperProps={{ maw: CONTENT_WIDTH }}>
                    <Title order={2} mb="xs">
                        Select compatible products
                    </Title>
                    <CompatibleWithField componentId={component?.id} />
                </Content>
            </ComponentForm>
        </Box>
    );
};

export { StepCompatible };
