import React, { FC } from 'react';

import { Box, Stack } from '@mantine/core';

import {
    type DiagramComponentInstance,
    currentConverter,
    powerConverter,
    voltageConverter,
} from '@repo/dcide-component-models';
import { getComponentDefinition } from '@repo/dcide-component-models';

import { FormatHelpers } from 'helpers/formatters';

import { PortHelpers } from 'components/diagram/helpers/PortHelpers';

import { SpecificationsRow } from '../sections/Specifications.Row';

import cx from './PortFields.module.css';

const PortFieldsView: FC<{
    componentInstance: DiagramComponentInstance;
    port: number;
}> = ({ componentInstance, port }) => {
    const definition = getComponentDefinition(componentInstance.componentType);

    const portSpecifications = PortHelpers.getSpecifications(componentInstance, port);
    const voltageType = PortHelpers.getVoltageTypeConfiguration(componentInstance, port);

    if (!voltageType || !(voltageType in portSpecifications)) {
        return null;
    }

    const voltage = FormatHelpers.formatMinNomMax(
        // @ts-ignore
        portSpecifications[voltageType].voltage,
        voltageConverter,
    );
    const maximumPowerPointVoltage = FormatHelpers.formatValue(
        // @ts-ignore
        portSpecifications[voltageType].maximumPowerPointVoltage,
        voltageConverter,
    );
    const current = FormatHelpers.formatMinNomMax(
        // @ts-ignore
        portSpecifications[voltageType].current,
        currentConverter,
    );
    const maximumPowerPointCurrent = FormatHelpers.formatValue(
        // @ts-ignore
        portSpecifications[voltageType].maximumPowerPointCurrent,
        currentConverter,
    );
    const power = FormatHelpers.formatMinNomMax(
        // @ts-ignore
        portSpecifications[voltageType].power,
        powerConverter,
    );

    // @ts-ignore
    const poles = portSpecifications[voltageType]?.poles;

    // @ts-ignore
    const voltagePerPole = portSpecifications[voltageType].voltagePerPole?.value
        ? FormatHelpers.formatValue(
              // @ts-ignore
              portSpecifications[voltageType].voltagePerPole.value,
              voltageConverter,
          )
        : '';

    const openCircuitVoltage = FormatHelpers.formatValue(
        // @ts-ignore
        portSpecifications[voltageType].openCircuitVoltage,
        voltageConverter,
    );

    const shortCircuitCurrent = FormatHelpers.formatValue(
        // @ts-ignore
        portSpecifications[voltageType].shortCircuitCurrent,
        currentConverter,
    );

    let powerFlowDirection = '';

    if (definition.ports.displayAsSinglePort) {
        powerFlowDirection = portSpecifications.powerFlowDirection === 'input' ? 'Unidirectional' : 'Bidirectional';
    }

    return (
        <Stack gap={6}>
            {voltage && (
                <SpecificationsRow label="Voltage">
                    <Box className={cx.linkedInfo}>{voltage}</Box>
                </SpecificationsRow>
            )}
            {maximumPowerPointVoltage && (
                <SpecificationsRow label="Voltage (MPP)">
                    <Box className={cx.linkedInfo}>{maximumPowerPointVoltage}</Box>
                </SpecificationsRow>
            )}
            {current && (
                <SpecificationsRow label="Current">
                    <Box className={cx.linkedInfo}>{current}</Box>
                </SpecificationsRow>
            )}
            {maximumPowerPointCurrent && (
                <SpecificationsRow label="Current">
                    <Box className={cx.linkedInfo}>{maximumPowerPointCurrent}</Box>
                </SpecificationsRow>
            )}
            {power && (
                <SpecificationsRow label="Power">
                    <Box className={cx.linkedInfo}>{power}</Box>
                </SpecificationsRow>
            )}
            {poles && (
                <SpecificationsRow label="Poles">
                    <Box className={cx.linkedInfo}>
                        {poles}
                        {voltagePerPole && ` • ${voltagePerPole} per pole`}
                    </Box>
                </SpecificationsRow>
            )}
            {powerFlowDirection && (
                <SpecificationsRow label="Power Flow">
                    <Box className={cx.linkedInfo}>{powerFlowDirection}</Box>
                </SpecificationsRow>
            )}
            {openCircuitVoltage && (
                <SpecificationsRow label="Open Circuit Voltage">
                    <Box className={cx.linkedInfo}>{openCircuitVoltage}</Box>
                </SpecificationsRow>
            )}
            {shortCircuitCurrent && (
                <SpecificationsRow label="Short Circuit Current">
                    <Box className={cx.linkedInfo}>{shortCircuitCurrent}</Box>
                </SpecificationsRow>
            )}
            {/* @ts-ignore */}
            {portSpecifications?.isolated !== undefined && (
                <SpecificationsRow label="Isolated">
                    <Box className={cx.linkedInfo}>
                        {FormatHelpers.formatIsolation(
                            //@ts-ignore
                            portSpecifications.isolated,
                        )}
                    </Box>
                </SpecificationsRow>
            )}
        </Stack>
    );
};

export { PortFieldsView };
