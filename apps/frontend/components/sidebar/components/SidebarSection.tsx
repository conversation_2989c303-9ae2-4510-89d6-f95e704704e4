import React, { FC, useEffect, useRef, useState } from 'react';

import { Box, BoxProps, Collapse, Group, Space, Text, Title } from '@mantine/core';
import { IoChevronForward } from 'react-icons/io5';

import { SidebarSectionTitle } from './SidebarSectionTitle';

import { LocalStorageService } from 'services/LocalStorageService';
import { SidebarService } from 'components/diagram/services/SidebarService';

import cx from './SidebarComponents.module.css';

export const SidebarSection: FC<
    {
        title?: React.ReactNode;
        subtitle?: React.ReactNode;
        height?: number | string;
        padding?: number | string;
        children?: React.ReactNode;
        rightSection?: React.ReactNode;
        rightSectionVisibility?: {
            collapsed: boolean;
            expanded: boolean;
        };
        onClick?: () => void;
        onMouseEnter?: () => void;
        onMouseLeave?: () => void;
        collapsable?: {
            key: string;
            initial: boolean;
            enabled: boolean;
        };
        highlightable?: string;
    } & BoxProps
> = ({
    title,
    subtitle,
    padding,
    height,
    children,
    rightSection,
    rightSectionVisibility = {
        collapsed: false,
        expanded: true,
    },
    onClick,
    onMouseEnter,
    onMouseLeave,
    collapsable = {
        key: '',
        initial: false,
        enabled: false,
    },
    highlightable,
    ...rest
}) => {
    const ref = useRef<HTMLDivElement | null>(null);
    let initiallyCollapsed = collapsable.enabled ? collapsable.initial : false;

    if (collapsable.key) {
        const key = collapsable.key;
        const collapsableSections = LocalStorageService.get('collapsable-sections') || {};

        if (collapsableSections[key] !== undefined) {
            initiallyCollapsed = collapsableSections[collapsable.key];
        }
    }

    const [collapsed, setCollapsed] = useState(initiallyCollapsed);

    const toggleCollapsed = () => {
        setCollapsed(!collapsed);

        if (collapsable.key) {
            const key = collapsable.key;
            const collapsableSections = LocalStorageService.get('collapsable-sections') || {};

            collapsableSections[key] = !collapsed;
            LocalStorageService.store('collapsable-sections', collapsableSections);
        }
    };

    useEffect(() => {
        const highlightedSection = SidebarService.getHighlightedSection();

        if (highlightable && highlightable === highlightedSection && ref.current) {
            ref.current.dataset.highlight = 'true';
            SidebarService.clearHighlightedSection();
        }
    }, [highlightable]);

    if (!children) {
        return null;
    }

    return (
        <Box
            className={cx.section}
            style={(theme) => ({
                height,
                padding: padding !== undefined ? padding : `${theme.spacing.md} ${theme.spacing.xs}`,
            })}
            onClick={onClick}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            ref={ref}
            {...rest}
        >
            {title && (
                <React.Fragment>
                    <SidebarSectionTitle onClick={collapsable.enabled ? toggleCollapsed : undefined}>
                        <Group gap="xs">
                            {collapsable.enabled && (
                                <IoChevronForward
                                    size={14}
                                    style={{
                                        transform: collapsed ? 'rotate(0)' : 'rotate(90deg)',
                                        transition: 'transform 0.2s ease',
                                    }}
                                />
                            )}
                            <Title order={3} fw={600} lh="16px">
                                {title}
                            </Title>
                        </Group>
                        {!collapsable && rightSection}
                        {collapsable ? (
                            <React.Fragment>
                                {rightSectionVisibility.collapsed && collapsed && rightSection}
                                {rightSectionVisibility.expanded && !collapsed && rightSection}
                            </React.Fragment>
                        ) : (
                            rightSection
                        )}
                    </SidebarSectionTitle>
                    {subtitle && (
                        <Text fz="xs" c="dimmed">
                            {subtitle}
                        </Text>
                    )}
                    {(!collapsable || !collapsed) && <Space h="xs" />}
                </React.Fragment>
            )}
            {collapsable.enabled ? <Collapse in={!collapsed}>{children}</Collapse> : children}
        </Box>
    );
};
