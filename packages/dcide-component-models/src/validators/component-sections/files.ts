import { z } from 'zod';

import { ComponentFileTypes } from '../../types/ComponentFileType';

const file = z.object({
    id: z.string(),
    name: z.string(),
    group: z.string(),
    filename: z.string(),
    url: z.string().url(),
});

export const files = z
    .array(
        z.object({
            file: z.union([file, file.shape.id]),
            type: ComponentFileTypes.validator,
        }),
    )
    .default([]);
