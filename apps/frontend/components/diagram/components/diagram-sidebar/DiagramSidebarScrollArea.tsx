import React, { <PERSON> } from 'react';

import cx from './DiagramSidebar.module.css';

type Props = {
    children?: React.ReactNode;
    style?: React.CSSProperties;
};

export const DiagramSidebarScrollArea: FC<Props> = ({ style, children }) => {
    return (
        <div className={cx.sidebarScrollArea} style={style} data-diagram-sidebar-scroll-area="">
            {children}
        </div>
    );
};
