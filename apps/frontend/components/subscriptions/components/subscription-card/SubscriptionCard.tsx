import { ReactNode } from 'react';
import Link from 'next/link';

import { Card, Grid, GridCol, Space, Text } from '@mantine/core';

import {
    DesignerSubscription,
    Subscription,
    CompanySubscription,
    SubscriptionBillingCycle,
} from '@repo/dcide-component-models';

import { SubscriptionFeatureList } from '../../SubscriptionFeatureList';
import { SubscriptionButton } from '../SubscriptionButton';
import { SubscriptionHeader } from '../SubscriptionHeader';
import { SubscriptionPromotion } from 'components/subscriptions/SubscriptionPromotion';

import cx from './SubscriptionCard.module.css';
import { useSubscriptionUpdateState } from '../../SubscriptionUpdateContext';

export const StaticSubscriptionCard = ({
    subscriptionToSetOnClick,
    href,
    button,
    showPromotion = false,
    showEndAnnotation = false,
    hideAnnotationBelowSubscriptionButton = true,
    active = false,
    billingCycle,
}: {
    subscriptionToSetOnClick: Subscription;
    href?: string;
    button?: ReactNode;
    showPromotion?: boolean;
    showEndAnnotation?: boolean;
    hideAnnotationBelowSubscriptionButton?: boolean;
    active?: boolean;
    billingCycle?: SubscriptionBillingCycle;
}) => {
    return (
        <GridCol miw={250} maw={{ base: '100%', sm: 350 }} span={{ base: 'auto', sm: 12 }}>
            <Card shadow="none" p="sm" pb="lg" radius="md" classNames={cx} data-active={active}>
                <SubscriptionHeader subscription={subscriptionToSetOnClick} billingCycle={billingCycle} />
                <Space h="md" />
                {button ? (
                    <>
                        {button}
                        <Space h="xs" />
                    </>
                ) : null}
                {hideAnnotationBelowSubscriptionButton ? null : (
                    <>
                        <AnnotationBelowSubsriptionButton subscription={subscriptionToSetOnClick} />
                        <Space h="xs" />
                    </>
                )}
                <SubscriptionFeatureList subscription={subscriptionToSetOnClick} />
                {href && (
                    <>
                        <Space h="md" />
                        <Link href={href} style={{ textDecoration: 'none', color: 'var(--mantine-color-dimmed)' }}>
                            More features...
                        </Link>
                    </>
                )}

                {showPromotion && (
                    <>
                        <Space h="md" />
                        <SubscriptionPromotion subscription={subscriptionToSetOnClick} />
                    </>
                )}

                {showEndAnnotation && (
                    <>
                        <Space h="md" />
                        <CurrentEndAnnotation subscription={subscriptionToSetOnClick} />
                    </>
                )}
            </Card>
        </GridCol>
    );
};

export const SubscriptionCard = ({
    subscriptionToSetOnClick,
    href,
}: {
    subscriptionToSetOnClick: Subscription;
    href?: string;
}) => {
    const { currentSubscription, toBillingCycle } = useSubscriptionUpdateState();

    const isCurrentPlan = subscriptionToSetOnClick === currentSubscription;

    return (
        <StaticSubscriptionCard
            subscriptionToSetOnClick={subscriptionToSetOnClick}
            href={href}
            active={isCurrentPlan}
            button={<SubscriptionButton subscriptionToSetOnClick={subscriptionToSetOnClick} />}
            hideAnnotationBelowSubscriptionButton={false}
            showPromotion={!isCurrentPlan}
            showEndAnnotation={isCurrentPlan}
            billingCycle={toBillingCycle}
        />
    );
};

const AnnotationBelowSubsriptionButton = ({ subscription }: { subscription: Subscription }) => {
    const { currentSubscription } = useSubscriptionUpdateState();

    const isCurrentPlan = subscription === currentSubscription;

    if (subscription === CompanySubscription.FREE || subscription === CompanySubscription.PREMIUM) {
        return (
            <Text c="var(--mantine-color-dimmed)" ta="center" fz="sm">
                {subscription === CompanySubscription.PREMIUM && !isCurrentPlan
                    ? 'Limited offer until Sept 30'
                    : '\u00A0'}
            </Text>
        );
    }

    if (subscription === DesignerSubscription.FREE) {
        return <Text c="var(--mantine-color-dimmed)">&nbsp;</Text>;
    }

    return (
        <Text c="var(--mantine-color-dimmed)" fz="sm">
            Everything in {getLowerPlanName(subscription)} and:
        </Text>
    );
};

const CurrentEndAnnotation = ({ subscription }: { subscription: Subscription }) => {
    if (subscription === CompanySubscription.FREE) {
        return (
            <Text fz="sm" c="dimmed">
                Ready to grow? Consider upgrading to Premium to appear in AI-powered search results and gain insights
                and analytics.
            </Text>
        );
    }

    return null;
};

const getLowerPlanName = (subscription: Subscription) => {
    switch (subscription) {
        case DesignerSubscription.PLUS:
            return 'Free';
        case DesignerSubscription.PRO:
            return 'Plus';
        case CompanySubscription.PREMIUM:
            return 'Free';
    }
};

export const SubscriptionCardGrid = ({ children }: { children: ReactNode }) => (
    <Grid justify="flex-start" align="stretch" type="container">
        {children}
    </Grid>
);
