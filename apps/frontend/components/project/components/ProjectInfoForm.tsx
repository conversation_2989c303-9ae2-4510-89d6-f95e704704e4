import React from 'react';

import { z } from 'zod';
import { unique } from 'radash';

import { <PERSON>ack, Anchor, MultiSelectProps, ActionIcon } from '@mantine/core';
import { IoTrashOutline } from 'react-icons/io5';

import { Component, ProjectSchema, ProjectTemplateSchema } from '@repo/dcide-component-models';

import { ProjectService } from 'services/ProjectService';
import { DiagramService } from 'components/diagram/services';

import { useBillOfMaterials } from 'components/diagram/hooks';
import { useProductsByIds } from 'hooks/use-products-by-ids';
import { useCurrentProject } from 'hooks/use-current-project';

import { Form, FormOnSubmit } from 'components/forms/Form';
import { TextField } from 'components/forms/fields/TextField';
import { MultilineTextField } from 'components/forms/fields/MultilineTextField';
import { TagsField } from 'components/forms/fields/TagsField';
import { ImageField } from 'components/forms/fields/ImageField';
import { FormSubmit } from 'components/forms/FormSubmit';
import { AutoSave } from 'components/forms/AutoSave';
import { MultiSelectField } from 'components/forms/fields/MultiSelectField';
import { AddressAutocompleteField } from 'components/forms/fields/AddressAutocompleteField';

import { ComponentOverviewHitSimple } from 'components/component-overview/ComponentOverviewHit.Simple';

import { APPLICATION_SUGGESTIONS } from 'data/application-suggestions';
import { CurrentTeamProfilesField } from 'components/forms/fields/CurrentTeamProfilesField';

const ProjectInfoValidator = ProjectSchema.pick({
    name: true,
    description: true,
    tags: true,
    location: true,
}).extend({
    thumbnail: ProjectSchema.shape.thumbnail.optional(),
});

export const ProjectInfoValidatorStrict = ProjectSchema.pick({
    name: true,
    description: true,
    tags: true,
    location: true,
    visibility: true,
    thumbnail: true,
    template: true,
}).extend({
    description: ProjectSchema.shape.description._def.innerType.min(
        20,
        'Description must be at least 20 characters long',
    ),
    tags: ProjectSchema.shape.tags._def.innerType.min(3, 'At least 3 tags are required'),
    thumbnail: ProjectSchema.shape.thumbnail._def.innerType._def.innerType.min(1, 'Thumbnail is required'),
    template: ProjectTemplateSchema.extend({
        profile: z.string({
            required_error: 'Choose a profile to publish under',
            invalid_type_error: 'Choose a profile to publish under',
        }),
    }),
});

type ProjectInfoFormValues = z.infer<typeof ProjectInfoValidator>;
export type ProjectInfoFormValuesStrict = z.infer<typeof ProjectInfoValidatorStrict>;

const ProjectInfoForm = ({
    publish: incomingPublish,
    autosave,
    useStrictValidation,
    showPublishFields,
    defaultValues: incomingDefaultValues,
}: {
    publish?: FormOnSubmit<any>;
    autosave?: boolean;
    useStrictValidation?: boolean;
    showPublishFields?: boolean;
    defaultValues?: ProjectInfoFormValues | ProjectInfoFormValuesStrict;
}) => {
    const Validator = useStrictValidation ? ProjectInfoValidatorStrict : ProjectInfoValidator;

    const project = useCurrentProject();

    if (!project) return null;

    const publish: FormOnSubmit<ProjectInfoFormValues | ProjectInfoFormValuesStrict> = async (values) => {
        const payload = Validator.safeParse(values);

        if (payload.success) {
            await ProjectService.update(project.id, payload.data);
        }
    };

    const defaultValues = incomingDefaultValues || ProjectInfoValidator.parse(project);

    return (
        <Form<ProjectInfoFormValues | ProjectInfoFormValuesStrict>
            defaultValues={defaultValues}
            zodSchema={Validator}
            onSubmit={incomingPublish || publish}
        >
            {autosave && <AutoSave instant={['tags']} />}
            <Stack>
                {showPublishFields && (
                    <CurrentTeamProfilesField
                        name="template.profile"
                        label="Publish under profile"
                        emptyMessage="You need a profile to publish a design"
                    />
                )}

                <TextField name="name" label="Title" />
                <MultilineTextField name="description" label="Description" autosize />
                <AddressAutocompleteField name="location" label="Location" />
                <TagsField
                    name="tags"
                    label="Tags"
                    placeholder="Enter tags to specify the application"
                    description='Add your own tags separated with commas, e.g. "solar, ev charging, hybrid microgrid'
                    data={APPLICATION_SUGGESTIONS}
                />

                <ImageField
                    name="thumbnail"
                    label="Thumbnail"
                    buttonLabel="Upload a thumbnail"
                    group={`project:${project.id}:images`}
                    description={
                        <Anchor type="button" onClick={() => DiagramService.downloadAsPng()} fz="sm">
                            Download current diagram as image
                        </Anchor>
                    }
                />

                {showPublishFields && <ReferenceProducts />}

                {!autosave && <FormSubmit size="lg">Publish this design</FormSubmit>}
            </Stack>
        </Form>
    );
};

const ReferenceProducts = () => {
    const project = useCurrentProject();
    const { billOfMaterials, isLoading } = useBillOfMaterials();

    const bomComponents = (billOfMaterials?.components?.map((component) => component.component) ?? []) as Component[];
    const { components: templateComponents } = useProductsByIds(project?.template.components ?? []);

    const components = unique([...templateComponents, ...bomComponents], ({ id }) => id);

    if (isLoading || !components.length) return null;

    const renderMultiSelectOption: MultiSelectProps['renderOption'] = ({ option, checked }) => {
        const selectedComponent = components?.find((component) => component.id === option.value);

        if (!selectedComponent) return null;

        return (
            <ComponentOverviewHitSimple
                component={selectedComponent}
                rightSide={
                    checked && (
                        <ActionIcon component="span" size="xs" color="red" variant="light" ml="auto">
                            <IoTrashOutline />
                        </ActionIcon>
                    )
                }
            />
        );
    };

    return (
        <MultiSelectField
            name="template.components"
            label="Reference products"
            placeholder="Select reference products for your design"
            data={components?.map((component) => ({
                value: component.id,
                label: component.name,
            }))}
            renderOption={renderMultiSelectOption}
        />
    );
};

export { ProjectInfoForm };
