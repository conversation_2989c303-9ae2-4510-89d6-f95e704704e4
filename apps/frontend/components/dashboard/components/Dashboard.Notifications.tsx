import { Box, Stack, Text } from '@mantine/core';

import { useInbox } from 'hooks/use-inbox';

import { Dashboard } from 'components/dashboard/Dashboard';
import { InboxActions } from 'components/inbox/InboxActions';
import { Notification } from 'components/inbox/Notification';

import cx from './Dashboard.Notifications.module.css';

const DashboardNotifications = () => {
    const { notifications, flags } = useInbox();

    const all = flags.read ? notifications : notifications.filter((n) => !n.readAt);

    return (
        <Stack gap="md" p="md">
            <Dashboard.Title rightSection={!!notifications.length && <InboxActions showMenu={false} />}>
                Notifications
            </Dashboard.Title>
            <Stack>
                {!all.length && (
                    <Text c="dimmed" fz="xs">
                        {notifications.length ? 'No unread notifications' : 'No notifications'}
                    </Text>
                )}
                {all.map((notification) => (
                    <Box key={notification.id} className={cx.notification} data-read={!!notification.readAt}>
                        <Notification notification={notification} compact />
                    </Box>
                ))}
            </Stack>
        </Stack>
    );
};

export { DashboardNotifications };
