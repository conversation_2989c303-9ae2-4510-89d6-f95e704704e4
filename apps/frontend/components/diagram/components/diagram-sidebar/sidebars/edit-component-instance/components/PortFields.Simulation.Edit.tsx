import React, { <PERSON> } from 'react';

import { Box, Flex, Stack } from '@mantine/core';

import { getComponentDefinition, DiagramComponentInstance } from '@repo/dcide-component-models';

import { set } from '@repo/dcide-sync-engine';

import { diagram } from 'components/diagram/state/diagram';

import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { PortControlMethodField } from 'components/component-fields/PortControlMethodField';
import { PowerFlowField } from 'components/component-fields/PowerFlowField';

import { CurrentField } from 'components/component-fields/CurrentField';
import { VoltageField } from 'components/component-fields/VoltageField';
import { PowerField } from 'components/component-fields/PowerField';
import { ResistanceField } from 'components/component-fields/ResistanceField';

import { SpecificationsRow } from '../sections/Specifications.Row';
import { SimulationPortWrapper } from '../sections/Simulation.Port';
import { SimulationService } from 'components/diagram/services/SimulationService';
import { PowerVoltageChart } from 'components/power-voltage-chart/PowerVoltageChart';

const PortFieldsSimulationEdit: FC<{
    componentInstance: DiagramComponentInstance;
    port: number;
}> = ({ componentInstance, port }) => {
    const { configuration } = componentInstance;

    const componentDefinition = getComponentDefinition(componentInstance.componentType);

    const portConfiguration = configuration.ports[port];
    const { controlMethod, voltageType } = portConfiguration;

    const children: React.ReactNode[] = [];

    if (componentDefinition.ports.powerFlowDirections.length > 1) {
        children.push(
            <SpecificationsRow label="Power Flow" key="powerFlowDirection">
                <Flex direction="column" w="100%">
                    <PowerFlowField
                        name={`configuration.ports.${port}.powerFlowDirection`}
                        componentDefinition={componentDefinition}
                    />
                </Flex>
            </SpecificationsRow>,
        );
    }

    if (voltageType && componentDefinition.ports.allowedControlMethods?.length) {
        children.push(
            <SpecificationsRow label="Control method" key="controlMethod">
                <Flex direction="column" w="100%">
                    <PortControlMethodField
                        name={`configuration.ports.${port}.controlMethod`}
                        componentType={componentInstance.componentType}
                        voltageType={voltageType}
                    />
                </Flex>
            </SpecificationsRow>,
        );
    }

    if (controlMethod === 'constant-current') {
        children.push(
            <SpecificationsRow label="Current" key="current">
                <Flex direction="column" w="100%">
                    <CurrentField name={`configuration.ports.${port}.current`} fields={['value']} />
                </Flex>
            </SpecificationsRow>,
        );
    }

    if (
        controlMethod === 'constant-voltage' ||
        controlMethod === 'constant-voltage-frequency' ||
        controlMethod === 'maximum-power-point-tracking'
    ) {
        children.push(
            <SpecificationsRow label="Voltage" key="voltage">
                <Flex direction="column" w="100%">
                    <VoltageField name={`configuration.ports.${port}.voltage`} fields={['value']} />
                </Flex>
            </SpecificationsRow>,
        );
    }

    if (controlMethod === 'droop-voltage') {
        children.push(
            <SpecificationsRow label="Droop resistance" key="droopResistance">
                <Flex direction="column" w="100%">
                    <ResistanceField name={`configuration.ports.${port}.droopResistance`} />
                </Flex>
            </SpecificationsRow>,
            <SpecificationsRow label="Current" key="droopCurrent">
                <Box w="100%">
                    <CurrentField name={`configuration.ports.${port}.droopCurrent`} fields={['value']} hideIcons />
                </Box>
            </SpecificationsRow>,
            <SpecificationsRow label="Voltage" key="voltage">
                <Flex direction="column" w="100%">
                    <VoltageField name={`configuration.ports.${port}.voltage`} fields={['value']} />
                </Flex>
            </SpecificationsRow>,
        );
    }

    const hasPowerField = SimulationService.componentHasPowerField(componentInstance);
    const isUncontrolled = controlMethod === null || controlMethod === 'uncontrolled';
    const isConstantPower = controlMethod === 'constant-power';
    const isConstantActiveReactivePower = controlMethod === 'constant-active-reactive-power';

    if ((hasPowerField && isUncontrolled) || isConstantPower || isConstantActiveReactivePower) {
        children.push(
            <SpecificationsRow label="Power" key="power">
                <Flex direction="column" w="100%">
                    <PowerField name={`configuration.ports.${port}.power`} fields={['value']} />
                </Flex>
            </SpecificationsRow>,
        );
    }

    if (controlMethod === 'power-voltage') {
        children.push(
            <PowerVoltageChart
                componentInstanceId={componentInstance.id}
                port={port}
                voltageSeries={configuration.ports[port].voltageSeries ?? undefined}
                powerSeries={configuration.ports[port].powerSeries ?? undefined}
                updateVoltagePowerSeries={(data) => {
                    set(diagram.componentInstances[componentInstance.id].configuration.ports[port], 'voltageSeries', {
                        value: data.voltageSeries.value,
                        unit: 'V',
                    });

                    set(diagram.componentInstances[componentInstance.id].configuration.ports[port], 'powerSeries', {
                        value: data.powerSeries.value,
                        unit: 'W',
                    });

                    DiagramSyncService.save();
                }}
                clearVoltagePowerSeries={() => {
                    diagram.componentInstances[componentInstance.id].configuration.ports[port].voltageSeries = null;
                    diagram.componentInstances[componentInstance.id].configuration.ports[port].powerSeries = null;

                    DiagramSyncService.save();
                }}
            />,
        );
    }

    return children.length ? (
        <SimulationPortWrapper componentInstance={componentInstance} port={port}>
            <Stack gap={6}>{children}</Stack>
        </SimulationPortWrapper>
    ) : null;
};

export { PortFieldsSimulationEdit };
