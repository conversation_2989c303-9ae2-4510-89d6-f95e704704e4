import React, { useEffect, useState } from 'react';

import { Component } from '@repo/dcide-component-models';

import { Box, Button, Stack, Text, UnstyledButton } from '@mantine/core';
import { IoCheckmark } from 'react-icons/io5';

import { DiagramService } from 'components/diagram/services';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { DiagramGenerators, generatorState } from 'components/diagram/state/generator';

import { ComponentOverviewHitSimple } from 'components/component-overview/ComponentOverviewHit.Simple';

import { DiagramGeneratorBack } from './DiagramGenerator.Back';

import cx from './DiagramGenerator.module.css';

const DiagramGeneratorCompatibleProducts = ({
    referenceProducts,
    compatibleProducts,
}: {
    referenceProducts: Component[];
    compatibleProducts: Component[];
}) => {
    const [selectedComponents, setSelectedComponents] = useState<Component[]>(referenceProducts);

    useEffect(() => {
        setSelectedComponents(referenceProducts);
    }, [referenceProducts.length]);

    const toggleComponent = (component: Component) => {
        if (selectedComponents.includes(component)) {
            setSelectedComponents(selectedComponents.filter((selectedComponent) => selectedComponent !== component));
        } else {
            setSelectedComponents([...selectedComponents, component]);
        }
    };

    const generate = () => {
        DiagramService.createDiagramFromComponents(selectedComponents);
        DiagramSyncService.save();

        generatorState.generator = DiagramGenerators.EMPTY;
    };

    return (
        <Box className={cx.productsContent}>
            <DiagramGeneratorBack />

            <h2 className={cx.title} data-size="large">
                Start design with these products?
            </h2>

            <Stack gap={4}>
                <Text fw={600}>Reference products</Text>

                <Box className={cx.productsGrid}>
                    {referenceProducts.map((component) => (
                        <UnstyledButton
                            className={cx.productsGridItem}
                            onClick={() => {
                                toggleComponent(component);
                            }}
                            data-selected={selectedComponents.includes(component)}
                            key={component.type}
                        >
                            <ComponentOverviewHitSimple
                                component={component}
                                rightSide={<IoCheckmark className={cx.selectedIcon} />}
                            />
                        </UnstyledButton>
                    ))}
                </Box>
            </Stack>

            {compatibleProducts.length > 0 && (
                <Stack gap={4}>
                    <Text fw={600}>Compatible products</Text>

                    <Box className={cx.productsGrid}>
                        {compatibleProducts.map((component) => (
                            <UnstyledButton
                                className={cx.productsGridItem}
                                onClick={() => {
                                    toggleComponent(component);
                                }}
                                data-selected={selectedComponents.includes(component)}
                                key={component.type}
                            >
                                <ComponentOverviewHitSimple
                                    component={component}
                                    rightSide={<IoCheckmark className={cx.selectedIcon} />}
                                />
                            </UnstyledButton>
                        ))}
                    </Box>
                </Stack>
            )}

            <Box className={cx.componentsActions}>
                <Button onClick={generate} disabled={selectedComponents.length === 0}>
                    Generate diagram
                </Button>
            </Box>
        </Box>
    );
};

export { DiagramGeneratorCompatibleProducts };
