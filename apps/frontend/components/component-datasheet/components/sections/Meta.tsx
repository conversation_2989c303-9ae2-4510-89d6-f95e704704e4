import React, { FC } from 'react';

import { Box, Group, Stack, Text } from '@mantine/core';

import { ComponentSection } from '@repo/dcide-component-models';

import { useComponentContext } from '../../hooks/use-component-context';

import { ComponentDatasheetHeaderImagesWithModal } from 'components/component-datasheet/components/ComponentDatasheetHeaderImages';
import { HideWhenNoValidChildrenWrapper } from 'components/HideWhenNoValidChildrenWrapper';

import { GeneralView } from './General.View';
import { Compliances } from './Compliances';

import cx from './Meta.module.css';

const Meta: FC = () => {
    const { component } = useComponentContext();

    const images = (component.images || []).filter((image: any) => Boolean(image.file));
    const nbImages = images.length;

    return (
        <Box id={ComponentSection.GENERAL} className={cx.root} data-images={!!nbImages}>
            <Stack className={cx.content} flex={1}>
                {component.description && (
                    <Text style={{ maxWidth: 640, whiteSpace: 'pre-wrap' }}>{component.description}</Text>
                )}

                <Group>
                    <Compliances />
                    <GeneralView />
                </Group>
            </Stack>

            {!!nbImages && (
                <HideWhenNoValidChildrenWrapper>
                    <Box className={cx.imagesInner}>
                        <ComponentDatasheetHeaderImagesWithModal images={component.images} />
                    </Box>
                </HideWhenNoValidChildrenWrapper>
            )}
        </Box>
    );
};

export { Meta };
