import { useState } from 'react';
import z from 'zod';

import { <PERSON>ton, Card, Collapse, Group, Stack, Text, Textarea, TextInput } from '@mantine/core';

import { config } from 'config';

import { ApiService } from 'services/ApiService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { FormSubmissionType } from '@repo/dcide-component-models';

import cx from './PlanPage.module.scss';

const SAMPLE_PROMPTS = [
    {
        label: 'Products',
        prompt: 'I am looking for a 600 kWh energy storage system for my commercial building in Texas',
    },
    {
        label: 'Projects',
        prompt: 'I am looking for a project developer for my 5 MW solar project in Florida',
    },
    {
        label: 'Service providers',
        prompt: 'I am looking for a engineering contractor to help me with my 400 kW microgrid in North Carolina',
    },
    {
        label: 'Suppliers',
        prompt: 'I am looking for a supplier of solar panels for my 1 MW project in Georgia',
    },
    // {
    //     label: 'Installers',
    //     prompt: 'I am looking for an installer for my 1 MW EV charging station project near Atlanta, GA',
    // },
];

const emailValidator = z.string().email();

export const PlanPageForm = () => {
    const [submitted, setSubmitted] = useState(false);
    const [feedback, setFeedback] = useState('');
    const [email, setEmail] = useState('');
    const [emailError, setEmailError] = useState<null | string>(null);

    const [placeholder, setPlaceholder] = useState('How can we help you?');

    const submit = async () => {
        try {
            const safeEmail = emailValidator.safeParse(email);

            if (!safeEmail.success) {
                setEmailError('Invalid email address');
                return;
            }

            await ApiService.post(`${config.api.backend}/formSubmissions`, {
                type: FormSubmissionType.REPLUS_PLAN,
                name: email,
                content: { feedback },
            });

            setSubmitted(true);
        } catch (error) {
            LocalNotificationService.showError({
                message: 'Error submitting your request. Please try again later.',
            });
        }
    };

    return (
        <>
            <Collapse in={submitted}>
                <Card className={cx.card}>
                    <Text ta="center" fz="lg" fw="bold">
                        Thank you for your submission! We will get back to you within 24 hours.
                    </Text>
                    <Text>
                        In the meantime, feel free to explore our platform and discover the latest trends in the
                        electric power industry.
                    </Text>
                </Card>
            </Collapse>
            <Collapse in={!submitted}>
                <Stack gap="xl" align="center">
                    <Stack gap={'xs'}>
                        <Textarea
                            rows={4}
                            placeholder={placeholder}
                            value={feedback}
                            onChange={(event) => setFeedback(event.target.value)}
                            className={cx.textarea}
                            miw={600}
                            autoFocus
                        />
                        <Text fz="sm" c="dimmed" ta="left">
                            Whether you’re working on a project, planning one, or just exploring,
                            <br />
                            the more you share, the better we can match you with exhibitors, products, and services.
                        </Text>
                    </Stack>
                    <Group justify="center">
                        {SAMPLE_PROMPTS.map(({ label, prompt }, index) => (
                            <Button
                                key={index}
                                variant="outline"
                                radius="lg"
                                color="white"
                                onClick={() => setFeedback(prompt)}
                                onMouseOver={() => setPlaceholder(prompt)}
                                className={cx.sample}
                            >
                                {label}
                            </Button>
                        ))}
                    </Group>
                    <Collapse in={Boolean(feedback)}>
                        <TextInput
                            placeholder="Enter your email address"
                            value={email}
                            onChange={(event) => setEmail(event.target.value)}
                            onBlur={() => setEmailError(null)}
                            error={emailError}
                            className={cx.email}
                            miw={400}
                        />
                    </Collapse>
                    <Button
                        size="lg"
                        onClick={submit}
                        style={{ maxWidth: 'fit-content', alignSelf: 'center' }}
                        data-disable={!feedback || !email}
                        variant="gradient"
                        className={cx.button}
                    >
                        Get matched
                    </Button>
                </Stack>
            </Collapse>
        </>
    );
};
