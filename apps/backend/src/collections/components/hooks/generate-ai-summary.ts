import { CollectionAfterChangeHook } from 'payload';
import { Component } from '@/payload-types';

import { after } from 'next/server';

import { OpenAI } from 'openai';
import { construct, crush, pick } from 'radash';

import { config } from '@/config';

const filtered = (object: Component) => {
    return pick(object, [
        'name',
        'productSeries',
        'productIdentifier',
        'type',
        'communication',
        'electrical',
        'environmental',
        'mechanical',
        'performance',
    ]);
};

const removeIds = (object: object) =>
    construct(
        Object.fromEntries(
            Object.entries(crush(object)).filter(([key]) => !key.endsWith('.id') && !key.endsWith('._id')),
        ),
    );

const comparable = (object: Component) => {
    return object ? JSON.stringify(removeIds(filtered(object))) : null;
};

const generateAISummary: CollectionAfterChangeHook<Component> = async ({ doc, previousDoc, req }) => {
    const { payload } = req;
    const { apiKey } = config.openai;

    if (!apiKey || (doc.specificationsSummary && comparable(doc) === comparable(previousDoc))) {
        return;
    }

    const openai = new OpenAI({
        apiKey,
    });

    const execute = async () => {
        try {
            const completion = await openai.chat.completions.create({
                model: config.aiModels.default,
                messages: [
                    {
                        role: 'system',
                        content: `
                        You are a technical writer specializing in electrical component documentation.
                        Generate concise, accurate technical summaries.
                    `,
                    },
                    {
                        role: 'user',
                        content: `
                        Please generate a concise technical summary for this component. Here are the specifications:
                        ${JSON.stringify(filtered(doc), null, 2)}

                        The summary should:
                        1. Be 2-3 sentences long
                        2. Focus on key technical specifications
                        3. Highlight unique features or capabilities
                        4. Use professional technical language
                    `,
                    },
                ],
                temperature: 0.4,
                max_tokens: 320,
            });

            await payload.db.collections['components'].updateOne(
                { _id: doc.id },
                {
                    $set: {
                        specificationsSummary: completion.choices[0].message.content,
                    },
                },
            );
        } catch (error) {
            console.error('Error generating AI summary', error);
        }
    };

    after(execute);
};

export { generateAISummary };
