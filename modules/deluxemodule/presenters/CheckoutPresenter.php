<?php

namespace deluxemodule\presenters;

use Craft;
use craft\elements\Entry;
use yii\base\Behavior;

class CheckoutPresenter extends Behavior
{
    public function presentJavaScriptData()
    {
        /** @var Entry $checkout */
        $checkout = $this->owner;

        $methods = [];
        $daysLookup = array('Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat');

        if ($checkout->pickupEnabled) {
            $days = [];
            $timeSlotDuration = $checkout->pickupTimeSlotDuration->value ?? 10;

            for ($i = 0; $i <= 6; $i++) {
                $day = $checkout->pickupCalendar[$i];
                $timeSlots = [];

                if ($day->openingOne && $day->closingOne) {
                    $start = (Int) date_format($day->openingOne, 'G') * 3600 + date_format($day->openingOne, 'i') * 60;
                    $end = (Int) date_format($day->closingOne, 'G') * 3600 + date_format($day->closingOne, 'i') * 60;

                    for ($timeSlot = $start; $timeSlot < $end; $timeSlot += $timeSlotDuration * 60) {
                        $timeSlots[] = $timeSlot;
                    }
                }

                if ($day->openingTwo && $day->closingTwo) {
                    $start = (Int) date_format($day->openingTwo, 'G') * 3600 + date_format($day->openingTwo, 'i') * 60;
                    $end = (Int) date_format($day->closingTwo, 'G') * 3600 + date_format($day->closingTwo, 'i') * 60;

                    for ($timeSlot = $start; $timeSlot < $end; $timeSlot += $timeSlotDuration * 60) {
                        $timeSlots[] = $timeSlot;
                    }
                }

                $days[$daysLookup[$i]] = [
                    'enabled' => $day->getIsBlank() === false,
                    'timeSlots' => $timeSlots,
                ];
            }

            $methods[] = [
                'field' => [
                    'value' => 'pickup',
                    'label' => 'Afhalen',
                ],
                'enabledFields' => [
                    'email' => $checkout->pickupEnabledFields->email,
                    'phone' => $checkout->pickupEnabledFields->phone,
                    'address' => $checkout->pickupEnabledFields->address,
                    'day' => $checkout->pickupEnabledFields->pickupDay,
                    'time' => $checkout->pickupEnabledFields->pickupTime,
                    'remarks' => $checkout->pickupEnabledFields->remarks,
                ],
                'locations' => array_map(function ($location) {
                    return $location['location'];
                }, $checkout->pickupLocations ?? []),
                'locationsDescription' => $checkout->pickupLocationsDescription,
                'calendar' => $days,
                'availableMoments' => array_map(function ($row) {
                    return (Int) date_format( $row['date'], 'U');
                }, $checkout->pickupAvailableMomentsSafe ?? []),
                'unavailableMoments' => array_map(function ($row) {
                    return (Int) date_format( $row['date'], 'U');
                }, $checkout->pickupUnavailableMoments ?? []),
                'daysBuffer' => $checkout->pickupDaysBuffer ?? 1,
                'daysRange' => $checkout->pickupDaysRange ?? 14,
                'timeSlotDuration' => $checkout->pickupTimeSlotDuration ?? 10,
                'paymentMoments' => (array) $checkout->pickupPaymentMoments,
                'remarksPlaceholder' => $checkout->pickupRemarksPlaceholder,
            ];
        }

        if ($checkout->deliveryEnabled) {
            $days = [];
            $timeSlotDuration = $checkout->pickupTimeSlotDuration->value ?? 10;

            for ($i = 0; $i <= 6; $i++) {
                $day = $checkout->deliveryCalendar[$i];

                $timeSlots = [];

                if ($day->openingOne && $day->closingOne) {
                    $start = (Int) date_format($day->openingOne, 'G') * 3600 + date_format($day->openingOne, 'i') * 60;
                    $end = (Int) date_format($day->closingOne, 'G') * 3600 + date_format($day->closingOne, 'i') * 60;

                    for ($timeSlot = $start; $timeSlot < $end; $timeSlot += $timeSlotDuration * 60) {
                        $timeSlots[] = $timeSlot;
                    }
                }

                if ($day->openingTwo && $day->closingTwo) {
                    $start = (Int) date_format($day->openingTwo, 'G') * 3600 + date_format($day->openingTwo, 'i') * 60;
                    $end = (Int) date_format($day->closingTwo, 'G') * 3600 + date_format($day->closingTwo, 'i') * 60;

                    for ($timeSlot = $start; $timeSlot < $end; $timeSlot += $timeSlotDuration * 60) {
                        $timeSlots[] = $timeSlot;
                    }
                }

                $days[$daysLookup[$i]] = [
                    'enabled' => $day->getIsBlank() === false,
                    'timeSlots' => $timeSlots,
                ];
            }

            $methods[] = [
                'field' => [
                    'value' => 'delivery',
                    'label' => $checkout->deliveryCost ? ('Leveren • € ' . $checkout->deliveryCost) : 'Leveren',
                ],
                'enabledFields' => [
                    'email' => $checkout->deliveryEnabledFields->email,
                    'phone' => $checkout->deliveryEnabledFields->phone,
                    'address' => $checkout->deliveryEnabledFields->address,
                    'day' => $checkout->deliveryEnabledFields->deliveryDay,
                    'time' => $checkout->deliveryEnabledFields->deliveryTime,
                    'remarks' => $checkout->deliveryEnabledFields->remarks,
                ],
                'deliveryCost' => $checkout->deliveryCost,
                'calendar' => $days,
                'availableMoments' => array_map(function ($row) {
                    return (Int) date_format( $row['date'], 'U');
                }, $checkout->deliveryAvailableMomentsSafe ?? []),
                'unavailableMoments' => array_map(function ($row) {
                    return (Int) date_format( $row['date'], 'U');
                }, $checkout->deliveryUnavailableMoments ?? []),
                'daysBuffer' => $checkout->deliveryDaysBuffer ?? 1,
                'daysRange' => $checkout->deliveryDaysRange ?? 14,
                'timeSlotDuration' => $checkout->deliveryTimeSlotDuration ?? 10,
                'paymentMoments' => (array) $checkout->deliveryPaymentMoments,
                'remarksPlaceholder' => $checkout->deliveryRemarksPlaceholder,
            ];
        }

        return [
            'methods' => $methods,
        ];
    }
}
