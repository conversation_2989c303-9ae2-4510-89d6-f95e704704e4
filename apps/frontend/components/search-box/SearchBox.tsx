import React, { FC } from 'react';

import { ActionIcon, ActionIconProps, Button, Group, Loader, Text, Textarea, TextareaProps } from '@mantine/core';

import { TbX } from 'react-icons/tb';
import { IoSearch } from 'react-icons/io5';
import { useIsMobile } from 'hooks/use-is-mobile';

import cx from './SearchBox.module.css';

interface Props {
    isLoading?: boolean;
    showSubmitHint?: boolean;
    submit?: () => void;
    handleReset?: () => void;
    rightSection?: React.ReactNode;
    autocompleteOpen?: boolean;
}

const SearchBox: FC<Props & TextareaProps> = ({
    isLoading,
    showSubmitHint,
    submit,
    handleReset,
    leftSection,
    rightSection = null,
    autocompleteOpen,
    ...props
}) => {
    const isMobile = useIsMobile('sm');

    const renderLeftSection = () => {
        if (isLoading) {
            return <Loader size={20} color="primary.4" />;
        }

        if (leftSection) {
            return leftSection;
        }

        return <IoSearch size={20} />;
    };

    const renderRightSection = () => {
        const children = [];

        if (showSubmitHint) {
            children.push(
                <Text fz="sm" c="gray.5">
                    ↵
                </Text>,
            );
        }

        if (handleReset) {
            children.push(
                <ResetButton
                    size={props.size === 'md' ? 'compact-sm' : 'sm'}
                    handleClick={() => {
                        handleReset();
                    }}
                    key="reset"
                />,
            );
        }

        if (submit) {
            if (isMobile) {
                children.push(
                    <ActionIcon
                        onClick={submit}
                        disabled={isLoading}
                        data-search-submit
                        key="search"
                        variant="gradient"
                        radius={99}
                        size={props.size === 'md' ? 30 : 38}
                    >
                        <IoSearch size={16} />
                    </ActionIcon>,
                );
            } else {
                children.push(
                    <Button
                        className={cx.submit}
                        onClick={submit}
                        size={props.size === 'md' ? 'compact-md' : 'lg'}
                        disabled={isLoading}
                        data-search-submit
                        key="search"
                        variant="gradient"
                        leftSection={<IoSearch size={16} />}
                    >
                        Search
                    </Button>,
                );
            }
        }

        if (rightSection) {
            children.push(rightSection);
        }

        return children;
    };

    return (
        <Textarea
            autosize
            maxRows={props.value ? undefined : 1}
            classNames={cx}
            enterKeyHint="search"
            leftSection={renderLeftSection()}
            rightSection={
                <Group gap={8} wrap="nowrap">
                    {renderRightSection()}
                </Group>
            }
            rightSectionWidth="fit-content"
            onKeyDown={(event) => {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    submit?.();
                }
            }}
            data-autocomplete-open={autocompleteOpen}
            {...props}
        />
    );
};

const ResetButton: FC<{
    size?: ActionIconProps['size'];
    handleClick: () => void;
}> = ({ size, handleClick }) => {
    return (
        <ActionIcon onClick={handleClick} className={cx.reset} variant="white" size={size}>
            <TbX size={16} />
        </ActionIcon>
    );
};

export { SearchBox };
