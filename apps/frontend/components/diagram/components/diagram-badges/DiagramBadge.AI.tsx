import React, { <PERSON> } from 'react';

import { UnstyledButton } from '@mantine/core';

import { TbSparkles } from 'react-icons/tb';
import { SidebarService } from 'components/diagram/services/SidebarService';

import { usePermission } from 'hooks/use-permission';
import { PermissionDiagramGeneral } from '@repo/dcide-component-models';

import cx from './DiagramBadges.module.css';

const DiagramBadgeAI: FC = () => {
    const canEdit = usePermission(PermissionDiagramGeneral.EDIT);

    return canEdit ? (
        <UnstyledButton
            className={cx.badge}
            onClick={() => {
                SidebarService.openAIConversationSidebar();
            }}
        >
            <TbSparkles className={cx.pulsing} strokeWidth={1.5} />
            AI Assistant
        </UnstyledButton>
    ) : null;
};

export { DiagramBadgeAI };
