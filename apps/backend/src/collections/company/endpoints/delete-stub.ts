import type { Endpoint } from 'payload';

import { CompanyProfileStubFields } from '@repo/dcide-component-models';

import { handleEndpointError } from '../../../helpers';

const deleteStub: Endpoint = {
    path: '/:id/stub/:field/:stubId',
    method: 'delete',
    handler: async (request) => {
        try {
            const field = request.routeParams?.field as CompanyProfileStubFields;
            const stubId = request.routeParams?.stubId as string;

            const payload = request.payload;

            const company = (await payload.findByID({
                collection: 'manufacturers',
                id: request.routeParams?.id as string,
                depth: 0,
            })) as any;

            if (!(field in company)) {
                throw new Error(`Field ${field} not found in company`);
            }

            // Update company
            const doc = await payload.update({
                collection: 'manufacturers',
                id: company.id,
                data: {
                    [field]: company[field].filter((stub: any) => stub.id !== stubId),
                },
                depth: 0,
                req: request,
            });

            return Response.json({
                doc,
                success: true,
            });
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { deleteStub };
