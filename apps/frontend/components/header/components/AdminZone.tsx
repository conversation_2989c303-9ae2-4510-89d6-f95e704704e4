import React from 'react';

import Link from 'next/link';

import { Menu } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { BsArrowUpRight, BsGraphUp } from 'react-icons/bs';
import { IoBriefcaseOutline, IoTvOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';

import { IS_TOUCH_LOCAL_STORAGE_KEY } from 'helpers/isTouchDevice';

import { LocalStorageService } from 'services/LocalStorageService';

const AdminZone = () => {
    const user = useCurrentUser();

    if (!user?.developer) {
        return null;
    }

    const showDeviceTypeModal = () => {
        openContextModal({
            modal: 'deviceType',
            innerProps: {},
            withCloseButton: false,
        });
    };

    const savedDeviceType = LocalStorageService.get(IS_TOUCH_LOCAL_STORAGE_KEY);

    return (
        <>
            <Menu.Divider />
            <Menu.Label bg="yellow.0">
                ⚠️ Admin zone <br /> (Only visible for developers)
            </Menu.Label>
            <Menu.Item
                component={Link}
                href="/companies/manage"
                bg="yellow.0"
                leftSection={<IoBriefcaseOutline size={14} />}
            >
                Manage companies
            </Menu.Item>
            <Menu.Item
                component={Link}
                href="/exhibitor-match"
                bg="yellow.0"
                leftSection={<IoBriefcaseOutline size={14} />}
            >
                Exhibitor Match
            </Menu.Item>
            <Menu.Item component={Link} href="/profiles/import" bg="yellow.0" leftSection="AI">
                Bulk create profiles
            </Menu.Item>
            <Menu.Item component={Link} href="/products/create/bulk" bg="yellow.0" leftSection="AI">
                Bulk create products
            </Menu.Item>
            <Menu.Item component={Link} href="/secret" bg="yellow.0" leftSection={<BsGraphUp size={14} />}>
                Event Overview
            </Menu.Item>
            <Menu.Item component={Link} href="/secret-search" bg="yellow.0" leftSection={<BsGraphUp size={14} />}>
                Search Query Overview
            </Menu.Item>
            <Menu.Item
                component={Link}
                href="https://dcideai.grafana.net/goto/cUNMfBcHg?orgId=1"
                target="_blank"
                bg="yellow.0"
                leftSection={<BsGraphUp size={14} />}
                rightSection={<BsArrowUpRight size={14} />}
            >
                Grafana Stats
            </Menu.Item>
            <Menu.Item bg="yellow.0" leftSection={<IoTvOutline size={14} />} onClick={showDeviceTypeModal}>
                Set device type (current: {savedDeviceType ?? 'N/A'})
            </Menu.Item>
        </>
    );
};

export { AdminZone };
