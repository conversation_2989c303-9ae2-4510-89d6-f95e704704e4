import { Flex, Text } from '@mantine/core';

import { Page } from 'components/page/Page';

import { ReplusLogo } from 'components/logo/ReplusLogo';

import Link from 'next/link';

import cx from './Title.module.scss';

export const Title = () => {
    return (
        <Flex className={cx.root}>
            <Flex className={cx.logo} component={Link} href="/products#overview">
                <ReplusLogo isWhite />
            </Flex>
            <Flex className={cx.title} visibleFrom="sm">
                <Page.Title opacity={0.9}>Search & Discover</Page.Title>
                <Text fz="lg" fw={600} opacity={0.8} ta="left">
                    Microgrid Businesses, Products, Services, and more.
                </Text>
            </Flex>
            <Flex className={cx.title} hiddenFrom="sm">
                <Page.Title opacity={0.9}>Discover Microgrid Businesses</Page.Title>
                <Text fz="lg" fw={600} opacity={0.8} ta="left">
                    Search for Products, Services, and more.
                </Text>
            </Flex>
            {/* <Flex className={cx.right}>
                <Flex fw={500} opacity={0.6} fz="xs" ta="right" pt={-6}>
                    Powered by
                </Flex>
                <Flex
                    component={Link}
                    href="https://seia.org/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cx.rightlogo}
                >
                    <SEIALogo isWhite />
                </Flex>
                <Flex
                    component={Link}
                    href="https://sepapower.org/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cx.rightlogo}
                >
                    <SEPALogo isWhite />
                </Flex>
            </Flex> */}
        </Flex>
    );
};
