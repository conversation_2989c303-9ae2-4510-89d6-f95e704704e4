import { CollectionConfig } from 'payload';

import {
    ArchivedAt,
    CreatedBy,
    DeletedAt,
    FilesField,
    getSubscribeEndpoint,
    getUnsubscribeEndpoint,
    Name,
    subscribeRequestUser,
    Subscribers,
} from '../fields';

import { VectorStore } from '@/fields/VectorStore';

import { deleteDesigns, OrderField } from './project-designs';

import Projects from './Projects';
import { getRequestUser } from '../helpers/get-request-user';

import { AccessChecks } from './projects/access-checks';
import { files } from '../collections/project-designs/endpoints/files';

const ProjectDesigns: CollectionConfig = {
    slug: 'projectDesigns',
    admin: {
        useAsTitle: 'name',
    },
    access: {
        create: async (args) => {
            return Projects.access!.create!(args);
        },
        read: async ({ req, id }) => {
            if (id && !req.user) {
                return AccessChecks.everyone('project');
            }

            if (id) {
                if (AccessChecks.userIsDeveloper(req.user)) {
                    return true;
                }

                return {
                    or: [
                        AccessChecks.userIsPartOfTeam(req.user, 'project'),
                        AccessChecks.userIsACollaborator(req.user, 'project'),
                        AccessChecks.userIsPartOfManufacturer(req.user, 'project'),
                        AccessChecks.everyone('project'),
                    ],
                };
            }

            return req.user
                ? {
                      or: [
                          AccessChecks.userIsPartOfTeam(req.user, 'project'),
                          AccessChecks.userIsACollaborator(req.user, 'project'),
                          AccessChecks.userIsPartOfManufacturer(req.user, 'project'),
                      ],
                  }
                : false;
        },
        update: async ({ req, id }) => {
            if (id) {
                if (AccessChecks.userIsDeveloper(req.user)) {
                    return true;
                }

                return {
                    or: [
                        AccessChecks.userIsPartOfTeam(req.user, 'project'),
                        AccessChecks.userIsACollaborator(req.user, 'project'),
                    ],
                };
            }

            return (await getRequestUser(req))?.developer ?? false;
        },
        delete: async (args) => {
            return Projects.access!.delete!(args);
        },
    },
    fields: [
        {
            type: 'tabs',
            tabs: [
                {
                    label: 'General',
                    fields: [
                        Name,
                        {
                            name: 'diagrams',
                            type: 'relationship',
                            relationTo: 'projectDesignDiagrams',
                            hasMany: true,
                            maxDepth: 0,
                            defaultValue: () => [],
                        },
                    ],
                },
                {
                    label: 'Orders',
                    fields: [OrderField],
                },
                {
                    label: 'Subscribers',
                    fields: [Subscribers],
                },
                {
                    label: 'Files',
                    fields: [FilesField(), VectorStore('filesVectorStore')],
                },
                {
                    label: 'Meta',
                    fields: [
                        CreatedBy,
                        {
                            name: 'project',
                            type: 'relationship',
                            relationTo: 'projects',
                            maxDepth: 0,
                            index: true,
                        },
                        {
                            type: 'row',
                            fields: [ArchivedAt, DeletedAt],
                        },
                    ],
                },
            ],
        },
    ],
    hooks: {
        beforeChange: [subscribeRequestUser],
        afterChange: [
            async ({ doc, operation, req, context }) => {
                const { payload } = req;
                const { createDefaultDiagram = true } = context;

                if (operation === 'create' && createDefaultDiagram) {
                    const diagram = await payload.create({
                        collection: 'projectDesignDiagrams',
                        data: {
                            name: doc.name,
                            project: doc.project,
                        },
                        req,
                    });

                    await payload.update({
                        collection: 'projectDesigns',
                        id: doc.id,
                        data: {
                            diagrams: [diagram.id],
                        },
                        req,
                    });
                }
            },
        ],
        afterDelete: [deleteDesigns],
    },
    endpoints: [getSubscribeEndpoint('projectDesigns'), getUnsubscribeEndpoint('projectDesigns'), files],
};

export default ProjectDesigns;
