import { Card, Stack } from '@mantine/core';

import {
    Communication,
    Electrical,
    Environmental,
    Mechanical,
    Performance,
    Standards,
} from 'components/component-datasheet/components/sections';

import { useGenerateDatasheet } from './GenerateDatasheet.Context';

import { GenerateDatasheet } from './GenerateDatasheet';

import cx from './GenerateDatasheet.module.css';

const GenerateDatasheetSpecifiactions = () => {
    const { showOverlay } = useGenerateDatasheet();

    return (
        <Card className={cx.specifications}>
            <Stack gap="xl">
                <Electrical />
                <Performance />
                <Communication />
                <Standards />
                <Mechanical />
                <Environmental />
            </Stack>

            {showOverlay && <GenerateDatasheet.Overlay />}
        </Card>
    );
};

export { GenerateDatasheetSpecifiactions };
