import { handleEndpointError } from '../../../helpers/handle-endpoint-error';
import { JobLogSchema } from '@repo/dcide-component-models';
import { JobsEndpointFactory } from '../types';
import { JsonSuccessResponse } from '@/responses/JsonSuccessResponse';

const log: JobsEndpointFactory = (slug) => ({
    path: '/log',
    method: 'post',
    handler: async (request) => {
        try {
            const log = JobLogSchema.parse({
                ...(await request.json()),
                type: slug,
            });

            await request.payload.create({
                collection: 'jobLogs',
                data: {
                    job: {
                        relationTo: log.jobId ? slug : null,
                        value: log.jobId,
                    },
                    parentJob: {
                        relationTo: log.parentJobType,
                        value: log.parentJobId,
                    },
                    type: log.type,
                    message: log.message,
                    data: { zoneIndex: log.zoneIndex },
                },
            });

            return JsonSuccessResponse();
        } catch (error) {
            return handleEndpointError(error);
        }
    },
});

export { log };
