import React, { FC, useState } from 'react';

import { Box, Button, Collapse, Group, Text } from '@mantine/core';

import { TeamService } from 'services/TeamService';

import { useShowDesignEditor } from 'hooks/use-show-design-editor';
import { useCurrentTeam } from 'hooks/use-current-team';

import { SteppedLoadingOverlay } from './SteppedLoadingOverlay';
import { ProjectHelpers } from 'helpers/ProjectHelpers';

import cx from './SidebarNav.FinishSignup.module.css';

import { config } from 'config';

const steps = [
    <Text p="md" fw={600} ta="center" key="design_editor_message_1">
        Please wait a moment...
    </Text>,
    <Text p="md" fw={600} ta="center" key="design_editor_message_2">
        We are setting up the design editor for you...
    </Text>,
    <Text p="md" fw={600} ta="center" key="design_editor_message_3">
        Almost there...
    </Text>,
    <Text p="md" fw={600} ta="center" key="design_editor_message_4">
        Ready!
        <br />
        Go ahead and explore!
    </Text>,
];

const SidebarNavDesignEditorMessage: FC = () => {
    const team = useCurrentTeam();

    const [enabling, setEnabling] = useState(false);
    const [justEnabled, setJustEnabled] = useState(false);
    const showDesignEditor = useShowDesignEditor();

    if (!team) return null;

    if (!justEnabled && showDesignEditor) return null;

    const enableDesignEditor = () => {
        TeamService.setShowDesignEditor(team?.id, true).then();

        setEnabling(false);
        setJustEnabled(true);
    };

    return (
        <Box className={cx.finishSignup}>
            <SteppedLoadingOverlay steps={steps} enabled={enabling} onEnd={enableDesignEditor} />

            <Group align="center" justify="space-between" fz="inherit" fw={600}>
                Try our Design Editor
            </Group>

            <Collapse in={!justEnabled}>
                <Text fz="inherit">
                    This platform is powered by DCIDE, which features a microgrid design editor to create architectures
                    at the speed-of-thought.
                </Text>
            </Collapse>

            <Box>
                {!justEnabled ? (
                    <Button
                        fullWidth
                        variant="filled"
                        onClick={() => {
                            setEnabling(true);
                        }}
                        size="xs"
                    >
                        Try our design editor
                    </Button>
                ) : (
                    <Button
                        fullWidth
                        variant="filled"
                        component="a"
                        href={ProjectHelpers.urls.create({ name: 'My First Design' })}
                        size="xs"
                    >
                        Create your first design
                    </Button>
                )}
                <Button
                    fullWidth
                    variant="transparent"
                    component="a"
                    href={config.urls.landingPage}
                    target="_blank"
                    size="xs"
                >
                    Learn more
                </Button>
            </Box>
        </Box>
    );
};

export { SidebarNavDesignEditorMessage };
