.dropzone {
    position: relative;
}

.dropzoneOverlay {
    position: absolute;
    left: 0;
    top: 0;

    z-index: 100;

    display: flex;
    flex-direction: column;

    justify-content: center;
    align-items: center;

    gap: var(--mantine-spacing-xs);

    width: 100%;
    height: 100%;

    font-weight: 500;

    background-color: rgba(0, 0, 0, 0.5);
    color: #ffffff;

    svg {
        width: 40px;
        height: 40px;
    }
}

.tableScrollContainer {
    &::before,
    &::after {
        content: "";

        position: absolute;
        top: 0;

        z-index: 10;

        width: 1px;
        height: 100%;

        background-color: var(--mantine-color-gray-2);
    }

    &::before {
        left: 0;
    }

    &::after {
        right: 0;
    }
}

.table {
    position: relative;

    th {
        white-space: nowrap;
    }

    td:has(input) {
        padding: 0;

        input {
            width: 100%;

            padding: var(--table-vertical-spacing) var(--table-horizontal-spacing, var(--mantine-spacing-xs));
            border: none;
            border-radius: 0;

            &:hover {
                background: var(--mantine-color-gray-0);
            }

            &:focus {
                outline: none;
            }
        }
    }
}
