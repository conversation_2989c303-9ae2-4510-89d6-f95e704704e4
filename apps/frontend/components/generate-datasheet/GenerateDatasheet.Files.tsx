import { useFormContext, useWatch } from 'react-hook-form';

import { Alert, AlertProps, Anchor, Box, Button, Card, Group, Stack, Switch } from '@mantine/core';
import { PDF_MIME_TYPE } from '@mantine/dropzone';
import { IoRepeat } from 'react-icons/io5';

import { Component, ComponentFileType, File, FileVisibility } from '@repo/dcide-component-models';

import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { DropzoneField } from 'components/forms/fields/DropzoneField';
import { ComponentFilesOverview } from 'components/component-fields/ComponentFilesField';
import { GenerateDatasheetState, useGenerateDatasheet } from './GenerateDatasheet.Context';
import { InternalTrackingService } from 'services/InternalTrackingService';

const messageProps: AlertProps = {
    variant: 'light',
    color: 'primary',
    p: 'xs',
};

const GenerateDatasheetFiles = () => {
    const { component } = useOptionalComponentContext();

    const { state, generateDatasheet, overwrite, setOverwrite, aiResult, showChooseModal } = useGenerateDatasheet();

    const files = useWatch({ name: 'files' }) as Component['files'];
    const { setValue } = useFormContext();

    const datasheets = files?.filter((file) => file.type === ComponentFileType.DATASHEET) ?? [];

    const renderMessages = () => {
        if (state === GenerateDatasheetState.SUCCESS) {
            return (
                <Alert {...messageProps}>
                    Datasheet generated successfully. Please review the data and make any necessary changes.
                    {aiResult?.length && aiResult.length > 1 && (
                        <Box mt="md">
                            <Anchor inherit fw={500} onClick={() => showChooseModal(true)}>
                                <Group gap={4}>
                                    <IoRepeat size={14} />
                                    <span>Choose different product variant</span>
                                </Group>
                            </Anchor>
                        </Box>
                    )}
                </Alert>
            );
        }

        if (state === GenerateDatasheetState.ERROR) {
            return (
                <Alert {...messageProps} color="red">
                    Something went wrong when generating the datasheet. Please try again later or fill in the details
                    manually.
                </Alert>
            );
        }

        return (
            <Alert {...messageProps}>
                We use AI to scan your document and generate a datasheet for you. The better the source document, the
                better the result.
            </Alert>
        );
    };

    const isLoading = state === GenerateDatasheetState.LOADING;
    const isRegenerate = state === GenerateDatasheetState.SUCCESS || state === GenerateDatasheetState.ERROR;
    const isDisabled = !datasheets.length;

    const onUpload = async (uploadedFiles: File[]) => {
        const newFiles = uploadedFiles.map((file) => ({
            file: file.id,
            type: ComponentFileType.DATASHEET,
            visibility: FileVisibility.PRIVATE,
        }));

        setValue('files', [...(files ?? []), ...newFiles]);

        InternalTrackingService.track('product.aiDatasheet.upload', {
            componentId: component?.id,
            componentType: component?.type,
        });
    };

    return (
        <Card>
            <Stack>
                {!datasheets.length && (
                    <DropzoneField
                        group={component ? `component:${component.id}:files` : 'component:files'}
                        accept={[...PDF_MIME_TYPE]}
                        onUpload={onUpload}
                        multiple={false}
                        title="Drag datasheet here or click to select a datasheet"
                        subtitle="Only one PDF file is allowed"
                    />
                )}

                {!!datasheets.length && <ComponentFilesOverview editable showFileType={false} />}

                <Stack gap={8} align="start">
                    {!!datasheets.length && (
                        <Switch
                            color="primary.4"
                            checked={overwrite}
                            onChange={(event) => setOverwrite(event.currentTarget.checked)}
                            label="Overwrite existing values"
                        />
                    )}

                    <Button
                        fullWidth
                        variant="gradient"
                        onClick={generateDatasheet}
                        loading={isLoading}
                        loaderProps={{ type: 'bars', size: 12 }}
                        disabled={isDisabled}
                    >
                        {isRegenerate ? 'Regenerate Datasheet' : 'Generate Datasheet'}
                    </Button>
                </Stack>

                {renderMessages()}
            </Stack>
        </Card>
    );
};

export { GenerateDatasheetFiles };
