import React from 'react';

import { Gradient } from '@/helpers/gradient';
import {
    IconArrowsDoubleNeSw,
    IconBattery2,
    IconClock,
    IconCloud,
    IconCurrencyDollar,
    IconEdit,
    IconListSearch,
    IconMathMaxMin,
    IconMessage,
    IconMessageCircle,
    IconPointer,
    IconShieldCheck,
} from '@tabler/icons-react';

type FeatureKey =
    | 'prototyping'
    | 'cloudBased'
    | 'realtimeCollaboration'
    | 'inAppCommunication'
    | 'productCatalog'
    | 'annotations'
    | 'validations'
    | 'inAppSupport'
    | 'billOfMaterials'
    | 'wireSizing'
    | 'powerFlowSimulation'
    | 'batteryCharging';

export type Feature = {
    title: string;
    description: React.ReactNode;
    icon: React.ReactNode;
    badge?: string;
    action?: {
        label: string;
        href: string;
    };
    preview?: {
        type: 'video';
        video: string;
    } | {
        type: 'image';
        image: {
            src: string;
            width: number;
            height: number;
        };
    };
    gradient?: Gradient;
};

import designImage from '../app/design.png';
import productCatalogImage from '../app/features/product-catalog/product-catalog.png';

const features: Record<FeatureKey, Feature> = {
    prototyping: {
        title: 'Prototype in Minutes',
        description: <>Sketch complete concepts in minutes. Simply drag, drop, specify & connect.</>,
        icon: <IconClock strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/prototyping',
        },
        preview: {
            type: 'video',
            video: '/videos/features/prototyping/drag-and-drop.mp4',
        },
        gradient: 'red',
    },
    cloudBased: {
        title: 'Cloud-Based',
        description: (
            <>
                It is web-based and accessible from any device with a browser. No time-consuming setup and installation.
            </>
        ),
        icon: <IconCloud strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/cloud-based',
        },
        gradient: 'green',
    },
    realtimeCollaboration: {
        title: 'Realtime Collaboration',
        description: (
            <>
                Get on the same page. Collaborate with your team, manufacturers and clients. Always access the latest version of the design.
            </>
        ),
        icon: <IconPointer strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/realtime-collaboration',
        },
        preview: {
            type: 'image',
            image: designImage,
        },
        gradient: 'blue',
    },
    inAppCommunication: {
        title: 'Chat Channels',
        description: <>Chat directly about your designs. Create different channels to talk with customers, manufacturers and your teammates.</>,
        icon: <IconMessageCircle strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/in-app-communication',
        },
        gradient: 'purple',
    },
    productCatalog: {
        title: 'Product Catalog',
        description: (
            <>
                Find the products you need that fit your design. Search by product type, manufacturer, or specifications.
            </>
        ),
        icon: <IconListSearch strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/product-catalog',
        },
        preview: {
            type: 'image',
            image: productCatalogImage,
        },
        gradient: 'pink',
    },
    annotations: {
        title: 'Annotations',
        description: <>Improve your designs with your logo, images of equipment and text. More clarity, less back-and-forth!</>,
        icon: <IconEdit strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/annotations',
        },
        gradient: 'orange',
    },
    validations: {
        title: 'Validation Center',
        description: <>
            Specifically for DC systems, we continuously check for electrical issues. We validate your design, based on the NEC, IEC, CurrentOS and ODCA rules.
        </>,
        icon: <IconShieldCheck strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/validations',
        },
        gradient: 'green',
    },
    inAppSupport: {
        title: 'Technical Support',
        description: <>Get expert engineering or product support directly in your designs.</>,
        icon: <IconMessage strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/in-app-support',
        },
        gradient: 'orange',
        badge: 'Beta',
    },
    billOfMaterials: {
        title: 'Bill of Materials',
        description: <>Keep an overview of the materials used in your design and their costs.</>,
        icon: <IconCurrencyDollar strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/bill-of-materials',
        },
        gradient: 'pink',
        badge: 'Beta',
    },
    powerFlowSimulation: {
        title: 'Power Flow Simulation',
        description: <>Simulate the power flow in your design to ensure that it meets your requirements.</>,
        icon: <IconArrowsDoubleNeSw strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/power-flow-simulation',
        },
        gradient: 'blue',
    },
    batteryCharging: {
        title: 'Battery (dis)charging',
        description: <>Simulate how batteries charge and discharge over time.</>,
        icon: <IconBattery2 strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/battery-charging',
        },
        gradient: 'red',
    },
    wireSizing: {
        title: 'Wire Sizing',
        description: (
            <>
                Say goodbye to spreadsheets. We derive the worst-case current in each connection and suggest suitable wire sizes according to IEC and NEC standards.
            </>
        ),
        icon: <IconMathMaxMin strokeWidth={1.25} />,
        action: {
            label: 'Learn more',
            href: '/features/wire-sizing',
        },
        gradient: 'purple',
        badge: 'Beta',
    },
} as const;

export { features };
