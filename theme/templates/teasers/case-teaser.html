<div class="relative" data-case-teaser>
    <div class="{{ breakoutContainerClasses() }}">
        <img
            class="
                w-[calc(100%-24px)] md:w-[calc(100%-40px)] lg:w-[calc(100%-56px)] xl:w-[calc(100%-80px)]
                h-auto max-h-[900px]
                object-cover
            "
            src="https://images.pexels.com/photos/31090535/pexels-photo-31090535/free-photo-of-rustic-western-style-wooden-saloon-front.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
            alt=""
            data-case-teaser-image
        />
    </div>
    <div class="md:absolute md:bottom-6 w-full mt-6" data-case-teaser-content>
        <div class="grid md:grid-cols-2 items-start gap-6 md:gap-8 lg:gap-10 max-w-7xl mx-auto">
            <h2 class="relative pl-12 py-4 lg:pl-16 lg:py-6 font-bold text-2xl sm:text-3xl md:text-4xl lg:text-5xl md:text-white">
                <span class="relative z-20">Woning</span>
                <svg class="absolute inset-0 z-10 w-auto h-full" viewBox="0 0 187 119">
                    <path d="M185.61.5H60.86a4.63 4.63 0 0 0-4.43 3.26L28.36 85.9a3.1 3.1 0 0 1-2.95 2.15H13c-2.43.03-3.06 1.37-3.51 2.41L.08 116.46c-.28.9.15 2.04 1.34 2.04h124.75a4.63 4.63 0 0 0 4.42-3.26l28.08-82.15a3.08 3.08 0 0 1 2.95-2.13h12.4c2.43-.02 3.05-1.36 3.5-2.4l9.4-26.02c.28-.9-.15-2.04-1.34-2.04" fill="#645FFF" />
                </svg>
            </h2>
            <div class="lg:text-lg" data-polygon-content>
                {{ description ?? 'DESCRIPTION' }}
                <a class="flex items-center gap-2 mt-1 sm:mt-2 md:mt-4 font-bold hover:text-brand-purple" href="#">
                    <svg class="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M0 0h24v24H0z" fill="none" />
                        <path d="M4.93 4.93a10 10 0 1 1 14.14 14.14A10 10 0 0 1 4.93 4.93zM13 9a1 1 0 1 0-2 0v2H9a1 1 0 1 0 0 2h2v2a1 1 0 1 0 2 0v-2h2a1 1 0 1 0 0-2h-2V9z" />
                    </svg>
                    Meer info
                </a>
            </div>
        </div>
    </div>
</div>

<style>
    .lala {
        max-width: calc(1280px + (100vw - 1280px) * 0.5);
    }
</style>


