import { isNumber, sum } from 'radash';

import { DiagramComponentInstance, powerConverter } from '@repo/dcide-component-models';

import { useZones } from 'components/diagram/hooks/use-zones';
import { ZoneHelpers } from 'components/diagram/helpers/ZoneHelpers';
import { openContextModal } from '@mantine/modals';

import { XYChart } from 'components/xy-chart/XYChart';

import { VoltageField } from 'components/component-fields/VoltageField';
import { PowerField } from 'components/component-fields/PowerField';

type VoltageSeries = NonNullable<DiagramComponentInstance['configuration']['ports'][0]['voltageSeries']>;
type PowerSeries = NonNullable<DiagramComponentInstance['configuration']['ports'][0]['powerSeries']>;

const PowerVoltageChart = ({
    componentInstanceId,
    port,
    voltageSeries = { value: [], unit: 'V' },
    powerSeries = { value: [], unit: 'W' },
    updateVoltagePowerSeries,
    clearVoltagePowerSeries,
}: {
    componentInstanceId: DiagramComponentInstance['id'];
    port: number;
    voltageSeries?: VoltageSeries;
    powerSeries?: PowerSeries;
    updateVoltagePowerSeries: (data: { voltageSeries: VoltageSeries; powerSeries: PowerSeries }) => void;
    clearVoltagePowerSeries: () => void;
}) => {
    const {
        zones: { control: zones },
    } = useZones();
    const zone = ZoneHelpers.getZone(zones, componentInstanceId, port);

    const incomingData = (voltageSeries.value || []).map((_, index) => ({
        voltage: (voltageSeries.value || [])[index],
        power: (powerSeries.value || [])[index],
    }));

    const openZoneDroopCurvesModal = () => {
        if (zone) {
            openContextModal({
                modal: 'zoneDroopCurves',
                innerProps: {
                    zone,
                },
                withCloseButton: false,
                size: 'xl',
            });
        }
    };

    const bestMaxPower = incomingData.length ? Math.max(...incomingData.map((d) => Math.abs(d.power))) : 0;
    const bestPowerUnit = powerConverter.toBest({ value: bestMaxPower, from: powerConverter.baseUnit }).unit;

    // convert all power to bestPowerUnit
    const data = incomingData.map((d) => ({
        ...d,
        power: powerConverter.convert({
            value: d.power,
            from: powerConverter.baseUnit,
            to: bestPowerUnit,
        }).value,
    }));

    const nomVoltage = data.length ? Math.round(sum(data, (data) => data.voltage) / data.length) : 230;

    const maxVoltageDiff = data.length ? Math.max(...data.map((d) => Math.abs(d.voltage - nomVoltage))) : 0;
    const leftDomainVoltage = nomVoltage - maxVoltageDiff - 10;
    const rightDomainVoltage = nomVoltage + maxVoltageDiff + 10;

    const nomPower = 0;
    const maxPower = data.length ? Math.max(...data.map((d) => Math.abs(d.power))) : 0;
    const maxPowerDiff = data.length ? Math.max(...data.map((d) => Math.abs(d.power - nomPower))) : 0;
    const leftDomainPower = Math.round(nomPower - maxPowerDiff - maxPower / 2);
    const rightDomainPower = Math.round(nomPower + maxPowerDiff + maxPower / 2);

    const firstPower = data.length ? data[0].power : 0;
    const lastPower = data.length ? data[data.length - 1].power : 0;

    const voltageSeries_ = {
        value: data.map((d) => d.voltage),
        unit: 'V',
    };
    const powerSeries_ = {
        value: data.map((d) => d.power),
        unit: bestPowerUnit,
    };

    return (
        <XYChart
            yUnitConverter={(value) =>
                powerConverter.convert({
                    value,
                    from: bestPowerUnit,
                    to: powerConverter.baseUnit,
                }).value
            }
            xAxisLabel="Voltage (V)"
            xAxisDomain={[leftDomainVoltage, rightDomainVoltage]}
            yAxisLabel={`Power (${bestPowerUnit})`}
            yAxisDomain={[leftDomainPower, rightDomainPower]}
            title={'Power-voltage droop'}
            fields={{
                x: <VoltageField name="voltage" label="Voltage" defaultValue={0} />,
                y: <PowerField name="power" label="Power" defaultValue={0} />,
            }}
            xSeries={voltageSeries_}
            ySeries={powerSeries_}
            updateXYSeries={({ x, y }) => {
                const voltageSeries: VoltageSeries = {
                    value: x,
                    unit: 'V',
                };

                const powerSeries: PowerSeries = {
                    value: y,
                    unit: 'W',
                };

                updateVoltagePowerSeries({ voltageSeries, powerSeries });
            }}
            clearXYSeries={clearVoltagePowerSeries}
            referenceLines={[
                {
                    // y-axis
                    y: 0,
                    color: 'rgba(0, 0, 0, 0.1)',
                },
                {
                    // x-axis
                    x: nomVoltage,
                    color: 'rgba(0, 0, 0, 0.1)',
                },
                {
                    // extend left tail
                    color: 'primary.6',
                    strokeWidth: 2,
                    ifOverflow: 'visible',

                    segment: [
                        {
                            x: leftDomainVoltage,
                            y: firstPower,
                        },
                        {
                            x: isNumber(data[0]?.voltage) ? data[0].voltage : rightDomainVoltage,
                            y: firstPower,
                        },
                    ],
                },
                {
                    // extend right tail
                    color: 'primary.6',
                    ifOverflow: 'visible',
                    strokeWidth: 2,
                    segment: [
                        {
                            x: isNumber(data[data.length - 1]?.voltage)
                                ? data[data.length - 1].voltage
                                : rightDomainVoltage,
                            y: lastPower,
                        },
                        {
                            x: rightDomainVoltage,
                            y: lastPower,
                        },
                    ],
                },
            ]}
            additionalButtons={[
                {
                    label: 'Overview',
                    onClick: openZoneDroopCurvesModal,
                    disabled: !zone,
                },
            ]}
        />
    );
};

export { PowerVoltageChart };
