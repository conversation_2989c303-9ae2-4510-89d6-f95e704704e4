import React, { useState, FC } from 'react';

import { zodToJsonSchema } from 'zod-to-json-schema';

import { all, ComponentType, getComponentPayloadValidator } from '@repo/dcide-component-models';
import type { Component } from '@repo/dcide-component-models';

import { Page } from 'components/page';
import { Box, Button, Group, Select } from '@mantine/core';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { MultilineTextField } from 'components/forms/fields/MultilineTextField';

import cx from './product.module.css';

type FormValues = {
    jsonAsString: string;
};

const ProductValidator: FC = () => {
    const [parseError, setParseError] = useState('');

    const [jsonAsString, setJsonAsString] = useState('');
    const [json, setJson] = useState<any>({});

    const [downloadComponent, setDownloadComponent] = useState<ComponentType | null>(null);
    const [downloadType, setDownloadType] = useState<'example' | 'schema' | null>(null);

    const onSubmit = (values: FormValues) => {
        try {
            setParseError('');

            const jsonObject = JSON.parse(values.jsonAsString);
            const prettyJsonAsString = JSON.stringify(jsonObject, null, 4);

            setJsonAsString(prettyJsonAsString);
            setJson(jsonObject);
        } catch (error) {
            setParseError(`Invalid JSON: ${error}`);
        }
    };

    const download = () => {
        if (!downloadComponent || !downloadType) {
            return;
        }

        const validator = getComponentPayloadValidator(downloadComponent);
        const content = {
            example: () => {
                return validator.parse({
                    name: `Example ${downloadComponent}`,
                    type: downloadComponent,
                });
            },
            schema: () => {
                return zodToJsonSchema(validator);
            },
        }[downloadType]();

        const jsonData = JSON.stringify(content, null, 4);
        const blob = new Blob([jsonData], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = `${downloadComponent}-${downloadType}.json`;

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    return (
        <Page title="Product Validator" showBackground>
            <Page.WideContent>
                <Form<FormValues> values={{ jsonAsString }} onSubmit={onSubmit}>
                    <Box className={cx.validator}>
                        <Box className={cx.intro}>
                            Use this validator to check if your product JSON can get parsed by DCIDE.
                        </Box>
                        <Box className={cx.split}>
                            <Box className={cx.input}>
                                <MultilineTextField
                                    name="jsonAsString"
                                    placeholder="Paste your product JSON"
                                ></MultilineTextField>
                            </Box>
                            <Box className={cx.output}>
                                {parseError ? <Box>{parseError}</Box> : <ValidationViewer json={json} />}
                            </Box>
                        </Box>
                        <Group justify="center">
                            <FormSubmit>Validate Product</FormSubmit>
                        </Group>
                        <Box className={cx.examples}>
                            Want to download an example or JSON Schema?
                            <br />
                            <Group align="center" justify="center" gap={8} maw={600} mx="auto" mt="sm">
                                <Select
                                    placeholder="Select a component"
                                    data={Object.values(all).map((component) => ({
                                        value: component.type,
                                        label: component.name,
                                    }))}
                                    value={downloadComponent}
                                    onChange={(value) => setDownloadComponent(value as ComponentType)}
                                />
                                <Select
                                    placeholder="Select a type"
                                    data={[
                                        { value: 'example', label: 'JSON Example' },
                                        { value: 'schema', label: 'JSON Schema' },
                                    ]}
                                    value={downloadType}
                                    onChange={(value) => setDownloadType(value as 'example' | 'schema')}
                                />
                                <Button
                                    variant="gradient"
                                    onClick={download}
                                    disabled={!downloadComponent || !downloadType}
                                >
                                    Download
                                </Button>
                            </Group>
                        </Box>
                    </Box>
                </Form>
            </Page.WideContent>
        </Page>
    );
};

const ValidationViewer: FC<{ json: Component }> = ({ json }) => {
    const validator = getComponentPayloadValidator(json.type);
    const validated = validator.strict().safeParse(json);

    if (!json.type) {
        return null;
    }

    return validated.success ? (
        <Box className={cx.success}>Your product is valid</Box>
    ) : (
        <Box className={cx.issues}>
            {validated.error.issues.map((issue) => (
                <Box key={issue.path.join('.')}>
                    <Box className={cx.issueMessage}>{issue.message}</Box>
                    {issue.path.length > 0 && <Box className={cx.issuePath}>Property: {issue.path.join('.')}</Box>}
                </Box>
            ))}
        </Box>
    );
};

export default ProductValidator;
