import React, { useEffect, useState } from 'react';

import { Box, Button, Modal } from '@mantine/core';

import { useSnapshot } from 'hooks/use-safe-snapshot';

import { FileService } from 'components/diagram/services/FileService';
import { FileService as GlobalFileService } from 'services/FileService';

import { state as addFileModalState } from 'components/diagram/state/add-file-modal';

import cx from './DiagramAddFileModal.module.css';
import {
    DiagramAddFileForm,
    DiagramAddFileFormValues,
} from 'components/diagram/components/diagram-add-file/DiagramAddFileForm';
import { LocalNotificationService } from 'services/LocalNotificationService';

const DiagramAddFileModalComponent = () => {
    const { opened, type, file, component } = useSnapshot(addFileModalState);
    const [files, setFiles] = useState<File[]>([]);
    const [loading, setLoading] = useState(false);

    const isOpen = opened && type === 'component';

    useEffect(() => {
        if (!isOpen) return;

        if (file.ref) {
            setFiles([file.ref].filter(Boolean));
        }
    }, [isOpen, file.ref]);

    const close = () => {
        FileService.closeAddFileModal();

        setLoading(false);
        setFiles([]);
    };

    const submit = async (values: DiagramAddFileFormValues) => {
        setLoading(true);

        const [file] = files;
        const { name, type } = values.metadata[0];

        const upload = await GlobalFileService.create({
            file,
            name,
            group: `components`,
        });

        await FileService.addComponentFile({
            component: component!.id,
            file: upload.id,
            type,
        });

        await FileService.mutateComponentFilesPrivateOnly(component!.id);

        LocalNotificationService.showSuccess({
            message: 'File added to component',
        });

        setLoading(false);
        close();
    };

    return (
        <Modal opened={isOpen} onClose={close} withCloseButton={false} key={files.length}>
            {component ? (
                <React.Fragment>
                    <Box className={cx.description}>
                        <h3 className={cx.title}>Add a team file to {component.name}</h3>
                        Team files are attached to this component but only visible to people in your team.
                    </Box>
                    <DiagramAddFileForm
                        files={files}
                        setFiles={setFiles}
                        loading={loading}
                        onSubmit={submit}
                        onCancel={close}
                    />
                </React.Fragment>
            ) : (
                <React.Fragment>
                    <Box className={cx.description}>
                        <h3 className={cx.title}>Add a team file</h3>
                        Link this component instance to an existing product or create a reusable component to add files.
                    </Box>
                    <Box className={cx.actions}>
                        <Button size="xs" variant="light" onClick={close}>
                            Cancel
                        </Button>
                    </Box>
                </React.Fragment>
            )}
        </Modal>
    );
};

export { DiagramAddFileModalComponent };
