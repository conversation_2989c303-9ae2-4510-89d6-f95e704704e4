import { Html, Head, Main, NextScript } from 'next/document';

import Script from 'next/script';

import { ColorSchemeScript, mantineHtmlProps } from '@mantine/core';

import { config } from 'config';

const Document = () => {
    return (
        <Html lang="en" {...mantineHtmlProps}>
            <Head>
                <ColorSchemeScript forceColorScheme="light" />
            </Head>
            {config.serverEnvironment === 'production' && config.analytics && (
                <Script id="google-tag-manager" strategy="afterInteractive">
                    {`
                (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${config.analytics}');
                `}
                </Script>
            )}
            <body>
                <Main />
                <NextScript />
            </body>
        </Html>
    );
};

export default Document;
