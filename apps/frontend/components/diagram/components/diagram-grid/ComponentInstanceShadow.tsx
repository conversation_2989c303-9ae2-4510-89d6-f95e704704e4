import { FC } from 'react';

import { Box } from '@mantine/core';

import { DiagramPosition } from '@repo/dcide-component-models';

import { CELL_HEIGHT, CELL_PADDING, CELL_WIDTH, ICON_HEIGHT, ICON_WIDTH } from 'components/diagram/diagram-dimensions';

import { DiagramService } from 'components/diagram/services/DiagramService';

import cx from './ComponentInstanceShadow.module.css';

export const ComponentInstanceShadow: FC<{
    position: DiagramPosition;
    colSpan?: number;
    rowSpan?: number;
    variant?: 'default' | 'warning';
    pulsing?: boolean;
    onClick?: () => void;
    onClickOutside?: () => void;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
    ref?: React.ForwardedRef<HTMLDivElement>;
}> = ({
    position,
    colSpan = 1,
    rowSpan = 1,
    variant = 'default',
    pulsing = false,
    onClick,
    onClickOutside,
    onMouseEnter,
    onMouseLeave,
    ref,
}) => {
    const enablePointerEvents = Boolean(onClick);

    const isValidPosition = DiagramService.isValidPosition(position, colSpan, rowSpan);

    return (
        <>
            {onClickOutside && (
                <Box
                    className={cx.wrapper}
                    style={{
                        left: CELL_WIDTH * position.x,
                        top: CELL_HEIGHT * position.y,

                        width: colSpan * CELL_WIDTH,
                        height: rowSpan * CELL_HEIGHT,
                    }}
                    onClick={onClickOutside}
                />
            )}
            <Box
                className={cx.shadow}
                style={{
                    left: CELL_WIDTH * position.x + CELL_WIDTH - ICON_WIDTH - CELL_PADDING,
                    top: CELL_HEIGHT * position.y + CELL_PADDING,

                    width: ICON_WIDTH + (colSpan - 1) * CELL_WIDTH,
                    height: ICON_HEIGHT + (rowSpan - 1) * CELL_HEIGHT,

                    pointerEvents: enablePointerEvents ? 'auto' : 'none',
                }}
                data-variant={isValidPosition ? variant : 'warning'}
                data-animation={pulsing ? 'pulsing' : ''}
                onClick={onClick}
                onMouseEnter={onMouseEnter}
                onMouseLeave={onMouseLeave}
                ref={ref}
            />
        </>
    );
};
