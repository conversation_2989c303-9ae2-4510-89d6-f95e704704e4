import React, { FC } from 'react';

import { SavedItemType } from '@repo/dcide-component-models';

import { Button, Group, Tooltip } from '@mantine/core';

import { IconCopy } from '@tabler/icons-react';
import { IoAddOutline } from 'react-icons/io5';

import { useCurrentUser } from 'hooks/use-current-user';
import { useComponentContext } from '../hooks/use-component-context';

import { CopyComponentButton } from 'components/component-datasheet/components/CopyComponentButton';
import { SaveButton } from 'components/save-button/SaveButton';
import { ProjectService } from 'services/ProjectService';
import { ShareButton } from 'components/share-button/ShareButton';

const ComponentDatasheetActionsPublic: FC = () => {
    const user = useCurrentUser();

    const { component } = useComponentContext();

    const createDesign = () => {
        ProjectService.navigate
            .create({
                referenceComponent: component.id,
            })
            .then();
    };

    return (
        <Group gap={4}>
            {user && (
                <Tooltip label="Create a new design around this component">
                    <Button
                        size="compact-xs"
                        variant="subtle"
                        onClick={createDesign}
                        leftSection={<IoAddOutline size={12} />}
                        visibleFrom="md"
                    >
                        Create design...
                    </Button>
                </Tooltip>
            )}
            <CopyComponentButton
                size="compact-xs"
                variant="subtle"
                leftSection={<IconCopy size={12} />}
                component={component}
                visibleFrom="md"
            />
            <ShareButton type="product" />
            {component.id && <SaveButton id={component.id} type={SavedItemType.COMPONENT} />}
        </Group>
    );
};

export { ComponentDatasheetActionsPublic };
