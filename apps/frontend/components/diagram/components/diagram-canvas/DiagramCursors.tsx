import { FC, useEffect, useRef } from 'react';

import { Point } from '@repo/dcide-component-models';

import { Box } from '@mantine/core';
import { BsCursorFill } from 'react-icons/bs';

import { useThrottledCallback, useWindowEvent } from '@mantine/hooks';

import { useAbly } from 'ably/react';
import { useCursors, useMembers } from '@ably/spaces/react';

import { ViewportService } from 'components/diagram/services';

import { getBackgroundColor } from 'components/avatar/Avatar';

import cx from './DiagramCursor.module.css';

export const DiagramCursors: FC = () => {
    const { connection } = useAbly();

    const { self, others } = useMembers();
    const { cursors, set } = useCursors({ returnCursors: true });

    const position = useRef<Point | null>(null);

    const broadcast = () => {
        if (self?.isConnected && connection.state === 'connected' && position.current) {
            set?.({ position: position.current }).then();
        }
    };

    const updatePosition = useThrottledCallback((event: MouseEvent) => {
        position.current = ViewportService.getLocalCoordinates({
            x: event.clientX,
            y: event.clientY,
        });

        broadcast();
    }, 1000 / 15);

    useWindowEvent('mousemove', updatePosition);

    useEffect(() => {
        if (others.length) {
            broadcast();
        }
    }, [others]);

    const activeCursors = others
        .map((member) => {
            return cursors[member.connectionId];
        })
        .filter((cursor) => {
            return cursor && cursor.member;
        });

    return activeCursors.map((cursor) => {
        const name = (cursor.member.profileData?.name ?? cursor.member.profileData?.email ?? 'Anonymous') as string;

        return (
            <Box
                className={cx.cursor}
                style={{
                    'left': cursor.cursorUpdate.position.x,
                    'top': cursor.cursorUpdate.position.y,

                    '--cursor-color': getBackgroundColor(name).from,
                }}
                key={cursor.cursorUpdate.connectionId}
            >
                <BsCursorFill className={cx.icon} />
                <Box className={cx.name}>{name}</Box>
            </Box>
        );
    });
};
