import React, { FC } from 'react';
import { Group, GroupProps, ScrollArea } from '@mantine/core';

import cx from './InlineFilters.Wrapper.module.css';

export type InlineFiltersWrapperProps = GroupProps & {
    variant?: 'dark' | 'light';
    size?: 'sm' | 'md' | 'lg';
};

const InlineFiltersWrapper: FC<
    GroupProps &
        InlineFiltersWrapperProps & {
            children: React.ReactNode;
        }
> = ({ variant, size = 'lg', justify = 'start', children, ...props }) => {
    return (
        <ScrollArea w="100%" type="never" className={cx.scrollArea}>
            <Group classNames={cx} justify={justify} gap={6} data-variant={variant} data-size={size} {...props}>
                {children}
            </Group>
        </ScrollArea>
    );
};

export { InlineFiltersWrapper };
