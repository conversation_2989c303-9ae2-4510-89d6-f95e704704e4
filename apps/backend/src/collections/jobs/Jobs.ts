import { CollectionConfig, Endpoint } from 'payload';

import { FeatureLimit, JOB_STATUS_OPTIONS, JobStatus } from '@repo/dcide-component-models';

import { projectCreateAccess } from '../Projects';
import { AccessChecks } from '../projects/access-checks';
import { RequestAccess } from '../../access/RequestAccess';

import { CreatedBy, DeletedAt, Name, TeamFromProject } from '../../fields';

import { broadcast } from './hooks/broadcast';
import { generateDiagramSnapshot } from './hooks/generate-diagram-snapshot';

import { pending } from './endpoints/pending';
import { update } from './endpoints/update';
import { remove } from './endpoints/delete';
import { rerun } from './endpoints/rerun';
import { webhook } from './endpoints/webhook';

import { excludeDeleted } from '../../hooks';
import { log } from './endpoints/log';
import { error } from './endpoints/error';
import { JobsCollection } from './types';

const Jobs: Omit<CollectionConfig, 'slug' | 'endpoints'> = {
    access: {
        create: projectCreateAccess(FeatureLimit.SIMULATIONS),
        read: async ({ req, id }) => {
            if (id) {
                return {
                    or: [
                        AccessChecks.userIsPartOfTeam(req.user, 'project'),
                        AccessChecks.userIsACollaborator(req.user, 'project'),
                    ],
                };
            }

            return req.user
                ? {
                      or: [
                          AccessChecks.userIsPartOfTeam(req.user, 'project'),
                          AccessChecks.userIsACollaborator(req.user, 'project'),
                          AccessChecks.userIsPartOfManufacturer(req.user, 'project'),
                      ],
                  }
                : false;
        },
        update: async ({ req, id }) => {
            if (id) {
                return {
                    or: [
                        AccessChecks.userIsPartOfTeam(req.user, 'project'),
                        AccessChecks.userIsACollaborator(req.user, 'project'),
                        AccessChecks.userIsPartOfManufacturer(req.user, 'project'),
                    ],
                };
            }

            return RequestAccess.userIsDeveloper({ req });
        },
        delete: async ({ req }) => {
            return {
                or: [
                    AccessChecks.userIsPartOfTeam(req.user, 'project'),
                    AccessChecks.userIsACollaborator(req.user, 'project'),
                    AccessChecks.userIsPartOfManufacturer(req.user, 'project'),
                ],
            };
        },
    },
    fields: [
        Name,
        {
            name: 'status',
            type: 'select',
            options: JOB_STATUS_OPTIONS,
            defaultValue: JobStatus.PENDING,
            required: true,
        },
        {
            name: 'error',
            type: 'json',
            defaultValue: {},
        },
        {
            name: 'execution',
            type: 'json',
            defaultValue: {},
        },
        {
            type: 'json',
            name: 'result',
            defaultValue: {},
        },
        {
            type: 'json',
            name: 'diagramSnapshot',
            defaultValue: {},
        },
        {
            type: 'relationship',
            name: 'diagram',
            relationTo: 'projectDesignDiagrams',
            required: true,
            maxDepth: 0,
        },
        {
            type: 'relationship',
            name: 'project',
            relationTo: 'projects',
            required: true,
            maxDepth: 0,
        },
        TeamFromProject,
        CreatedBy,
        DeletedAt,
    ],
    hooks: {
        beforeOperation: [excludeDeleted],
        beforeChange: [generateDiagramSnapshot],
        afterChange: [broadcast],
        // @ts-expect-error
        afterDelete: [broadcast],
    },
};

export const JobsEndpoints = (collection: JobsCollection): Endpoint[] =>
    [pending, update, remove, rerun, webhook, log, error].map((factory) => factory(collection));

export default Jobs;
