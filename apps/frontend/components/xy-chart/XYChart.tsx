import { useMemo, useState } from 'react';

import { Card, Flex, Overlay, Text } from '@mantine/core';

import { SimpleButton } from 'elements/buttons';

import { Chart, ChartPoint, ChartProps } from './components/Chart';
import { ChartForm, ChartFormPoint, ChartFormProps } from './components/Form';

import cx from './Chart.module.scss';

type Series = {
    value: number[];
    unit: string;
};

type FormState = {
    type: 'add' | 'edit';
    opened: boolean;
    activeIndex: number | null;
};

type Props = ChartProps & {
    title: string;
    fields: ChartFormProps['fields'];
    xSeries: Series;
    xUnitConverter?: (value: number) => number;
    ySeries: Series;
    yUnitConverter?: (value: number) => number;
    updateXYSeries: (data: { x: number[]; y: number[] }) => void;
    clearXYSeries: () => void;
    additionalButtons?: {
        label: string;
        onClick: () => void;
        disabled?: boolean;
    }[];
};

const XYChart = ({
    title,
    fields,
    xSeries,
    xUnitConverter = (value) => value,
    ySeries,
    yUnitConverter = (value) => value,
    updateXYSeries,
    clearXYSeries,
    additionalButtons,
    ...props
}: Props) => {
    const data: ChartPoint[] = xSeries.value.map((_, index) => ({
        x: xSeries.value[index],
        y: ySeries.value[index],
    }));

    const [formState, setFormState] = useState<FormState>({ type: 'add', opened: false, activeIndex: null });

    const openAddForm = () => setFormState({ ...formState, type: 'add', opened: true });
    const openEditForm = (index: number) => setFormState({ type: 'edit', opened: true, activeIndex: index });
    const closeForm = () => setFormState({ ...formState, opened: false, activeIndex: null });

    const updateData = (fx: (dataInBaseUnit: { x: number; y: number }[]) => { x: number; y: number }[]) => {
        const dataInBaseUnit = data.map((item) => ({
            x: xUnitConverter(item.x),
            y: yUnitConverter(item.y),
        }));

        const newData = fx(dataInBaseUnit);

        newData.sort((a, b) => a.x - b.x);

        const transformedData = newData.reduce(
            (result, item) => {
                result.x.push(item.x);
                result.y.push(item.y);

                return result;
            },
            {
                x: [],
                y: [],
            } as {
                x: number[];
                y: number[];
            },
        );

        updateXYSeries(transformedData);
    };

    const handleAddData = (values: ChartFormPoint) => {
        updateData((dataInBaseUnit) => {
            if (values.x.value === null || values.y.value === null) return dataInBaseUnit;

            return [...dataInBaseUnit, { x: values.x.value, y: values.y.value }];
        });

        closeForm();
    };

    const handleEditData = (values: ChartFormPoint, index: number) => {
        if (values.x.value === null || values.y.value === null) return;

        updateData((dataInBaseUnit) => {
            if (values.x.value === null || values.y.value === null) return dataInBaseUnit;

            const newData = [...dataInBaseUnit];

            newData[index] = { x: values.x.value, y: values.y.value };

            return newData;
        });

        closeForm();
    };

    const handleDeleteData = (index: number) => {
        updateData((dataInBaseUnit) => {
            const newData = [...dataInBaseUnit];

            newData.splice(index, 1);

            return newData;
        });

        closeForm();
    };

    const clearData = () => {
        clearXYSeries();
    };

    const defaultFormValues = useMemo(() => {
        if (formState.activeIndex !== null) {
            return {
                x: {
                    value: xUnitConverter(data[formState.activeIndex].x),
                },
                y: {
                    value: yUnitConverter(data[formState.activeIndex].y),
                },
            };
        }

        return {
            x: { value: null },
            y: { value: null },
        };
    }, [formState.activeIndex, xUnitConverter, yUnitConverter, data]);

    return (
        <Card pos="relative" withBorder radius="xs" p="xs" bg="gray.0">
            <Flex align="center" gap={4} mb="xs">
                <Text fw={600} fz="xs">
                    {title}
                </Text>
                <SimpleButton className={cx.button} onClick={openAddForm} ml="auto">
                    Add data
                </SimpleButton>
                <SimpleButton className={cx.button} onClick={clearData} disabled={!data.length}>
                    Clear data
                </SimpleButton>
                {additionalButtons?.map(({ label, onClick, disabled }) => (
                    <SimpleButton key={label} className={cx.button} onClick={onClick} disabled={disabled}>
                        {label}
                    </SimpleButton>
                ))}
            </Flex>
            <Chart data={data} handleDotClick={openEditForm} xUnit={xSeries.unit} yUnit={ySeries.unit} {...props} />

            {formState.opened && <Overlay color="black" backgroundOpacity={0.1} />}

            <ChartForm
                fields={fields}
                opened={formState.opened}
                closeForm={closeForm}
                label={formState.type === 'add' ? 'Add data' : 'Save'}
                defaultValues={defaultFormValues}
                handleSubmit={(values) =>
                    formState.type === 'add' ? handleAddData(values) : handleEditData(values, formState.activeIndex!)
                }
                handleDelete={formState.type === 'edit' ? () => handleDeleteData(formState.activeIndex!) : undefined}
            />
        </Card>
    );
};

export { XYChart };
