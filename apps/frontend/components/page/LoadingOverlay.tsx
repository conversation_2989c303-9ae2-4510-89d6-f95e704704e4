import React from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box } from '@mantine/core';

import { loadingState } from 'state/loading';

import cx from './LoadingOverlay.module.css';

const LoadingOverlay = () => {
    const { page, suspense } = useSnapshot(loadingState);

    return <Box className={cx.bar} data-loading={page || suspense} />;
};

export { LoadingOverlay };
