<div class="absolute z-20 w-full pt-6 md:pt-8 lg:pt-10 xl:pt-20">

    {% embed 'components/container' %}
        {% block containerContent %}

            <div class="flex justify-between items-center max-w-7xl mx-auto">
                <a class="text-brand-purple hover:text-white transition-colors" href="/">
                    {% include 'logo' with {
                        class: 'w-auto h-8 lg:h-10'
                    } %}
                </a>
                <button
                    class="
                        w-8 h-8 lg:w-10 lg:h-10 {{ entry.section.handle != 'homepage' ? 'lg:mr-10' }}
                        text-white hover:text-brand-purple
                        hover:rotate-90
                        transition-all
                    "
                    data-mobile-navigation-toggle
                >
                    <span class="sr-only">Open menu</span>
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M0 0h24v24H0z" stroke="none"/><path d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>

        {% endblock %}
    {% endembed %}

</div>

<div class="fixed inset-0 z-20 | hidden" data-mobile-navigation-container>

    <div
        class="absolute inset-0 | bg-black | opacity-0 transition-opacity"
        data-mobile-navigation-background
    ></div>

    <div
        class="
            absolute top-0 right-0 bottom-0
            flex flex-col
            w-full max-w-md
            p-14
            bg-grading-black text-white
            translate-x-full
        "
        data-mobile-navigation-content
    >

        <div class="flex justify-end">
            <button type="button" data-mobile-navigation-toggle>
                <span class="sr-only">Close menu</span>
                <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M0 0h24v24H0z" stroke="none"/>
                    <path d="M18 6 6 18M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <ul class="flex flex-col gap-8 | font-display font-bold text-2xl">
            {% for item in craft.navigation.nodes('main') %}
                <li>
                    <a class="transition-colors" href="{{ item.url }}">
                        {{ item.title }}
                    </a>
                </li>
            {% endfor %}

        </ul>

    </div>

</div>
