const generateToolPrompt = ({
    name,
    objective,
    activationCondition,
    exampleQuestions = [],
    toolCalling,
    mandatoryToolCalling,
    handler,
    notes = [],
}: {
    name: string;
    objective: string;
    activationCondition?: string;
    exampleQuestions?: string[];
    toolCalling?: string;
    mandatoryToolCalling?: string;
    handler: string;
    notes?: {
        title: string;
        description?: string;
    }[];
}) => {
    const base = `
        ## ${name}

        **Objective:**
        ${objective}
    `;

    const sections = [base];

    if (activationCondition) {
        sections.push(`
            **Activation Condition:**
            ${activationCondition}
        `);
    }

    if (exampleQuestions.length) {
        sections.push(`
            **Example Questions:**
            ${exampleQuestions.map((question) => `* "${question}"`).join('\n')}
        `);
    }

    if (toolCalling) {
        sections.push(`
            **Tool Calling:**
            ${toolCalling}
        `);
    }

    if (mandatoryToolCalling) {
        sections.push(`
            **Mandatory Tool Calling:**
            ${mandatoryToolCalling}
        `);
    }

    if (handler) {
        sections.push(`
            **Handling the User's Question:**
            ${handler}
        `);
    }

    if (notes.length) {
        sections.push(`
            **Notes:**
            ${notes.map((note) => `* **${note.title}**: ${note.description}`).join('\n')}
        `);
    }

    return sections.join('\n\n');
};

export { generateToolPrompt };
