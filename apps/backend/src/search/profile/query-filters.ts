import { Payload } from 'payload';
import { Types } from 'mongoose';
import { ProfileSearchState } from '@/search/types';
import { getId } from '@/helpers';
import { CompanyService } from '@repo/dcide-component-models';

export const addManufacturerQueryFilters = async (search: ProfileSearchState, payload: Payload): Promise<void> => {
    const { query } = search;

    // must be first
    if ('location' in query && query.location) {
        search.hasLocation = true;

        const coordinates = query.location.coordinates;
        search.stages.push({
            $geoNear: {
                near: { type: 'Point', coordinates: [coordinates.longitude, coordinates.latitude] },
                distanceField: 'distance',
                key: 'locations.address.coordinates',
            },
        });
    }

    if ('services' in query && query.services) {
        const servicesQuery = {
            $match: {
                $or: [
                    {
                        services: { $in: query.services },
                    },
                    {
                        serviceTags: { $in: query.services },
                    },
                ],
            },
        };
        search.stages.push(servicesQuery);
    }

    if ('applicationTags' in query && query.applicationTags) {
        const applicationQuery = {
            $match: {
                applicationTags: { $in: query.applicationTags },
            },
        };
        search.stages.push(applicationQuery);
    }

    if ('inAppSupport' in query && query.inAppSupport) {
        const inAppSupportQuery = {
            $match: { services: { $eq: CompanyService.IN_APP_SUPPORT } },
        };
        search.stages.push(inAppSupportQuery);
    }

    if ('status' in query && query.status) {
        const statusQuery = {
            $match: {
                status: query.status,
            },
        };
        search.stages.push(statusQuery);
    }
    if (
        'compliances' in query &&
        query.compliances &&
        Array.isArray(query.compliances) &&
        query.compliances.length > 0
    ) {
        const statusQuery = {
            $match: {
                $or: query.compliances.map((compliance) => ({
                    [`compliance.${compliance}`]: true,
                })),
            },
        };
        search.stages.push(statusQuery);
    }

    if ('event' in query && query.event) {
        const event = await payload.findByID({ collection: 'events', id: query.event });
        const manufacturersForEvent = event.companies?.map(({ company }) => getId(company)) ?? [];

        search.stages.push({
            $match: {
                _id: { $in: manufacturersForEvent.map((id) => new Types.ObjectId(id)) },
            },
        });
    }

    if ('manufacturer' in query && query.manufacturer) {
        search.stages.push({
            $match: {
                _id: new Types.ObjectId(query.manufacturer),
            },
        });
    }

    if ('systemSize' in query && query.systemSize) {
        search.stages.push({
            $match: {
                'systemSize.min': {
                    $lte: query.systemSize,
                },
                'systemSize.max': {
                    $gte: query.systemSize,
                },
            },
        });
    }

    if ('projectBudget' in query && query.projectBudget) {
        search.stages.push({
            $match: {
                'projectBudget.min': {
                    $lte: query.projectBudget,
                },
                'projectBudget.max': {
                    $gte: query.projectBudget,
                },
            },
        });
    }
};
