.table {
    :global(.mantine-ScrollArea-viewport) {
        padding-bottom: 0;
    }

    tr[data-voltage-type='AC'] {
        td:first-child {
            border-left: 6px solid var(--mantine-color-AC-0);
        }
    }

    tr[data-voltage-type='DC'] {
        td:first-child {
            border-left: 6px solid var(--mantine-color-DC-0);
        }
    }
}

.table {
    --row-height: 36px;

    background-color: white;

    border-radius: var(--mantine-radius-sm);
    overflow: hidden;

    border: 1px solid var(--mantine-color-gray-7);

    .header,
    .subheader {
        min-height: var(--row-height);

        display: flex;
        justify-content: flex-start;
        align-items: center;

        padding: 0 var(--mantine-spacing-xs);

        font-weight: 600;
        text-transform: uppercase;
        font-size: var(--mantine-font-size-xs);

        .headerTitle {
            font-weight: 700;
        }
    }

    .header {
        background-color: var(--mantine-color-gray-7);

        color: var(--mantine-color-gray-5);

        .headerTitle {
            color: white;
        }
    }

    .subheader {
        background-color: var(--mantine-color-gray-3);

        span:not(:last-child) {
            color: var(--mantine-color-gray-6);
        }
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;

        min-height: var(--row-height);

        padding-left: var(--mantine-spacing-xs);

        @media (max-width: var(--mantine-breakpoint-sm)) {
            padding: var(--mantine-spacing-xs);
        }

        &:nth-child(odd) {
            background-color: var(--mantine-color-gray-0);
        }

        &:nth-child(even) {
            background-color: white;
        }

        transition: background-color 0.2s ease-in-out;

        &:hover {
            background-color: var(--mantine-color-gray-1);
        }

        .label {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            min-width: 240px;

            font-size: var(--mantine-font-size-sm);
            white-space: nowrap;
            font-weight: 700;

            color: var(--color);
        }

        .content {
            display: flex;
            justify-content: center;
            gap: 8px;
            flex: 1;
            min-width: 280px;

            @media (max-width: var(--mantine-breakpoint-sm)) {
                margin-left: -10px;
            }

            > *:first-child {
                width: 100%;
            }
        }

        .rightSection {
            display: block;
            width: 24px;

            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .row + .row {
        border-top: 1px solid var(--mantine-color-gray-2);
    }

    .rowSpan {
        --color: var(--mantine-color-black);

        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;

        .content {
            flex-grow: 1;
        }
    }
}
