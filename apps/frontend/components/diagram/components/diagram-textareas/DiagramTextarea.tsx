import React, { useEffect, FC } from 'react';
import { PermissionDiagramGroups, type DiagramTextarea } from '@repo/dcide-component-models';

import { useSnapshot } from 'hooks/use-safe-snapshot';
import { uid, isArray } from 'radash';

import { Box } from '@mantine/core';
import { RichTextEditor, getTaskListExtension } from '@mantine/tiptap';
import { useElementSize } from '@mantine/hooks';

import { useHotkeys } from 'react-hotkeys-hook';

import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import TaskItem from '@tiptap/extension-task-item';
import TaskList from '@tiptap/extension-task-list';

// @ts-ignore
import UniqueId from 'tiptap-unique-id';

import { DiagramResizableElement } from './DiagramResizableElement';

import { useSelectedTextareas } from 'components/diagram/hooks/selection/use-selected-textareas';

import { TextareaService } from 'components/diagram/services/TextareaService';
import { textareaState } from 'components/diagram/state/textarea';
import { usePermission } from 'hooks/use-permission';
import { SelectionService } from 'components/diagram/services/SelectionService';

import { DraggingType } from 'types/DraggingType';

import { DiagramTextareaToolbar } from './DiagramTextareaToolbar';

import cx from './DiagramTextarea.module.css';
import { useDiagram } from 'components/diagram/hooks';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

const DiagramTextarea: FC<{
    textarea: DiagramTextarea;
}> = ({ textarea }) => {
    const canEdit = usePermission(PermissionDiagramGroups.EDIT);

    const { created } = useSnapshot(textareaState);

    const contentAsComparableString = JSON.stringify(textarea.content);
    const editor = useEditor(
        {
            extensions: [
                StarterKit,
                TextAlign.configure({ types: ['heading', 'paragraph'] }),
                TaskItem.configure({
                    nested: true,
                    onReadOnlyChecked: (node) => {
                        if (canEdit) {
                            const id = node.attrs.id;
                            const content = JSON.parse(JSON.stringify(textarea.content));

                            const map = (node: any): any => {
                                if (node.type === 'taskItem' && node.attrs.id === id) {
                                    return {
                                        ...node,
                                        attrs: {
                                            ...node.attrs,
                                            checked: !node.attrs.checked,
                                        },
                                    };
                                }

                                if (node.content && isArray(node.content)) {
                                    return {
                                        ...node,
                                        content: node.content.map(map),
                                    };
                                }

                                return node;
                            };

                            content.content = content.content.map(map);

                            TextareaService.updateContent(textarea.id, content);

                            DiagramSyncService.save();
                        }

                        return false;
                    },
                }),
                UniqueId.configure({
                    attributeName: 'id',
                    types: ['taskItem'],
                    createId: () => uid(8),
                }),
                getTaskListExtension(TaskList),
            ],
            content: textarea.content,
            editable: false,
            immediatelyRender: false,
        },
        [contentAsComparableString],
    );

    const enableEditable = () => {
        if (!canEdit) {
            return;
        }

        if (!editor) {
            return;
        }

        if (!editor.isEditable) {
            editor!.setEditable(true, true);
            editor!.commands.focus('end');
        }
    };

    const { isSelectedTextarea, isOnlySelectedTextarea } = useSelectedTextareas();
    const selected = isSelectedTextarea(textarea.id);
    const isOnlySelected = isOnlySelectedTextarea(textarea.id);
    const isPartOfSelection = selected && !isOnlySelected;

    useEffect(() => {
        if (!!editor && textarea.id === created) {
            editor.setEditable(true, true);
            textareaState.created = null;

            setTimeout(() => {
                editor.commands.focus('end');
            });
        }
    }, [!!editor, textarea.id, created]);

    useEffect(() => {
        if (!editor || !editor.isEditable) {
            return;
        }

        if (isOnlySelected) {
            return;
        }

        const isEmpty = editor.isEmpty || editor.getText().trim() === '';

        if (isEmpty) {
            TextareaService.delete(textarea.id);
        } else {
            TextareaService.updateContent(textarea.id, editor.getJSON());
            DiagramSyncService.save();
        }

        editor?.setEditable(false, true);
    }, [isOnlySelected, !!editor]);

    const { ref, height } = useElementSize();

    useEffect(() => {
        if (!editor?.isEditable) return;
        if (height === 0) return;

        TextareaService.resize(
            textarea.id,
            textarea.position,
            {
                width: textarea.dimensions.width,
                height,
            },
            false,
        );
    }, [height]);

    useHotkeys(['enter'], () => {
        if (isOnlySelected && !editor?.isEditable) {
            enableEditable();
        }
    });

    return editor ? (
        <DiagramResizableElement
            type={DraggingType.TEXTAREA}
            element={textarea}
            resizeHandles={editor.isEditable ? [] : ['e', 'w']}
            draggable={!editor.isEditable}
            onResizeStop={(data) => {
                TextareaService.resize(textarea.id, data.position, {
                    width: data.dimensions.width,
                    height,
                });
            }}
        >
            <RichTextEditor
                className={cx.textareaWrapper}
                style={{
                    '--textarea-border-style': textarea.borderStyle,
                    '--textarea-border-color': textarea.borderColor,
                    '--textarea-background-color': textarea.backgroundColor,
                }}
                editor={editor}
                onKeyUp={(event) => {
                    if (editor.isEditable && event.key === 'Escape') {
                        editor.setEditable(false, true);
                    }
                }}
            >
                <DiagramTextareaToolbar textarea={textarea} editor={editor} />
                <Box
                    ref={ref}
                    className={cx.textarea}
                    onClick={(event) => {
                        if (!isOnlySelected) {
                            return;
                        }

                        const type = (event.target as HTMLInputElement).type;

                        if (type === 'checkbox') {
                            return;
                        }

                        enableEditable();
                    }}
                    onContextMenu={(event) => {
                        if (editor.isEditable) {
                            event.stopPropagation();
                            return;
                        }

                        event.preventDefault();

                        if (!canEdit) {
                            return;
                        }

                        if (isPartOfSelection) {
                            return;
                        }

                        SelectionService.selectTextarea(textarea.id);
                    }}
                    data-editable={editor.isEditable}
                    style={{
                        '--textarea-color': textarea.color,
                        '--textarea-font-size': textarea.fontSize,
                    }}
                >
                    <RichTextEditor.Content />
                </Box>
            </RichTextEditor>
        </DiagramResizableElement>
    ) : null;
};

const WrappedDiagramTextarea = ({ id }: { id: DiagramTextarea['id'] }) => {
    const { textareas } = useDiagram();
    const textarea = textareas.find((textarea) => textarea.id === id);

    if (!textarea) {
        return null;
    }

    return <DiagramTextarea textarea={textarea} />;
};

export { WrappedDiagramTextarea as DiagramTextarea };
