import { ProfileSearchState } from '@/search/types';
import {
    getSynonymsStages,
    addTextSearch,
    addVectorSearch,
    setTextSearchFlags,
    getLocationStage,
    getTextStages,
} from '@/search/helpers/text-and-vector-search-stages';
import { addManufacturerQueryFilters } from './query-filters';
import { addSortStages } from './add-sort-stages';
import { addPostSortStages } from './post-sort-operations';
import { addScoreFields } from './add-score-fields';
import { SearchIndex } from '@repo/dcide-component-models';
import { Payload } from 'payload';
import { config } from '@/config';

export const configureProfileSearch = async (search: ProfileSearchState, payload: Payload) => {
    await setupTextAndVectorSearch(search);
    await addManufacturerQueryFilters(search, payload);
    addScoreFields(search);
    addSortStages(search);
    addPostSortStages(search);
};

const setupTextAndVectorSearch = async (search: ProfileSearchState) => {
    search.exactMatch = false;

    setTextSearchFlags(search);

    if (search.hasVectorSearch) {
        await addVectorSearch(search, SearchIndex.MANUFACTURER_INDEX);
    }

    if (search.hasTextSearch) {
        const textQuery = search.query.search!;
        addTextSearch(SearchIndex.MANUFACTURERS_FULL_TEXT_INDEX, search, [
            ...getTextStages(textQuery, 'name', config.search.textSearch.nameBoost),
            ...getSynonymsStages(textQuery, 'embeddingText', config.search.textSearch.embeddingTextBoost),
            ...getLocationStage(search, config.search.textSearch.locationBoost),
        ]);
    }
};
