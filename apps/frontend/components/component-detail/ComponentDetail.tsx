import React, { FC, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Affix, Anchor, Box, Drawer, Group, Text } from '@mantine/core';

import { CompanyService, Component, PermissionComponent } from '@repo/dcide-component-models';

import { useCurrentUser } from 'hooks/use-current-user';
import { useDatasheetMeta } from 'components/component-datasheet/hooks/use-datasheet-meta';
import { useDatasheetProductSeriesComponents } from 'components/component-datasheet/hooks/use-datasheet-product-series-components';
import {
    componentChatState,
    componentChatStateDefaults,
    useComponentChat,
} from 'components/component-chat/hooks/useComponentChat';
import { DatasheetMode, useDatasheetMode } from 'components/datasheet';
import { useCompanyProfile } from 'hooks/use-company-profile';
import { useComponentPermissions } from 'hooks/use-component-permissions';

import { componentSignupState } from 'components/component-signup/state';

import { Page } from 'components/page/Page';
import { AISidebar } from 'components/component-signup/components/AISidebar';

import { ComponentForm } from 'components/component-form/ComponentForm';
import { ComponentChatButton } from 'components/component-chat';
import { ComponentDatasheet } from 'components/component-datasheet';
import { ComponentChatSidebar } from 'components/component-chat/components/ComponentChatSidebar';
import { ComponentDatasheetProductVariants } from 'components/component-datasheet/components/ComponentDatasheetProductVariants';
import { ComponentDatasheetActionsPublic } from 'components/component-datasheet/components/ComponentDatasheetActions.Public';
import { ComponentDatasheetActionsEdit } from 'components/component-datasheet/components/ComponentDatasheetActions.Edit';
import { SubscriptionNotice } from 'components/component-datasheet/components/sections';
import { ComponentDetailTabs } from 'components/component-detail/ComponentDetail.Tabs';
import { ManufacturedBy } from 'components/component-datasheet/components/sections/ManufacturedBy';

import { ComponentIntercom } from 'components/intercom/types/ComponentIntercom';
import { ComponentService } from 'services/ComponentService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { useWatch } from 'react-hook-form';

import { StateHelpers } from 'helpers/StateHelpers';
import { diff } from 'helpers/diff';

import { CurrentComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import cx from './ComponentDetail.module.css';

interface Props {
    component: Component;
    initialMode: DatasheetMode;
}

const ComponentDetail: FC<Props> = ({ component, initialMode }) => {
    const mode = useDatasheetMode();
    const permissions = useComponentPermissions(component);
    const canEdit = permissions[PermissionComponent.EDIT];

    const componentId = useWatch({
        name: 'id',
    });

    const user = useCurrentUser();
    const { company: manufacturer } = useCompanyProfile(component.manufacturer);

    const { chatOpen, toggleChatOpen } = useComponentChat();

    const hasFiles = component.files?.length > 0;

    let title = initialMode === DatasheetMode.DUPLICATE ? `Duplicate ${component.name}` : component.name;

    if (mode === 'bulk') {
        title = `Editing series: ${component.productSeries}`;
    }

    const { componentType } = useDatasheetMeta(component);

    const { components: variants } = useDatasheetProductSeriesComponents();
    const { aiOpened: showAiAssistant, aiOpenedExpanded } = useSnapshot(componentSignupState);
    const aiAssistantWidth = aiOpenedExpanded ? '70%' : '40%';

    const closeAiAssistant = () => {
        componentSignupState.aiOpened = false;
    };

    const subtitleItems = [
        componentType?.label,
        !variants || variants.length <= 1 ? component.productIdentifier : <ComponentDatasheetProductVariants />,
        component.productSeries,
    ].filter(Boolean);

    const router = useRouter();
    const { query, pathname } = router;

    // open chat if query param is present
    useEffect(() => {
        const { action, ...updatedQuery } = query;

        if (action === 'chat') {
            hasFiles && toggleChatOpen();

            const newPath = {
                pathname: pathname,
                query: updatedQuery,
            };

            router.replace(newPath, undefined, { shallow: true }).then();
        }
    }, [query, hasFiles, pathname, router, toggleChatOpen]);

    // This effect is dirty and could use a rework..
    useEffect(() => {
        if (mode === DatasheetMode.DUPLICATE && componentId) {
            ComponentService.navigate.view(componentId);
        }
    }, [componentId, mode]);

    const showAiChat = user && hasFiles;
    const showIntercom = manufacturer && manufacturer.services.includes(CompanyService.IN_APP_SUPPORT);

    return (
        <Page
            showBackground
            breadcrumbs={{
                isSticky: true,
                rightSection: canEdit ? <ComponentDatasheetActionsEdit buttonSize="xs" /> : null,
            }}
            title={title}
        >
            {showAiChat && (
                <>
                    <Drawer
                        keepMounted
                        position="right"
                        withCloseButton={false}
                        opened={chatOpen}
                        onClose={toggleChatOpen}
                        styles={{
                            body: { padding: 0, height: '100%' },
                        }}
                    >
                        <ComponentChatSidebar component={component} />
                    </Drawer>
                    <Drawer
                        keepMounted
                        position="right"
                        withCloseButton={false}
                        opened={showAiAssistant}
                        onClose={closeAiAssistant}
                        styles={{
                            body: { padding: 0, height: '100%' },
                        }}
                        size={aiAssistantWidth}
                    >
                        <AISidebar />
                    </Drawer>
                </>
            )}

            <Page.Content
                title={title}
                subtitle={subtitleItems.map((item, index) => (
                    <React.Fragment key={index}>
                        {item}
                        {index < subtitleItems.length - 1 && ' · '}
                    </React.Fragment>
                ))}
                afterTitle={<ComponentDatasheetActionsPublic />}
                rightSection={<ManufacturedBy />}
            >
                <SubscriptionNotice component={component} />
                <Box className={cx.root}>
                    <ComponentDetailTabs component={component}>
                        <ComponentDatasheet component={component} initialMode={initialMode} />
                    </ComponentDetailTabs>

                    <Affix
                        position={{ right: 16, bottom: 16 }}
                        // Drawer with AiChat is at zIndex 200
                        zIndex={199}
                    >
                        <Group gap={8}>
                            {showAiChat && <ComponentChatButton toggleChatOpen={toggleChatOpen} />}
                            {showIntercom && <ComponentIntercom company={manufacturer} component={component} />}
                        </Group>
                    </Affix>
                </Box>
                <Text c="dimmed" style={{ textAlign: 'center' }}>
                    This datasheet is powered by the{' '}
                    <Anchor href="https://github.com/Direct-Energy-Partners/interoperability-data-model" inline>
                        Interoperability Data Model
                    </Anchor>{' '}
                    maintained by{' '}
                    <Anchor href="https://www.emergealliance.org/" inline>
                        EMerge Alliance
                    </Anchor>
                </Text>
            </Page.Content>
        </Page>
    );
};

const WrappedComponentDetail: FC<Props> = (props) => {
    const [currentComponent, setCurrentComponent] = useState<Component>(props.component);

    useEffect(() => {
        return () => {
            StateHelpers.reset(componentChatState, componentChatStateDefaults);
        };
    }, [props.component.id]);

    useEffect(() => {
        setCurrentComponent(props.component);
    }, [props.component.id]);

    return (
        <CurrentComponentContext.Provider
            value={{
                component: currentComponent,
                setComponent: setCurrentComponent,
            }}
        >
            <ComponentForm
                key={props.component.id}
                initialValues={props.component}
                onUpdate={(component) => {
                    setCurrentComponent(component);

                    const { updates, deletes } = diff(currentComponent, component);

                    InternalTrackingService.track('product.update', {
                        component,
                        updates,
                        deletes,
                    });
                }}
            >
                <ComponentDetail {...props} />
            </ComponentForm>
        </CurrentComponentContext.Provider>
    );
};

export { WrappedComponentDetail as ComponentDetail };
