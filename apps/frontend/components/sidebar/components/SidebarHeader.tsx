import React, { <PERSON> } from 'react';

import { Box, Space, Text } from '@mantine/core';

import { SidebarSectionTitle } from './SidebarSectionTitle';

import cx from './SidebarComponents.module.css';

export const SidebarHeader: FC<{
    title?: React.ReactNode;
    subtitle?: React.ReactNode;
    children?: React.ReactNode;
    rightSection?: React.ReactNode;
    onClick?: () => void;
    onMouseEnter?: () => void;
    onMouseLeave?: () => void;
}> = ({ title, subtitle, children, rightSection, onClick, onMouseEnter, onMouseLeave, ...rest }) => {
    if (!children) {
        return null;
    }

    return (
        <Box className={cx.header} onClick={onClick} onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} {...rest}>
            {title && (
                <React.Fragment>
                    <SidebarSectionTitle>
                        {title}
                        {rightSection}
                    </SidebarSectionTitle>
                    {subtitle && (
                        <Text fz="xs" c="dimmed">
                            {subtitle}
                        </Text>
                    )}
                    <Space h="xs" />
                </React.Fragment>
            )}
            {children}
        </Box>
    );
};
