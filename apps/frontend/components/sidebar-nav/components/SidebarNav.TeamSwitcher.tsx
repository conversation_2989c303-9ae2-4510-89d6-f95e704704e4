import { FC, useEffect, useState } from 'react';

import Link from 'next/link';

import { Button, Menu, Text } from '@mantine/core';

import { TbUsers } from 'react-icons/tb';
import { IoAddSharp, IoChevronDown, IoSettingsOutline } from 'react-icons/io5';

import { Typewriter } from 'components/typewriter/Typewriter';

import { PermissionProject, TeamInfo } from '@repo/dcide-component-models';

import { useTeams } from 'hooks';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { TextHelpers } from 'helpers/TextHelpers';

import { UserService } from 'services/UserService';
import { TeamService } from 'services/TeamService';
import { RouterService } from 'services/RouterService';

import cxMenu from './Menu.module.css';
import cx from './SidebarNav.TeamSwitcher.module.css';
import { useRouterQuery } from 'hooks/use-router-query';

const MAX_TEAM_NAME_LENGTH = 22;

const SidebarNavTeamSwitcher: FC = () => {
    const user = useCurrentUser();
    const { teams } = useTeams();
    const currentTeam = useCurrentTeam();

    const [render, setRender] = useState(false);
    const [animate, setAnimate] = useState(false);

    useEffect(() => {
        setRender(true);

        const switched = window.sessionStorage.getItem('switched-team');

        if (switched !== currentTeam?.id) {
            setAnimate(true);
        }

        window.sessionStorage.setItem('switched-team', currentTeam?.id || 'non-existing');
    }, []);

    const availableTeams = teams?.filter(
        (team) => team.permissions.includes(PermissionProject.ALL) && team.id !== currentTeam?.id,
    );

    const openCreateTeamModal = () => {
        TeamService.openCreateTeam();
    };

    if (!user) return null;
    if (!render) return null;

    return (
        <Menu
            classNames={cxMenu}
            offset={4}
            position="bottom-start"
            transitionProps={{
                transition: 'pop-top-left',
            }}
        >
            <Menu.Target>
                <Button
                    variant="subtle"
                    size="compact-xs"
                    leftSection={<TbUsers size={12} />}
                    rightSection={<IoChevronDown size={10} />}
                    className={cx.teamSwitcher}
                >
                    <Typewriter enabled={animate}>{currentTeam ? currentTeam.name : '[no team]'}</Typewriter>
                </Button>
            </Menu.Target>
            <Menu.Dropdown>
                {currentTeam && (
                    <Menu.Item component={Link} href="/account#team" leftSection={<IoSettingsOutline />} fw={600}>
                        Team settings
                        <Text fz="xs" c="dimmed">
                            {TextHelpers.getTextWithEllipsis(currentTeam.name, MAX_TEAM_NAME_LENGTH)}
                        </Text>
                    </Menu.Item>
                )}

                <Menu.Divider />

                {availableTeams.length > 0 && <Menu.Label>Switch to team</Menu.Label>}

                {availableTeams.map((team) => (
                    <TeamRow key={team.id} team={team} />
                ))}

                {availableTeams.length > 0 && <Menu.Divider />}

                <Menu.Item leftSection={<IoAddSharp />} fw={500} onClick={openCreateTeamModal}>
                    Create team
                </Menu.Item>
            </Menu.Dropdown>
        </Menu>
    );
};

const TeamRow: FC<{
    team: TeamInfo;
}> = ({ team }) => {
    const { projectId } = useRouterQuery<{ projectId?: string }>();

    const switchTeam = async () => {
        await UserService.switchTeam(team.id);

        if (projectId) {
            RouterService.push('/projects').then();
            return;
        }

        RouterService.reload();
    };

    return (
        <Menu.Item leftSection={<TbUsers />} onClick={switchTeam}>
            {TextHelpers.getTextWithEllipsis(team.name, MAX_TEAM_NAME_LENGTH)}
        </Menu.Item>
    );
};

export { SidebarNavTeamSwitcher };
