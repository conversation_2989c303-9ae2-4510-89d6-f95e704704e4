import { Access, CollectionConfig, Field } from 'payload';

import { intersects } from 'radash';

import {
    CreatedBy,
    Name,
    Tags,
    TeamFromUser,
    DateField,
    User,
    Subscribers,
    subscribeRequestUser,
    getUnsubscribeEndpoint,
    getSubscribeEndpoint,
    FilesField,
    PermissionsField,
    Address,
} from '../fields';

import { ProjectPermissions } from './projects/permissions';

import {
    duplicate,
    deleteCollaborator,
    deleteManufacturer,
    deleteProject,
    editCollaborator,
    editStatus,
    inviteCollaborator,
    recent,
    restoreProject,
    teamProjectCounts,
    preload,
    requestAccess,
    marketplace,
    marketplaceTags,
    marketplaceManufacturers,
} from './projects/endpoints';

import {
    addLastUpdated,
    addPermissions,
    deleteDesigns,
    excludeDeleted,
    onProjectCreate,
    switchTeam,
    updateStatus,
} from './projects/hooks';

import { getByDesign } from './projects/endpoints/get-by-design';

import { getRequestUser } from '../helpers/get-request-user';
import { getRequestTeam } from '../helpers/get-request-team';

import { AccessHelpers } from '../helpers/AccessHelpers';
import { RequestAccess } from '../access/RequestAccess';

import { getFeatureLimitCount } from '../helpers/get-feature-limit-count';

import { AccessChecks } from './projects/access-checks';
import {
    DesignerSubscription,
    FeatureLimit,
    FeatureLimits,
    getDesignerSubscriptionData,
    SubscriptionData,
} from '@repo/dcide-component-models';

const projectStatuses = {
    progress: {
        key: 'progress',
        name: 'In progress',
    },
    review: {
        key: 'review',
        name: 'In review',
    },
    done: {
        key: 'done',
        name: 'Done',
    },
    archived: {
        key: 'archived',
        name: 'Archived',
    },
    deleted: {
        key: 'deleted',
        name: 'Deleted',
    },
};

export const projectCreateAccess =
    (limit: FeatureLimit.PROJECTS | FeatureLimit.SIMULATIONS): Access =>
    async ({ req, id }) => {
        const user = await getRequestUser(req);
        const team = await getRequestTeam(req);

        if (!user || !team) return false;

        if (AccessHelpers.userIsDeveloper(user)) {
            return true;
        }

        const hasTeamPermission = team?.users?.some((needle) => {
            return needle.user === user?.id && intersects(needle.newPermissions ?? [], ['project.all', 'project.edit']);
        });

        if (!hasTeamPermission) return false;

        if (id) {
            const count = await getFeatureLimitCount(limit, team, req);

            const subscription =
                getDesignerSubscriptionData(team.subscriptions as SubscriptionData[])?.subscription ??
                DesignerSubscription.FREE;

            if (count >= FeatureLimits[limit][subscription]) {
                return false;
            }
        }

        return true;
    };

export const ProjectStatuses = Object.keys(projectStatuses);

const imageDescription = 'Group: projects:[id]:images eg. projects:65b8e1df4923ac5ccb823796:images';

const Projects: CollectionConfig = {
    slug: 'projects',
    admin: {
        useAsTitle: 'name',
    },
    access: {
        create: projectCreateAccess(FeatureLimit.PROJECTS),
        read: async ({ req, id }) => {
            if (id && !req.user) {
                return AccessChecks.everyone();
            }

            if (id) {
                if (AccessChecks.userIsDeveloper(req.user)) {
                    return true;
                }

                return {
                    or: [
                        AccessChecks.userIsPartOfTeam(req.user),
                        AccessChecks.userIsACollaborator(req.user),
                        AccessChecks.userIsPartOfManufacturer(req.user),
                        AccessChecks.everyone(),
                    ],
                };
            }

            return req.user
                ? {
                      or: [
                          AccessChecks.userIsPartOfTeam(req.user),
                          AccessChecks.userIsACollaborator(req.user),
                          AccessChecks.userIsPartOfManufacturer(req.user),
                      ],
                  }
                : false;
        },
        update: async ({ req }) => {
            if (AccessChecks.userIsDeveloper(req.user)) {
                return true;
            }

            return {
                or: [AccessChecks.userIsPartOfTeam(req.user), AccessChecks.userIsACollaborator(req.user)],
            };
        },
        delete: RequestAccess.userIsDeveloper,
    },
    fields: [
        {
            type: 'tabs',
            tabs: [
                {
                    label: 'General',
                    fields: [
                        {
                            ...Name,
                            index: true,
                        } as Field,
                        {
                            name: 'thumbnail',
                            type: 'upload',
                            relationTo: 'files',
                            admin: {
                                description: imageDescription,
                            },
                        },
                        {
                            name: 'description',
                            type: 'textarea',
                        },
                        {
                            name: 'customer',
                            type: 'text',
                        },
                        Tags('tags'),
                        Address('location'),
                        {
                            name: 'designs',
                            type: 'relationship',
                            relationTo: 'projectDesigns',
                            hasMany: true,
                            maxDepth: 0,
                        },
                        {
                            name: 'collaborators',
                            type: 'group',
                            fields: [
                                {
                                    name: 'users',
                                    type: 'array',
                                    fields: [
                                        {
                                            name: 'user',
                                            type: 'relationship',
                                            relationTo: 'users',
                                            required: true,
                                            maxDepth: 0,
                                            index: true,
                                        },
                                        {
                                            ...PermissionsField,
                                            name: 'permissions',
                                            options: [...ProjectPermissions],
                                        } as Field,
                                    ],
                                },
                                {
                                    name: 'manufacturers',
                                    type: 'relationship',
                                    relationTo: 'manufacturers',
                                    hasMany: true,
                                    maxDepth: 0,
                                    defaultValue: () => [],
                                    index: true,
                                },
                            ],
                        },
                    ],
                },
                {
                    label: 'Status updates',
                    fields: [
                        {
                            name: 'status',
                            type: 'text',
                            admin: {
                                disabled: true,
                                readOnly: true,
                            },
                            index: true,
                        },
                        {
                            name: 'statusUpdates',
                            type: 'array',
                            fields: [
                                {
                                    name: 'status',
                                    type: 'select',
                                    options: Object.values(projectStatuses).map((status) => ({
                                        value: status.key,
                                        label: status.name,
                                    })),
                                },
                                DateField,
                                {
                                    ...User,
                                    maxDepth: 0,
                                } as Field,
                            ],
                            defaultValue: [],
                        },
                    ],
                },
                {
                    name: 'wireSizing',
                    label: 'Wire sizing',
                    fields: [
                        {
                            name: 'standard',
                            type: 'select',
                            options: [
                                { label: 'IEC', value: 'IEC' },
                                { label: 'NEC', value: 'NEC' },
                            ],
                        },
                        {
                            name: 'voltageDrop',
                            type: 'number',
                            min: 0,
                            max: 1,
                        },
                    ],
                },
                {
                    label: 'Files',
                    fields: [FilesField()],
                },
                {
                    label: 'Meta',
                    fields: [
                        {
                            name: 'visibility',
                            type: 'select',
                            options: [
                                { label: 'Public', value: 'public' },
                                { label: 'Private', value: 'private' },
                                { label: 'Marketplace', value: 'marketplace' },
                            ],
                        },
                        {
                            name: 'isReferenceDesign',
                            type: 'checkbox',
                            admin: {
                                readOnly: true,
                            },
                            hooks: {
                                afterRead: [
                                    ({ value, originalDoc }) => {
                                        return value || originalDoc.visibility === 'marketplace';
                                    },
                                ],
                            },
                        },
                        {
                            name: 'template',
                            type: 'group',
                            fields: [
                                {
                                    name: 'profile',
                                    type: 'relationship',
                                    relationTo: 'manufacturers',
                                    maxDepth: 0,
                                },
                                {
                                    name: 'components',
                                    type: 'relationship',
                                    relationTo: 'components',
                                    hasMany: true,
                                },
                                {
                                    name: 'indicators',
                                    type: 'json',
                                },
                            ],
                            defaultValue: {},
                        },
                        {
                            name: 'reference', // this project was copied from another project
                            type: 'group',
                            fields: [
                                {
                                    name: 'profile',
                                    type: 'relationship',
                                    relationTo: 'manufacturers',
                                    maxDepth: 0,
                                },
                                {
                                    name: 'project',
                                    type: 'relationship',
                                    relationTo: 'projects',
                                },
                            ],
                            defaultValue: {},
                        },
                        {
                            ...PermissionsField,
                            name: 'everyonePermissions',
                            options: [...ProjectPermissions],
                        } as Field,
                        CreatedBy,
                        TeamFromUser,
                        Subscribers,
                        {
                            name: 'updatedBy',
                            type: 'relationship',
                            relationTo: 'users',
                        },
                    ],
                },
            ],
        },
    ],
    hooks: {
        beforeChange: [updateStatus, subscribeRequestUser, addLastUpdated],
        afterChange: [onProjectCreate],
        afterDelete: [deleteDesigns],
        afterRead: [addPermissions, switchTeam],
        beforeOperation: [excludeDeleted],
    },
    endpoints: [
        duplicate,
        inviteCollaborator,
        editCollaborator,
        deleteCollaborator,
        deleteManufacturer,
        editStatus,
        deleteProject,
        restoreProject,
        getSubscribeEndpoint('projects'),
        getUnsubscribeEndpoint('projects'),
        recent,
        teamProjectCounts,
        getByDesign,
        preload,
        requestAccess,
        marketplace,
        marketplaceTags,
        marketplaceManufacturers,
    ],
};

export default Projects;
