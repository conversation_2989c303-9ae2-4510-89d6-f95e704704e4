import React from 'react';

import { ActionIcon, Badge, BadgeProps, Tooltip } from '@mantine/core';
import { IoCheckmarkSharp, IoEyeOffSharp, IoTimeOutline } from 'react-icons/io5';

import { PublishedStatus } from '@repo/dcide-component-models';

import cx from './PublishedBadge.module.css';

const PublishedBadge = ({
    status,
    iconOnly,
    withTooltip,
    publishedLabel = 'Published',
    reviewLabel = 'In review',
    notPublishedLabel = 'Unpublished',
    ...rest
}: {
    status: PublishedStatus;
    iconOnly?: boolean;
    withTooltip?: boolean | string;
    onClick?: () => void;
    publishedLabel?: string;
    reviewLabel?: string;
    notPublishedLabel?: string;
} & Pick<BadgeProps, 'variant' | 'size' | 'style'>) => {
    const props = {
        'className': cx.root,
        'data-clickable': !!rest.onClick,
        'size': 'sm',
        'variant': 'light',
    };

    const actionProps = {
        'className': cx.root,
        'data-clickable': !!rest.onClick,
        'size': 'xs',
        'variant': 'light',
        'radius': 99,
    };

    const getContent = () => {
        if (iconOnly) {
            switch (status) {
                case PublishedStatus.DRAFT:
                    return (
                        <ActionIcon {...actionProps} color="orange" {...rest}>
                            <IoEyeOffSharp size={10} />
                        </ActionIcon>
                    );
                case PublishedStatus.REVIEW:
                    return (
                        <ActionIcon {...actionProps} color="yellow" {...rest}>
                            <IoTimeOutline size={10} />
                        </ActionIcon>
                    );
                default:
                    return (
                        <ActionIcon {...actionProps} color="green" {...rest}>
                            <IoCheckmarkSharp size={10} />
                        </ActionIcon>
                    );
            }
        }

        switch (status) {
            case PublishedStatus.DRAFT:
                return (
                    <Badge {...props} color="orange" leftSection={<IoEyeOffSharp />} {...rest}>
                        {notPublishedLabel}
                    </Badge>
                );

            case PublishedStatus.REVIEW:
                return (
                    <Badge {...props} color="yellow" leftSection={<IoTimeOutline />} {...rest}>
                        {reviewLabel}
                    </Badge>
                );

            default:
                return (
                    <Badge {...props} color="green" leftSection={<IoCheckmarkSharp />} {...rest}>
                        {publishedLabel}
                    </Badge>
                );
        }
    };

    const getTooltip = () => {
        if (!withTooltip && !iconOnly) {
            return '';
        }

        if (typeof withTooltip === 'string') {
            return withTooltip;
        }

        switch (status) {
            case PublishedStatus.DRAFT:
                return notPublishedLabel;
            case PublishedStatus.REVIEW:
                return reviewLabel;
            default:
                return publishedLabel;
        }
    };

    return (
        <Tooltip label={getTooltip()} disabled={!getTooltip()}>
            <span>{getContent()}</span>
        </Tooltip>
    );
};

PublishedBadge.displayName = 'PublishedBadge';

export { PublishedBadge };
