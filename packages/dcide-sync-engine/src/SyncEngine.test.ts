import { SyncEngineClient, SyncEngineServer } from './';
import { Pointer, Transaction } from './types';

type State = {
    [key: string]: any;
};

(async () => {
    test('create item on client and sync to server', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
            c: 3,
            d: 4,
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        let clientState: State = JSON.parse(JSON.stringify(initialState));

        const client = new SyncEngineClient({
            getState: () => clientState,
            setState: (state: State) => {
                console.log('Client state updated', state);
                clientState = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client.create({ e: 5 });
        client.save();

        // Push changes of client to server
        const localTransactions = JSON.parse(JSON.stringify(client.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(localTransactions, client.pointer);
        client.pullFromServer(returnedTransactions);

        expect(serverState).toEqual({
            a: 1,
            b: 2,
            c: 3,
            d: 4,
            e: 5,
        });

        expect(serverState).toEqual(clientState);

        expect(client.getNumberOfTransactions().created).toEqual(0);
        expect(client.getNumberOfTransactions().validatedByServer).toEqual(1);
    });

    test('create, update and delete on two clients', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
            c: 3,
            d: 4,
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];
        let clientState1: State = JSON.parse(JSON.stringify(initialState));
        let clientState2: State = JSON.parse(JSON.stringify(initialState));

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        const client2 = new SyncEngineClient({
            getState: () => clientState2,
            setState: (state: State) => {
                console.log('Client 2 state updated', state);
                clientState2 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.create({ e: 5 });
        client1.update({ a: 111 });
        client1.delete(['b']);
        client1.save();

        // Push changes of client 1 to server
        const localTransactions = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(localTransactions, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        expect(clientState1).toEqual({
            a: 111,
            c: 3,
            d: 4,
            e: 5,
        });

        // Invalid update which will discard updating b, because the key does not exist
        client2.update({ b: 222 });
        client2.save();

        // Push update of client 2 to the server
        const localTransactions2 = JSON.parse(JSON.stringify(client2.pushToServer()));
        const { transactionsToReturn: returnedTransactions2 } = await server.pull(localTransactions2, client2.pointer);
        client2.pullFromServer(returnedTransactions2);

        expect(clientState2).toEqual({
            a: 111,
            c: 3,
            d: 4,
            e: 5,
        });

        // Update on client 2
        client2.update({ c: 333 });
        client2.save();

        // Push changes of client 2 to server
        const localTransactions3 = JSON.parse(JSON.stringify(client2.pushToServer()));
        const { transactionsToReturn: returnedTransactions3 } = await server.pull(localTransactions3, client2.pointer);
        client2.pullFromServer(returnedTransactions3);

        expect(clientState2).toEqual({
            a: 111,
            c: 333,
            d: 4,
            e: 5,
        });

        // Update changes on client 1
        const returnedTransactions4 = await server.push(client1.pointer);
        client1.pullFromServer(returnedTransactions4);

        expect(serverState).toEqual({
            a: 111,
            c: 333,
            d: 4,
            e: 5,
        });
        expect(serverState).toEqual(clientState1);
        expect(serverState).toEqual(clientState2);
    });

    test('store previous value on update', async () => {
        const initialState: State = {
            componentInstances: {
                component1: {
                    name: 'component1',
                },
            },
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];
        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const server = new SyncEngineServer({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        const client1 = new SyncEngineClient({
            snapshot: initialState,
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
        });

        client1.update({ 'componentInstances.component1.name': 'component1-update' });
        client1.save();

        // Push changes of client 1 to server
        const localTransactions = JSON.parse(JSON.stringify(client1.pushToServer()));
        const transactionsToPush = localTransactions;
        const { transactionsToReturn: returnedTransactions } = await server.pull(transactionsToPush, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        expect(serverState).toEqual({
            componentInstances: {
                component1: {
                    name: 'component1-update',
                },
            },
        });

        expect(serverState).toEqual(clientState1);
        expect(serverState).toEqual({
            componentInstances: {
                component1: {
                    name: 'component1-update',
                },
            },
        });

        expect(returnedTransactions[0].changes[0].previousData).toEqual({
            'componentInstances.component1.name': 'component1',
        });
    });

    test('update non-existing key', async () => {
        const initialState: State = {
            componentInstances: {
                component1: {
                    name: 'component1',
                },
            },
        };

        let clientState: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            snapshot: initialState,
            getState: () => clientState,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState = state;
            },
        });

        client1.update({ 'componentInstances.component2.name': 'component2' });

        expect(clientState).toEqual({
            componentInstances: {
                component1: {
                    name: 'component1',
                },
                component2: {
                    name: 'component2',
                },
            },
        });
    });

    test('apply-transactions-in-reversed-order', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];
        let clientState1: State = JSON.parse(JSON.stringify(initialState));
        let clientState2: State = JSON.parse(JSON.stringify(initialState));

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        const client2 = new SyncEngineClient({
            getState: () => clientState2,
            setState: (state: State) => {
                console.log('Client 2 state updated', state);
                clientState2 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.update({ a: 111 });
        client1.save();

        client2.update({ b: 222 });
        client2.save();

        // Push changes of client 2 to server
        const localTransactions2 = JSON.parse(JSON.stringify(client2.pushToServer()));
        const { transactionsToReturn: returnedTransactions2 } = await server.pull(localTransactions2, client2.pointer);
        client2.pullFromServer(returnedTransactions2);

        // Push changes of client 1 to server
        const localTransactions1 = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions1 } = await server.pull(localTransactions1, client1.pointer);
        client1.pullFromServer(returnedTransactions1);

        // Update changes on client 2
        const returnedTransactions3 = await server.push(client2.pointer);
        client2.pullFromServer(returnedTransactions3);

        expect(serverState).toEqual({
            a: 111,
            b: 222,
        });
        expect(serverState).toEqual(clientState1);
        expect(serverState).toEqual(clientState2);
    });

    test('undo-redo-transaction', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.update({ a: 111 });
        client1.save();

        expect(clientState1).toEqual({
            a: 111,
            b: 2,
        });

        client1.undo();

        expect(clientState1).toEqual({
            a: 1,
            b: 2,
        });

        client1.redo();

        expect(clientState1).toEqual({
            a: 111,
            b: 2,
        });
    });

    test('multiple-undo-redo-transactions', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.create({ c: 3 });
        client1.save();

        client1.update({ a: 111 });
        client1.save();

        client1.delete(['b']);
        client1.save();

        expect(clientState1).toEqual({
            a: 111,
            c: 3,
        });

        expect(client1.getUndoCount()).toEqual(3);

        client1.undo();

        expect(clientState1).toEqual({
            a: 111,
            b: 2,
            c: 3,
        });

        client1.undo();

        expect(clientState1).toEqual({
            a: 1,
            b: 2,
            c: 3,
        });

        expect(client1.getUndoCount()).toEqual(1);
        expect(client1.getRedoCount()).toEqual(2);

        client1.redo();

        expect(clientState1).toEqual({
            a: 111,
            b: 2,
            c: 3,
        });
    });

    test('multiple-undo-redo-transactions-with-push-to-server', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.create({ c: 3 });
        client1.save();

        client1.update({ a: 111 });
        client1.save();

        client1.delete(['b']);
        client1.save();

        expect(clientState1).toEqual({
            a: 111,
            c: 3,
        });

        expect(client1.getUndoCount()).toEqual(3);

        client1.undo();

        expect(clientState1).toEqual({
            a: 111,
            b: 2,
            c: 3,
        });

        const localTransactions = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(localTransactions, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        client1.undo();

        expect(clientState1).toEqual({
            a: 1,
            b: 2,
            c: 3,
        });

        const transactionsToPush = JSON.parse(JSON.stringify(client1.pushToServer()));
        expect(transactionsToPush.length).toEqual(1);

        expect(client1.getUndoCount()).toEqual(1);
        expect(client1.getRedoCount()).toEqual(2);

        client1.redo();

        expect(clientState1).toEqual({
            a: 111,
            b: 2,
            c: 3,
        });

        const transactionsToPush2 = JSON.parse(JSON.stringify(client1.pushToServer()));
        expect(transactionsToPush2.length).toEqual(2);
    });

    test('dispatch-to-other-clients', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));
        let clientState2: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        const client2 = new SyncEngineClient({
            getState: () => clientState2,
            setState: (state: State) => {
                console.log('Client 2 state updated', state);
                clientState2 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.update({ a: 111 });
        client1.save();

        const transactionsToPush = client1.pushToClient();
        expect(transactionsToPush.length).toEqual(1);

        client2.pull(JSON.parse(JSON.stringify(transactionsToPush)));

        expect(clientState1).toEqual(clientState2);

        const numberOfTransactions1 = client1.getNumberOfTransactions();
        expect(numberOfTransactions1.created).toEqual(1);
        expect(numberOfTransactions1.dispatched).toEqual(0);

        const numberOfTransactions2 = client2.getNumberOfTransactions();
        expect(numberOfTransactions2.created).toEqual(0);
        expect(numberOfTransactions2.dispatched).toEqual(1);
    });

    test('create an array with a number of items and delete some of them', async () => {
        const initialState: State = {
            items: [],
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.create({ 'items.0': { name: 'item1' } });
        client1.create({ 'items.1': { name: 'item2' } });
        client1.create({ 'items.2': { name: 'item3' } });
        client1.create({ 'items.3': { name: 'item4' } });
        client1.create({ 'items.4': { name: 'item5' } });

        expect(clientState1).toEqual({
            items: [{ name: 'item1' }, { name: 'item2' }, { name: 'item3' }, { name: 'item4' }, { name: 'item5' }],
        });

        client1.delete(['items.1', 'items.3']);

        expect(clientState1).toEqual({
            items: [{ name: 'item1' }, { name: 'item3' }, { name: 'item4' }],
        });

        client1.delete(['items.9']);

        expect(clientState1).toEqual({
            items: [{ name: 'item1' }, { name: 'item3' }, { name: 'item4' }],
        });
    });

    test('Create a transaction with no changes', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        try {
            client1.create({});
        } catch (e) {
            expect((e as Error).message).toEqual('Cannot create change with no data');
        }

        try {
            client1.update({});
        } catch (e) {
            expect((e as Error).message).toEqual('Cannot create change with no data');
        }

        try {
            client1.delete([]);
        } catch (e) {
            expect((e as Error).message).toEqual('Cannot create change with no data');
        }

        const { created: numberOfTransactions } = client1.getNumberOfTransactions();

        expect(numberOfTransactions).toEqual(0);

        expect(clientState1).toEqual({
            a: 1,
            b: 2,
        });
    });

    test('Filter transactions outside of SyncEngineServer', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
            c: 3,
            d: 4,
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];
        let clientState1: State = JSON.parse(JSON.stringify(initialState));
        let clientState2: State = JSON.parse(JSON.stringify(initialState));

        const getTransactionsFromDatabase = async (pointer: Pointer | undefined) => {
            const index = pointer
                ? serverTransactions.findIndex((transaction) => transaction.id === pointer.id) + 1
                : 0;
            const transactionsAfterPointer = serverTransactions.slice(index);

            return {
                // We only pass the transactions after the pointer into the SyncEngineServer
                // this is useful if you want to filter out transactions that are not relevant before passing them to the SyncEngineServer
                // where they are anyway filtered out
                // this opens up perspectives to query the database for transactions after the pointer id and only pass those to the SyncEngineServer
                transactions: transactionsAfterPointer,
                filtered: true,
            };
        };

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: getTransactionsFromDatabase,
        });

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        const client2 = new SyncEngineClient({
            getState: () => clientState2,
            setState: (state: State) => {
                console.log('Client 2 state updated', state);
                clientState2 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.create({ e: 5 });
        client1.update({ a: 111 });
        client1.delete(['b']);
        client1.save();

        // Push changes of client 1 to server
        const localTransactions = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(localTransactions, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        expect(clientState1).toEqual({
            a: 111,
            c: 3,
            d: 4,
            e: 5,
        });

        // Valid update which will insert b
        client2.update({ b: 222 });
        client2.save();

        // Push update of client 2 to the server
        const localTransactions2 = JSON.parse(JSON.stringify(client2.pushToServer()));
        const { transactionsToReturn: returnedTransactions2 } = await server.pull(localTransactions2, client2.pointer);
        client2.pullFromServer(returnedTransactions2);

        expect(clientState2).toEqual({
            a: 111,
            c: 3,
            d: 4,
            e: 5,
        });

        // Update on client 2
        client2.update({ c: 333 });
        client2.save();

        // Push changes of client 2 to server
        const localTransactions3 = JSON.parse(JSON.stringify(client2.pushToServer()));
        const { transactionsToReturn: returnedTransactions3 } = await server.pull(localTransactions3, client2.pointer);
        client2.pullFromServer(returnedTransactions3);

        expect(clientState2).toEqual({
            a: 111,
            c: 333,
            d: 4,
            e: 5,
        });

        // Update changes on client 1
        const returnedTransactions4 = await server.push(client1.pointer);
        client1.pullFromServer(returnedTransactions4);

        expect(serverState).toEqual({
            a: 111,
            c: 333,
            d: 4,
            e: 5,
        });
        expect(serverState).toEqual(clientState1);
        expect(serverState).toEqual(clientState2);
    });

    test('Changes shall not be applied when validateState returns errors', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        const validateState = (state: State) => {
            if (state.a === 111) {
                return {
                    errors: ['a cannot be 111'],
                    error: true,
                    success: false,
                };
            }

            return {
                success: true,
                error: false,
                errors: [],
            };
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
            validateState,
        });

        let clientState: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
            validateState,
        });

        client1.update({ a: 111 });
        client1.save();

        expect(clientState).toEqual({
            a: 1,
            b: 2,
        });

        expect(client1.getNumberOfTransactions().created).toEqual(0);

        // Push changes of client 1 to server
        const localTransactions = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(localTransactions, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        expect(serverState).toEqual({
            a: 1,
            b: 2,
        });
    });

    test('Undo and redo shall happen in reverse order of occurrence', async () => {
        const initialState: State = {
            a: {
                b: 1,
            },
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.update({ 'a.b': 111 });
        client1.delete(['a']);
        client1.save();

        expect(clientState1).toEqual({});

        const undoChanges = client1.undo();

        expect(undoChanges?.map((change) => change.type)).toEqual(['update', 'delete']);

        expect(clientState1).toEqual({
            a: {
                b: 1,
            },
        });

        const redoChanges = client1.redo();

        expect(redoChanges?.map((change) => change.type)).toEqual(['update', 'delete']);

        expect(clientState1).toEqual({});
    });

    test('Undo and redo shall happen in reverse order of occurrence with intermediate push to server', async () => {
        const initialState: State = {
            a: {
                b: 1,
            },
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];
        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        client1.update({ 'a.b': 111 });
        client1.delete(['a']);
        client1.save();

        expect(clientState1).toEqual({});

        const undoChanges = client1.undo();

        expect(undoChanges?.map((change) => change.type)).toEqual(['update', 'delete']);

        expect(clientState1).toEqual({
            a: {
                b: 1,
            },
        });

        const transactionsToPush = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(transactionsToPush, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        expect(clientState1).toEqual({
            a: {
                b: 1,
            },
        });

        const redoChanges = client1.redo();

        expect(redoChanges?.map((change) => change.type)).toEqual(['update', 'delete']);

        expect(clientState1).toEqual({});
    });

    test('Undo delete array element after sync to server', async () => {
        const initialState: State = {
            textAreas: [],
        };

        let serverState: State = JSON.parse(JSON.stringify(initialState));
        const serverTransactions: Transaction[] = [];
        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        const server = new SyncEngineServer<State>({
            getState: () => serverState,
            setState: (state, transactions) => {
                console.log('Server state updated and transactions pushed', state, transactions);
                serverState = state;
                serverTransactions.push(...transactions);
            },
            getTransactions: async () => ({
                transactions: serverTransactions,
            }),
        });

        client1.create({ 'textAreas.0': { id: 'id-0', text: 'text0' } });
        client1.create({ 'textAreas.1': { id: 'id-1', text: 'text1' } });
        client1.create({ 'textAreas.2': { id: 'id-2', text: 'text2' } });
        client1.save();

        expect(clientState1).toEqual({
            textAreas: [
                { id: 'id-0', text: 'text0' },
                { id: 'id-1', text: 'text1' },
                { id: 'id-2', text: 'text2' },
            ],
        });

        client1.delete(['textAreas.id-1']);
        client1.save();

        expect(clientState1).toEqual({
            textAreas: [
                { id: 'id-0', text: 'text0' },
                { id: 'id-2', text: 'text2' },
            ],
        });

        const localTransactions = JSON.parse(JSON.stringify(client1.pushToServer()));
        const { transactionsToReturn: returnedTransactions } = await server.pull(localTransactions, client1.pointer);
        client1.pullFromServer(returnedTransactions);

        client1.undo();

        expect(clientState1).toEqual({
            textAreas: [
                { id: 'id-0', text: 'text0' },
                { id: 'id-2', text: 'text2' },
                { id: 'id-1', text: 'text1' },
            ],
        });
    });

    test('Update with apply false should save previous data in transaction', async () => {
        const initialState: State = {
            a: 1,
            b: 2,
        };

        let clientState1: State = JSON.parse(JSON.stringify(initialState));

        const client1 = new SyncEngineClient({
            getState: () => clientState1,
            setState: (state: State) => {
                console.log('Client 1 state updated', state);
                clientState1 = state;
            },
            snapshot: JSON.parse(JSON.stringify(initialState)),
        });

        const change = client1.update({ a: 111 }, false, { a: 1 });

        if (!change) {
            throw new Error('Change is required');
        }

        expect(change.previousData).toEqual({
            a: 1,
        });
    });
})();
