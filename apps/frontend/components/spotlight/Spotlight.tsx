import React, { useState } from 'react';
import useSWR from 'swr';

import { Spotlight as MantineSpotlight } from '@mantine/spotlight';
import type { SpotlightActionGroupData, SpotlightActionData } from '@mantine/spotlight';

import { IoAddCircleOutline, IoArrowForwardOutline, IoSearchOutline } from 'react-icons/io5';

import { RouterHelpers } from 'helpers/RouterHelpers';

import { ComponentService } from 'services/ComponentService';
import { ProjectService } from 'services/ProjectService';
import { CompanyProfileService } from 'services/CompanyProfileService';
import { RouterService } from 'services/RouterService';
import { SpotlightService } from 'services/SpotlightService';

import { useCurrentUser } from 'hooks/use-current-user';

import cx from './Spotlight.module.css';
import { InternalTrackingService } from 'services/InternalTrackingService';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

const Spotlight = () => {
    const user = useCurrentUser();
    const { companies: teamCompanies } = useCurrentTeamCompanies();

    const [query, setQuery] = useState('');

    const key = query ? `/spotlight?search=${query}` : null;
    const { data } = useSWR(key, async () => {
        return SpotlightService.search(query);
    });

    const { companies, components, projects } = data || {
        companies: [],
        components: [],
        projects: [],
    };

    const actionsItems = [
        {
            id: 'create-project',
            label: 'Create new project...',
            keywords: ['add', 'new', 'design', 'diagram'],
            leftSection: <IoAddCircleOutline size={14} />,
            onClick: () => {
                InternalTrackingService.track('spotlight.create-project', {
                    query,
                });

                ProjectService.navigate.create().then();
            },
        },
        {
            id: 'create-product',
            label: 'Create new product...',
            keywords: ['add', 'new', 'component'],
            leftSection: <IoAddCircleOutline size={14} />,
            onClick: () => {
                InternalTrackingService.track('spotlight.create-product', {
                    query,
                });

                ComponentService.navigate.create().then();
            },
        },
    ];

    const hasProfile = teamCompanies.length > 0;

    if (!hasProfile) {
        actionsItems.push({
            id: 'create-company',
            label: 'Create company profile',
            keywords: ['add', 'new', 'component'],
            leftSection: <IoAddCircleOutline size={14} />,
            onClick: () => {
                InternalTrackingService.track('spotlight.create-company', {
                    query,
                });

                CompanyProfileService.navigate.create().then();
            },
        });
    }

    const actions: (SpotlightActionGroupData | SpotlightActionData)[] = [
        {
            group: 'Actions',
            actions: actionsItems,
        },
        {
            group: 'Projects',
            actions: projects.map((project) => ({
                id: project.id,
                label: project.name,
                onClick: () => {
                    InternalTrackingService.track('spotlight.navigate-project', {
                        query,
                        project: project.id,
                    });

                    ProjectService.navigate.editor(project.id).then();
                },
            })),
        },
        {
            group: 'Products',
            actions: components.map((component) => ({
                id: component.id,
                label: component.name,
                onClick: () => {
                    InternalTrackingService.track('spotlight.navigate-component', {
                        query,
                        component: component.id,
                    });

                    ComponentService.navigate.view(component.id).then();
                },
            })),
        },
        {
            group: 'Companies',
            actions: companies.map((company) => ({
                id: company.id,
                label: company.name,
                onClick: () => {
                    InternalTrackingService.track('spotlight.navigate-company', {
                        query,
                        company: company.id,
                    });

                    CompanyProfileService.navigate.view(company.slug).then();
                },
            })),
        },
    ];

    const quicklinks: SpotlightActionGroupData = {
        group: 'Navigate',
        actions: [
            {
                id: 'projects',
                label: 'Go to your projects...',
                keywords: ['navigate', 'designs', 'diagrams'],
                leftSection: <IoArrowForwardOutline size={14} />,
                onClick: () => {
                    InternalTrackingService.track('spotlight.navigate-projects', {
                        query,
                    });

                    ProjectService.navigate.overview().then();
                },
            },
            {
                id: 'component-search',
                label: 'Search for products...',
                keywords: ['navigate', 'component', 'products'],
                leftSection: <IoArrowForwardOutline size={14} />,
                onClick: () => {
                    InternalTrackingService.track('spotlight.navigate-products', {
                        query,
                    });

                    RouterService.push(RouterHelpers.urls.searchTab('products'));
                },
            },
            {
                id: 'designs',
                label: 'Search for reference designs...',
                keywords: ['navigate', 'designs'],
                leftSection: <IoArrowForwardOutline size={14} />,
                onClick: () => {
                    InternalTrackingService.track('spotlight.navigate-designs', {
                        query,
                    });

                    RouterService.push(RouterHelpers.urls.searchTab('designs'));
                },
            },
        ],
    };

    if (user?.isSupportUser) {
        quicklinks.actions.push({
            id: 'support-center',
            label: 'Go to Lead Management',
            keywords: ['navigate', 'support center', 'lead management'],
            leftSection: <IoArrowForwardOutline size={14} />,
            onClick: () => {
                InternalTrackingService.track('spotlight.navigate-support-center', {
                    query,
                });

                RouterService.push('/support-center').then();
            },
        });
    }

    actions.push(quicklinks);

    if (query) {
        actions.push({
            group: 'Search',
            actions: [
                {
                    id: 'product-search',
                    label: `Search our product catalog for ${query}`,
                    leftSection: <IoArrowForwardOutline size={14} />,
                    onClick: () => {
                        InternalTrackingService.track('spotlight.navigate-product-search', {
                            query,
                        });

                        RouterService.push(RouterHelpers.urls.search({ productsQuery: { search: query } }));
                    },
                },
            ],
        });
    }

    return (
        <MantineSpotlight
            shortcut={['mod + K', 'mod + P', '/']}
            actions={actions}
            query={query}
            onQueryChange={setQuery}
            highlightQuery
            searchProps={{
                leftSection: <IoSearchOutline size={20} />,
                placeholder: 'Search for projects, products, companies,...',
            }}
            limit={7}
            classNames={{
                actionsGroup: cx.actionsGroup,
                action: cx.action,
                actionSection: cx.actionSection,
            }}
        />
    );
};

export { Spotlight };
