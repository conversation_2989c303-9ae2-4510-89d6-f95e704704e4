{% macro size(breakpoints, attribute, maxAttribute) %}
    {% for breakpoint in breakpoints %}
        {% if breakpoint.breakpoint %}
            @media (min-width: {{ breakpoint.breakpoint }}) {
                {% if breakpoint.value %}
                    {{ attribute }}: {{ breakpoint.value }};
                {% endif %}

                {% if maxAttribute and breakpoint.maximum %}
                    {{ maxAttribute }}: {{ breakpoint.maximum }};
                {% endif %}
            }
        {% else %}
            {% if breakpoint.value %}
                {{ attribute }}: {{ breakpoint.value }};
            {% endif %}

            {% if maxAttribute and breakpoint.maximum %}
                {{ maxAttribute }}: {{ breakpoint.maximum }};
            {% endif %}
        {% endif %}
    {% endfor %}
{% endmacro %}

{% macro border(border) %}
    border-width: {{ border.width }};
    border-style: {{ border.style }};
    border-color: {{ var(border.color) }};
    border-radius: {{ border.radius }};
{% endmacro %}

<style>
    {% for width in craft.entries.section('width').all %}
        .x-w-{{ width.id }} {
            {{ _self.size(width.sizesWithMaximum, 'width', 'max-width') }}
        }
    {% endfor %}

    {% for height in craft.entries.section('height').all %}
        .x-h-{{ height.id }} {
            {{ _self.size(height.sizesWithMaximum, 'height', 'max-height') }}
        }
    {% endfor %}

    {% for padding in craft.entries.section('padding').all %}
        .x-p-{{ padding.id }} {
            {{ _self.size(padding.sizes, 'padding') }}
        }
    {% endfor %}

    {% for spacing in craft.entries.section('spacer').all %}
        .x-h-{{ spacing.id }} {
            {{ _self.size(spacing.sizesWithMaximum, 'height', 'max-height') }}
        }
    {% endfor %}

    {% for border in craft.entries.section('border').all %}
        .x-border-{{ border.id }} {
            {{ _self.border(border) }}
        }
    {% endfor %}

    {% for fontSize in craft.entries.section('fontSize').all %}
        .x-text-{{ fontSize.id }} {
            {{ _self.size(fontSize.sizes, 'font-size') }}
        }
    {% endfor %}

    {% for font in craft.entries.section('font').all %}
        .x-font-{{ font.id }} {
            font-family: {{ font.family }};
            font-weight: {{ font.weight }};
        }
    {% endfor %}

    {% for embedStyle in craft.entries.section(['embedStyle', 'imageStyle']).all %}
        .x-embed-{{ embedStyle.id }} {
            position: relative;

            display: block;
            width: 100%;
            height: auto;

            overflow: hidden;

            box-shadow: {{ var(embedStyle.boxShadow) }};

            {% for border in craft.entries.section('border').all %}
                {{ _self.border(border) }}
            {% endfor %}

            {% if embedStyle.aspectRatios | length %}
                > * {
                    position: absolute;
                    inset: 0;

                    width: 100%;
                    height: 100%;
                }
            {% endif %}

            {{ _self.size(embedStyle.aspectRatios, 'aspect-ratio', 'max-height') }}
        }
    {% endfor %}

    {% for cardStyle in craft.entries.section('cardStyle').all %}
        .x-card-{{ cardStyle.id }} {
            background: {{ var(cardStyle.background) }};
            box-shadow: {{ var(cardStyle.boxShadow) }};

            {% for border in cardStyle.border.all %}
                {{ _self.border(border) }}
            {% endfor %}
        }
    {% endfor %}

    {% for gridStyle in craft.entries.section('gridStyle').all %}
        .x-grid-{{ gridStyle.id }} {
            display: grid;

            {% for breakpoint in gridStyle.grid %}
                {% if breakpoint.breakpoint %}
                    @media (min-width: {{ breakpoint.breakpoint }}) {
                        grid-template-columns: {{ breakpoint.columns }};
                        gap: {{ breakpoint.gap }};
                    }
                {% else %}
                    grid-template-columns: {{ breakpoint.columns }};
                    gap: {{ breakpoint.gap }};
                {% endif %}
            {% endfor %}
        }
    {% endfor %}
</style>

{% if getenv('ENVIRONMENT') == 'production' %}
    <style>
        {{ craft.getCssContent('src/index.js') | raw }}
    </style>
{% endif %}
