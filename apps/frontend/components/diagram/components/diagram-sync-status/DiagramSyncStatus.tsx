import { FC } from 'react';

import { useSyncing } from 'components/diagram/hooks/use-syncing';
import { IoCheckmarkSharp, IoCloseSharp, IoSyncSharp } from 'react-icons/io5';

import classes from './DiagramSyncStatus.module.css';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';
import { FadingBadge } from 'components/FadingBadge';

export const DiagramSyncStatus: FC = () => {
    const { status } = useSyncing();

    const icon =
        status === 'syncing' ? (
            <IoSyncSharp />
        ) : status === 'synced' ? (
            <IoCheckmarkSharp />
        ) : status === 'error' ? (
            <IoCloseSharp />
        ) : null;

    const onClick = () => {
        if (status === 'error') {
            DiagramSyncService.sync().then();
        }
    };

    return status !== null ? (
        <FadingBadge
            leftSection={icon}
            color="gray"
            onClick={onClick}
            classNames={classes}
            fadeOut={status === 'synced' || status === 'error'}
        >
            {status === 'syncing' && 'Saving..'}
            {status === 'synced' && 'Saved'}
            {status === 'error' && 'Auto save failed'}
        </FadingBadge>
    ) : null;
};
