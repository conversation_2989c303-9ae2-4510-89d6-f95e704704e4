import React, { FC, ReactNode } from 'react';

import { Text, Stack, Card, Flex, Box, UnstyledButton } from '@mantine/core';
import { BsPencil } from 'react-icons/bs';

import { useBillOfMaterialsContext } from 'components/diagram/components/diagram-sidebar/sidebars/bill-of-materials/BillOfMaterialContex';

import { OrderComponent } from 'services/BillOfMaterialsService';
import { NumberHelpers } from 'helpers/NumberHelpers';

import { TextField } from 'components/forms/fields/TextField';
import { PriceField } from 'components/forms/fields/PriceField';
import { AutocompleteField } from 'components/forms/fields/AutocompleteField';

import cx from './BillOfMaterialsItem.module.css';

const BillOfMaterialsItem: FC<{
    path: string;
    name: string;
    subtitle: ReactNode;
    quantity: number;
    image: React.ReactNode;
    orderData?: OrderComponent[];
    onClick?: () => void;
}> = ({ path, name, subtitle, quantity, image, onClick, orderData }) => {
    const { installedByData, furnishedByData } = useBillOfMaterialsContext();
    return (
        <Card className={cx.root} data-bom-item>
            <Box className={cx.image}>{image}</Box>

            <Stack className={cx.info}>
                <UnstyledButton fz="md" fw={700} onClick={onClick}>
                    {name}
                </UnstyledButton>
                <Text c="gray.5" fz="sm" fw={600}>
                    {subtitle}
                </Text>

                {orderData?.map(({ order, price }) => {
                    if (!price) return null;

                    return (
                        <Text fz="xs" c="dimmed" key={order.id}>
                            {order.name} {NumberHelpers.formatPrice(price)}
                        </Text>
                    );
                })}
            </Stack>

            <Flex className={cx.price}>
                <Text className={cx.quantity}>
                    <Text span inherit fz={9}>
                        ✕
                    </Text>{' '}
                    {quantity}
                </Text>

                <PriceField name={`${path}.price`} placeholder="Price" />
            </Flex>

            <Box className={cx.form}>
                <AutocompleteField
                    name={`${path}.installedBy`}
                    placeholder="Installed by"
                    variant="edit-on-hover-dashed"
                    data={installedByData}
                    filter={({ options }) => options} // show all options
                    comboboxProps={{
                        offset: 2,
                    }}
                />
                <AutocompleteField
                    name={`${path}.furnishedBy`}
                    placeholder="Furnished by"
                    variant="edit-on-hover-dashed"
                    data={furnishedByData}
                    filter={({ options }) => options} // show all options
                    comboboxProps={{
                        offset: 2,
                    }}
                />
                <TextField
                    className={cx.comment}
                    name={`${path}.notes`}
                    placeholder="Notes"
                    variant="edit-on-hover-dashed"
                    leftSection={<BsPencil />}
                />
            </Box>
        </Card>
    );
};

export { BillOfMaterialsItem };
