import { Field } from 'payload';

import {
    AllCompanySubscriptions,
    CompanyProfileType,
    CompanyProfileTypeOptions,
    CompanyService,
    CompanySubscription,
    DEFAULT_POWER_LEVEL,
    PublishedStatus,
} from '@repo/dcide-component-models';

import { StubFields, StubInterfaceName } from '../../fields/Stub';
import { ColorField } from '../../fields/Color';
import { VideoFields } from '../../fields/Video';
import { RTEField } from '../RichTextEditor';
import { Socials } from '../../fields/contact-information/Socials';

import {
    Address,
    CreatedBy,
    Description,
    Email,
    Name,
    Phone,
    Website,
    SlugField,
    FilesField,
    TeamFromUser,
    PublishedAt,
    PublishedBy,
    ContactPeople,
    Tags,
} from '../../fields';

import { getRequestUser } from '../../helpers/get-request-user';
import { SlackService } from '../../services/SlackService';
import { updatePartnersCrossReference } from '@/collections/company/hooks/update-partners-cross-references';
import { notifyAddedAsPartner } from '@/collections/company/hooks/notify-added-as-partner';
import { RequestAccess } from '@/access/RequestAccess';

const companySizes = [
    'Self-employed',
    '1-10 employees',
    '11-50 employees',
    '51-200 employees',
    '201-500 employees',
    '501-1000 employees',
    '1001-5000 employees',
    '5001-10,000 employees',
    '10,001+ employees',
];

const imageDescription = 'Group: company:[id]:images eg. company:65b8e1df4923ac5ccb823796:images';

const CompanyProfileFields = (type: CompanyProfileType): Field[] => [
    {
        type: 'tabs',
        tabs: [
            {
                label: 'General',
                fields: [
                    Name,
                    // TODO: remove after migration
                    {
                        type: 'select',
                        name: 'type',
                        options: CompanyProfileTypeOptions,
                        defaultValue: type,
                    },
                    {
                        ...Description,
                        admin: {
                            description:
                                'A short description of the company that will be used in overviews and teasers.',
                        },
                    } as Field,
                    ColorField,
                    {
                        label: "Logo's",
                        name: 'logos',
                        type: 'group',
                        fields: [
                            {
                                name: 'small',
                                type: 'upload',
                                relationTo: 'files',
                                maxDepth: 1,
                            },
                            {
                                name: 'large',
                                type: 'upload',
                                relationTo: 'files',
                                maxDepth: 1,
                            },
                        ],
                        admin: {
                            description: imageDescription,
                        },
                    },
                    {
                        name: 'cover',
                        label: 'Cover image',
                        type: 'upload',
                        relationTo: 'files',
                        maxDepth: 1,
                        admin: {
                            description: imageDescription,
                        },
                    },
                    {
                        name: 'systemSize',
                        type: 'group',
                        fields: [
                            {
                                name: 'min', // Watt
                                type: 'number',
                                defaultValue: DEFAULT_POWER_LEVEL.min,
                            },
                            {
                                name: 'max', // Watt
                                type: 'number',
                                defaultValue: DEFAULT_POWER_LEVEL.max,
                            },
                        ],
                    },
                    {
                        name: 'projectBudget',
                        type: 'group',
                        fields: [
                            {
                                name: 'min', // dollar
                                type: 'number',
                            },
                            {
                                name: 'max', // dollar
                                type: 'number',
                            },
                        ],
                    },
                    Tags('applicationTags'),
                ],
            },
            {
                label: 'Highlights',
                fields: [
                    {
                        name: 'highlights',
                        type: 'array',
                        interfaceName: StubInterfaceName,
                        fields: StubFields,
                        defaultValue: [],
                    },
                ],
            },
            {
                label: 'Services',
                fields: [
                    // TODO: remove after migration
                    {
                        name: 'inAppSupport',
                        label: 'Enable in-app support for this company',
                        type: 'checkbox',
                        admin: {
                            description:
                                'Allow users to ask questions about your products in the app. Users will be able to add support users to their projects to ask questions.',
                        },
                    },
                    {
                        name: 'services',
                        type: 'select',
                        hasMany: true,
                        options: Object.values(CompanyService),
                        defaultValue: [],
                    },
                    {
                        name: 'otherServices',
                        type: 'text',
                    },
                    Tags('serviceTags'),
                    {
                        name: 'inviteButton',
                        type: 'ui',
                        admin: {
                            components: {
                                Field: '/InviteSupportUser',
                            },
                        },
                    },
                    {
                        name: 'users',
                        label: 'Choose existing support users',
                        type: 'relationship',
                        relationTo: 'users',
                        maxDepth: 0,
                        hasMany: true,
                        defaultValue: () => [],
                        index: true,
                    },
                ],
            },
            {
                label: 'About',
                fields: [
                    RTEField({
                        name: 'about',
                    }),
                    RTEField({ name: 'mission' }),
                    {
                        name: 'foundedIn',
                        label: 'Founded in',
                        type: 'text',
                    },
                    {
                        name: 'companySize',
                        type: 'select',
                        options: companySizes.map((size) => ({ value: size, label: size })),
                    },
                    {
                        name: 'certificates',
                        type: 'text',
                    },
                    {
                        name: 'compliance',
                        label: 'Compliance',
                        type: 'group',
                        fields: [
                            {
                                name: 'currentOS',
                                type: 'checkbox',
                            },
                            {
                                name: 'emergeAlliance',
                                type: 'checkbox',
                            },
                            {
                                name: 'ODCA',
                                type: 'checkbox',
                            },
                            {
                                name: 'other',
                                type: 'checkbox',
                            },
                            {
                                name: 'otherInput',
                                type: 'text',
                            },
                        ],
                    },
                ],
            },
            {
                label: 'Contact',
                fields: [
                    Website,
                    {
                        name: 'locations',
                        type: 'array',
                        fields: [
                            {
                                name: 'name',
                                type: 'text',
                            },
                            RTEField({
                                name: 'description',
                            }),
                            {
                                name: 'image',
                                type: 'upload',
                                relationTo: 'files',
                                maxDepth: 1,
                                admin: {
                                    description: imageDescription,
                                },
                            },
                            Address(),
                            {
                                name: 'contactInformation',
                                type: 'group',
                                fields: [Email, Phone],
                            },
                            {
                                name: 'technicalSupport',
                                type: 'group',
                                fields: [Email, Phone],
                            },
                            {
                                name: 'isHeadquarter',
                                label: 'Mark as headquarter',
                                type: 'checkbox',
                            },
                        ],
                    },
                    Socials,
                    ContactPeople,
                ],
            },
            {
                label: 'Highlighted products',
                fields: [
                    {
                        name: 'highlightedComponents',
                        label: 'Highlighted products',
                        type: 'relationship',
                        relationTo: 'components',
                        maxDepth: 0,
                        hasMany: true,
                    },
                ],
            },
            {
                label: 'Highlighted reference designs',
                fields: [
                    {
                        name: 'highlightedProjects',
                        label: 'Highlighted reference designs',
                        type: 'relationship',
                        relationTo: 'projects',
                        maxDepth: 0,
                        hasMany: true,
                    },
                ],
            },
            {
                label: 'Partners',
                fields: [
                    {
                        name: 'partners',
                        type: 'array',
                        fields: [
                            {
                                name: 'company',
                                type: 'relationship',
                                relationTo: 'manufacturers',
                                maxDepth: 0,
                            },
                            {
                                name: 'name',
                                type: 'text',
                            },
                            {
                                name: 'description',
                                type: 'text',
                            },
                            {
                                name: 'linkedin',
                                type: 'text',
                            },
                            {
                                name: 'email',
                                type: 'text',
                            },
                            {
                                name: 'logo',
                                type: 'upload',
                                relationTo: 'files',
                            },
                        ],
                        defaultValue: [],
                        hooks: {
                            afterChange: [updatePartnersCrossReference, notifyAddedAsPartner],
                        },
                    },
                    {
                        name: 'partnersCrossReference',
                        type: 'relationship',
                        relationTo: 'manufacturers',
                        hasMany: true,
                        maxDepth: 0,
                    },
                ],
            },
            {
                label: 'Promotions',
                fields: [
                    {
                        name: 'promos',
                        type: 'array',
                        fields: StubFields,
                        interfaceName: StubInterfaceName,
                        defaultValue: [],
                    },
                ],
            },
            {
                label: 'Resources',
                fields: [
                    FilesField({
                        name: 'files',
                    }),
                    {
                        name: 'videos',
                        type: 'array',
                        fields: VideoFields,
                    },
                ],
            },
            {
                label: 'Meta',
                fields: [
                    SlugField,
                    {
                        name: 'verified',
                        type: 'checkbox',
                        defaultValue: false,
                    },
                    CreatedBy,
                    TeamFromUser,
                    {
                        name: 'internal',
                        type: 'checkbox',
                        defaultValue: false,
                        hooks: {
                            beforeChange: [
                                ({ siblingData }) => {
                                    // ensures data is not stored in DB
                                    delete siblingData['internal'];
                                },
                            ],
                            afterRead: [
                                async ({ data, req }) => {
                                    if (!data || !data.createdBy) return;

                                    const createdByUser = await req.payload.findByID({
                                        collection: 'users',
                                        id: data.createdBy,
                                        depth: 0,
                                    });

                                    return createdByUser?.internal;
                                },
                            ],
                        },
                        virtual: true,
                    },
                    PublishedBy,
                    PublishedAt,
                    {
                        type: 'number',
                        name: 'completeness',
                        defaultValue: 0,
                    },
                    {
                        name: 'imported',
                        type: 'checkbox',
                        defaultValue: false,
                    },
                    {
                        name: 'showUpgradeMessage', // after profile has been claimed, show upgrade message
                        type: 'checkbox',
                        defaultValue: false,
                        hooks: {
                            afterRead: [
                                ({ originalDoc, value }) => {
                                    if (
                                        originalDoc.subscription === CompanySubscription.FREE ||
                                        originalDoc.subscription === CompanySubscription.PREMIUM
                                    ) {
                                        return false;
                                    }

                                    return value;
                                },
                            ],
                        },
                    },
                ],
            },
            {
                label: 'Subscription',
                fields: [
                    {
                        name: 'subscription',
                        type: 'select',
                        options: AllCompanySubscriptions,
                    },
                ],
            },
            {
                label: 'Admin',
                fields: [
                    {
                        name: 'admin',
                        type: 'group',
                        fields: [
                            {
                                name: 'note',
                                type: 'textarea',
                            },
                        ],
                        access: {
                            read: RequestAccess.userIsDeveloper,
                            create: RequestAccess.userIsDeveloper,
                            update: RequestAccess.userIsDeveloper,
                        },
                    },
                ],
            },
        ],
    },
    {
        name: 'status',
        type: 'radio',
        defaultValue: 'draft',
        options: [
            {
                label: 'Profile is not published',
                value: 'draft',
            },
            {
                label: 'Profile is in review',
                value: 'review',
            },
            {
                label: 'Profile is published',
                value: 'published',
            },
        ],
        admin: {
            position: 'sidebar',
        },
        hooks: {
            afterChange: [
                // Send a slack message when a company is published
                async ({ req, data, previousDoc, originalDoc }) => {
                    const user = await getRequestUser(req);

                    const wasUnpublished = !previousDoc?.status || previousDoc.status !== PublishedStatus.PUBLISHED;
                    const isPublished = data?.status === PublishedStatus.PUBLISHED;

                    if (wasUnpublished && isPublished && user) {
                        SlackService.companyPublish({
                            publishedBy: user,
                            company: originalDoc,
                        });
                    }
                },
                // Send a slack message when publish profile is requested
                async ({ req, data, previousDoc, originalDoc }) => {
                    const user = await getRequestUser(req);
                    const wasNotInReview = !originalDoc?.status || previousDoc.status !== PublishedStatus.REVIEW;
                    const isInReview = data?.status === PublishedStatus.REVIEW;

                    if (wasNotInReview && isInReview && user) {
                        SlackService.companyRequestPublish({
                            requestBy: user,
                            company: originalDoc,
                        });
                    }
                },
            ],
        },
    },
    {
        name: 'embedding',
        type: 'json',
        hidden: true,
    },
    {
        name: 'embeddingText',
        type: 'text',
        hidden: true,
    },
];

export { CompanyProfileFields };
