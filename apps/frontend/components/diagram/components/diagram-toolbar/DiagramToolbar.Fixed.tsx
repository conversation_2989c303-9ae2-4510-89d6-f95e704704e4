import type { FC, ReactNode } from 'react';

import { Box, BoxProps } from '@mantine/core';

import { Z_INDEX } from 'components/diagram/diagram-z-index';

import cx from './DiagramToolbar.module.css';

const DiagramToolbarFixed: FC<{
    className?: BoxProps['className'];
    children: ReactNode;
}> = ({ className, children }) => {
    return (
        <Box
            data-keep-selection
            className={`${cx.toolbar} ${cx.fixedToolbar} ${className}`}
            style={{
                zIndex: Z_INDEX.CANVAS.POPOVER,
            }}
        >
            {children}
        </Box>
    );
};

export { DiagramToolbarFixed };
