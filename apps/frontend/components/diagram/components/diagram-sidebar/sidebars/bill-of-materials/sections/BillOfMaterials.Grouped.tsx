import { useEffect, useMemo, useState } from 'react';

import { unique } from 'radash';

import { Accordion, Box, Divider, Text } from '@mantine/core';

import {
    BillOfMaterialsComponent as BOMComponent,
    BillOfMaterialsComponentInstance as BOMComponentInstance,
    DiagramGroup,
} from '@repo/dcide-component-models';

import { useBillOfMaterials, useDiagram } from 'components/diagram/hooks';

import { GroupService } from 'components/diagram/services/GroupService';

import { CheckboxButton } from 'components/checkbox-button/CheckboxButton';

import { BillOfMaterialsGroup } from '../components/BillOfMaterials.Group';
import { BillOfMaterialsComponent } from '../components/BillOfMaterialsComponent';
import { BillOfMaterialsComponentInstance } from '../components/BillOfMaterialsComponentInstance';

import cx from './BillOfMaterials.Grouped.module.css';

type BOMComponentWithIndex = BOMComponent & { index: number };
type BOMComponentInstanceWithIndex = BOMComponentInstance & { index: number };

const BillOfMaterialsGrouped = () => {
    const [openedItems, setOpenedItems] = useState<string[]>([]);

    const { groups } = useDiagram();

    const { billOfMaterials } = useBillOfMaterials();

    const components = billOfMaterials?.components ?? [];
    const componentInstances = billOfMaterials?.componentInstances ?? [];

    const { componentsPerGroup, ungroupedComponents, ungroupedComponentInstances } = useMemo(() => {
        const ungroupedComponents = components.map((component, index) => ({
            ...component,
            index,
        }));
        const ungroupedComponentInstances = componentInstances.map((componentInstance, index) => ({
            ...componentInstance,
            index,
        }));

        const componentsPerGroup: {
            group: DiagramGroup;
            components: BOMComponentWithIndex[];
            componentInstances: BOMComponentInstanceWithIndex[];
        }[] = groups.map((group) => {
            const groupComponentIntances = GroupService.getComponentInstancesInsideGroup(group.id);
            const uniqueGroupComponentIntances = unique(groupComponentIntances, (item) => item.componentId || item.id);

            return {
                group,
                components: components
                    .map((component, index) => {
                        const quantity = groupComponentIntances.filter(
                            (needle) => needle.componentId === component.component.id,
                        ).length;

                        const componentIndex = ungroupedComponents.findIndex(
                            (needle) => needle.component === component.component,
                        );
                        if (componentIndex >= 0) {
                            ungroupedComponents[componentIndex].quantity -= quantity;
                        }

                        return {
                            ...component,
                            quantity,
                            index,
                        };
                    })
                    .filter(({ component }) =>
                        uniqueGroupComponentIntances.find((needle) => needle.componentId === component.id),
                    ),
                componentInstances: componentInstances
                    .map((componentInstance, index) => {
                        const quantity = groupComponentIntances.filter(
                            (needle) => needle.id === componentInstance.componentInstance,
                        ).length;

                        const componentInstanceIndex = ungroupedComponentInstances.findIndex(
                            (needle) => needle.componentInstance === componentInstance.componentInstance,
                        );
                        if (componentInstanceIndex >= 0) {
                            ungroupedComponentInstances[componentInstanceIndex].quantity -= quantity;
                        }

                        return {
                            ...componentInstance,
                            quantity,
                            index,
                        };
                    })
                    .filter(({ componentInstance: componentInstanceId }) =>
                        uniqueGroupComponentIntances.find((needle) => needle.id === componentInstanceId),
                    ),
            };
        });

        return {
            componentsPerGroup,
            ungroupedComponents: ungroupedComponents.filter((item) => item.quantity > 0),
            ungroupedComponentInstances: ungroupedComponentInstances.filter((item) => item.quantity > 0),
        };
    }, [groups, components, componentInstances]);

    const defaultOpenedItems = useMemo(
        () => [...componentsPerGroup.map((group) => group.group.id), 'ungrouped'],
        [componentsPerGroup],
    );

    useEffect(() => {
        setOpenedItems(defaultOpenedItems);
    }, [defaultOpenedItems]);

    const hasGroupWithComponents = componentsPerGroup.some(
        (group) => group.components?.length || group.componentInstances?.length,
    );

    const hasUngroupedComponents = !!(ungroupedComponents.length || ungroupedComponentInstances.length);

    if (!hasGroupWithComponents) {
        return (
            <Text c="dimmed" p="xs">
                No components in groups
            </Text>
        );
    }

    const handleHoverGroup = (group: DiagramGroup) => {
        GroupService.moveCenterIntoView(group.id);
    };

    return (
        <>
            <CheckboxButton
                m="xs"
                ml={0}
                display="inline-flex"
                label="Open all"
                checked={openedItems.length === defaultOpenedItems.length}
                setChecked={(checked) => setOpenedItems(checked ? defaultOpenedItems : [])}
            />

            <Accordion
                multiple
                chevronPosition="left"
                chevronSize={10}
                className={cx.accordion}
                value={openedItems}
                onChange={setOpenedItems}
            >
                {componentsPerGroup.map((group) => (
                    <Box key={group.group.id} onMouseEnter={() => handleHoverGroup(group.group)} tabIndex={0}>
                        <BillOfMaterialsGroup {...group} />
                    </Box>
                ))}
                {hasUngroupedComponents && (
                    <Accordion.Item value="ungrouped">
                        <Accordion.Control>
                            <Divider label="Ungrouped" labelPosition="left" />
                        </Accordion.Control>
                        <Accordion.Panel>
                            {ungroupedComponents.map((component) => (
                                <BillOfMaterialsComponent key={component.id} {...component} />
                            ))}
                            {ungroupedComponentInstances.map((componentInstance) => (
                                <BillOfMaterialsComponentInstance key={componentInstance.id} {...componentInstance} />
                            ))}
                        </Accordion.Panel>
                    </Accordion.Item>
                )}
            </Accordion>
        </>
    );
};

export { BillOfMaterialsGrouped };
