import type { FC } from 'react';
import type { DiagramConnection, DiagramComponentInstance } from '@repo/dcide-component-models';

import { Combobox, useCombobox } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { TbArrowNarrowLeft, TbArrowNarrowRight, TbArrowsLeftRight, TbLineDashed } from 'react-icons/tb';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';
import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

import { ConnectionService } from 'components/diagram/services/ConnectionService';
import { ConnectionHelpers } from 'components/diagram/helpers/ConnectionHelpers';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

const ConnectionToolbarPowerFlow: FC<{
    connection: DiagramConnection;
    fromComponentInstance: DiagramComponentInstance;
    toComponentInstance: DiagramComponentInstance;
    direction: 'horizontal' | 'vertical';
}> = ({ connection, fromComponentInstance, toComponentInstance }) => {
    const [opened, handlers] = useDisclosure();
    const combobox = useCombobox({
        opened,
        onDropdownClose: () => {
            handlers.close();
        },
    });

    const availablePowerFlowDirections = ConnectionHelpers.getAvailablePowerFlowDirections(
        connection,
        fromComponentInstance,
        toComponentInstance,
    );
    const relativePowerFlowDirection = ConnectionHelpers.getRelativePowerFlowDirection(
        connection,
        fromComponentInstance,
        toComponentInstance,
    );

    const rotation = {
        n: -90,
        ne: -45,
        e: 0,
        se: 45,
        s: 90,
        sw: 135,
        w: 180,
        nw: 225,
    }[ConnectionHelpers.getOrientation(connection, fromComponentInstance, toComponentInstance)!];

    const rotationStyle = {
        transform: `rotate(${rotation}deg)`,
    };

    return (
        <Combobox
            store={combobox}
            classNames={{
                dropdown: cx.dropdown,
                options: cx.dropdownOptions,
                option: cx.dropdownOption,
            }}
            onOptionSubmit={(value) => {
                ConnectionService.updatePowerFlow(connection, value as any);
                DiagramSyncService.save();
                combobox.closeDropdown();
            }}
            position="top"
            withArrow
            withinPortal={false}
            arrowOffset={18}
        >
            <Combobox.Target>
                <div>
                    <DiagramToolbar.IconButton
                        tooltip={!opened && 'Power Flow'}
                        onClick={() => {
                            handlers.toggle();
                        }}
                        active={!!relativePowerFlowDirection}
                    >
                        {!relativePowerFlowDirection && <TbLineDashed />}
                        {relativePowerFlowDirection === 'load' && <TbArrowNarrowLeft style={rotationStyle} />}
                        {relativePowerFlowDirection === 'bidirectional' && <TbArrowsLeftRight style={rotationStyle} />}
                        {relativePowerFlowDirection === 'generate' && <TbArrowNarrowRight style={rotationStyle} />}
                    </DiagramToolbar.IconButton>
                </div>
            </Combobox.Target>
            <Combobox.Dropdown>
                <Combobox.Options data-horizontal>
                    <Combobox.Option
                        value="input"
                        active={relativePowerFlowDirection === 'load'}
                        disabled={!availablePowerFlowDirections.relative.includes('load')}
                    >
                        <TbArrowNarrowLeft style={rotationStyle} />
                    </Combobox.Option>
                    <Combobox.Option
                        value="bidirectional"
                        active={relativePowerFlowDirection === 'bidirectional'}
                        disabled={!availablePowerFlowDirections.relative.includes('bidirectional')}
                    >
                        <TbArrowsLeftRight style={rotationStyle} />
                    </Combobox.Option>
                    <Combobox.Option
                        value="output"
                        active={relativePowerFlowDirection === 'generate'}
                        disabled={!availablePowerFlowDirections.relative.includes('generate')}
                    >
                        <TbArrowNarrowRight style={rotationStyle} />
                    </Combobox.Option>
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );
};

export { ConnectionToolbarPowerFlow };
