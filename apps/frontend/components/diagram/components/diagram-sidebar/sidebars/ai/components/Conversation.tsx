import React, { FC } from 'react';

import { useDiagram } from 'components/diagram/hooks';
import { useAIConversation } from 'components/diagram/hooks/use-ai-conversation';
import { Avatar, Box, Group, Loader, Paper, ScrollArea, Stack } from '@mantine/core';
import { TbSparkles } from 'react-icons/tb';

import Markdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

import { ErrorBoundary } from 'react-error-boundary';

import { DiagramAIMessage } from 'components/diagram/services/AIConversationService';

import { ApplyChanges } from './actions/ApplyChanges';
import { RequestPowerFlowSimulation } from './actions/RequestPowerFlowSimulation';
import { RunPowerFlowSimulation } from './actions/RunPowerFlowSimulation';
import { ShowPowerFlowSimulation } from './actions/ShowPowerFlowSimulation';
import { ShowProductSuggestions } from './actions/ShowProductSuggestions';
import { ShowProductCatalog } from './actions/ShowProductCatalog';

import { StringHelpers } from 'helpers/StringHelpers';

import cx from './Conversation.module.css';

const Conversation = () => {
    const { id } = useDiagram();
    const { conversation } = useAIConversation(id);

    return conversation.length ? (
        <Stack gap="md">
            {conversation.map((message, index) => (
                <Stack gap="md" key={message.id}>
                    {message.question && (
                        <Box maw="80%" ml="auto">
                            <Paper
                                p="sm"
                                radius="md"
                                shadow="md"
                                dangerouslySetInnerHTML={{ __html: StringHelpers.nl2br(message.question) }}
                            />
                        </Box>
                    )}
                    {message.status !== 'cancelled' && (
                        <Group gap="md" wrap="nowrap" align="self-start">
                            <Avatar className={cx.avatar} size={30} variant="gradient">
                                {message.status === 'pending' || message.status === 'processing' ? (
                                    <Loader size={14} color="white" />
                                ) : (
                                    <TbSparkles size={14} />
                                )}
                            </Avatar>
                            {(message.status === 'pending' || message.status === 'processing') && (
                                <Group h={30}>{message.feedback || '...'}</Group>
                            )}
                            {message.status === 'error' && (
                                <Box className={cx.content}>
                                    Sorry, something went wrong.
                                    <br />
                                    Our engineers are notified and working on a fix.
                                    <br />
                                </Box>
                            )}
                            {message.answer && (
                                <Box className={cx.content}>
                                    <Markdown
                                        className={cx.markdown}
                                        remarkPlugins={[remarkGfm]}
                                        components={{
                                            table: MarkdownScrollableTable,
                                        }}
                                    >
                                        {removeActionString(removeLegacyChangesString(message.answer))}
                                    </Markdown>
                                    <ErrorBoundary fallback={null}>
                                        <Actions
                                            message={message}
                                            markdown={message.answer}
                                            isLastMessage={index === conversation.length - 1}
                                        />
                                    </ErrorBoundary>
                                </Box>
                            )}
                        </Group>
                    )}
                </Stack>
            ))}
        </Stack>
    ) : null;
};

const MarkdownScrollableTable: FC<any> = ({ children, ...props }) => (
    <ScrollArea type="never" className={cx.markdownScrollableTable}>
        <table {...props}>{children}</table>
    </ScrollArea>
);

const Actions: FC<{
    message: DiagramAIMessage;
    markdown: string;
    isLastMessage: boolean;
}> = ({ message, markdown, isLastMessage }) => {
    const match = findActionString(markdown);
    const action = match ? JSON.parse(match.action) : { action: 'none' };

    const Handler = {
        applyChanges: ApplyChanges,
        requestPowerFlowSimulation: RequestPowerFlowSimulation,
        runPowerFlowSimulation: RunPowerFlowSimulation,
        showPowerFlowSimulation: ShowPowerFlowSimulation,
        showProductCatalog: ShowProductCatalog,
        showProductSuggestions: ShowProductSuggestions,
    }[action.action as string];

    return Handler ? <Handler message={message} action={action} isLastMessage={isLastMessage} /> : null;
};

const findLegacyChangesString = (markdown: string) => {
    const regex = /=== CHANGES ===(.*?)=== END CHANGES ===/s;
    const match = markdown.match(regex);

    return match
        ? {
              match: match[0],
              changes: match[1],
          }
        : null;
};

const removeLegacyChangesString = (markdown: string) => {
    const match = findLegacyChangesString(markdown);

    return match ? markdown.replace(match.match, '') : markdown;
};

const findActionString = (markdown: string) => {
    const regex = /=== ACTION ===(.*?)=== END ACTION ===/s;
    const match = markdown.match(regex);

    return match
        ? {
              match: match[0],
              action: match[1],
          }
        : null;
};

const removeActionString = (markdown: string) => {
    const match = findActionString(markdown);

    return match ? markdown.replace(match.match, '') : markdown;
};

export { Conversation };
