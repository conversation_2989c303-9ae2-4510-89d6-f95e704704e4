import { FC } from 'react';

import { Box } from '@mantine/core';

import { ComponentSection } from '@repo/dcide-component-models';

import { useOptionalComponentContext } from 'components/component-datasheet/hooks/use-component-context';

import { ComponentForm } from 'components/component-form';
import { CreateComponentDatasheet } from 'components/component-datasheet/components/ComponentDatasheetCreate';
import { ComponentSignupProps } from 'components/component-signup/ComponentSignup';
import { Content } from 'components/component-signup/components/Content';

import cx from './StepX.module.css';

const StepX: FC<
    ComponentSignupProps & {
        showSections: ComponentSection[];
    }
> = ({ nextStep, showSections, ...props }) => {
    const { component, setComponent } = useOptionalComponentContext();

    return (
        <Box className={cx.root}>
            <ComponentForm
                initialValues={component ?? undefined}
                onUpdate={(updatedComponent) => {
                    setComponent(updatedComponent);
                    nextStep();
                }}
            >
                <Content nextStep={nextStep} {...props}>
                    <CreateComponentDatasheet showSections={showSections} />
                </Content>
            </ComponentForm>
        </Box>
    );
};

export { StepX };
