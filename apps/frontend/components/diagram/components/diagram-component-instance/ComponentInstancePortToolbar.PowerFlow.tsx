import type { FC } from 'react';
import type { DiagramComponentInstance } from '@repo/dcide-component-models';
import type { PowerFlowDirection } from '@repo/dcide-component-models';

import { Combobox, useCombobox } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { IconArrowNarrowLeft, IconArrowNarrowRight, IconArrowsLeftRight, IconLineDashed } from '@tabler/icons-react';

import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';

import { PortService } from 'components/diagram/services/PortService';
import { ComponentInstanceService } from 'components/diagram/services/ComponentInstanceService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { PortHelpers } from 'components/diagram/helpers/PortHelpers';

import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.scss';

const ComponentInstancePortToolbarPowerFlow: FC<{
    componentInstance: DiagramComponentInstance;
    portIndex: number;
}> = ({ componentInstance, portIndex }) => {
    const [opened, handlers] = useDisclosure();
    const combobox = useCombobox({
        opened,
        onDropdownClose: () => {
            handlers.close();
        },
    });

    const powerFlowDirection = PortHelpers.getPowerFlowDirectionConfiguration(componentInstance, portIndex);
    const powerFlowDirections = PortHelpers.getPowerFlowDirectionOptions(componentInstance, portIndex);

    let portConnectionDirection = null;

    ComponentInstanceService.getConnections(componentInstance).forEach((connection) => {
        if (connection.from.componentInstanceId === componentInstance.id) {
            portConnectionDirection = connection.from.edge;
        }

        if (connection.to.componentInstanceId === componentInstance.id) {
            portConnectionDirection = connection.to.edge;
        }
    });

    const rotation = {
        top: -90,
        right: 0,
        bottom: 90,
        left: 180,
    }[portConnectionDirection || 'right'];

    const rotationStyle = {
        transform: `rotate(${rotation}deg)`,
    };

    return (
        <Combobox
            store={combobox}
            classNames={{
                dropdown: cx.dropdown,
                option: cx.dropdownOption,
            }}
            onOptionSubmit={(value) => {
                const componentInstanceProxy = ComponentInstanceService.getById(componentInstance.id);
                PortService.updatePowerFlowDirection(componentInstanceProxy, portIndex, value as PowerFlowDirection);
                DiagramSyncService.save();

                combobox.closeDropdown();
            }}
            position="top"
            withArrow
            withinPortal={false}
            arrowOffset={18}
        >
            <Combobox.Target>
                <div>
                    <DiagramToolbar.IconButton
                        tooltip={!opened && 'Power Flow'}
                        onClick={() => {
                            handlers.toggle();
                        }}
                        active={!!powerFlowDirection}
                    >
                        {!powerFlowDirection && <IconLineDashed style={rotationStyle} />}
                        {powerFlowDirection === 'input' && <IconArrowNarrowLeft style={rotationStyle} />}
                        {powerFlowDirection === 'bidirectional' && <IconArrowsLeftRight style={rotationStyle} />}
                        {powerFlowDirection === 'output' && <IconArrowNarrowRight style={rotationStyle} />}
                    </DiagramToolbar.IconButton>
                </div>
            </Combobox.Target>
            <Combobox.Dropdown>
                <Combobox.Options>
                    <Combobox.Option
                        value="input"
                        active={powerFlowDirection === 'input'}
                        disabled={!powerFlowDirections.includes('input')}
                    >
                        <IconArrowNarrowLeft style={rotationStyle} />
                        Input
                    </Combobox.Option>
                    <Combobox.Option
                        value="bidirectional"
                        active={powerFlowDirection === 'bidirectional'}
                        disabled={!powerFlowDirections.includes('bidirectional')}
                    >
                        <IconArrowsLeftRight style={rotationStyle} />
                        Bidirectional
                    </Combobox.Option>

                    <Combobox.Option
                        value="output"
                        active={powerFlowDirection === 'output'}
                        disabled={!powerFlowDirections.includes('output')}
                    >
                        <IconArrowNarrowRight style={rotationStyle} />
                        Output
                    </Combobox.Option>
                </Combobox.Options>
            </Combobox.Dropdown>
        </Combobox>
    );
};

export { ComponentInstancePortToolbarPowerFlow };
