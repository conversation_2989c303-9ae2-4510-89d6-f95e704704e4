/* eslint-disable */
// @ts-nocheck

import { AIService } from '@/services/AIService';
import { PayloadService } from '@/services/PayloadService';

import { z } from 'zod'

import { zodToJsonSchema } from 'zod-to-json-schema';

export const GET = async (request: Request) => {
    const payload = await PayloadService.get();

    const { searchParams } = new URL(request.url);
    const title = searchParams.get('title');

    type Collection =  'accessories' | 'flavours' | 'ingredients' | 'occasions' | 'styles' | 'units';

    const list = async (collection: Collection) => {
        const { docs } = await payload.find({
            collection,
            select: {
                title: true,
            },
            pagination: false,
        });

        return docs;
    };

    const accessories = await list('accessories');
    const flavours = await list('flavours');
    const ingredients = await list('ingredients');
    const occasions = await list('occasions');
    const styles = await list('styles');
    const units = await list('units');

    const schema = z.object({
        title: z.string(),
        slug: z.string(),
        description: z.string().describe('make it a good length to score well for SEO'),
        ingredients: z.array(z.object({
            amount: z.number().nullable(),
            unit: z.string().nullable().describe('Reference one of the unit ids'),
            ingredient: z.string().describe('Reference one of the ingredient ids'),
        })),
        accessories: z.array(z.object({
            amount: z.number().nullable(),
            accessory: z.string().describe('Reference one of the accessory ids'),
        })),
        instructions: z.array(z.object({
            instruction: z.string(),
        })),
        questions: z.array(z.object({
            question: z.string().describe("A question that could score good for SEO"),
            answer: z.string().describe("The answer to the question that should also score well for SEO, don't make it too short"),
        })).min(6),
        flavours: z.array(z.string().describe('Reference one or many of the flavour ids')),
        occasions: z.array(z.string().describe('Reference one of the occasion ids if applicable')),
        styles: z.array(z.string().describe('Reference one of the style ids')),
        teaser: z.object({
            title: z.string().describe('Make a good title for a teaser'),
            description: z.string(),
        }),
        seo: z.object({
            title: z.string().describe('Make a good title for SEO purposes'),
            description: z.string().describe('make it a good length to score well for SEO'),
        }),
    });

    const response = await AIService.generate({
        model: "gemini-2.5-pro-exp-03-25",
        contents: `
            Can you create a ${title} recipe?
            We also provide a list of ingredients, units, flavours, styles, occasions and accessories.
            We also provide a JSON schema that you should adhere to.

            The title should be the provided title, you can generate the seo.title and make a slug out of that

            It's important that this content will score good for SEO.

            === Accessories ===
            ${JSON.stringify(accessories)}

            === Flavours ===
            ${JSON.stringify(flavours)}

            === Ingredients ===
            ${JSON.stringify(ingredients)}

            === Occasions ===
            ${JSON.stringify(occasions)}

            === Styles ===
            ${JSON.stringify(styles)}

            === Units ===
            ${JSON.stringify(units)}

            === Schema ===
            ${JSON.stringify(zodToJsonSchema(schema))}
        `,
        config: {
            responseMimeType: 'application/json',
        },
    });

    const data = JSON.parse(response.text!);

    data.description = createLexicalObject(data.description);

    data.instructions = data.instructions.map((row: any) => ({
        ...row,
        instruction: createLexicalObject(row.instruction),
    }));

    data.questions = data.questions.map((row: any) => ({
        ...row,
        answer: createLexicalObject(row.answer),
    }));

    const recipe = await payload.create({
        collection: 'recipes',
        data,
    });

    return Response.json(recipe);
};

const createLexicalObject = (text: string) => {
    return {
        root: {
            children: [
                {
                    children: [
                        {
                            detail: 0,
                            format: 0,
                            mode: 'normal',
                            style: '',
                            text: text,
                            type: 'text',
                            version: 1,
                        },
                    ],
                    direction: 'ltr',
                    format: '',
                    indent: 0,
                    type: 'paragraph',
                    version: 1,
                    textFormat: 0,
                    textStyle: '',
                },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
        },
    };
};
