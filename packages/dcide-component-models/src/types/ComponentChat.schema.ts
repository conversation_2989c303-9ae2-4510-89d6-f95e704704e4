import { z } from 'zod';

export enum ComponentChatRating {
    THUMBS_UP = 'thumbsUp',
    THUMBS_DOWN = 'thumbsDown',
}

export const ComponentChatMessageSchema = z.object({
    id: z.string().optional(),
    question: z.string(),
    answer: z.string().optional(),
    references: z
        .array(
            z.object({
                tag: z.string(),
                page_number: z.number(),
                document_id: z.string(),
                document_name: z.string(),
                document_url: z.string(),
            }),
        )
        .optional(),
    isHistory: z.boolean().optional(),
    isStreaming: z.boolean().optional(),
    rating: z.nativeEnum(ComponentChatRating).optional(),
    component: z.string().optional(),
    project: z.string().optional(),
});

export enum AIConversationRole {
    USER = 'user',
    ASSISTANT = 'assistant',
}

export const AIConversationMessageSchema = z.object({
    role: z.nativeEnum(AIConversationRole),
    content: z.string().optional(),
});

export const ComponentChatMessagePayload = ComponentChatMessageSchema.extend({
    id: z.string(),
    answer: z.string(),
    component: z.string().optional(),
    project: z.string().optional(),
});
