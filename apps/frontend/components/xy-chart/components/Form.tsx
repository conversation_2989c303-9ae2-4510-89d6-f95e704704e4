import React, { FC, useEffect } from 'react';
import { Box, Popover, SimpleGrid, Stack, Group, UnstyledButton } from '@mantine/core';

import { Form } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import { useFieldArray, useWatch } from 'react-hook-form';
import { last, list } from 'radash';

import { Series } from '../XYChart';

import cx from '../Chart.module.css';

type ChartFormPoint = { x: { value: number | null; unit: string }; y: { value: number | null; unit: string } };

type ChartFormProps = {
    fields: {
        x: {
            label: string;
            Component: React.ComponentType<any>;
        };
        y: {
            label: string;
            Component: React.ComponentType<any>;
        };
    };
    opened: boolean;
    close: () => void;
    handleSubmit: (data: { x: number[]; y: number[] }) => void;
    xSeries: Series;
    ySeries: Series;
    xUnitConverter?: (value: number) => number;
    yUnitConverter?: (value: number) => number;
};

const isNotNull = (
    point: ChartFormPoint,
): point is { x: { value: number; unit: string }; y: { value: number; unit: string } } => {
    return point.x.value !== null && point.y.value !== null;
};

const ChartForm = ({
    fields,
    opened,
    close,
    handleSubmit,
    xSeries,
    ySeries,
    xUnitConverter,
    yUnitConverter,
}: ChartFormProps) => {
    const submit = (values: { data: ChartFormPoint[] }) => {
        const filtered = values.data.filter(isNotNull);

        const converted = filtered.map((point) => ({
            x: xUnitConverter ? xUnitConverter(point.x.value) : point.x.value,
            y: yUnitConverter ? yUnitConverter(point.y.value) : point.y.value,
        }));

        const sorted = converted.sort((a, b) => a.x - b.x);

        const x = sorted.map((p) => p.x);
        const y = sorted.map((p) => p.y);

        handleSubmit({ x, y });
        close();
    };

    const data: ChartFormPoint[] = (xSeries.value || []).map((x, i) => ({
        x: { value: x, unit: xSeries.unit },
        y: { value: ySeries.value[i] ?? null, unit: ySeries.unit },
    }));

    data.push({ x: { value: null, unit: xSeries.unit }, y: { value: null, unit: ySeries.unit } });

    return (
        <Popover
            width={300}
            position="bottom"
            offset={2}
            opened={opened}
            onChange={close}
            transitionProps={{ duration: 0 }}
            shadow="xs"
        >
            <Popover.Target>
                <Box className={cx.formTarget} />
            </Popover.Target>
            <Popover.Dropdown className={cx.form}>
                <Form onSubmit={submit} defaultValues={{ data }}>
                    <InnerForm fields={fields} xUnit={xSeries.unit} yUnit={ySeries.unit} />
                </Form>
            </Popover.Dropdown>
        </Popover>
    );
};

const InnerForm: FC<{
    fields: ChartFormProps['fields'];
    xUnit: string;
    yUnit: string;
}> = ({ fields, xUnit, yUnit }) => {
    const {
        fields: array,
        append,
        remove,
    } = useFieldArray({
        name: 'data',
    });

    const values = useWatch();
    const lastValues = last(values.data) as any;

    useEffect(() => {
        if (array.length === 0) {
            append({
                x: { value: null, unit: xUnit },
                y: { value: null, unit: yUnit },
            });
        }
    }, [array.length]);

    useEffect(() => {
        if (lastValues?.x.value || lastValues?.y.value) {
            if (array.length === values.data.length) {
                append({
                    x: { value: null, unit: xUnit },
                    y: { value: null, unit: yUnit },
                });
            }
        }
    }, [lastValues?.x.value, lastValues?.y.value, array.length, values.data.length]);

    const clear = () => {
        remove(list(array.length - 1));
    };

    return (
        <Stack gap={4}>
            <SimpleGrid cols={2} spacing={4}>
                <Box fz="xs" fw={700}>
                    {fields.x.label}
                </Box>
                <Box fz="xs" fw={700}>
                    {fields.y.label}
                </Box>
            </SimpleGrid>
            {array.map((field, index) => (
                <Row key={field.id} index={index} fields={fields} />
            ))}
            <Group mt="xs" gap="xs">
                <FormSubmit disableIfClean size="xs" variant="gradient" radius={2} style={{ flexShrink: 0 }}>
                    Save
                </FormSubmit>
                <UnstyledButton fz="xs" onClick={clear}>
                    Clear data
                </UnstyledButton>
            </Group>
        </Stack>
    );
};

const Row: FC<{
    index: number;
    fields: ChartFormProps['fields'];
}> = ({ index, fields }) => {
    const XField = fields.x.Component;
    const YField = fields.y.Component;

    return (
        <SimpleGrid cols={2} spacing={4}>
            <XField name={`data.${index}.x`} label="" fields={['value']} />
            <YField name={`data.${index}.y`} label="" fields={['value']} />
        </SimpleGrid>
    );
};

export type { ChartFormProps, ChartFormPoint };
export { ChartForm };
