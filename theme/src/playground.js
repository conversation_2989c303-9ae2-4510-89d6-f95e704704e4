(() => {
    document.querySelectorAll('[data-header-carousel]').forEach((carousel) => {
        let currentSlide = 1;
        let isReversing = false;

        const totalSlides = carousel.children.length;

        const updateActiveSlide = () => {
            currentSlide += isReversing ? -1 : 1;

            if (isReversing && currentSlide === 1) {
                isReversing = false;
            }

            if (currentSlide === totalSlides) {
                isReversing = true;
            }

            carousel.style.setProperty('--header-carousel-active-slide', currentSlide);
        };

        if (totalSlides > 1) {
            setInterval(updateActiveSlide, 5000);
        }
    });
})();
