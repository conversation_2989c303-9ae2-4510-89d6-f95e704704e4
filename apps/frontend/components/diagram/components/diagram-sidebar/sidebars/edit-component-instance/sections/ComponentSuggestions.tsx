import React, { useEffect, FC, useState } from 'react';
import { Component, DiagramComponentInstance } from '@repo/dcide-component-models';

import { proxy } from 'valtio';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Anchor, Box, ScrollArea, Stack } from '@mantine/core';
import { useElementSize } from '@mantine/hooks';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';

import { ComponentService } from 'services/ComponentService';
import { ProductSearchService } from 'components/diagram/services/ProductSearchService';

import { DraggableDiagramComponentOverviewHit } from 'components/component-overview/ComponentOverviewHit.Diagram';

import cx from './ComponentSuggestions.module.css';

const localState = proxy({
    showSpecifications: false,
});

const ComponentSuggestions: FC<{
    componentInstance: DiagramComponentInstance;
}> = ({ componentInstance }) => {
    const { showSpecifications } = useSnapshot(localState);
    const [suggestions, setSuggestions] = useState<Component[]>([]);

    const { ref, width, height } = useElementSize();

    const defaultValues = ProductSearchService.generateDefaultValues(componentInstance);

    useEffect(() => {
        ComponentService.search({
            limit: 4,
            ...defaultValues,
        }).then((response) => {
            if (response.docs) {
                setSuggestions(response.docs);
            }
        });
    }, [JSON.stringify(defaultValues)]);

    useEffect(() => {
        ProductSearchService.setState({
            componentInstance,
            linkedComponentId: componentInstance.componentId,
        });
    }, [componentInstance.id, componentInstance.componentId]);

    const openProductSearch = () => {
        ProductSearchService.openProductSearchFor(componentInstance);
    };

    const openProductSearchWithoutSpecifications = () => {
        ProductSearchService.openProductSearchFor(componentInstance, false);
    };

    return (
        <React.Fragment>
            <Box style={{ width, height }} />
            <Box className={cx.section} ref={ref}>
                <DiagramSidebar.Divider />
                <DiagramSidebar.Section
                    title="Component Suggestions"
                    rightSection={
                        suggestions.length ? (
                            <Anchor component="button" onClick={openProductSearch} style={{ fontSize: 'inherit' }}>
                                view more suggestions
                            </Anchor>
                        ) : null
                    }
                    collapsable={{
                        enabled: true,
                        key: 'diagram.suggested-components',
                        initial: false,
                    }}
                >
                    <ScrollArea.Autosize mah={232} type="always">
                        {suggestions.length > 0 ? (
                            <Stack gap="xs">
                                {suggestions.slice(0, 3).map((hit: any) => (
                                    <DraggableDiagramComponentOverviewHit
                                        component={hit}
                                        showSpecifications={showSpecifications}
                                        setShowSpecifications={() => {
                                            localState.showSpecifications = true;
                                        }}
                                        key={hit.id}
                                    />
                                ))}
                            </Stack>
                        ) : (
                            <Box className={cx.noResults}>
                                We didn&apos;t find any matching components.
                                <br />
                                Go take a look our{' '}
                                <Anchor component="button" onClick={openProductSearchWithoutSpecifications}>
                                    Product Catalog
                                </Anchor>{' '}
                                to find more components.
                            </Box>
                        )}
                    </ScrollArea.Autosize>
                </DiagramSidebar.Section>
            </Box>
        </React.Fragment>
    );
};

export { ComponentSuggestions };
