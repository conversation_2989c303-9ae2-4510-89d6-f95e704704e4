import { SimpleButton } from 'elements/buttons';

import cx from '../Chart.module.css';

type ChartTooltipProps = {
    xValue: string;
    xUnit: string;
    yValues: Record<string, any>[] | undefined;
    yUnit: string;
};

const ChartTooltip = ({ xValue, xUnit, yValues, yUnit }: ChartTooltipProps) => {
    if (!yValues) return null;

    return (
        <SimpleButton className={cx.button}>
            {xValue}
            {xUnit}, {yValues.map((item) => `${item.value}${yUnit}`)}
        </SimpleButton>
    );
};

export { ChartTooltip };
export type { ChartTooltipProps };
