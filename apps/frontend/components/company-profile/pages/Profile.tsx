import { FC } from 'react';

import { Card } from '@mantine/core';

import { CompanyProfile } from '@repo/dcide-component-models';

import { useCurrentProfile } from 'components/company-profile/hooks/use-current-profile';
import { useShowCompanyInfo } from 'components/company-profile/hooks/use-show-company-info';

import { StubSection } from 'components/company-profile/components/StubSection';
import { Section } from 'components/section/Section';
import { RTEContent } from 'components/rte-content/RTEContent';

import cx from './styles.module.css';

const Profile: FC<{ company: CompanyProfile }> = ({ company }) => {
    const { showMission, showAbout } = useShowCompanyInfo(company);

    return (
        <Card classNames={cx}>
            {showMission && (
                <Section title="Mission">
                    <RTEContent content={company.mission!} />
                </Section>
            )}

            {showAbout && (
                <Section title="About">
                    <RTEContent content={company.about} />
                </Section>
            )}

            {!!company.caseStudies.length && (
                <StubSection
                    title="Case studies"
                    stubs={company.caseStudies}
                    nbCols={4}
                    company={company}
                    field="caseStudies"
                />
            )}
        </Card>
    );
};

const WrappedProfile = () => {
    const company = useCurrentProfile();

    if (!company) {
        return null;
    }

    return <Profile company={company} />;
};

export { WrappedProfile as Profile };
