import type { FC } from 'react';
import { EnergyCapacity } from '@repo/dcide-component-models';

import { InlineFilters } from 'components/component-overview/inline-filters/InlineFilters';
import { CapacityField } from 'components/component-fields/CapacityField';
import { SearchService } from 'components/component-overview/services/SearchService';
import { useComponentSearchFilters } from 'components/component-overview/hooks/use-component-search-filters';
import { FormatHelpers } from 'helpers/formatters';
import { energyConverter } from 'units/energy';

const InlineFiltersCapacity: FC = () => {
    const { energyCapacity } = useComponentSearchFilters();

    const label = FormatHelpers.formatValue(energyCapacity?.value, energyConverter, EnergyCapacity.defaultViewUnit);

    const onRemove = () => {
        SearchService.setFilter('energyCapacity', undefined);
    };

    if (!energyCapacity) return null;

    return (
        <InlineFilters.Section
            body={
                <InlineFilters.MeasurementWrapper label={label} placeholder="Capacity">
                    <CapacityField name="energyCapacity" size="xs" hideIcons />
                </InlineFilters.MeasurementWrapper>
            }
            onRemove={onRemove}
            active
        />
    );
};

export { InlineFiltersCapacity };
