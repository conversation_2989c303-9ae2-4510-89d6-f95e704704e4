.root {
    display: grid;

    grid-template-areas: "content" "images";
    grid-template-rows: auto auto;
    grid-template-columns: auto;
    align-items: start;
    justify-content: end;

    gap: var(--mantine-spacing-xs);

    margin-bottom: var(--mantine-spacing-xs);

    &[data-images="false"] {
        grid-template-areas: "content";
        grid-template-rows: auto;
        grid-template-columns: auto;
    }

    @media (min-width: var(--mantine-breakpoint-sm)) {
        grid-template-areas: "content images";
        grid-template-rows: auto;
        grid-template-columns: auto 200px;
        justify-content: initial;
    }
}

.content {
    grid-area: content;

    gap: var(--mantine-spacing-md);
}

.images {
    grid-area: images;

    display: flex;

    max-width: 200px;
}

.imagesInner {
    border: 1px solid var(--mantine-color-gray-2);
    border-radius: var(--mantine-radius-xs);
}
