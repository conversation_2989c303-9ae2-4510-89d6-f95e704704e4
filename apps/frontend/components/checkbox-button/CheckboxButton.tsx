import { Checkbox, CheckboxProps } from '@mantine/core';

import cx from './CheckboxButton.module.css';

const CheckboxButton = ({
    label,
    description,
    checked,
    setChecked,
    borderTop = true,
    borderBottom = true,
    ...props
}: {
    setChecked: (value: boolean) => void;
    borderTop?: boolean;
    borderBottom?: boolean;
} & CheckboxProps) => {
    return (
        <Checkbox
            size="xs"
            classNames={cx}
            checked={checked}
            label={label}
            description={description}
            onChange={(event) => setChecked(event.currentTarget.checked)}
            wrapperProps={{
                'onClick': () => setChecked(!checked),
                'data-border-top': borderTop,
                'data-border-bottom': borderBottom,
            }}
            {...props}
        />
    );
};

export { CheckboxButton };
