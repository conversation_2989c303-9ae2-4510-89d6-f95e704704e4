import { Payload } from 'payload';

import { Notification, NotificationKey } from '@repo/dcide-component-models';
import type { NotificationFormatter } from '../get-notification-formatter';

import { EmailCopyHelpers } from '../../../emails/helpers/EmailCopyHelpers';

type NotificationType = Extract<Notification, { type: NotificationKey.TEAM_DENY_ACCESS }>;

const teamDenyAccess: NotificationFormatter<NotificationType> = {
    key: NotificationKey.TEAM_DENY_ACCESS,
    email: async (payload, notification: NotificationType) => {
        const { userId, teamId, message } = notification.data;
        const { team } = await preloadData(payload, { userId, teamId });

        return EmailCopyHelpers.teamDenyAccess({
            team,
            message: {
                text: message,
            },
        });
    },
    app: async (payload, notification: NotificationType) => {
        const { userId, teamId, message } = notification.data;
        const { team } = await preloadData(payload, { userId, teamId });

        return {
            text: `
                Unfortunately, your request to access the <strong>${team}</strong> team has been denied.
                The team admin has provided the following message: ${message}`,
            url: '',
        };
    },
};

const preloadData = async (payload: Payload, { userId, teamId }: { userId: string; teamId: string }) => {
    const [user, team] = await Promise.all([
        await payload.findByID({
            collection: 'users',
            id: userId,
        }),
        await payload.findByID({
            collection: 'teams',
            id: teamId,
        }),
    ]);

    return {
        user,
        team,
    };
};

export { teamDenyAccess };
