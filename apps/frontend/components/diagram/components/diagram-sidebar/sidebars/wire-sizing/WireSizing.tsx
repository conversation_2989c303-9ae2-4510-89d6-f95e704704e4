import React, { FC } from 'react';

import { FeatureKey } from '@repo/dcide-component-models';
import type { WireSizing } from '@repo/dcide-component-models';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { SubscriptionOnboardingMessage } from 'components/subscriptions/SubscriptionOnboardingMessage';

import { Detail } from './WireSizing.Detail';
import { Overview } from './WireSizing.Overview';

import { useFeatureAccess } from 'hooks/use-feature-access';
import { useWireSizings } from 'components/diagram/hooks/use-wire-sizings';

const WireSizing: FC = () => {
    const { wireSizing, isLoading } = useWireSizings();

    const access = useFeatureAccess(FeatureKey.RUN_SIMULATIONS);

    if (!access) {
        return (
            <DiagramSidebar.Section>
                <SubscriptionOnboardingMessage>Want to run wire sizings on your diagram?</SubscriptionOnboardingMessage>
            </DiagramSidebar.Section>
        );
    }

    if (isLoading) {
        return <DiagramSidebar.Loader />;
    }

    return wireSizing ? <Detail /> : <Overview />;
};

export { WireSizing };
