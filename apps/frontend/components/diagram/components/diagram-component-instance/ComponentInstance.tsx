import { memo, FC } from 'react';

import { Box } from '@mantine/core';

import { ComponentInstanceLabels } from './ComponentInstanceLabels';
import { ComponentInstanceResize } from './ComponentInstanceResize';

import { useDraggable } from '@dnd-kit/core';
import {
    useComponentInstance,
    useConnections,
    getDiagramConverterIcon,
    useDiagramMode,
} from 'components/diagram/hooks';

import { useSelectedComponentInstances } from 'components/diagram/hooks/selection/use-selected-component-instances';

import { ContextMenuService } from 'components/diagram/services/ContextMenuService';
import { SelectionService } from 'components/diagram/services/SelectionService';
import { getDragAndDropTranslate } from 'components/diagram/helpers';

import { DiagramComponentInstanceDropzone, Z_INDEX } from 'components/diagram';

import { CELL_WIDTH, CELL_HEIGHT } from 'components/diagram/diagram-dimensions';

import { DiagramComponentInstance, PermissionDiagramElements } from '@repo/dcide-component-models';

import cx from './ComponentInstance.module.css';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';
import { usePermission } from 'hooks/use-permission';
import { DraggingType } from 'types/DraggingType';
import { drawConnectionState } from 'components/diagram/state/draw-connection';
import { useSnapshot } from 'hooks/use-safe-snapshot';

type UseDraggableProps = Partial<ReturnType<typeof useDraggable>>;

const InternalComponentInstance: FC<
    {
        componentInstanceId: DiagramComponentInstance['id'];
        selected: boolean;
        isPartOfSelection: boolean;
    } & UseDraggableProps
> = memo(({ componentInstanceId, selected, isPartOfSelection, ...draggableProps }) => {
    const { isDragging, setNodeRef, transform, listeners, attributes } = draggableProps;

    const componentInstance = useComponentInstance(componentInstanceId);

    const { mode } = useDiagramMode();

    const { isConnecting } = useSnapshot(drawConnectionState);

    const connections = useConnections({
        componentInstance,
    });

    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const { isSmallConverter } = getDiagramConverterIcon(componentInstance);

    if (!componentInstance) {
        return null;
    }

    const { position, componentType } = componentInstance;
    const enableResize = canEdit && selected && !isPartOfSelection;

    const InnerComponentInstance: Parameters<typeof ComponentInstanceResize>[0]['children'] = (
        width,
        height,
        top,
        left,
        shadow,
        resizing,
    ) => {
        return (
            <Box
                className={cx.componentWrapper}
                style={{
                    '--col-span': componentInstance.colSpan,
                    '--row-span': componentInstance.rowSpan,

                    'width': width,
                    'height': height,

                    'left': left,
                    'top': top,

                    'transform': transform && !resizing ? getDragAndDropTranslate(transform) : '',
                }}
                onClick={(event) => {
                    if (isConnecting) {
                        console.log('onClick: connecting');
                        return;
                    }

                    // this is a dirty hack to prevent upper click handlers messing with the selection
                    if ((event.target as Element).getAttribute('data-allow-click') === 'true') {
                        SelectionService.selectOrToggleComponentInstance(componentInstanceId);
                    } else {
                        console.log('onClick: allow-click');
                    }

                    event.stopPropagation();
                }}
                onContextMenu={(event) => {
                    event.preventDefault();

                    if (!canEdit) {
                        return;
                    }

                    if (isPartOfSelection) {
                        return;
                    }

                    event.stopPropagation();

                    SelectionService.selectComponentInstance(componentInstanceId);
                    ContextMenuService.openComponentInstanceContextMenu(
                        {
                            x: event.clientX,
                            y: event.clientY,
                        },
                        componentInstance,
                    );
                }}
                {...attributes}
                {...listeners}
                aria-selected={selected}
                data-selectable
                data-selectable-type="component-instance"
                data-selectable-id={componentInstanceId}
                data-editable={mode === 'edit'}
                data-multiple={componentInstance.multiple?.amount > 1}
                data-allow-click="true"
            >
                <Box className={cx.componentSVGIcon} data-full-size={!!(isSmallConverter && connections.length)}>
                    <ComponentIcon
                        type={componentType}
                        componentInstance={componentInstance}
                        connections={connections}
                        resizing={resizing}
                    />
                </Box>
                {resizing ? (
                    <Box className={cx.componentResizeShadow} style={shadow} />
                ) : (
                    <Box className={cx.componentShadow} data-allow-click="true" ref={setNodeRef} key="draghandle" />
                )}
            </Box>
        );
    };

    return (
        <>
            <Box
                className={cx.cell}
                style={{
                    'top': position.y * CELL_HEIGHT,
                    'left': position.x * CELL_WIDTH,

                    'zIndex': isDragging ? Z_INDEX.CANVAS.COMPONENT_DRAGGING : Z_INDEX.CANVAS.COMPONENT_DEFAULT,

                    '--cell-width': `${CELL_WIDTH * componentInstance.colSpan}px`,
                    '--cell-height': `${CELL_HEIGHT * componentInstance.rowSpan}px`,
                }}
            >
                <DiagramComponentInstanceDropzone
                    componentInstanceId={componentInstanceId}
                    componentId={componentInstance.componentId}
                >
                    <ComponentInstanceResize componentInstance={componentInstance} enabled={enableResize}>
                        {InnerComponentInstance}
                    </ComponentInstanceResize>
                </DiagramComponentInstanceDropzone>
            </Box>
            {!isDragging && <ComponentInstanceLabels componentInstance={componentInstance} />}
        </>
    );
});

InternalComponentInstance.displayName = 'ComponentInstance';

export const ComponentInstance: FC<{
    componentInstanceId: DiagramComponentInstance['id'];
}> = ({ componentInstanceId }) => {
    const componentInstance = useComponentInstance(componentInstanceId);

    const { isSelectedComponentInstance, isOnlySelectedComponentInstance } = useSelectedComponentInstances();
    const selected = isSelectedComponentInstance(componentInstanceId);
    const isPartOfSelection = selected && !isOnlySelectedComponentInstance(componentInstanceId);

    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const id = isPartOfSelection ? 'selection' : `draggable-${componentInstanceId}`;

    const { isDragging, setNodeRef, transform, listeners, attributes } = useDraggable({
        id,
        data: {
            type: DraggingType.EXISTING_COMPONENT_INSTANCE,
            componentInstance: componentInstance,
        },
        disabled: !canEdit,
    });

    return (
        <InternalComponentInstance
            componentInstanceId={componentInstanceId}
            selected={selected}
            isPartOfSelection={isPartOfSelection}
            isDragging={isDragging}
            setNodeRef={setNodeRef}
            transform={transform}
            listeners={listeners}
            attributes={attributes}
        />
    );
};
