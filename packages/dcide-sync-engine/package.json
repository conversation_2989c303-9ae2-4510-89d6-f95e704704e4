{"name": "@repo/dcide-sync-engine", "description": "Synchronization engine to synchronize objects between clients and server over the internet", "main": "./src/index.ts", "type": "module", "exports": {".": {"serve": "./lib/commonjs/index.js", "default": "./src/index.ts"}}, "files": ["lib/"], "scripts": {"test:unit": "jest --silent", "build": "tsc -p tsconfig.json && tsc -p tsconfig-commonjs.json"}, "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "radash": "^12.1.0", "ts-jest": "^29.1.2", "typescript": "^5.4.5"}}