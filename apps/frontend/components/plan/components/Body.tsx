import React, { useEffect, useState } from 'react';
import { uid } from 'radash';

import { Flex, Loader, Transition, Stack } from '@mantine/core';

import { SearchAgentMessage, SearchAgentAssistantMessage } from '@repo/dcide-component-models';

import { planPageState } from 'components/plan/state/plan-page';

import { Welcome } from './Welcome';
import { Links } from './components/Links';
import { Input } from './components/Input';
import { Message } from './components/Message';
import { UpdateMessage } from './components/UpdateMessage';
import { ContentWrapper } from './components/ContentWrapper';
import { NextStep, NextSteps } from './components/NextSteps';

import { SearchAgentService } from 'services/AISearchAgentService';

import { useSearchThread } from '../hooks/use-search-thread';
import { useSetActiveEventOnMount } from '../hooks/use-set-active-event';
import { useSaveChatQueryAction } from '../hooks/use-save-chat-query-action';

import cx from './Body.module.scss';

export const Body = () => {
    useSaveChatQueryAction();
    useSetActiveEventOnMount();

    const { activeSearchThread, searchThreadId, create } = useSearchThread();

    const [activeHistoryId, setActiveHistoryId] = useState<string | null>(searchThreadId ?? null);

    const [history, setHistory] = useState<{ [id: string]: SearchAgentMessage }>({});
    const [updates, setUpdates] = useState<string[]>([]);
    const [nextSteps, setNextSteps] = useState<NextStep[]>([]);
    const [suggestions, setSuggestions] = useState<NextStep[]>([]);

    useEffect(() => {
        if (!activeSearchThread) return;
        if (activeHistoryId === activeSearchThread.id) return;

        setActiveHistoryId(activeSearchThread.id);

        const rawMessages = activeSearchThread.thread?.input ?? [];

        const relevantMessages = rawMessages.filter(({ role }) => role !== 'system' && role !== 'developer');

        const messagesById = relevantMessages.reduce(
            (acc, msg) => {
                const id = msg.id ?? uid(4);

                acc[id] = {
                    id,
                    content: msg.content,
                    role: msg.role,
                };

                return acc;
            },
            {} as Record<string, SearchAgentMessage>,
        );

        setUpdates([]);
        setNextSteps([]);
        setHistory(messagesById);
    }, [searchThreadId, JSON.stringify(activeSearchThread), setHistory]);

    const [searchText, setSearch] = useState('');
    const [streamReader, setStreamReader] = useState<ReadableStreamDefaultReader<any> | null>(null);

    const [isThinking, setIsThinking] = useState(false);
    const [streamingId, setStreamingId] = useState<string | null>(null); // streaming message id

    const isInitial = !Object.keys(history).length;

    useEffect(() => {
        planPageState.isInitial = isInitial;
    }, [isInitial]);

    const startStream = async (search: string, threadId: string) => {
        try {
            setUpdates([]);
            setNextSteps([]);
            setIsThinking(true);

            const stream = await SearchAgentService.ask(search, threadId);
            const reader = stream.getReader();
            setStreamReader(reader);

            const answerId = uid(4);

            const processStream = async () => {
                const { done, value } = await reader.read();

                setIsThinking(false);
                setStreamingId(answerId);

                if (done) {
                    setStreamReader(null);
                    setStreamingId(null);
                    return;
                }

                let parsedValue: any | null = null;

                try {
                    parsedValue = JSON.parse(value);
                } catch (error) {
                    // ignore JSON.parse errors
                }

                const handleParsedValue = (parsedValue: string | undefined, answerId: string) => {
                    if (!parsedValue || typeof parsedValue !== 'string') return;

                    if (parsedValue.includes('keep-alive')) return;

                    if (parsedValue.includes('agent-update')) {
                        setUpdates((oldUpdates) => {
                            const lastUpdate = oldUpdates[oldUpdates.length - 1];
                            const newUpdate = parsedValue.replace('agent-update:', '');

                            if (lastUpdate === newUpdate) return oldUpdates;

                            return [...oldUpdates, newUpdate];
                        });

                        return;
                    }

                    if (parsedValue.includes('next-steps')) {
                        try {
                            const nextSteps = JSON.parse(parsedValue.replace('next-steps:', ''));
                            if (nextSteps?.steps) {
                                setNextSteps(nextSteps.steps);
                            }
                            if (nextSteps?.suggestions) {
                                setSuggestions(nextSteps.suggestions);
                            }
                        } catch (error) {
                            console.error('Error parsing next steps:', error);
                        }
                        return;
                    }

                    setHistory((prevHistory) => ({
                        ...prevHistory,
                        [answerId]: {
                            id: answerId,
                            role: 'assistant',
                            content: (prevHistory[answerId]?.content ?? '') + parsedValue,
                        },
                    }));
                };

                // show the streamed update to user
                handleParsedValue(parsedValue, answerId);

                processStream(); // Continue reading the stream
            };

            processStream();
        } catch (err) {
            setIsThinking(false);
            setStreamingId(null);

            console.error('Stream error:', err);

            const errorMessage: SearchAgentAssistantMessage = {
                id: uid(4),
                content: 'Something went wrong',
                role: 'assistant',
            };

            setHistory((prevHistory) => {
                return {
                    ...prevHistory,
                    [errorMessage.id]: errorMessage,
                };
            });

            setStreamReader(null);
        }
    };

    const handleSubmit = async (stepNumber?: string) => {
        const content = stepNumber || searchText;

        if (!content) {
            return;
        }

        const question: SearchAgentMessage = {
            id: uid(4),
            content,
            role: 'user',
        };

        const threadId = searchThreadId || (await create());

        setActiveHistoryId(threadId);

        setHistory((prevHistory) => {
            return {
                ...prevHistory,
                [question.id]: question,
            };
        });

        setSearch('');

        await startStream(content, threadId);
    };

    useEffect(() => {
        return () => {
            if (streamReader) {
                streamReader.cancel();
            }
        };
    }, [streamReader]);

    const isSteaming = isThinking || !!streamingId;
    const isLoading = isThinking || (!!streamingId && !Object.keys(history).includes(streamingId));

    const placeholder = suggestions.length > 0 ? suggestions[0].label : '';

    return (
        <Flex id="container" className={cx.container} data-streaming={isSteaming}>
            <Transition transition="slide-up" mounted={isInitial} duration={500} enterDelay={500}>
                {(style) => <Links hiddenFrom="md" align="center" justify="center" mt="xs" style={style} />}
            </Transition>

            <ContentWrapper isSteaming={isSteaming}>
                <Transition transition="slide-up" mounted={!isInitial} duration={500} enterDelay={500}>
                    {(style) => (
                        <>
                            {Object.values(history).map((message) => (
                                <Message
                                    key={message.id}
                                    message={message}
                                    isStreaming={message.id === streamingId}
                                    style={style}
                                />
                            ))}
                        </>
                    )}
                </Transition>

                {isLoading && (
                    <Flex className={cx.block} data-type="answer" gap="xs">
                        <Loader size="sm" type="dots" />

                        <Transition transition="slide-up" mounted={!!updates.length} duration={500} enterDelay={500}>
                            {(style) => <UpdateMessage message={updates.join(', ')} style={style} />}
                        </Transition>
                    </Flex>
                )}
            </ContentWrapper>

            <Welcome mt="auto" hidden={!isInitial} />

            <Stack id="input" className={cx.input} data-position={isInitial ? 'default' : 'bottom'}>
                <NextSteps
                    nextSteps={nextSteps}
                    handleSubmit={handleSubmit}
                    mounted={!isSteaming && nextSteps.length > 0}
                />
                <Input
                    showHint={isInitial}
                    value={searchText}
                    setValue={setSearch}
                    isInitial={isInitial}
                    handleSubmit={() => handleSubmit()}
                    onChange={(e) => setSearch(e.currentTarget.value)}
                    isSteaming={isSteaming}
                    placeholder={placeholder}
                />
            </Stack>
        </Flex>
    );
};
