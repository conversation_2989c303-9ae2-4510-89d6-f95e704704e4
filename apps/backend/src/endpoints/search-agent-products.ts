import type { Payload } from 'payload';

import OpenAI from 'openai';

import { AIService } from '@/services';
import { SearchService } from '@/services/search/SearchService';
import { config } from '@/config';
import { evaluateCompatibility, evaluateSearchResults, productInternetSearch } from './requirements-prompts';
import { getPayload } from '@/helpers/get-payload';
import { MAX_ITERATIONS, RESULT_LIMIT, SHOWN_RESULT_LIMIT, TEMPERATURE } from './search-agent2';
import { SearchAgentHelpers } from '@/helpers';

const PRODUCTS_AGENT_SYSTEM_PROMPT = `
    You are an agent tasked with quickly directing calls the right tools. 
    Only ouput the answers to <planning>

    # INSTRUCTIONS:
    - stop when there are results
    - use searchProducts tool to find products
    - when previousSearchResultCount = 0, use the searchProducts tool again with relaxed search query
    - use the evaluate Compatibility tool when the user is interested compatibility with a specific product
    - Only ouput the answers to the <planning> questions

    # INFORMATION:
    - a "solution" product is a valid converter
    - a "solution" product is a valid battery

    # TOOL RULES:
    ## searchProducts:
        - determine the search filter based on the user question
        - do not include subject terms in the search query, such as "high capacity"
        - keep search filters concise to a minimum:
            - example: "converter 800 V to 450 V Bidirectional 40 kW AC/DC"
            - example: "converter 11 kW maximum DC output, isolated, AC power source, 130 V nominal"
            - example: "battery 800 V 40 kW"
            - example: "40V solar panel 500W bifacial"
            - example: "three phase 480vac 20kW converter"
            - example: "25kw 5a 240v converter"
        - when pairing products together, determine the specification of the appropriate port:
            - when looking for a battery, use the converter DC battery output port specification
            - when looking for a converter, use the battery DC output port specification
        - use the searchProducts to get the product specifications by searching for the product name
    ## evaluateCompatibility:
        - only evaluate compatiblity when the user has a specific product in mind and is looking for a compatible products
        - use the evaluateCompatibility tool to evaluate of compatibility of battery and converter products

    # OUTPUT FORMAT:
    <planning>
        - has the user specified project goals/requirements?
        - did the searchProducts return any results?
        - compatibility: is the user interested in compatiblity with a specific product?
        - did you use the evaluateCompatibility tool to evaluate compatibility? ( required to evaluate compatibility using expert knowledge )
        - did you fulfill the users request, next step?
    </planning>
`;

const tools: OpenAI.Responses.FunctionTool[] = [
    {
        type: 'function',
        name: 'searchProducts',
        description: 'Call the preffered search engine to find products',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                previousSearchFilter: {
                    type: 'string',
                    description: 'previous search filter used',
                },
                previousSearchResultCount: {
                    type: 'number',
                    description: 'previous searchProducts results count',
                },
                searchFilters: {
                    type: 'string',
                    description: 'search filter used to filter product catalog',
                },
                specificProductSpecification: {
                    type: 'string',
                    description: 'specific product specification',
                },
                projectRequirements: {
                    type: 'string',
                    description:
                        'summarize the project goals and requirements. This field should be empty if the user has not specified any project goals or requirements',
                },
            },
            required: [
                'previousSearchFilter',
                'previousSearchResultCount',
                'searchFilters',
                'specificProductSpecification',
                'projectRequirements',
            ],
            additionalProperties: false,
        },
    },
    {
        type: 'function',
        name: 'evaluateCompatibility',
        description: 'evaluate the compatibility of search results against the users target product',
        strict: true,
        parameters: {
            type: 'object',
            properties: {
                specificProductName: {
                    type: 'string',
                    description: 'user specific product name',
                },
                specificProductSpecification: {
                    type: 'string',
                    description: 'user specific product specification',
                },
                searchProductNames: {
                    type: 'array',
                    items: {
                        type: 'string',
                        description:
                            'names of specific products to evaluate for compatibility with the user specific product',
                    },
                },
            },
            required: ['specificProductName', 'specificProductSpecification', 'searchProductNames'],
            additionalProperties: false,
        },
    },
];

const handleToolCall = async (
    toolCall: OpenAI.Responses.ResponseFunctionToolCall,
    options: {
        input: any;
        searchTries: number;
        searchThreadId?: string;
        tradeshowEvent?: string;
        payload: Payload;
        onUpdate?: (message: string, details?: string) => void;
    },
) => {
    const { name, arguments: args } = toolCall;
    const { input, searchTries, searchThreadId, tradeshowEvent, payload, onUpdate } = options;

    let output = '';
    let filtersUsed = undefined;

    if (name === 'searchProducts') {
        const { searchFilters, requirements } = JSON.parse(args);
        console.log('Searching for products with args:', JSON.stringify(args, null, 2));
        onUpdate?.(`Searching for products`, `Query: "${searchFilters}" Requirements: "${requirements ?? 'none'}"`);

        const filters = await SearchService.getComponentFilters(JSON.stringify({ searchFilters }, null, 2), payload);
        filtersUsed = JSON.stringify({ filters }, null, 2);

        const componentSearchResults = await SearchService.getComponents(
            payload,
            { ...filters, event: tradeshowEvent },
            {
                limit: requirements ? RESULT_LIMIT : SHOWN_RESULT_LIMIT,
                project: {
                    id: { $toString: '$_id' },
                    name: 1,
                    description: 1,
                    productSeries: 1,
                    productIdentifier: 1,
                    type: 1,
                    manufacturer: 1,
                    manufacturerDetails: 1,
                    compliance: 1,
                    embeddingText: 1,
                    specificationsSummary: 1,
                    electrical: 1,
                    communication: 1,
                    environmental: 1,
                    performance: 1,
                    mechanical: 1,
                },
            },
        );

        if (requirements && componentSearchResults.docs.length > 3) {
            try {
                const evaluatedResults = await evaluateSearchResults(
                    componentSearchResults.docs,
                    requirements,
                    'product',
                );

                componentSearchResults.docs = evaluatedResults.slice(0, SHOWN_RESULT_LIMIT);
            } catch {
                console.warn(`Failed to evaluate search results, defaulting to first ${SHOWN_RESULT_LIMIT}`);
                componentSearchResults.docs = componentSearchResults.docs.slice(0, SHOWN_RESULT_LIMIT);
            }
        }

        componentSearchResults.docs = componentSearchResults.docs.map((product) => ({
            productId: product._id.toString(),
            name: product.name,
            type: product.type,
            productSeries: product.productSeries,
            productIdentifier: product.productIdentifier,
            description: product.description,
            electrical: product.electrical,
            manufacturer: product.manufacturer,
            manufacturerDetails: product.manufacturerDetails,
            embeddingText: product.embeddingText,
            // TODO: use specificationsSummary: product.specificationsSummary, need to regenerate data in db, required for compatibility evaluation, then can drop electrical: product.electrical
        }));

        const saveProductsAsComponentIds: string[] =
            componentSearchResults?.docs?.map((product: any) => product.productId) ?? [];
        AIService.upsertExhibitorMatchResults(
            searchThreadId,
            {
                productSearchResults: saveProductsAsComponentIds,
            },
            tradeshowEvent,
        );

        const hasNoResults = componentSearchResults.docs.length === 0;
        let searchResults;

        if (hasNoResults && searchTries >= MAX_SEARCH_TRIES) {
            onUpdate?.(`Searching for internet products`, `Query: "${searchFilters}"`);
            console.log('Searching for internet products with args:', searchFilters);
            searchResults = await productInternetSearch(searchFilters);
        } else {
            searchResults = JSON.stringify(componentSearchResults.docs, null, 2);
        }

        output = searchResults;
    } else if (name === 'evaluateCompatibility') {
        const { specificProductName, specificProductSpecification } = JSON.parse(args);

        console.log('Evaluating Converter And Battery Products Compatibility: ', JSON.stringify(args));
        onUpdate?.(
            `Evaluating Converter And Battery Products Compatibility`,
            `Target Product: "${specificProductName}" Specification: "${specificProductSpecification}"`,
        );

        const results = await evaluateCompatibility(
            truncateContext(input),
            specificProductName,
            specificProductSpecification,
        );
        output = results;
    }

    return {
        toolCall,
        output,
        filters: filtersUsed,
    };
};

const truncateContext = (input: any) => {
    const maxChars = 128000; // ~32k tokens

    const context = JSON.stringify(input, null, 2);

    if (context.length <= maxChars) return context;
    return context.slice(0, maxChars);
};

const setupAbortController = (writer?: WritableStreamDefaultWriter): AbortSignal => {
    const { signal } = new AbortController();
    const encoder = new TextEncoder();

    // Send keep-alive messages every 30 seconds to maintain connection
    const keepAlive = setInterval(async () => {
        if (!signal.aborted) {
            try {
                await writer?.write(encoder.encode(`event: ping\ndata: ${JSON.stringify('keep-alive')}\n\n`));
            } catch {
                clearInterval(keepAlive);
            }
        }
    }, 30000);

    signal.addEventListener('abort', async () => {
        clearInterval(keepAlive);
        await writer?.close();
        writer = undefined;
    });

    return signal;
};

const formatSystemPrompt = (toolCalls: OpenAI.Responses.ResponseFunctionToolCall[]) => {
    let hasCompatiblity = false;
    let hasProductScore = false;
    toolCalls.forEach((toolCall) => {
        if (toolCall.name === 'evaluateCompatibility') {
            hasCompatiblity = true;
        }
        if (toolCall.name === 'searchProducts') {
            hasProductScore = true;
        }
    });

    const formattedPrompt = PRODUCTS_AGENT_SYSTEM_PROMPT.replace(
        '{{compatibility}}',
        hasCompatiblity ? '- compatibiltiy:' : '',
    ).replace('{{productScore}}', hasProductScore ? '- product score :\n- product score rationale :' : '');

    return formattedPrompt;
};

const setupInitialSearchToolCall = async (
    searchFilters: string,
    requirements: string,
): Promise<OpenAI.Responses.ResponseFunctionToolCall[]> => {
    const toolCalls: OpenAI.Responses.ResponseFunctionToolCall[] = [
        {
            name: 'searchProducts',
            type: 'function_call',
            arguments: JSON.stringify({ searchFilters, requirements }),
            call_id: '0',
        },
    ];
    return toolCalls;
};

const processToolCalls = async (
    input: OpenAI.Responses.ResponseInput,
    toolCalls: OpenAI.Responses.ResponseFunctionToolCall[],
    reasoningCalls: OpenAI.Responses.ResponseReasoningItem[],
    options: {
        searchTries: number;
        searchThreadId?: string;
        tradeshowEvent?: string;
        payload: Payload;
        onUpdate?: (message: string) => void;
    },
): Promise<{ searchResults: string | null; filtersUsed: string | null; shouldBreak: boolean }> => {
    let shouldBreak = false;
    let searchResults = null;
    let filtersUsed = null;
    const { searchTries, searchThreadId, tradeshowEvent, payload, onUpdate } = options;

    const toolCallResults = await Promise.all(
        toolCalls
            .filter((toolCall) => toolCall !== null)
            .map((toolCall) =>
                handleToolCall(toolCall, {
                    input,
                    searchTries,
                    searchThreadId,
                    tradeshowEvent,
                    payload: payload,
                    onUpdate,
                }),
            ),
    );

    if (toolCallResults.length === 0) {
        shouldBreak = true;
    }

    for (const { output, toolCall, filters } of toolCallResults) {
        input.push(toolCall);

        const functionCallOutput: OpenAI.Responses.ResponseInputItem = {
            type: 'function_call_output',
            call_id: toolCall.call_id,
            output: output ?? '',
        };
        input.push(functionCallOutput);

        if (toolCall.name === 'searchProducts') {
            searchResults = JSON.stringify(functionCallOutput, null, 2);
        }

        if (filters) {
            filtersUsed = filters;
        }
    }

    if (reasoningCalls.length > 0) {
        reasoningCalls.forEach((reasoningCall) => {
            input.push(reasoningCall);
        });
    }

    return {
        searchResults,
        filtersUsed,
        shouldBreak,
    };
};

const generateInput = async (
    payload: Payload,
    question: string,
    searchThreadId: string | undefined,
    tradeshowEvent?: string,
) => {
    const input: OpenAI.Responses.ResponseInput = [];
    const searchThread = await SearchAgentHelpers.getExistingSearchThreadInputFromPayload(payload, searchThreadId);

    if (searchThread) {
        input.push(...searchThread);
    }

    if (tradeshowEvent) {
        await SearchAgentHelpers.addTradeShowEvent(payload, tradeshowEvent, input);
    }

    input.push({ role: 'user', content: question });

    return input;
};

const cleanInput = (input: string) => {
    const result = input.replace(/\\\\/g, '');
    return result;
};

const getApiKey = () => {
    const apiKey = config.openai.apiKey;

    if (!apiKey) {
        throw new Error('API key not configured.');
    }

    return apiKey;
};

const MAX_SEARCH_TRIES = 2;

export const searchProducts = async (
    searchFilters: string,
    requirements: string,
    question: string,
    searchThreadId: string | undefined,
    tradeshowEvent: string | undefined,
    onUpdate?: (message: string) => void,
    writer?: WritableStreamDefaultWriter,
): Promise<{ searchResults: string; previousProductFilters: string }> => {
    try {
        const apiKey = getApiKey();
        const openai = new OpenAI({ apiKey });
        const payload = await getPayload();
        const input: OpenAI.Responses.ResponseInput = await generateInput(
            payload,
            question,
            searchThreadId,
            tradeshowEvent,
        );

        const signal = setupAbortController(writer);
        let response: string = '';
        let reasoningCalls: OpenAI.Responses.ResponseReasoningItem[] = [];
        let previousProductFilters = '';
        let searchTries = 0;
        let toolCalls = await setupInitialSearchToolCall(searchFilters, requirements);

        for (let iteration = 0; iteration <= MAX_ITERATIONS; iteration++) {
            try {
                if (signal.aborted) break;

                console.log('PRODUCT AGENT ITERATION', iteration);

                const { searchResults, filtersUsed, shouldBreak } = await processToolCalls(
                    input,
                    toolCalls,
                    reasoningCalls,
                    {
                        searchTries,
                        searchThreadId,
                        tradeshowEvent,
                        payload,
                        onUpdate,
                    },
                );

                if (searchResults) {
                    response = searchResults;
                    previousProductFilters = filtersUsed ? filtersUsed : previousProductFilters;
                    searchTries += 1;
                }
                if (shouldBreak) {
                    break;
                }

                const aiStream = await openai.responses.create({
                    model: config.aiModels.productAgent,
                    instructions: formatSystemPrompt(toolCalls),
                    input: cleanInput(JSON.stringify(input, null, 2)),
                    stream: true,
                    tools: tools,
                    tool_choice: 'auto',
                    parallel_tool_calls: false,
                    reasoning: {
                        effort: 'low',
                    },
                    max_output_tokens: 500,
                    temperature: TEMPERATURE,
                });

                signal.addEventListener('abort', aiStream.controller?.abort);

                toolCalls = [];
                reasoningCalls = [];
                for await (const event of aiStream) {
                    if (signal.aborted) break;

                    if (event.type === 'response.output_text.done') {
                        input.push({
                            id: event.item_id,
                            role: 'assistant',
                            content: event.text,
                        });

                        if (searchThreadId) {
                            await payload.update({
                                collection: 'searchThreads',
                                id: searchThreadId,
                                data: {
                                    thread: {
                                        input,
                                    },
                                },
                            });
                        }
                    } else if (event.type === 'response.output_item.added') {
                        if (event.item.type === 'function_call') {
                            toolCalls[event.output_index] = event.item;
                        }
                        if (event.item.type === 'reasoning') {
                            reasoningCalls[event.output_index] = event.item;
                        }
                    } else if (event.type === 'response.function_call_arguments.delta') {
                        const index = event.output_index;

                        if (toolCalls[index]) {
                            toolCalls[index].arguments += event.delta;
                        }
                    } else if (event.type === 'response.output_item.done' && event.item.type === 'reasoning') {
                        const index = event.output_index;
                        if (reasoningCalls[index]) {
                            reasoningCalls[index].summary = event.item.summary;
                        }
                    } else if (event.type === 'response.function_call_arguments.done') {
                        const index = event.output_index;

                        if (toolCalls[index]) {
                            toolCalls[index].status = 'completed';
                        }
                    }
                }
                if (toolCalls.length === 0) {
                    break;
                }
            } catch (error) {
                console.log('ERROR', error);

                if (!signal.aborted) {
                    try {
                        await writer?.write(
                            new TextEncoder().encode(`event: error\ndata: {"message": "Stream failed"}\n\n`),
                        );
                    } catch {
                        console.log('writer already closed');
                    }
                }
                break;
            }
        }

        return { searchResults: response, previousProductFilters };
    } catch (error) {
        console.error('Search Agent Products error: ', error);
        try {
            await writer?.write(new TextEncoder().encode(`event: error\ndata: {"message": "Request Failed"}\n\n`));
        } catch {
            console.log('writer already closed');
        }
        return { searchResults: '', previousProductFilters: '' };
    }
};
