import React, { FC, useCallback, useEffect, useMemo, useState } from 'react';

import { uid } from 'radash';
import { Kbd, Table } from '@mantine/core';

import Markdown, { Components } from 'react-markdown';
import RemarkGFM from 'remark-gfm';

import cx from './AIMarkdown.module.css';
const regexId = /DCIDE_ID_([a-zA-Z0-9]+(?:_[\d]+)?)/;
const regexWord = /\S*DCIDE_ID_\S*/g;

const markdownComponents: Components = {
    a: ({ href, ...props }: any & { href: string }) => {
        return <a {...props} href={href} target={href.startsWith('http') ? '_blank' : '_self'}></a>;
    },
    code: ({ children }) => {
        return <Kbd>{children}</Kbd>;
    },
    table: ({ children }) => {
        return (
            <Table.ScrollContainer minWidth="100%">
                <Table
                    striped
                    withTableBorder
                    borderColor="gray.9"
                    horizontalSpacing="xs"
                    stripedColor="rgba(255, 255, 255, 0.05)"
                >
                    {children}
                </Table>
            </Table.ScrollContainer>
        );
    },
    tr: ({ children }) => {
        return <Table.Tr>{children}</Table.Tr>;
    },
    td: ({ children }) => {
        return <Table.Td>{children}</Table.Td>;
    },
    th: ({ children }) => {
        return <Table.Th>{children}</Table.Th>;
    },
    thead: ({ children }) => {
        return <Table.Thead>{children}</Table.Thead>;
    },
    tbody: ({ children }) => {
        return <Table.Tbody>{children}</Table.Tbody>;
    },
};

const AIMarkdown: FC<{
    id?: string;
    references?: any[];
    isStreaming?: boolean;
    children: string;
}> = ({ id = uid(4), references = [], isStreaming = false, children = '' }) => {
    const [shownText, setShownText] = useState(children);

    const getCleanText = useCallback(
        (text: string) => {
            return (
                text
                    // unescape new lines
                    .replaceAll('\\n', '\n')
                    // replace file references eg. DCIDE_ID_...
                    .replace(regexWord, (fullMatch: string) => {
                        // if streaming, footnotes don't get shown yet
                        if (isStreaming) return '**_[?]_**';

                        if (!references?.length) return '';

                        // footnotes format
                        return `[^${id}_${fullMatch.match(regexId)?.[0]}]`;
                    })
            );
        },
        [id, references, isStreaming],
    );

    useEffect(() => {
        if (typeof children !== 'string') return;

        if (!isStreaming) {
            setShownText(getCleanText(children));
            return;
        }

        const lastSpaceIndex = children.lastIndexOf(' ');
        const lastWord = children.substring(lastSpaceIndex + 1);

        // wait until word is long enough so we can extract DCIDE_ID_ from it
        if (lastWord?.length < 10) {
            const textWithoutLastWord = children.substring(0, lastSpaceIndex);

            setShownText(getCleanText(textWithoutLastWord));
            return;
        }

        setShownText(getCleanText(children));
    }, [children, isStreaming, setShownText, getCleanText]);

    const getShortDocumentName = (name: string) => {
        if (name.length <= 33) return name;

        return `${name.slice(0, 30)}${name.length > 30 && `...${name.slice(name.length - 3, name.length)}`}`;
    };

    const getDocumentUrl = (url: string, page: number) => {
        if (url.includes('.pdf')) return `${url}#page=${page}`;

        return url;
    };

    // format references as footnotes
    const formattedReferences = useMemo(
        () =>
            references
                ?.map(
                    ({ tag, document_name, document_url, page_number }) =>
                        `\n [^${id}_${tag}]: [${getShortDocumentName(document_name)}](${getDocumentUrl(
                            document_url,
                            page_number,
                        )}), page: ${page_number}`,
                )
                .join(', '),
        [references, id],
    );

    const loader = isStreaming ? '_**.**_' : '';

    return (
        <Markdown className={cx.markdown} remarkPlugins={[RemarkGFM]} components={markdownComponents}>
            {`${shownText} ${loader} ${formattedReferences}`}
        </Markdown>
    );
};

export { AIMarkdown };
