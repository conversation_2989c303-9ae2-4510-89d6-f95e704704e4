export enum ConnectionCores {
    SINGLE = 'single',
    MULTI = 'multi',
    MULTI_PE = 'multi-pe',
}

export const ConnectionCoresOptions = [
    {
        value: ConnectionCores.SINGLE,
        label: 'Single-Core (1 conductor)',
    },
    {
        value: ConnectionCores.MULTI,
        label: 'Multi-Core (multiple conductors)',
    },
    {
        value: ConnectionCores.MULTI_PE,
        label: 'Multi-Core with separate PE conductor',
    },
];
