import React, { FC, useEffect, useState } from 'react';
import { subscribe } from 'valtio';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { ActionIcon, Box, Button, Group, Popover, Text } from '@mantine/core';
import { TbX } from 'react-icons/tb';

import { diagram } from 'components/diagram/state/diagram';
import { tourState, TourSteps } from 'components/diagram/state/tour';

import { InternalTrackingService } from 'services/InternalTrackingService';

import cx from './DiagramTour.module.css';

const DiagramTour: FC = () => {
    const { step } = useSnapshot(tourState);

    const [bcr, setBCR] = useState<DOMRect | null>(null);
    const [position, setPosition] = useState<typeof Popover.prototype.position>('left');

    useEffect(() => {
        if (step) {
            InternalTrackingService.track('diagram.tour.step', {
                step,
            });
        }

        if (step === TourSteps.ADD_COMPONENTS) {
            const sidebar = document.querySelector('[data-diagram-sidebar]')!;

            setPosition('left');
            setBCR(sidebar.getBoundingClientRect());
        }

        if (step === TourSteps.COMPONENT_SPECIFICATIONS) {
            const component = document.querySelector('[data-component-instance]')!;

            setPosition('top');
            setBCR(component.getBoundingClientRect());
        }

        if (step === TourSteps.CONNECT_COMPONENTS) {
            const toolbar = document.querySelector('[data-component-instance]')!;

            setPosition('top');
            setBCR(toolbar.getBoundingClientRect());
        }

        if (step === TourSteps.ADD_ANNOTATIONS) {
            const toolbar = document.querySelector('[data-diagram-toolbar]')!;

            setPosition('right');
            setBCR(toolbar.getBoundingClientRect());
        }
    }, [step]);

    useEffect(() => {
        subscribe(diagram, (operations) => {
            if (tourState.step === TourSteps.ADD_COMPONENTS && Object.keys(diagram.componentInstances).length >= 2) {
                tourState.step = TourSteps.COMPONENT_SPECIFICATIONS;
            }

            if (tourState.step === TourSteps.CONNECT_COMPONENTS && Object.keys(diagram.connections).length > 0) {
                tourState.step = TourSteps.ADD_ANNOTATIONS;
            }

            if (tourState.step === TourSteps.ADD_ANNOTATIONS) {
                const contentChanged = operations.some(([operation, path]) => {
                    return operation === 'set' && path.includes('textareas') && path.includes('content');
                });

                if (contentChanged) {
                    tourState.step = TourSteps.FINISH;
                }
            }
        });
    }, []);

    return step !== null && bcr ? (
        <Popover opened position={position} withArrow>
            <Popover.Target>
                <Box
                    style={{
                        position: 'fixed',

                        left: bcr.left,
                        top: bcr.top,

                        width: bcr.width,
                        height: bcr.height,

                        pointerEvents: 'none',
                    }}
                />
            </Popover.Target>
            <Popover.Dropdown w={360} p="lg">
                <ActionIcon
                    className={cx.close}
                    variant="subtle"
                    size="xs"
                    onClick={() => {
                        tourState.step = null;
                    }}
                >
                    <TbX />
                </ActionIcon>
                {step === TourSteps.ADD_COMPONENTS && (
                    <Step title="Add components">
                        <Text>
                            <p>This collapsible sidebar is where you can click and drag components to your design.</p>
                            <p>Try dragging a few components to start your design.</p>
                        </Text>
                    </Step>
                )}
                {step === TourSteps.COMPONENT_SPECIFICATIONS && (
                    <Step
                        title="Component specifications"
                        actions={[
                            <StepButton
                                label="Next: connecting components"
                                step={TourSteps.CONNECT_COMPONENTS}
                                key="connect"
                            />,
                        ]}
                    >
                        <Text>
                            <p>By clicking a component you will open the specifications sidebar.</p>
                            <p>C&apos;mon, click it!</p>
                        </Text>
                    </Step>
                )}
                {step === TourSteps.CONNECT_COMPONENTS && (
                    <Step
                        title="Connecting components"
                        actions={[
                            <StepButton
                                label="Next: adding annotations"
                                step={TourSteps.ADD_ANNOTATIONS}
                                key="annotations"
                            />,
                        ]}
                    >
                        <p>
                            Move your cursor close to the the edge of a component and drag a line to another component
                            to connect them.
                        </p>
                    </Step>
                )}
                {step === TourSteps.ADD_ANNOTATIONS && (
                    <Step
                        title="Annotations"
                        actions={[<StepButton label="Continue" step={TourSteps.FINISH} key="finish" />]}
                    >
                        <p>Apart from components and connections, you can also add text and images to your diagram.</p>
                        <p>Don&apos;t be shy, add some text to your diagram!</p>
                    </Step>
                )}
                {step === TourSteps.FINISH && (
                    <Step title="Finished" actions={[<StepButton label="Close" step={null} key="close" />]}>
                        <p>You&apos;ve completed DCIDE&apos;s mini tutorial!</p>
                        <p>
                            Now it&apos;s time to create an account and discover all our other features like real-time
                            collaboration, simulations, and more.
                        </p>
                    </Step>
                )}
            </Popover.Dropdown>
        </Popover>
    ) : null;
};

const Step: FC<{
    title: string;
    children: React.ReactNode;
    actions?: React.ReactNode[];
}> = ({ title, children, actions }) => {
    return (
        <Box className={cx.step}>
            <Text>
                <strong>{title}</strong>
            </Text>
            {children}
            {actions && actions.filter(Boolean).length > 0 && <Group gap="xs">{actions.filter(Boolean)}</Group>}
        </Box>
    );
};

const StepButton: FC<{
    label: string;
    step: TourSteps | null;
    action?: () => void;
}> = ({ label, step, action }) => (
    <Button
        size="xs"
        variant="default"
        onClick={() => {
            tourState.step = step;

            action?.();
        }}
    >
        {label}
    </Button>
);

export { DiagramTour };
