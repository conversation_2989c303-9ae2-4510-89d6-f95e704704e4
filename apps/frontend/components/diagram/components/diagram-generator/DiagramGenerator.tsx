import React, { useEffect, useRef } from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { flat } from 'radash';

import { Box, Button, Modal, UnstyledButton } from '@mantine/core';
import { useClickOutside } from '@mantine/hooks';

import { DiagramGeneratorComponents } from './DiagramGenerator.Components';
import { DiagramGeneratorCompatibleProducts } from './DiagramGenerator.CompatibleProducts';

import { TourService } from 'components/diagram/services/TourService';
import { RouterService } from 'services/RouterService';
import { DiagramService } from 'components/diagram/services';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { useDiagramEmpty } from 'components/diagram/hooks/use-diagram-empty';
import { useCurrentProject } from 'hooks/use-current-project';
import { useInitialized } from 'components/diagram/hooks';
import { useProductsByIds } from 'hooks/use-products-by-ids';

import { DiagramGenerators, generatorState } from 'components/diagram/state/generator';

import cx from './DiagramGenerator.module.css';
import { IoGridOutline, IoHelpCircleOutline, IoSearchCircleOutline } from 'react-icons/io5';

const DiagramGenerator = () => {
    const { generator } = useSnapshot(generatorState);

    const initialized = useInitialized();
    const isEmptyDiagram = useDiagramEmpty();

    const project = useCurrentProject();

    const { components: referenceProducts, isLoading } = useProductsByIds(project?.template.components ?? []);

    const compatibleProductIds = flat(
        referenceProducts
            .filter(({ compatibleWith }) => !!compatibleWith)
            .map(({ compatibleWith }) => compatibleWith ?? []) ?? [],
    );
    const { components: compatibleProducts, isLoading: isLoadingCompatible } = useProductsByIds(compatibleProductIds);

    // Show compatible products generator if there are reference products
    useEffect(() => {
        if (!initialized) return;
        if (!isEmptyDiagram) return;
        if (generatorState.generator) return;

        if (referenceProducts.length) {
            generatorState.generator = DiagramGenerators.COMPATIBLE_PRODUCTS;
        }
    }, [initialized, isEmptyDiagram, referenceProducts.length]);

    const referenceProductsAdded = useRef(false);

    // If there is only one related product, add it to the diagram
    useEffect(() => {
        if (!isEmptyDiagram) return;
        if (referenceProductsAdded.current) return;
        if (isLoading || isLoadingCompatible) return;

        const allProducts = [...referenceProducts, ...compatibleProducts];

        if (allProducts.length === 1) {
            DiagramService.createDiagramFromComponents(allProducts);
            DiagramSyncService.save();

            referenceProductsAdded.current = true;
            generatorState.generator = DiagramGenerators.EMPTY;
        }
    }, [referenceProducts.length, compatibleProducts.length, isLoading, isLoadingCompatible]);

    const clickOutsideRef = useClickOutside(() => {
        if (!generator) {
            generatorState.generator = DiagramGenerators.EMPTY;
        }
    });

    if (isLoading || isLoadingCompatible) return null;

    return (
        <React.Fragment>
            {isEmptyDiagram && (
                <Box className={cx.menu} data-generator={generator} ref={clickOutsideRef}>
                    <h2 className={cx.menuTitle}>Get started</h2>
                    <Box className={cx.menuButtons}>
                        {[
                            {
                                icon: <IoSearchCircleOutline />,
                                text: 'Browse reference designs',
                                action: () => {
                                    RouterService.push('/designs').then();
                                },
                            },
                            {
                                icon: <IoHelpCircleOutline />,
                                text: 'Follow the Getting Started tour',
                                generator: DiagramGenerators.EMPTY,
                                action: () => {
                                    TourService.start();
                                },
                            },
                            {
                                icon: <IoGridOutline />,
                                text: 'Select components to start from',
                                generator: DiagramGenerators.COMPONENTS,
                            },
                        ].map((action) => (
                            <UnstyledButton
                                className={cx.diagramGeneratorButton}
                                onClick={() => {
                                    if (action.action) {
                                        action.action();
                                    }

                                    if (action.generator) {
                                        generatorState.generator = action.generator;
                                    }
                                }}
                                key={action.text}
                            >
                                {action.icon}
                                {action.text}
                            </UnstyledButton>
                        ))}
                    </Box>
                    <Button
                        mt="xs"
                        mx="auto"
                        size="xs"
                        display="block"
                        variant="transparent"
                        onClick={() => {
                            generatorState.generator = DiagramGenerators.EMPTY;
                        }}
                    >
                        Start from an empty canvas
                    </Button>
                </Box>
            )}
            <Modal
                classNames={{
                    root: cx.components,
                    overlay: cx.componentsOverlay,
                }}
                size={500}
                padding="xl"
                opened={generator === DiagramGenerators.COMPONENTS}
                onClose={() => {
                    generatorState.generator = null;
                }}
                withCloseButton={false}
                centered
            >
                <DiagramGeneratorComponents />
            </Modal>
            <Modal
                size="xl"
                padding="xl"
                opened={generator === DiagramGenerators.COMPATIBLE_PRODUCTS}
                onClose={() => {
                    generatorState.generator = null;
                }}
                withCloseButton={false}
                centered
            >
                <DiagramGeneratorCompatibleProducts
                    referenceProducts={referenceProducts}
                    compatibleProducts={compatibleProducts}
                />
            </Modal>
            <Modal
                classNames={{}}
                size="xl"
                padding="xl"
                opened={generator === DiagramGenerators.DESIGNS}
                onClose={() => {
                    generatorState.generator = null;
                }}
                withCloseButton={false}
                centered
            >
                Designs
            </Modal>
        </React.Fragment>
    );
};

export { DiagramGenerator };
