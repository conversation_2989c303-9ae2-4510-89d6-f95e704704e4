.root {
    --input-size: 40px;
    --input-border-radius: calc(var(--input-size) / 2);

    :global([data-size='lg'] .mantine-Input-input),
    :global([data-size='lg'] .mantine-Input-section) {
        --input-size: 48px;

        --input-border-radius: calc(var(--input-size) / 2);
        --input-font-size: max(16px, var(--mantine-font-size-md));

        @media (min-width: var(--mantine-breakpoint-sm)) {
            --input-size: 60px;
        }
    }
}

.input {
/* increasing specificity */
    &.input {
        overflow: hidden;

        border-radius: var(--input-border-radius);

        transition: border-radius 150ms;

        padding-right: 70px !important; /*  search button */

        &[data-autocomplete-open='true'] {
            border-radius: var(--input-border-radius) var(--input-border-radius) 0 0 !important;
        }

        @media (min-width: var(--mantine-breakpoint-sm)) {
            padding-right: 120px !important; /*  search button */

            [data-size='lg'] & {
                padding-top: var(--mantine-spacing-md);
                padding-bottom: var(--mantine-spacing-md);
                padding-left: 48px;
                padding-right: 160px !important; /*  search button */
            }
        }
    }
}

.reset {
    display: flex;

    &:hover {
        color: var(--mantine-color-gray-8);
    }
}

.submit {
    border-radius: var(--input-border-radius);
}

.section {
    padding-right: calc(5 * 0.0625rem * 1);

    height: var(--input-size);

    top: 0;

    &[data-position='left'] {
        left: 6px;
    }
}
