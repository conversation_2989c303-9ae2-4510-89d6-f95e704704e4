columnSuffix: null
handle: vizy
instructions: null
name: Vizy
searchable: false
settings:
  availableTransforms: ''
  availableVolumes: '*'
  blockTypeBehaviour: click
  configSelectionMode: choose
  defaultTransform: ''
  defaultUploadLocationSource: 'volume:a66f376e-928d-4aa9-8947-c44a937f0061' # Images
  editorMode: combined
  fieldData:
    -
      __assoc__:
        -
          - id
          - group-V3sGssBJn8
        -
          - name
          - General
        -
          - blockTypes
          -
            -
              __assoc__:
                0:
                  - id
                  - type-yR44qFgeCf
                1:
                  - name
                  - Spacer
                2:
                  - handle
                  - spacer
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - 'Arrows Up Down'
                      -
                        - value
                        - arrows-up-down-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 995dd2b8-6bb3-42e7-a210-3a33a935335a
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - c7e9d7f9-ec38-4641-96ac-491bf284e454
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 627d8286-9228-4cfe-b287-55f8ee8dd490]
                                      14: [fieldUid, 429d19e7-8383-4794-bfaa-44ba0a5ff937] # Spacer
            -
              __assoc__:
                0:
                  - id
                  - type-9Fu35aRZaw
                1:
                  - name
                  - Separator
                2:
                  - handle
                  - separator
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Slash
                      -
                        - value
                        - slash-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 6034dfc3-9d7c-44aa-8589-b26d5b50dfef
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - d1ee1c15-6592-41f8-a50a-3f367ed46d29
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, e44d0f65-c1a5-4304-82c9-beb9e9be8f31]
                                      14: [fieldUid, 9bf8ed2e-e5ac-470b-b56f-ed8f4560dbab] # Separator Size
    -
      __assoc__:
        -
          - id
          - group-QOM24iDgj2
        -
          - name
          - Text
        -
          - blockTypes
          -
            -
              __assoc__:
                0:
                  - id
                  - type-TjgnKtG2nR
                1:
                  - name
                  - Title
                2:
                  - handle
                  - title
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Font
                      -
                        - value
                        - font-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 85c40895-9374-44ee-ab9a-4f4101c27f58
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 44476932-b3ea-451b-b29f-6f4bd0cc27dd
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, internaltitle]
                                      2: [label, Title]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-10T09:51:04+00:00']
                                      11: [uid, eb8411cf-ab76-45fa-b62e-74b9b374e9ba]
                                      14: [fieldUid, 920a3883-cad9-4e7b-b063-a7fe2220b8d5] # CKE Bold Only
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, description]
                                      2: [label, Description]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-10T09:51:04+00:00']
                                      11: [uid, cb326147-d941-4f96-ac1e-e551a686eaa6]
                                      14: [fieldUid, 17c422c2-ec7a-4c12-9b38-129f63d2e0a8] # CKE Full
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, button]
                                      2: [label, Button]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, bd027b8e-43aa-4ab1-974e-d3fca98b1cd1]
                                      14: [fieldUid, 2b7a774b-249d-42ed-933b-028f455e91a9] # Hyper
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-10T11:24:58+00:00']
                                      11: [uid, 73c8dcc0-fad8-4330-91d8-6b493336e183]
                                      14: [fieldUid, f9954fb7-bf89-48ac-88a0-e6ad364c1cd4] # Alignment
            -
              __assoc__:
                0:
                  - id
                  - type-GPwQHMCdhr
                1:
                  - name
                  - 'Title with Text'
                2:
                  - handle
                  - titleWithText
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Font
                      -
                        - value
                        - font-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - c44b211f-2159-4515-9c07-513d86229513
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 877dd172-6833-47db-8b28-2d9dd642dfa9
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, internaltitle]
                                      2: [label, Title]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-10T07:44:40+00:00']
                                      11: [uid, 7804c3f2-cb1f-4f4b-89b0-b2d021fc0bd8]
                                      14: [fieldUid, 920a3883-cad9-4e7b-b063-a7fe2220b8d5] # CKE Bold Only
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, textLeft]
                                      2: [label, 'Text Left']
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 3f7ae557-b782-4278-abee-0de05be65a02]
                                      14: [fieldUid, 17c422c2-ec7a-4c12-9b38-129f63d2e0a8] # CKE Full
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, textRight]
                                      2: [label, 'Text Right']
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 81a54e79-51f2-46bd-b918-f674db135fc0]
                                      14: [fieldUid, 17c422c2-ec7a-4c12-9b38-129f63d2e0a8] # CKE Full
            -
              __assoc__:
                0:
                  - id
                  - type-CUOVW2BxeO
                1:
                  - name
                  - Quote
                2:
                  - handle
                  - quote
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - 'Quote Left'
                      -
                        - value
                        - quote-left-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - ad103a85-1df7-45e0-8e40-bc6ddbc1b0f6
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 0266cce6-7721-40f7-b029-e5f8f121f31f
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, quote]
                                      2: [label, __blank__]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-10T15:54:34+00:00']
                                      11: [uid, b0536ea7-8ef7-424b-903b-ea8ef53eaaef]
                                      14: [fieldUid, 17c422c2-ec7a-4c12-9b38-129f63d2e0a8] # CKE Full
    -
      __assoc__:
        -
          - id
          - group-aH8mpzPxLt
        -
          - name
          - Media
        -
          - blockTypes
          -
            -
              __assoc__:
                0:
                  - id
                  - type-tVy0SBsoXJ
                1:
                  - name
                  - Image
                2:
                  - handle
                  - image
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Image
                      -
                        - value
                        - image-regular
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 40fe0026-**************-e988a7c38303
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 902cba40-3e8e-48d8-9574-a08ecf38f929
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-10T08:52:25+00:00']
                                      11: [uid, e6d4a3a9-0941-4b14-946b-0f7eff17ac33]
                                      14: [fieldUid, eb431632-f463-45e8-b398-3576f4675d05] # Image
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 25]
                                      10: [dateAdded, '2025-05-10T08:52:25+00:00']
                                      11: [uid, 1e16d9ef-c88d-44b2-a622-02b354c43b03]
                                      14: [fieldUid, daac5c27-6b46-46e7-9814-18f04179e89c] # Fullscreen
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, limitHeight]
                                      2: [label, 'Limit Height']
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 25]
                                      11: [uid, 8f94ddce-8f3d-4666-ae6a-e8dc518f5070]
                                      14: [fieldUid, 2f61fbd5-9edc-49e1-863c-5384a2fe577b] # Lightswitch
            -
              __assoc__:
                0:
                  - id
                  - type-GUHLd3b6UF
                1:
                  - name
                  - Images
                2:
                  - handle
                  - images
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Images
                      -
                        - value
                        - images-regular
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 3090830f-d151-43c4-a770-8d7343a760e9
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 9c2907ab-854a-4284-a107-7edba25a2c28
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 2a8bf790-ee1e-44aa-8c08-2095d8bab662]
                                      14: [fieldUid, 80a3dc04-089f-49e3-8bf0-e9b396046145] # Images
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 6c495d80-b5d8-4e51-ba30-6b426774a3d1]
                                      14: [fieldUid, f9954fb7-bf89-48ac-88a0-e6ad364c1cd4] # Alignment
            -
              __assoc__:
                0:
                  - id
                  - type-5OE0FNmla1
                1:
                  - name
                  - Video
                2:
                  - handle
                  - video
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Video
                      -
                        - value
                        - video-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 801d00b5-9774-4397-991e-37d873c53310
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - c2547b18-fb74-4130-bde4-f84b5382a1f6
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, a1084b40-f50b-45b4-bff7-978bf01472e3]
                                      14: [fieldUid, 35daba9d-46d3-4ded-b2f6-ea79b8dbf7b6] # Video
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, a1d4e997-d581-4d41-bf19-3b91bc02c2cd]
                                      14: [fieldUid, daac5c27-6b46-46e7-9814-18f04179e89c] # Fullscreen
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, limitHeight]
                                      2: [label, 'Limit Height']
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, a3449814-1dfd-47c2-8feb-254560b2c641]
                                      14: [fieldUid, 2f61fbd5-9edc-49e1-863c-5384a2fe577b] # Lightswitch
    -
      __assoc__:
        -
          - id
          - group-83uAxWitse
        -
          - name
          - Teasers
        -
          - blockTypes
          -
            -
              __assoc__:
                0:
                  - id
                  - type-QkcRhNw0DG
                1:
                  - name
                  - Blogs
                2:
                  - handle
                  - blogs
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Grip
                      -
                        - value
                        - grip-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - b4e2c096-dbad-43e5-a986-eb5110afbda0
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 1e958572-30f8-4d01-8a41-22ca02d7123d
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, d8c86d98-a0b2-416d-9bf1-fdc85bc4f208]
                                      14: [fieldUid, c1da8b36-cec4-494d-9717-cb5f9dc172f4] # Blogs
            -
              __assoc__:
                0:
                  - id
                  - type-U1jiNgU7iL
                1:
                  - name
                  - Cases
                2:
                  - handle
                  - cases
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Grip
                      -
                        - value
                        - grip-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 45dc7a94-fc01-42a9-a6a1-49c512b87798
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 3c0e6d94-f11f-4655-a4ef-b68246f8af08
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, d67c4645-e6bc-40c0-a4be-dd0877239cc7]
                                      14: [fieldUid, b7c87572-3d07-434e-b0af-2c4e4fdb4323] # Cases
            -
              __assoc__:
                0:
                  - id
                  - type-LToMbXJta3
                1:
                  - name
                  - 'Case Categories'
                2:
                  - handle
                  - caseCategories
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Grip
                      -
                        - value
                        - grip-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 97477876-bd70-45e6-aaa3-43948923d3a1
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - e2074961-64e3-494c-94c0-f9308ad56b69
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, internaltitle]
                                      2: [label, Title]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 5f5a69ea-fb32-4807-9737-24b5c784254f]
                                      14: [fieldUid, 920a3883-cad9-4e7b-b063-a7fe2220b8d5] # CKE Bold Only
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 5c5809b8-b387-4d8c-ad27-9bc72f212da2]
                                      14: [fieldUid, 200efdee-d4a4-4bf6-b225-7011f37bb2a3] # Case Categories
            -
              __assoc__:
                0:
                  - id
                  - type-sTWNI519VR
                1:
                  - name
                  - Clients
                2:
                  - handle
                  - clients
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Grip
                      -
                        - value
                        - grip-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - ec77a2f7-ecb2-4de8-be1d-59a420392438
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 13037d27-acd2-4f0f-a238-19f6172e515c
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, c67fd5ba-c26e-4a0d-b03c-d0e3ab7d5a0f]
                                      14: [fieldUid, a3e0f760-bcf1-4193-8058-0a28512eba10] # Clients
            -
              __assoc__:
                0:
                  - id
                  - type-hguxar5Kto
                1:
                  - name
                  - Jobs
                2:
                  - handle
                  - jobs
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Apple
                      -
                        - value
                        - apple-brands
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - c54ff7e0-a141-449b-bf3a-cb9abeaabb5b
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 4806cc32-fe54-400b-bd87-c2426d14926f
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, internaltitle]
                                      2: [label, Title]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, a29aa2ac-fe16-42ac-8330-1ab5e8d10000]
                                      14: [fieldUid, 920a3883-cad9-4e7b-b063-a7fe2220b8d5] # CKE Bold Only
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, text]
                                      2: [label, Text]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, ae931090-d521-41f1-a032-2156d6ca2685]
                                      14: [fieldUid, 17c422c2-ec7a-4c12-9b38-129f63d2e0a8] # CKE Full
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, ac59a8e4-05a6-4fa0-a22c-7ac5d2fe092c]
                                      14: [fieldUid, ab09dbe7-5c2d-47df-b389-1ff107e5ea0c] # Jobs
            -
              __assoc__:
                0:
                  - id
                  - type-nSneLEj42v
                1:
                  - name
                  - Case
                2:
                  - handle
                  - case
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Square
                      -
                        - value
                        - square-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - c2341d11-f31d-463c-87d0-a7b53e06dde0
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - c07b3eee-4718-40bd-942e-618626b47fe7
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-26T12:57:12+00:00']
                                      11: [uid, efb6ccb0-3f75-4922-a83d-d0d0fd9e72c9]
                                      14: [fieldUid, 2c9e3547-2e81-40e0-816d-044f22b02c0c] # Case
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-26T12:57:12+00:00']
                                      11: [uid, f27e34d9-bee2-4e20-9082-cfd2e90d58e7]
                                      14: [fieldUid, 9b2454bb-d762-4602-8ddc-29027212d9e3] # Variant
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      1: [handle, swapped]
                                      2: [label, Swapped]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 2384f6be-8e0e-46a1-a0c0-2e790ff86f0c]
                                      14: [fieldUid, 2f61fbd5-9edc-49e1-863c-5384a2fe577b] # Lightswitch
    -
      __assoc__:
        -
          - id
          - group-LFCiViSyOs
        -
          - name
          - Specials
        -
          - blockTypes
          -
            -
              __assoc__:
                0:
                  - id
                  - type-eNTnznoGrJ
                1:
                  - name
                  - Contact
                2:
                  - handle
                  - contact
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Phone
                      -
                        - value
                        - phone-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 0d74e270-6628-420b-bc43-73ed2c900909
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              -
                                - name
                                - Content
                              -
                                - uid
                                - ba667b48-6668-44d5-a8a2-b0628eea2d82
            -
              __assoc__:
                0:
                  - id
                  - type-XUYiB3vSy9
                1:
                  - name
                  - FAQ
                2:
                  - handle
                  - faq
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - 'Circle Question'
                      -
                        - value
                        - circle-question-regular
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 5f3d73f6-8c08-4098-afb3-097d4ab17ffd
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 4ce1c08f-864b-413d-96a1-6868011e5bd0
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      2: [label, __blank__]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, 1da3545a-e885-408b-9ba3-2d5f2b641b39]
                                      14: [fieldUid, c042d165-7d32-48e6-bd81-1e4b1b65dc9b] # FAQ
            -
              __assoc__:
                0:
                  - id
                  - type-Z3UacBQ56R
                1:
                  - name
                  - Highlights
                2:
                  - handle
                  - highlights
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - Star
                      -
                        - value
                        - star-regular
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 9cef7bba-848d-4b5c-b6fc-cc4c20a1e60c
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - a79a932e-716b-41c8-a5b9-ff965c7011c0
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      2: [label, __blank__]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, ab7c56ee-ffff-4867-af35-e98e6d9570b4]
                                      14: [fieldUid, bbeb2e9e-fe5b-4540-bcce-2a66897cbd57] # Highlights
            -
              __assoc__:
                0:
                  - id
                  - type-0ABCKzmXHV
                1:
                  - name
                  - Properties
                2:
                  - handle
                  - properties
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - List
                      -
                        - value
                        - list-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 42199023-ee77-433e-afca-5dfde7af0cd3
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              0:
                                - name
                                - Content
                              1:
                                - uid
                                - 20200c3c-074b-464f-80c7-aae4de11648b
                              4:
                                - elements
                                -
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      11: [uid, b43665c3-306c-4fd5-b835-0b7c9450d938]
                                      14: [fieldUid, e4ef8e14-cef5-4818-94c7-91fcdf4d6b43] # Properties
                                  -
                                    __assoc__:
                                      0: [type, craft\fieldlayoutelements\CustomField]
                                      6: [required, false]
                                      7: [providesThumbs, false]
                                      8: [includeInCards, false]
                                      9: [width, 100]
                                      10: [dateAdded, '2025-05-17T06:52:59+00:00']
                                      11: [uid, e43de544-fc74-4fb1-affc-8297a870230d]
                                      14: [fieldUid, eb431632-f463-45e8-b398-3576f4675d05] # Image
            -
              __assoc__:
                0:
                  - id
                  - type-72DzaTCcpq
                1:
                  - name
                  - 'Google Maps'
                2:
                  - handle
                  - googleMaps
                3:
                  - icon
                  -
                    __assoc__:
                      -
                        - label
                        - 'Map Location Dot'
                      -
                        - value
                        - map-location-dot-solid
                5:
                  - enabled
                  - true
                6:
                  - layoutUid
                  - 35483039-b24c-44c4-b242-009da63080a9
                7:
                  - layoutConfig
                  -
                    __assoc__:
                      -
                        - tabs
                        -
                          -
                            __assoc__:
                              -
                                - name
                                - Content
                              -
                                - uid
                                - 5da0af86-9481-405a-aaed-178ea5a120f1
  initialRows: 7
  manualConfig: ''
  maxBlocks: null
  minBlocks: null
  pasteAsPlainText: false
  showUnpermittedFiles: false
  showUnpermittedVolumes: false
  trimEmptyParagraphs: true
  vizyConfig: ''
translationKeyFormat: null
translationMethod: none
type: verbb\vizy\fields\VizyField
