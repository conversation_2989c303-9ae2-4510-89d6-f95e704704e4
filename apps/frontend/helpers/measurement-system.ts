import { MeasurementSystem, LengthConverter } from '@repo/dcide-component-models';
import { FormatHelpers } from './formatters';

const MeasurementSystemHelpers = {
    formatLengthValue: (measurementSystem: MeasurementSystem, value: number | null | undefined) =>
        FormatHelpers.formatValue(value, new LengthConverter(measurementSystem)),

    formatLengthMeasurement: (measurementSystem: MeasurementSystem, measurement: any) =>
        FormatHelpers.formatValue(measurement.value, new LengthConverter(measurementSystem), measurement.unit),
};

export { MeasurementSystemHelpers };
