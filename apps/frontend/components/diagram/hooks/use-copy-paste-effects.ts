import { useWindowEvent } from '@mantine/hooks';
import { useHotkeys } from 'react-hotkeys-hook';

import { CopyPasteService } from 'components/diagram/services/CopyPasteService';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { useSelection } from 'components/diagram/hooks/selection/use-selection';

const useCopyPasteEffects = () => {
    const { selection } = useSelection();

    const copy = () => {
        CopyPasteService.copy();
    };

    const cut = () => {
        CopyPasteService.cut();
        DiagramSyncService.save();
    };

    const activatePastingMode = () => {
        CopyPasteService.activatePastingMode();
    };

    const deactivatePastingMode = () => {
        CopyPasteService.deactivatePastingMode();
    };

    useHotkeys(['mod+c'], copy, {
        enabled: !!selection.length,
        preventDefault: true,
    });

    useHotkeys(['mod+x'], cut, {
        enabled: !!selection.length,
        preventDefault: true,
    });

    useHotkeys(['mod+v'], activatePastingMode, {
        preventDefault: true,
    });

    useHotkeys(['esc'], deactivatePastingMode, {
        preventDefault: true,
    });

    useWindowEvent('blur', () => {
        CopyPasteService.deactivatePastingMode();
    });
};

export { useCopyPasteEffects };
