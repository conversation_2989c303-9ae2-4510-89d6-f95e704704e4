import { FC } from 'react';

import { UnstyledButton } from '@mantine/core';
import { IoClose } from 'react-icons/io5';

import cx from './InlineFilters.SectionButton.module.css';

export const InlineFiltersSectionRemove: FC<{ onRemove: () => void }> = ({ onRemove }) => {
    return (
        <UnstyledButton
            tabIndex={-1}
            classNames={cx}
            onClick={(e) => {
                e.stopPropagation();
                onRemove();
            }}
            data-section-remove
        >
            <IoClose />
        </UnstyledButton>
    );
};
