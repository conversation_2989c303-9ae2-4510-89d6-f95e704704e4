import React, { FC } from 'react';

import { SelectField, SelectFieldProps } from 'components/forms/fields/SelectField';

import { DuctToDuctClearance } from '@repo/dcide-component-models';

type DuctToDuctClearanceFieldProps = Omit<SelectFieldProps, 'data'> & {
    name: string;
};

export const DuctToDuctClearanceField: FC<DuctToDuctClearanceFieldProps> = ({ name, ...props }) => {
    return <SelectField name={name} {...props} data={DuctToDuctClearance.options} />;
};
