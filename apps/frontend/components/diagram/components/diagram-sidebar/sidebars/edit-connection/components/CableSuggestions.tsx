import React, { useEffect, FC, useState } from 'react';
import { Component, DiagramConnection } from '@repo/dcide-component-models';

import { Anchor, Box, ScrollArea, Stack } from '@mantine/core';
import { useElementSize } from '@mantine/hooks';

import { SidebarTab } from 'components/diagram/state/sidebar';

import { ComponentService } from 'services/ComponentService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { ConnectionHelpers } from 'components/diagram/helpers/ConnectionHelpers';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';
import { DiagramComponentOverviewHit } from 'components/component-overview/ComponentOverviewHit.Diagram';

import cx from './CableSuggestions.module.css';

const CableSuggestions: FC<{
    connection: DiagramConnection;
}> = ({ connection }) => {
    const [suggestions, setSuggestions] = useState<Component[]>([]);

    const { ref, width, height } = useElementSize();

    const defaultValues = ConnectionHelpers.generateDefaultValues(connection);

    useEffect(() => {
        ComponentService.search({
            limit: 3,
            ...defaultValues,
        }).then((response) => {
            if (response.docs) {
                setSuggestions(response.docs);
            }
        });
    }, [JSON.stringify(defaultValues)]);

    const openProductSearch = () => {
        SidebarService.setActiveTab(SidebarTab.CONNECT_CABLE);
    };

    if (!suggestions.length) {
        return null;
    }

    return (
        <React.Fragment>
            <Box style={{ width, height }} />
            <Box className={cx.section} ref={ref}>
                <DiagramSidebar.Divider />
                <DiagramSidebar.Section
                    title="Cable Suggestions"
                    rightSection={
                        suggestions.length && (
                            <Anchor component="button" onClick={openProductSearch} fz="sm">
                                view more suggestions
                            </Anchor>
                        )
                    }
                    collapsable={{
                        enabled: true,
                        key: 'diagram.suggested-cables',
                        initial: false,
                    }}
                >
                    <ScrollArea.Autosize mah={232} type="always">
                        <Stack gap="xs">
                            {suggestions.map((hit) => (
                                <div>
                                    <DiagramComponentOverviewHit key={hit.id} component={hit} />
                                </div>
                            ))}
                        </Stack>
                    </ScrollArea.Autosize>
                </DiagramSidebar.Section>
            </Box>
        </React.Fragment>
    );
};

export { CableSuggestions };
