import { z } from 'zod';

import type { Endpoint } from 'payload';
import { User } from '@/payload-types';

import { getCollection, getId, handleEndpointError } from '../../../helpers';

import { generateLoginCode } from '../../users/helpers';

import { EmailService } from '@/services/EmailService';
import { TeamService } from '@/services/TeamService';

import { JsonSuccessResponse } from '@/responses/JsonSuccessResponse';
import { BadRequestError } from '@/responses/errors';

import { config } from '@/config';

const addSupportUser: Endpoint = {
    path: '/:id/add-support-user',
    method: 'post',
    handler: async (request) => {
        try {
            const payload = request.payload;

            const body = await request.json!();

            const { email } = z
                .object({
                    email: z.string().email().toLowerCase(),
                })
                .parse(body);

            const company = await payload.findByID({
                collection: 'manufacturers',
                id: request.routeParams?.id as string,
                depth: 1,
            });

            const team = await getCollection('teams', company.team);

            // Existing user?
            let isNewUser = false;
            let user: User = (
                await payload.find({
                    collection: 'users',
                    where: {
                        email: {
                            equals: email,
                        },
                    },
                    depth: 0,
                    limit: 1,
                })
            ).docs[0];

            // New user
            if (!user) {
                isNewUser = true;
                user = await payload.create({
                    collection: 'users',
                    data: {
                        email,
                        // Pass team to bypass the createNewUserTeam hook
                        team: team.id,
                    },
                    req: request,
                });
            }

            await TeamService.addUser(team, user);

            const userInCompany = company.users?.some((companyUser) => getId(companyUser) === getId(user));

            if (userInCompany) {
                throw new BadRequestError('User is already a support user for this company');
            }

            // Update company
            await payload.update({
                collection: 'manufacturers',
                id: company.id,
                data: {
                    users: [...(company.users?.map((userOrId) => getId(userOrId)) ?? []), getId(user)],
                },
                req: request,
            });

            const { token } = await generateLoginCode({
                payload,
                tokenPayload: {
                    ...body,
                    email,
                },
                user,
            });

            EmailService.send({
                subject: `You've been added as a support user for ${company.name} on DCIDE`,
                to: email,
                data: EmailService.copy.companySupportUser({
                    company,
                    showIntro: isNewUser,
                    buttonUrl: `${config.urls.frontend}/token/${token}`,
                }),
            });

            return JsonSuccessResponse();
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};

export { addSupportUser };
