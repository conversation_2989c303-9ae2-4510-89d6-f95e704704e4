import React, { <PERSON> } from 'react';

import { keys } from 'radash';

import { Flex, SimpleGrid, Stack, TextInput } from '@mantine/core';

import { DiagramConnection, UserFeatureFlags } from '@repo/dcide-component-models';

import { AutoSave } from 'components/forms/AutoSave';
import { Calculate } from 'components/component-fields/Calculate';
import { ConductorMaterialField } from 'components/component-fields/ConductorMaterialField';
import { ConnectionCoresField } from 'components/component-fields/ConnectionCores';
import { CurrentField } from 'components/component-fields/CurrentField';
import { EditConnectionRatedCurrent } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/components/EditConnectionRatedCurrent';
import { EditConnectionRatedVoltage } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/components/EditConnectionRatedVoltage';
import { EditConnectionVoltageField } from './EditConnectionVoltageField';
import { EditConnectionVoltageType } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/components/EditConnectionVoltageType';
import { Form } from 'components/forms/Form';
import { InsulationMaterialField } from 'components/component-fields/InsulationMaterialField';
import { LinesField } from 'components/component-fields/LinesFields';
import { ResistanceField } from 'components/component-fields/ResistanceField';
import { SearchCablesButton } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/components/SearchCablesButton';
import { Values } from 'components/component-fields/Values';
import { WireSizeFeedback } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/components/WireSizeFeedback';
import { WireSizeField } from 'components/component-fields/WireSizeField';

import { ConnectionHelpers } from 'components/diagram/helpers/ConnectionHelpers';

import { useCurrentUserFlag } from 'hooks/use-current-user-flag';
import { useDefaultMeasurementSystem } from 'hooks/use-default-measurement-system';
import { useEditConnectionContext } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/context';

import cx from './EditConnectionForm.module.scss';

const EditConnectionForm: FC<{
    handleSubmit: (values: DiagramConnection) => void;
}> = ({ handleSubmit }) => {
    const { connection, voltageType } = useEditConnectionContext();
    const userHasWireSizing = useCurrentUserFlag(UserFeatureFlags.WIRE_SIZING);

    const measurementSystem = useDefaultMeasurementSystem();

    const linkedToCable = !!connection.cableId;

    const maxNbLines = connection.numberOfConductors || Infinity;
    const enablePE = connection.hasPE ?? true;

    const numberOfConductors = ConnectionHelpers.getNumberOfConductors(connection, voltageType);

    return (
        <div className={cx.root}>
            <Form<DiagramConnection> defaultValues={connection} onSubmit={handleSubmit} key={connection?.id}>
                <AutoSave
                    instant={[
                        ...keys(connection?.lines ?? {}).map((key) => `lines.${key}`),
                        'conductorTemperature',
                        'installationMethod',
                    ]}
                />
                <Stack gap="xs">
                    <SearchCablesButton />

                    <Flex gap="xs">
                        <EditConnectionVoltageType />
                        <LinesField voltageType={voltageType} maxNbLines={maxNbLines} enablePE={enablePE} />
                    </Flex>

                    <EditConnectionVoltageField />
                    {userHasWireSizing && <EditConnectionRatedVoltage />}

                    <CurrentField name="current" label="Current" fields={['max']} size="sm" />
                    {userHasWireSizing && <EditConnectionRatedCurrent />}

                    <SimpleGrid cols={2} spacing={4}>
                        <ConductorMaterialField
                            name="conductorMaterial"
                            label="Conductor Material"
                            size="sm"
                            disabled={linkedToCable}
                        />
                        <InsulationMaterialField
                            name="insulationMaterial"
                            label="Insulation Material"
                            size="sm"
                            disabled={linkedToCable}
                        />
                    </SimpleGrid>

                    <SimpleGrid cols={2} spacing={4}>
                        <ConnectionCoresField
                            name="cores"
                            label="Single-core/Multi-core"
                            size="sm"
                            disabled={linkedToCable}
                        />
                        <TextInput value={numberOfConductors} label="Number of Conductors" size="sm" disabled />
                    </SimpleGrid>

                    <SimpleGrid cols={2} spacing={4} verticalSpacing="xs">
                        <WireSizeField
                            name="wireSize"
                            label="Wire Size"
                            format={measurementSystem}
                            size="sm"
                            disabled={linkedToCable}
                        />

                        <Flex align="end" gap={4}>
                            <ResistanceField
                                name="resistance"
                                label="Resistance"
                                size="sm"
                                style={{ flexGrow: 1 }}
                                disabled={linkedToCable}
                            />
                            <Values>
                                {({ conductorMaterial, wireSize, length, resistancePerLength }) =>
                                    linkedToCable && length.value && resistancePerLength.value ? (
                                        <Calculate.Auto
                                            name="resistance"
                                            dependencies={['length', 'resistancePerLength']}
                                            calculate={(length, resistancePerLength) => {
                                                return ConnectionHelpers.calculateResistanceFromResistancePerLength(
                                                    length,
                                                    resistancePerLength,
                                                );
                                            }}
                                        />
                                    ) : conductorMaterial && wireSize ? (
                                        <Calculate
                                            name="resistance"
                                            dependencies={['length']}
                                            calculate={(length) => {
                                                return ConnectionHelpers.calculateResistance(
                                                    conductorMaterial,
                                                    +wireSize,
                                                    length,
                                                );
                                            }}
                                            size="md"
                                        />
                                    ) : null
                                }
                            </Values>
                        </Flex>
                    </SimpleGrid>

                    {userHasWireSizing && <WireSizeFeedback />}
                </Stack>
            </Form>
        </div>
    );
};

export { EditConnectionForm };
