.root {
    padding: var(--mantine-spacing-xl);

    gap: var(--mantine-spacing-xl);

    @media (max-width: var(--mantine-breakpoint-xs)) {
        padding: var(--mantine-spacing-md);
    }
}

.home {
    > [data-section] {
        &:nth-of-type(even) {
            --section-padding: var(--mantine-spacing-xl);

            position: relative;

            margin-left: calc(var(--section-padding) * -1);
            margin-right: calc(var(--section-padding) * -1);

            padding: var(--section-padding);

            &:first-child {
                margin-top: calc(var(--section-padding) * -1);
            }

            &:last-child {
                margin-bottom: calc(var(--section-padding) * -1);
            }

            @media (max-width: var(--mantine-breakpoint-xs)) {
                --section-padding: var(--mantine-spacing-md);
            }

            &::before {
                content: "";

                position: absolute;
                top: 0;
                left: 0;

                width: 100%;
                height: 100%;

                background: var(--mantine-color-brand-5);

                opacity: 0.1;
            }
        }
    }

    [data-highlighted-info] {
        padding: 0;
        background-color: transparent;

        [data-dashboard-title] {
            font-weight: 400 !important;
            font-size: var(--mantine-font-size-md) !important;
        }

        [data-highlighted-info-item] {
            border: 1px solid var(--mantine-color-gray-2);
        }

        [data-highlighted-info-actions] {
            display: none;
        }
    }

    @media (min-width: var(--mantine-breakpoint-md)) {
        [data-empty-message] {
            display: none;
        }
    }
}
