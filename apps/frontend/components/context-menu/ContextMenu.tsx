import React, { FC, ReactNode } from 'react';

import { Menu, UnstyledButton } from '@mantine/core';
import type { MenuProps } from '@mantine/core';

import classes from './ContextMenu.module.css';

const ContextMenu: FC<{
    position?: MenuProps['position'];
    target?: ReactNode;
    targetWidth?: number;
    targetIcon?: ReactNode;
    targetIconWidth?: number;
    children: ReactNode;
}> & {
    Divider: typeof Menu.Divider;
    Item: typeof Menu.Item;
    Label: typeof Menu.Label;
} = ({ position = 'bottom-end', target, targetIcon, targetWidth = 20, targetIconWidth = 20, children }) => {
    return (
        <Menu
            classNames={{
                dropdown: classes.dropdown,
                item: classes.item,
                itemSection: classes.itemSection,
            }}
            position={position}
            arrowPosition="center"
            withinPortal={false}
            withArrow
        >
            <Menu.Target>
                {target ? (
                    target
                ) : (
                    <UnstyledButton
                        className={classes.target}
                        style={{
                            '--target-width': `${targetWidth}px`,
                            '--target-icon-width': `${targetIconWidth}px`,
                        }}
                    >
                        {targetIcon}
                    </UnstyledButton>
                )}
            </Menu.Target>
            <Menu.Dropdown>{children}</Menu.Dropdown>
        </Menu>
    );
};

ContextMenu.Divider = Menu.Divider;
ContextMenu.Item = Menu.Item;
ContextMenu.Label = Menu.Label;

export { ContextMenu };
