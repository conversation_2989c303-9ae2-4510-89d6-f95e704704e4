import React, { useState, FC } from 'react';

import { Box, Popover, UnstyledButton } from '@mantine/core';

import { DiagramLegend } from 'components/diagram/components/diagram-legend/DiagramLegend';

import { useVoltageClasses } from 'components/diagram/hooks/use-voltage-classes';

import cx from 'components/diagram/components/diagram-badges/DiagramBadges.module.css';

const DiagramLegendControl: FC = () => {
    const [opened, setOpened] = useState(false);
    const voltageClasses = useVoltageClasses();

    const colors: string[] = [];

    const DCLength = voltageClasses.DC.length;
    colors.push(voltageClasses.DC[DCLength - 1]?.color);
    colors.push(voltageClasses.DC[DCLength - 2]?.color);

    const ACLength = voltageClasses.AC.length;
    colors.push(voltageClasses.AC[ACLength - 1]?.color);
    colors.push(voltageClasses.AC[ACLength - 2]?.color);

    return (
        <Popover opened={opened} position="top-end">
            <Popover.Target>
                <UnstyledButton
                    className={cx.badge}
                    onClick={() => {
                        setOpened((opened) => !opened);
                    }}
                >
                    <Box className={cx.badgeColors}>
                        {colors.filter(Boolean).map((color, index) => (
                            <Box
                                className={cx.badgeColor}
                                style={{ '--legend-item-color': color }}
                                key={`${color}${index}`}
                            />
                        ))}
                    </Box>
                    Legend
                </UnstyledButton>
            </Popover.Target>
            <Popover.Dropdown className={cx.tooltip}>
                <DiagramLegend
                    onRequestClose={() => {
                        setOpened(false);
                    }}
                />
            </Popover.Dropdown>
        </Popover>
    );
};

export { DiagramLegendControl };
