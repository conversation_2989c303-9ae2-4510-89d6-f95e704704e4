<div class="block {{ block.size == 'default' ? 'is-small' : 'is-large' }}">
    <div class="form">
        {% for form in block.form.all %}

            {% set submissions = craft.formie.submissions.form(form).payment_status('paid').all %}

            {% set count = 0 %}
            {% for submission in submissions %}
                {% if 'payment_status' in submission and submission.payment_status == 'paid' %}
                    {% set number = attribute(submission, block.limitationField).value ?: 0 %}
                    {% set count = count + number %}
                {% endif %}
                {% if 'paymentStatus' in submission and submission.paymentStatus == 'paid' %}
                    {% set number = attribute(submission, block.limitationField).value ?: 0 %}
                    {% set count = count + number %}
                {% endif %}
            {% endfor %}

            {% do form.setSettings({
                submitAction: 'url',
                submitActionUrl: siteUrl ~ 'actions/booking/payment/request-payment?submissionId={id}',
            }) %}

            {% if count >= block.limitationCount %}

                <div class="fui-alert fui-alert-error text">
                    {{ block.limitationMessage }}
                </div>

            {% else %}

                {{ craft.formie.renderForm(form, {
                    themeConfig: {
                        buttonWrapper: {
                            attributes: {
                                class: 'form-actions',
                            }
                        },
                        submitButton: {
                            attributes: {
                                class: 'button is-green is-large',
                            }
                        },
                    },
                }) }}

            {% endif %}

        {% endfor %}

    </div>
</div>