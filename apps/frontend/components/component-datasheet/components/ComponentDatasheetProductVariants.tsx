import React, { useEffect } from 'react';

import Link from 'next/link';

import { useScrollIntoView } from '@mantine/hooks';
import { Menu, ScrollArea, Text } from '@mantine/core';
import { IoCaretDownSharp } from 'react-icons/io5';

import { useComponentContext } from '../hooks/use-component-context';
import { useDatasheetMode } from 'components/datasheet';
import { useDatasheetProductSeriesComponents } from '../hooks/use-datasheet-product-series-components';

import { ComponentHelpers } from 'helpers/ComponentHelpers';

import cx from './ComponentDatasheetProductVariants.module.css';

const ComponentDatasheetProductVariants = () => {
    const { component } = useComponentContext();
    const mode = useDatasheetMode();

    const { components: variants } = useDatasheetProductSeriesComponents();

    if (!variants?.length) return null;

    return (
        <Menu offset={4} shadow="lg" position="bottom-start" trigger="click-hover">
            <Menu.Target>
                <Text span inherit className={cx.label}>
                    {mode === 'bulk'
                        ? `View all ${variants.length} variants`
                        : component.productIdentifier || component.name}
                    <IoCaretDownSharp size={8} />
                </Text>
            </Menu.Target>

            <Menu.Dropdown>
                <ComponentDatasheetProductVariantItems />
            </Menu.Dropdown>
        </Menu>
    );
};

const ComponentDatasheetProductVariantItems = () => {
    const { component } = useComponentContext();
    const { components: variants } = useDatasheetProductSeriesComponents();

    const { scrollIntoView, targetRef, scrollableRef } = useScrollIntoView<HTMLAnchorElement, HTMLDivElement>({
        duration: 0,
        offset: 20,
    });

    useEffect(() => {
        scrollIntoView();
    }, []);

    if (!variants?.length) return null;

    return (
        <ScrollArea.Autosize viewportRef={scrollableRef} mah={300} scrollbarSize={8}>
            {variants.map((variant) => (
                <Menu.Item
                    key={variant.id}
                    className={cx.item}
                    component={Link}
                    href={ComponentHelpers.urls.view(variant.id)}
                    data-active={component.id === variant.id}
                    {...(component.id === variant.id ? { ref: targetRef } : {})}
                >
                    {variant.productIdentifier || variant.name}
                </Menu.Item>
            ))}
        </ScrollArea.Autosize>
    );
};

export { ComponentDatasheetProductVariants };
