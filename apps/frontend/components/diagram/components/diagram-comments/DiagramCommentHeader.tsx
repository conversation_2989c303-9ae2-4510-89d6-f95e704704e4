import React, { FC } from 'react';
import { <PERSON>, Divider, <PERSON>u, <PERSON>ltip, UnstyledButton, useMantineTheme } from '@mantine/core';

import {
    IoChevronBackOutline,
    IoChevronForwardOutline,
    IoCloseOutline,
    IoEllipsisHorizontal,
    IoOpenOutline,
} from 'react-icons/io5';

import { TbLayoutSidebarRight } from 'react-icons/tb';

import { DiagramComment } from '@repo/dcide-component-models';

import { CommentService } from 'components/diagram/services/CommentService';
import { DiagramService } from 'components/diagram/services/DiagramService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { useDiagramComments, useDiagramMode } from 'components/diagram/hooks';
import { useActiveSidebarComment } from 'components/diagram/hooks/use-active-sidebar-comment';
import { ContextMenu } from 'components/context-menu/ContextMenu';
import { SidebarType } from 'components/diagram/state/sidebar';

import { DiagramCommentActions } from './DiagramCommentActions';

import classes from './DiagramCommentHeader.module.css';

const DiagramCommentHeader: FC<{
    comment: DiagramComment;
}> = ({ comment }) => {
    const { mode } = useDiagramMode();
    const theme = useMantineTheme();
    const comments = useDiagramComments();

    const activeSidebarComment = useActiveSidebarComment();
    const currentCommentIndex = comments.findIndex((needle) => needle.id === comment.id);

    const showArrows = comments.length > 1;

    const previous = () => {
        const previousIndex = currentCommentIndex - 1 < 0 ? comments.length - 1 : currentCommentIndex - 1;
        const previousComment = comments[previousIndex];

        if (!activeSidebarComment) {
            CommentService.activateSidebarComment(previousComment.id);
        }

        CommentService.activateCanvasComment(previousComment.id);
        CommentService.moveIntoView(previousComment);
    };

    const next = () => {
        const nextIndex = currentCommentIndex + 1 > comments.length - 1 ? 0 : currentCommentIndex + 1;
        const nextComment = comments[nextIndex];

        if (!activeSidebarComment) {
            CommentService.activateSidebarComment(nextComment.id);
        }

        CommentService.activateCanvasComment(nextComment.id);
        CommentService.moveIntoView(nextComment);
    };

    const handeOpenInSidebar = () => {
        DiagramService.setMode('comment');
        CommentService.activateSidebarComment(comment.id);
        SidebarService.open({ type: SidebarType.COMMENTS });
    };

    const handleClose = () => {
        CommentService.deactivateCanvasComment();
        CommentService.deactivateSidebarComment();
    };

    return (
        <React.Fragment>
            <Box style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Box style={{ display: 'flex', alignItems: 'center' }}>
                    {comment.id !== 'new' && (
                        <Tooltip label="Previous" disabled={!showArrows} withArrow>
                            <UnstyledButton
                                className={classes.action}
                                onClick={previous}
                                disabled={!showArrows}
                                data-compact
                            >
                                <IoChevronBackOutline />
                            </UnstyledButton>
                        </Tooltip>
                    )}
                    {comment.id !== 'new' && (
                        <Tooltip label="Next" disabled={!showArrows} withArrow>
                            <UnstyledButton
                                className={classes.action}
                                onClick={next}
                                disabled={!showArrows}
                                data-compact
                            >
                                <IoChevronForwardOutline />
                            </UnstyledButton>
                        </Tooltip>
                    )}
                    {comment.componentInstances.length > 0 && (
                        <DiagramCommentActions.LinkComponentInstance comment={comment}>
                            {({ linkedComponentInstance, linkedComponent }) =>
                                linkedComponentInstance ? (
                                    <Box className={classes.badge}>
                                        {linkedComponent
                                            ? linkedComponent.name
                                            : `#${linkedComponentInstance.designator}`}
                                    </Box>
                                ) : (
                                    <div />
                                )
                            }
                        </DiagramCommentActions.LinkComponentInstance>
                    )}
                </Box>
                <Box style={{ display: 'flex', gap: `calc(${theme.spacing.xs} / 2)` }}>
                    {comment.id !== 'new' && (
                        <ContextMenu
                            position="left-start"
                            targetIcon={<IoEllipsisHorizontal />}
                            targetWidth={20}
                            targetIconWidth={14}
                        >
                            <DiagramCommentActions.Subscribe comment={comment}>
                                {({ Icon, label, handle }) => (
                                    <Menu.Item leftSection={<Icon />} onClick={handle}>
                                        {label}
                                    </Menu.Item>
                                )}
                            </DiagramCommentActions.Subscribe>
                            <Menu.Divider />
                            <DiagramCommentActions.Resolve
                                comment={comment}
                                onUpdate={(comment) => {
                                    if (comment.status === 'resolved') {
                                        CommentService.deactivateCanvasComment();
                                    }
                                }}
                            >
                                {({ Icon, label, handle }) => (
                                    <Menu.Item leftSection={<Icon />} onClick={handle}>
                                        {label}
                                    </Menu.Item>
                                )}
                            </DiagramCommentActions.Resolve>
                            <DiagramCommentActions.Delete
                                comment={comment}
                                onUpdate={(comment) => {
                                    if (comment.status === 'deleted') {
                                        CommentService.deactivateCanvasComment();
                                    }
                                }}
                            >
                                {({ Icon, label, handle }) => (
                                    <Menu.Item leftSection={<Icon />} onClick={handle} color="red">
                                        {label}
                                    </Menu.Item>
                                )}
                            </DiagramCommentActions.Delete>
                            {(mode !== 'comment' || activeSidebarComment !== comment.id) && (
                                <React.Fragment>
                                    <Menu.Divider />
                                    <Menu.Item leftSection={<IoOpenOutline />} onClick={handeOpenInSidebar}>
                                        Open in sidebar
                                    </Menu.Item>
                                </React.Fragment>
                            )}
                        </ContextMenu>
                    )}
                    {(mode !== 'comment' || activeSidebarComment !== comment.id) && comment.id !== 'new' && (
                        <Tooltip label="Open in sidebar" withArrow>
                            <UnstyledButton className={classes.action} onClick={handeOpenInSidebar}>
                                <TbLayoutSidebarRight />
                            </UnstyledButton>
                        </Tooltip>
                    )}
                    <Tooltip label="Close" withArrow>
                        <UnstyledButton className={classes.action} onClick={handleClose}>
                            <IoCloseOutline />
                        </UnstyledButton>
                    </Tooltip>
                </Box>
            </Box>
            <Divider className={classes.divider} />
        </React.Fragment>
    );
};

export { DiagramCommentHeader };
