import { CollectionAfterChangeHook } from 'payload';
import { DiagramAIMessage } from '@/payload-types';

import { after } from 'next/server';

import { RealtimeService } from '@/services';
import { WebSocketEvent } from '@repo/dcide-component-models';

import { getId } from '@/helpers';

const broadcast: CollectionAfterChangeHook<DiagramAIMessage> = async ({ doc }) => {
    const execute = async () => {
        const diagramId = getId(doc.diagram);

        if (diagramId) {
            const channel = RealtimeService.getDiagramChannel(diagramId);
            await channel.publish(WebSocketEvent.DIAGRAM_REFRESH_AI_CONVERSATION, {
                diagramId,
            });
        }
    };

    after(execute);
};

export { broadcast };
