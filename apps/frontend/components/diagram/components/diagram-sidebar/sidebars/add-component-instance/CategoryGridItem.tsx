import React, { FC, forwardRef } from 'react';

import { ActionIcon, Box, Stack, Text, UnstyledButton } from '@mantine/core';
import { IoMove } from 'react-icons/io5';

import { useElementSize } from '@mantine/hooks';

import { UseDraggableArguments, useDraggable } from '@dnd-kit/core';

import { ComponentType } from '@repo/dcide-component-models';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import cx from './CategoryGridItem.module.css';

import { TouchDeviceType, getTouchDeviceType } from 'helpers/isTouchDevice';

type CategoryGridItemProps = {
    type: ComponentType;
    label?: string;
    subtitle?: string;
    withDragHandle?: boolean;
};

const CategoryGridItemContent: FC<CategoryGridItemProps> = ({ type, label, subtitle }) => {
    const hasLabel = Boolean(label || subtitle);
    const iconSize = hasLabel ? '25%' : '40%';

    return (
        <>
            <div style={{ width: iconSize, height: iconSize }}>
                <ComponentIcon type={type} />
            </div>
            {hasLabel && (
                <Stack gap={4}>
                    {label && (
                        <Text size="xs" c="gray.9" style={{ textAlign: 'center' }}>
                            {label}
                        </Text>
                    )}

                    {subtitle && (
                        <Text size="xs" c="dimmed" style={{ textAlign: 'center' }}>
                            {subtitle}
                        </Text>
                    )}
                </Stack>
            )}
        </>
    );
};

const CategoryGridItem = forwardRef<
    HTMLDivElement,
    CategoryGridItemProps & {
        children?: React.ReactNode;
    }
>(({ children, ...props }, ref) => {
    return (
        <Box className={cx.item} ref={ref}>
            <CategoryGridItemContent {...props} />
            {children}
        </Box>
    );
});
CategoryGridItem.displayName = 'CategoryGridItem';

const CategoryGridButton: FC<CategoryGridItemProps & { onClick: () => void }> = ({ onClick, ...props }) => {
    return (
        <UnstyledButton className={[cx.item, cx.hover].join(' ')} onClick={onClick}>
            <CategoryGridItemContent {...props} />
        </UnstyledButton>
    );
};

const DraggableCategoryGridItem: FC<
    CategoryGridItemProps & {
        dragOptions: UseDraggableArguments;
    }
> = ({ type, label, withDragHandle = true, dragOptions }) => {
    const touchDeviceType = getTouchDeviceType();
    const showDragHandle = withDragHandle && touchDeviceType !== TouchDeviceType.NOT_TOUCH_DEVICE;
    const isTabletDevice = touchDeviceType === TouchDeviceType.TABLET;

    const { ref: wrapper, width, height } = useElementSize();

    const { attributes, listeners, setNodeRef, transform, setActivatorNodeRef } = useDraggable(dragOptions);

    const wrapperBoundingClientRect = wrapper.current?.getBoundingClientRect();

    return (
        <CategoryGridItem type={type} label={label} ref={wrapper}>
            <UnstyledButton
                component="div"
                className={cx.item}
                style={{
                    position: transform ? 'fixed' : 'absolute',
                    left: transform ? wrapperBoundingClientRect?.left - 1 : undefined,
                    top: transform ? wrapperBoundingClientRect?.top - 1 : undefined,

                    zIndex: transform ? 9999 : undefined,

                    display: width && height ? 'flex' : 'none',

                    width: width + 2, // 2 = border width
                    height: height + 2, // 2 = border width

                    transform: transform ? `translate(${transform.x}px, ${transform.y}px)` : 'none',
                }}
                data-interactive={!isTabletDevice}
                {...attributes}
                {...(isTabletDevice ? {} : listeners)}
                ref={setNodeRef}
            >
                {showDragHandle && (
                    <ActionIcon
                        className={cx.dragHandle}
                        variant="light"
                        bg="gray.0"
                        c="dimmed"
                        size="lg"
                        ref={setActivatorNodeRef}
                        {...(isTabletDevice ? listeners : {})}
                    >
                        <IoMove />
                    </ActionIcon>
                )}

                <CategoryGridItemContent type={type} label={label} />
            </UnstyledButton>
        </CategoryGridItem>
    );
};

export { CategoryGridItem, DraggableCategoryGridItem, CategoryGridButton };
