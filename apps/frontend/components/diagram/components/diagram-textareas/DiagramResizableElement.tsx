import React, { useEffect, useState, useRef, FC, ReactNode } from 'react';

import { Resizable, ResizableProps } from 'react-resizable';
import { useDraggable } from '@dnd-kit/core';

import { Box } from '@mantine/core';

import { useSelection } from 'components/diagram/hooks/selection/use-selection';
import { SelectionService } from 'components/diagram/services/SelectionService';
import { ViewportService } from 'components/diagram/services';

import { getDragAndDropTranslate } from 'components/diagram/helpers';

type ResizableElement = {
    position: {
        x: number;
        y: number;
    };
    dimensions: {
        width: number;
        height: number;
    };
};

import cx from './DiagramResizableElement.module.css';
import { Z_INDEX } from 'components/diagram/diagram-z-index';

import { usePermission } from 'hooks/use-permission';
import { PermissionDiagramElements } from '@repo/dcide-component-models';
import { useDiagramMode } from 'components/diagram/hooks';
import { DraggingType } from 'types/DraggingType';

import { useCopyPaste } from 'components/diagram/hooks/use-copy-paste';
import { CELL_PADDING } from 'components/diagram/diagram-dimensions';

const DiagramResizableElement: FC<{
    type: DraggingType.IMAGE | DraggingType.GROUP | DraggingType.TEXTAREA;
    element: ResizableElement & {
        id: string;
    };
    draggable?: boolean;
    onResizeStop: (data: ResizableElement) => void;
    lockAspectRatio?: boolean;
    resizeHandles?: ResizableProps['resizeHandles'];
    children: ((args: { isOnlySelected: boolean }) => any) | ReactNode;
}> = ({
    type,
    element,
    draggable = true,
    lockAspectRatio = false,
    onResizeStop,
    resizeHandles = ['se', 'sw', 'ne', 'nw', 'e', 'w', 'n', 's'],
    children,
}) => {
    useSelection();

    const { mode } = useDiagramMode();

    const [resizing, setResizing] = useState(false);

    const { pasting } = useCopyPaste();

    const [x, setX] = useState(element.position.x);
    const [y, setY] = useState(element.position.x);

    const [width, setWidth] = useState(element.dimensions.width);
    const [height, setHeight] = useState(element.dimensions.height);

    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const latestTransform = useRef<any>(null);

    const selectable = {
        id: element.id,
        type,
    };

    const isSelected = SelectionService.isSelected(selectable);
    const isOnlySelected = SelectionService.isOnlySelected(selectable);

    useEffect(() => {
        setX(element.position.x);
        setY(element.position.y);

        setWidth(element.dimensions.width);
        setHeight(element.dimensions.height);

        // clear the latest transform when the element is updated
        latestTransform.current = null;
    }, [element.position.x, element.position.y, element.dimensions.width, element.dimensions.height]);

    const handleResize: ResizableProps['onResize'] = (_event, data) => {
        if (!canEdit) return;

        const delta = {
            x: data.size.width - element.dimensions.width,
            y: data.size.height - element.dimensions.height,
        };

        if (data.handle.includes('n')) {
            setY(element.position.y - delta.y);
        }

        if (data.handle.includes('w')) {
            setX(element.position.x - delta.x);
        }

        setWidth(data.size.width);
        setHeight(data.size.height);

        setResizing(true);
    };

    const handleResizeStop = () => {
        onResizeStop({
            position: {
                x,
                y,
            },
            dimensions: {
                width,
                height,
            },
        });

        setResizing(false);
    };

    const { attributes, listeners, transform, setNodeRef, isDragging } = useDraggable({
        id: isSelected && !isOnlySelected ? 'selection' : `resizable-element-${element.id}`,
        data: {
            id: element.id,
            type,
            [type]: element,
        },
        disabled: !canEdit || !draggable,
    });

    if (transform) {
        // make sure our component stays in place while the move update is propagated
        latestTransform.current = transform;
    }

    return (
        <Resizable
            width={width}
            height={height}
            onResize={handleResize}
            onResizeStop={handleResizeStop}
            resizeHandles={isOnlySelected ? resizeHandles : []}
            handleSize={[CELL_PADDING, CELL_PADDING]}
            lockAspectRatio={lockAspectRatio}
            transformScale={ViewportService.transform.scale}
        >
            <Box
                className={cx.element}
                style={{
                    'zIndex': Z_INDEX.RESIZEABLE_ELEMENT,
                    '--element-left': x + 'px',
                    '--element-top': y + 'px',

                    '--element-width': width + 'px',
                    '--element-height': height + 'px',

                    'transform': !resizing ? getDragAndDropTranslate(latestTransform.current) : undefined,

                    'cursor': isDragging ? 'move' : canEdit ? 'pointer' : 'default',
                }}
                onClick={() => {
                    if (!canEdit) return;

                    SelectionService.selectOrToggle(selectable);
                }}
                data-selectable
                data-selectable-type={selectable.type}
                data-selectable-id={selectable.id}
                data-selected={isSelected}
                data-dragging={isDragging}
                data-pasting={pasting}
                data-editable={mode === 'edit'}
            >
                <Box {...attributes} {...listeners} ref={setNodeRef}>
                    {typeof children === 'function' ? children({ isOnlySelected }) : children}
                </Box>
            </Box>
        </Resizable>
    );
};

export { DiagramResizableElement };
