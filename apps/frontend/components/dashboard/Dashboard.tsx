import React, { <PERSON> } from 'react';
import dynamic from 'next/dynamic';

import { Box, Loader, ScrollArea, Stack } from '@mantine/core';

import { Page } from 'components/page';

import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentTeamCompanies } from 'hooks/use-current-team-companies';

import { DashboardTitle } from './components/Dashboard.Title';
import { DashboardProjects } from './components/Dashboard.Projects';
import { DashboardProducts } from './components/Dashboard.Products';
import { DashboardChecklist } from './components/Dashboard.Checklist';
import { DashboardCompanyProfile } from './components/Dashboard.Profile';
import { DashboardNotifications } from './components/Dashboard.Notifications';
import { DashboardEmptyProjects } from './components/Dashboard.EmptyProjects';
import { DashboardEmptyProducts } from './components/Dashboard.EmptyProducts';
import { DashboardHighlightedInfo } from './components/Dashboard.HighlightedInfo';
import { DashboardBreadcrumbActions } from './components/Dashboard.BreadcrumbActions';
import { DashboardCompanySignupInfo } from './components/Dashboard.CompanySignupInfo';
import { DashboardEmptyReferenceDesigns } from './components/Dashboard.EmptyReferenceDesigns';

// Fix Ably hydration issue
const LazyDashboardUsers = dynamic(() => import('./components/Dashboard.Users').then((x) => x.DashboardUsers), {
    ssr: false,
    loading: () => null,
});

import cx from './Dashboard.module.css';

const Dashboard: FC & {
    Title: typeof DashboardTitle;
    Checklist: typeof DashboardChecklist;
    Projects: typeof DashboardProjects;
    EmptyProjects: typeof DashboardEmptyProjects;
    EmptyReferenceDesigns: typeof DashboardEmptyReferenceDesigns;
    Products: typeof DashboardProducts;
    EmptyProducts: typeof DashboardEmptyProducts;
    EmptyViewedProducts: typeof DashboardEmptyProducts.Viewed;
    Users: typeof LazyDashboardUsers;
    Notifications: typeof DashboardNotifications;
    Profile: typeof DashboardCompanyProfile;
    CompanySignupInfo: typeof DashboardCompanySignupInfo;
    HighlightedInfo: typeof DashboardHighlightedInfo;
} = () => {
    const user = useCurrentUser();

    const { companies, isLoading: isLoadingCompanies } = useCurrentTeamCompanies();

    const hasOneCompany = companies.length === 1;
    const firstCompany = companies[0];

    return (
        <Page
            title="Dashboard"
            showBackground
            hideFooter
            breadcrumbs={{
                type: 'fullWidth',
                rightSection: (
                    <DashboardBreadcrumbActions hasCompany={user?.hasCompany} isManufacturer={user?.isManufacturer} />
                ),
            }}
        >
            <Box className={cx.root}>
                <Box className={cx.content}>
                    <ScrollArea scrollbars="y" h="calc(100dvh - var(--header-height))">
                        <Page.WideContent>
                            <Stack gap={48}>
                                <Dashboard.Checklist />

                                {!isLoadingCompanies && (
                                    <>
                                        {user?.isManufacturer && !user?.hasCompany && <Dashboard.CompanySignupInfo />}
                                        {hasOneCompany && firstCompany && <Dashboard.Profile />}
                                    </>
                                )}

                                {user?.isLoading || !user ? (
                                    <Loader size="xs" color="gray.5" />
                                ) : user.isManufacturer ? (
                                    <Dashboard.Products />
                                ) : (
                                    <Dashboard.Projects />
                                )}
                            </Stack>
                        </Page.WideContent>

                        <Page.Footer />
                    </ScrollArea>
                </Box>
                <Box className={cx.notifications}>
                    <ScrollArea
                        type="never"
                        scrollbars="y"
                        scrollbarSize={8}
                        h="calc(100dvh - var(--users-height) - var(--header-height))"
                    >
                        <Dashboard.Notifications />
                    </ScrollArea>
                </Box>
                <Box className={cx.users}>
                    <ScrollArea type="never" scrollbars="y" scrollbarSize={8} h="var(--users-height)">
                        <Dashboard.Users />
                    </ScrollArea>
                </Box>
            </Box>
        </Page>
    );
};

Dashboard.Title = DashboardTitle;
Dashboard.Users = LazyDashboardUsers;
Dashboard.Projects = DashboardProjects;
Dashboard.Products = DashboardProducts;
Dashboard.Checklist = DashboardChecklist;
Dashboard.Profile = DashboardCompanyProfile;
Dashboard.Notifications = DashboardNotifications;
Dashboard.EmptyProjects = DashboardEmptyProjects;
Dashboard.EmptyProducts = DashboardEmptyProducts;
Dashboard.HighlightedInfo = DashboardHighlightedInfo;
Dashboard.CompanySignupInfo = DashboardCompanySignupInfo;
Dashboard.EmptyViewedProducts = DashboardEmptyProducts.Viewed;
Dashboard.EmptyReferenceDesigns = DashboardEmptyReferenceDesigns;

export { Dashboard };
