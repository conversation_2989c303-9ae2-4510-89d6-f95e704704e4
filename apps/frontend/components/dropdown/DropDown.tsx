import React, { useState } from 'react';

import { Button, Popover, Stack } from '@mantine/core';

import Link from 'next/link';
import { IoCaretDown } from 'react-icons/io5';

import { DropDownOption } from './DropDownOption';

import buttonClasses from './DropDownButton.module.css';

import { DropDownDivider } from './DropDownDivider';
import { DropDownTitle } from './DropDownTitle';
import { DropDownInput } from './DropDownInput';

import { useDisclosure } from 'hooks/use-disclosure';
import { isTouchDevice } from 'helpers/isTouchDevice';

type Props = {
    icon?: React.ReactNode;
    title?: string | React.ReactNode;
    children?: React.ReactNode;
    variant?: 'default' | 'outline' | 'filled';
    setTitle?: (title: string) => Promise<void>;
    onClick?: () => void;
    href?: string;
    expandOnMobile?: boolean;
    autofocus?: boolean;
};

const DropDown = ({
    icon,
    title,
    children,
    variant,
    setTitle,
    onClick: onClick_,
    href,
    expandOnMobile,
    autofocus,
}: Props) => {
    const [opened, { close, open }] = useDisclosure(false);
    const [editing, setEditing] = useState(autofocus ?? false);

    const showDropdown = Boolean(children);

    const editable = Boolean(setTitle);

    const isTouch = isTouchDevice();

    const onClick = () => {
        if (editable) {
            setEditing(true);
            close();

            return;
        }

        onClick_?.();
    };

    const handleOnClick = (event: any) => {
        // Prevents the dropdown from opening when the button is clicked on touch devices
        // so that the user can view the dropdown options instead of navigating to the link
        if (isTouch && showDropdown) {
            event.preventDefault();
        }
    };

    const buttonProps = {
        'classNames': buttonClasses,
        'leftSection': icon,
        'rightSection': children && <IoCaretDown data-caret-down size={10} />,
        'data-dropdown': showDropdown,
        'data-variant': variant,
        'onMouseEnter': open,
        'onMouseLeave': close,
        'data-cursor': editable ? 'text' : 'default',
        'data-dropdown-item': true,
    };

    const button = href ? (
        <Button component={Link} href={href} {...buttonProps} onClick={handleOnClick}>
            {title}
        </Button>
    ) : (
        <Button {...buttonProps} onClick={onClick}>
            {title}
        </Button>
    );

    const onSubmit = async (title: string) => {
        await setTitle?.(title);

        setEditing(false);
    };

    const onBlur = () => {
        setEditing(false);
    };

    const input = <DropDownInput placeholder={title as string} onSubmit={onSubmit} onBlur={onBlur} />;

    return (
        <Popover closeOnClickOutside closeOnEscape position="bottom-start" offset={4} opened={opened}>
            <Popover.Target>{editing ? input : button}</Popover.Target>
            {showDropdown && (
                <>
                    <Popover.Dropdown
                        p={0}
                        onMouseEnter={open}
                        onMouseLeave={close}
                        visibleFrom={expandOnMobile ? 'md' : undefined}
                    >
                        <Stack gap={'0.25rem'}>{children}</Stack>
                    </Popover.Dropdown>
                    {expandOnMobile && (
                        <Stack gap="xs" hiddenFrom="md" w="100%">
                            {children}
                        </Stack>
                    )}
                </>
            )}
        </Popover>
    );
};

DropDown.Option = DropDownOption;
DropDown.Title = DropDownTitle;
DropDown.Divider = DropDownDivider;

export { DropDown };
