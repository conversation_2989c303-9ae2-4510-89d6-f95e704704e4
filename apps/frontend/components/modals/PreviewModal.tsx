import React from 'react';

import { useWatch } from 'react-hook-form';

import { Modal, ModalProps, SimpleGrid, Stack, Text } from '@mantine/core';

import { Form } from 'components/forms/Form';

type Props<T = object> = ModalProps & {
    onSubmit: (data: T) => void;
    defaultValues: Partial<T> | undefined;
    validator: any;
    preview: (values: T) => React.ReactElement<any>;
};

const PreviewModal = <T,>({ defaultValues, validator, onSubmit, preview, children, ...props }: Props<T>) => (
    <Modal {...props} size={1400}>
        <Form<any> onSubmit={onSubmit} defaultValues={defaultValues} zodSchema={validator}>
            <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg">
                <Stack>{children}</Stack>
                <Preview<T> preview={preview} />
            </SimpleGrid>
        </Form>
    </Modal>
);

const Preview = <T = object,>({ preview }: { preview: (values: T) => React.ReactElement<any> }) => {
    const values = useWatch();

    return (
        <Stack bg="gray.0" p="md" gap="xs" visibleFrom="sm">
            <Text fz="xs" c="dimmed" fw={500}>
                Preview
            </Text>
            {preview(values as any)}
        </Stack>
    );
};

export { PreviewModal };
