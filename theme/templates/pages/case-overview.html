{% extends 'layout/app' %}

{% block content %}

    {% include 'components/header' with {
        centered: 1,
    } %}

    <div class="bg-brand-black text-white {{ containerClasses() }}">
        <div class="h-10"></div>

        <div class="flex flex-wrap justify-center gap-2 md:gap-4">
            {% for caseCategory in craft.entries.section('caseCategory') %}
                <a
                    href="{{ caseCategory.url }}"
                    class="
                        inline-flex justify-center items-center
                        h-10 sm:h-12 lg:h-14
                        px-4 sm:px-6 lg:px-8
                        text-sm sm:text-base lg:text-lg
                        hover:bg-brand-purple hover:text-white
                        border border-solid border-white hover:border-brand-purple
                        rounded-full
                        transition-colors
                    "
                >
                    {{ caseCategory.title }}
                </a>
            {% endfor %}
        </div>

        {% include 'components/spacer' with { size: 'large' } %}

        {% for case in craft.entries.section('case').all %}
            {% include 'teasers/case-teaser' with {
                case: case,
                variant: loop.index % 2 ? 'straight' : 'angled',
                swapped: '0',
            } %}
            {% if not loop.last %}
                {% include 'components/spacer' with { size: 'large' } %}
            {% endif %}
        {% endfor %}

        {% include 'components/spacer' with { size: 'large' } %}

    </div>

{% endblock %}
