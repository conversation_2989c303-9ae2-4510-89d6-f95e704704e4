import React from 'react';

import { Box, Text } from '@mantine/core';
import { IoCloseCircleOutline } from 'react-icons/io5';

import { useWireSizings } from 'components/diagram/hooks/use-wire-sizings';
import { WireSizingService } from 'components/diagram/services/WireSizingService';
import { DiagramToolbar } from 'components/diagram/components/diagram-toolbar/DiagramToolbar';

import cx from '../diagram-simulation/DiagramSimulationToolbar.module.css';

const DiagramWireSizingToolbar = () => {
    const { wireSizing } = useWireSizings();

    if (!wireSizing) return null;

    return (
        <Box className={cx.root}>
            <DiagramToolbar.Fixed>
                <DiagramToolbar.Text>{wireSizing.name}</DiagramToolbar.Text>
                <DiagramToolbar.Divider />

                <DiagramToolbar.TextButton onClick={() => WireSizingService.deactivate()}>
                    <IoCloseCircleOutline size={14} />
                    <Text inherit ml={6}>
                        Exit
                    </Text>
                </DiagramToolbar.TextButton>
            </DiagramToolbar.Fixed>
        </Box>
    );
};

export { DiagramWireSizingToolbar };
