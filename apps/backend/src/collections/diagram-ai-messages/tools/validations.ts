import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

const name = 'getValidationErrors';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: 'Retrieves a list of validation errors for this diagram',
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {} as const;

const handler = async (message: DiagramAIMessage) => {
    return JSON.stringify(message.diagramValidations);
};

const prompt = `
## Validation Specific Questions

This section activates only if the user explicitly asks a question or makes a request
directly related to diagram validation, checking for errors, understanding validation
issues, or fixing validation errors.

**Tool Calling:**
*Mandatory*: You must call the ${name} function.
This function will return information about any validation errors found or confirm that no errors were detected.

**Possible Responses:**
Based on what the user asks, you can respond in one of the following ways:

*   **Inform and Show Validation Errors**

    When the user asks about validation errors:
    * Clearly inform the user about the existing validation errors
    * Present each error in an understandable way and mention the affected component or connection
    * Suggest that you can help fix them if the user wants

*   **Suggest Possible Fixes**

    When the user asks for fixes to the validation errors:

    For each identified error (or a group of related errors):
    * Analyze the cause based on the diagram data, provided schemas, and sound microgrid engineering principles.
    * Propose one or more clear, actionable suggestions to fix each error.
    * If multiple valid fix options exist for an error, you can present them to the user.

    *Note*: don't automatically apply fixes. *Always* ask the user if they want to apply the fixes you suggest.
    After the user confirms they want to apply the fixes, you can generate an "=== ACTION ===" block with the necessary changes.

*   **If Unable to Suggest or Apply a Fix:**

    If an identified error is too complex for you to automatically suggest a reliable fix,
    or if applying a known fix is beyond your current capabilities, clearly explain this limitation to the user.
    You should still try to describe the error and why it's problematic.

*   **No Validation Errors:**

    If no validation errors are found, inform the user that their diagram is valid and does not contain any errors.
`;

const validations = {
    name,
    definition,
    actions,
    handler,
    prompt,
};

export { validations };
