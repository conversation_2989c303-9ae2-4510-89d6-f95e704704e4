import type { FC } from 'react';

import { Box } from '@mantine/core';

import { Component } from '@repo/dcide-component-models';

import { ComponentIcon as Icon } from 'components/component-icons/ComponentIcon';

import classes from './ComponentInstance.module.css';

const ComponentIcon: FC<{
    component: Component;
    size?: number;
    iconSize?: number;
}> = ({ component, size = 60 }) => {
    return (
        <Box className={classes.componentIcon} style={{ width: size, height: size }}>
            <Box style={{ width: size / 2, height: size / 2 }}>
                <Icon type={component.type} />
            </Box>
        </Box>
    );
};

export { ComponentIcon };
