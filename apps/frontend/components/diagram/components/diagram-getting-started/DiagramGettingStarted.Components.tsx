import React, { FC, useState } from 'react';

import { Box, Button, InputLabel, SimpleGrid, Stack, Text, UnstyledButton } from '@mantine/core';

import { all, ComponentDefinition } from '@repo/dcide-component-models';

import { DiagramService } from 'components/diagram/services';
import { DiagramSyncService } from 'components/diagram/services/DiagramSyncService';

import { ComponentIcon } from 'components/component-icons/ComponentIcon';

import cx from './DiagramGettingStarted.module.css';

const components: ComponentDefinition[] = [
    all.utility,
    all.solar,
    all.battery,
    all.charger,
    all.hvac,
    all.light,
    all.generator,
];

type Props = {
    onClose?: () => void;
};

const DiagramGettingStartedComponents: FC<Props> = ({ onClose }) => {
    const [selectedComponentTypes, setSelectedComponentTypes] = useState<ComponentDefinition['type'][]>([]);

    const toggleComponentType = (type: ComponentDefinition['type']) => {
        if (selectedComponentTypes.includes(type)) {
            setSelectedComponentTypes(selectedComponentTypes.filter((selectedType) => selectedType !== type));
        } else {
            setSelectedComponentTypes([...selectedComponentTypes, type]);
        }
    };

    const clearSelectedComponentTypes = () => {
        setSelectedComponentTypes([]);
    };

    const generate = () => {
        if (selectedComponentTypes.length > 0) {
            DiagramService.createDiagramFromComponentTypes(selectedComponentTypes);
            DiagramSyncService.save();
        }

        onClose?.();
    };

    return (
        <>
            <Stack gap={4}>
                <InputLabel>Select components to start from</InputLabel>
                <SimpleGrid cols={4} spacing={8}>
                    <UnstyledButton
                        key={'no-component'}
                        className={cx.component}
                        onClick={clearSelectedComponentTypes}
                        data-selected={selectedComponentTypes.length === 0}
                    >
                        <Text className={cx.componentName}>No components</Text>
                    </UnstyledButton>
                    {components.map((component) => (
                        <UnstyledButton
                            key={component.type}
                            className={cx.component}
                            onClick={() => {
                                toggleComponentType(component.type);
                            }}
                            data-selected={selectedComponentTypes.includes(component.type)}
                        >
                            <Box className={cx.componentIcon}>
                                <ComponentIcon type={component.type} />
                            </Box>
                            <Text className={cx.componentName}>{component.name}</Text>
                        </UnstyledButton>
                    ))}
                </SimpleGrid>
            </Stack>

            <Button onClick={generate}>Start designing</Button>
        </>
    );
};

export { DiagramGettingStartedComponents };
