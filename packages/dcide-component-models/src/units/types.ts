export type ConvertArgs<Unit extends string, ToUnit extends Unit> = {
    value: number;
    from: Unit;
    to: ToUnit;
};

export abstract class UnitConverter<Unit extends string, BaseUnit extends Unit> {
    abstract readonly baseUnit: BaseUnit;
    abstract readonly units: readonly Unit[];

    abstract toBase(args: { value: number; from: Unit }): { value: number; unit: BaseUnit };
    abstract convert<ToUnit extends Unit>(args: ConvertArgs<Unit, ToUnit>): { value: number; unit: ToUnit };

    /**
     * The threshold at which to switch to the next unit (exclusive upper bound).
     * For example, if set to 2000, 1999 will stay in the current unit, 2000 will switch to the next.
     */
    protected unitSwitchThreshold = 2000;

    toBest({ value, from }: { value: number; from: Unit }): { value: number; unit: Unit } {
        // Convert the value to each display unit
        const converted = this.getAvailableBestUnits().map((du) => {
            const { value: convertedValue } = this.convert({ value, from, to: du });
            return { unit: du, value: convertedValue };
        });

        // Subsequently, pick the best display unit
        // Examples for the best display unit:
        // 0.7 kV, 700 V, 0.0007 MV => 700 V
        // 1.4 kV, 1400 V, 0.0014 MV => 1400 V
        // 10 kV, 10 000 V, 0.01 MV => 10 kV
        // 30 000 W, 30 kW, 0.03 MW => 30 kW
        // 7000 W, 7 kW, 0.007 MW => 7 kW
        // 1000 W, 1 kW, 0.001 MW => 1000 W
        // 1500 W, 1.5 kW, 0.0015 MW => 1500 W
        // 2000 W, 2 kW, 0.002 MW => 2000 W

        const best = converted.find(({ value }) => Math.abs(value) >= 1 && Math.abs(value) < this.unitSwitchThreshold);

        if (best !== undefined) {
            return best;
        }

        const backupBest = converted
            .sort((a, b) => Math.abs(b.value) - Math.abs(a.value))
            .find(({ value }) => Math.abs(value) < 1);

        if (backupBest !== undefined) {
            return backupBest;
        }

        // If all else fails, return the first display unit
        return converted[0];
    }

    getDisplayUnits() {
        return this.units;
    }

    protected getAvailableBestUnits() {
        return this.getDisplayUnits();
    }
}
