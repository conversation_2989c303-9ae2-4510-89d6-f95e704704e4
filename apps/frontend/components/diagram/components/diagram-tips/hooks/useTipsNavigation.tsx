import React, { useState } from 'react';

import { Box, Group } from '@mantine/core';
import { TbChevronLeft, TbChevronRight } from 'react-icons/tb';

import { SimpleButton } from 'elements/buttons';

import cx from './styles.module.scss';

const useTipsNavigation = (totalTips: number) => {
    const [currentTip, setCurrentTip] = useState(0);

    const previous = () => {
        setCurrentTip(currentTip - 1);
    };

    const next = () => {
        setCurrentTip(currentTip + 1);
    };

    const TipsNav =
        totalTips > 1 ? (
            <Group gap={8} className={cx.tipsNavigation}>
                <SimpleButton onClick={previous} disabled={currentTip === 0}>
                    <TbChevronLeft />
                </SimpleButton>
                <Box className={cx.tipsPagination}>
                    Tip {currentTip + 1}/{totalTips}
                </Box>
                <SimpleButton onClick={next} disabled={currentTip === totalTips - 1}>
                    <TbChevronRight />
                </SimpleButton>
            </Group>
        ) : null;

    return { currentTip, previous, next, TipsNav };
};

export { useTipsNavigation };
