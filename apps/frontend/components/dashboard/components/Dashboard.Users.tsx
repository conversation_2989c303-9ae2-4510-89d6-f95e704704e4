import Link from 'next/link';

import { <PERSON>, Button, Stack } from '@mantine/core';
import { IoAdd } from 'react-icons/io5';

import { useMembers } from '@ably/spaces/react';
import { useUser } from 'hooks/use-user';
import { useCurrentTeam } from 'hooks/use-current-team';

import { Avatar } from 'components/avatar/Avatar';
import { Dashboard } from 'components/dashboard/Dashboard';
import { RealtimeTeamProvider } from 'components/realtime-user/RealtimeTeamProvider';

import cx from './Dashboard.Users.module.css';

const DashboardUsers = () => {
    const team = useCurrentTeam();

    if (!team) return null;

    return (
        <Stack p="md" gap="md">
            <Dashboard.Title
                rightSection={
                    <Button
                        size="compact-xs"
                        variant="transparent"
                        leftSection={<IoAdd />}
                        component={Link}
                        href="/account#team"
                    >
                        Invite
                    </Button>
                }
            >
                Team users
            </Dashboard.Title>

            <RealtimeTeamProvider team={team}>
                <Stack gap={8}>
                    {team.users.map(({ user }) => (
                        <DashboardUser key={user} userId={user} />
                    ))}
                </Stack>
            </RealtimeTeamProvider>
        </Stack>
    );
};

const DashboardUser = ({ userId }: { userId: string }) => {
    const { user } = useUser(userId);
    const { members } = useMembers();

    if (!user) return null;

    const isOnline = members.find((member) => member.profileData?.id === userId);

    return (
        <Box className={cx.user}>
            <Avatar user={user} />
            <span>{user.name ?? user.email}</span>
            {isOnline && <span className={cx.indicator} />}
        </Box>
    );
};

export { DashboardUsers };
