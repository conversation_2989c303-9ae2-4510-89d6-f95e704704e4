import { FC, useCallback, useEffect, useRef } from 'react';

import { Box } from '@mantine/core';

import { activeScrollItemState, useActiveScrollItem } from 'components/scroll-nav/hooks';

import cx from './ScrollNav.module.css';

const ScrollNavTarget: FC<{ id: string; topSpacing?: number; children: React.ReactNode }> = ({
    id,
    topSpacing = 0,
    children,
}) => {
    const activeItem = useActiveScrollItem();
    const setState = (id: string) => {
        activeScrollItemState.activeScrollItem = id;
    };

    const ref = useRef<HTMLDivElement | null>(null);
    const requestRef = useRef<number | undefined>(undefined);

    const checkIfInViewportTop = useCallback(() => {
        if (activeItem === id) return;

        const itemY = ref.current?.getBoundingClientRect().y ?? -1;
        const itemHeight = ref.current?.getBoundingClientRect().height ?? 0;

        if (itemY > topSpacing && itemY < topSpacing + itemHeight) {
            setState(id);
        }

        requestRef.current = window.requestAnimationFrame(checkIfInViewportTop);
    }, [activeItem, id, topSpacing, setState]);

    useEffect(() => {
        window.requestAnimationFrame(checkIfInViewportTop);

        return () => {
            requestRef.current && window.cancelAnimationFrame(requestRef.current);
        };
    }, [checkIfInViewportTop]);

    return (
        <Box
            ref={ref}
            data-scroll-id={id}
            id={id}
            className={`${cx.scrollTarget} ${activeItem === id && cx.activeScrollTarget}`}
        >
            {children}
        </Box>
    );
};

export { ScrollNavTarget };
