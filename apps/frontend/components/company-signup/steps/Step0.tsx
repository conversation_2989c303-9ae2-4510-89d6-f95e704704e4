import React from 'react';

import { List, Text, Title, Stack, Button } from '@mantine/core';

import { SignupLayout } from 'components/signup-layout/SignupLayout';
import { CompanySignupProps } from 'components/company-signup/CompanySignup';
import { ExampleProfileLink } from 'components/example-profile-link/ExampleProfileLink';
import { IoArrowForward } from 'react-icons/io5';
import { ComponentLanding } from 'components/component-landing';
import { StaticCompanySubscriptionOptions } from 'components/subscriptions/CompanySubscriptionOptions';
import { SetupYourProfileAlert } from '../components/SetupYourProfileAlert';

type Props = Omit<CompanySignupProps, 'progress' | 'active'>;

const Step0 = ({ nextStep }: Props) => {
    return (
        <>
            <SignupLayout.Content maw={700}>
                <Stack gap="lg">
                    <Text>
                        Your profile is your gateway to showcasing your company, products, reference designs, case
                        studies and more! Start for free or upgrade to Premium for maximum visibility and insights.{' '}
                        <ExampleProfileLink />
                    </Text>

                    <StaticCompanySubscriptionOptions />

                    <Title order={3} c="brand" fw={700} mb={-12}>
                        Why Upgrade?
                    </Title>

                    <List>
                        <List.Item>Gain premium visibility in AI-powered search results</List.Item>
                        <List.Item>Monitor performance of your products and profiles with analytics</List.Item>
                        <List.Item>Get discovered by the right audience</List.Item>
                    </List>

                    <SetupYourProfileAlert />

                    <ComponentLanding.Title isGradient>
                        Special Introductory Pricing for Early Adopters
                    </ComponentLanding.Title>

                    <Title order={3} c="brand" fw={700} mb={-12}>
                        Sign up before March 31, 2025 and get 6 months for free
                    </Title>

                    <List>
                        <List.Item>Lock-in $299/month until December 31, 2025</List.Item>
                        <List.Item>Then $499/month from January 1, 2026</List.Item>
                    </List>

                    <Title order={3} c="brand" fw={700} mb={-12}>
                        Sign up between April 1-September 30, 2025
                    </Title>

                    <List>
                        <List.Item>Lock-in $299/month until December 31, 2025</List.Item>
                        <List.Item>Then $499/month from January 1, 2026</List.Item>
                    </List>

                    <Title order={3} c="brand" fw={700} mb={-12}>
                        Sign up after September 30, 2025
                    </Title>

                    <List>
                        <List.Item>$499/month (Standard Rate)</List.Item>
                    </List>
                </Stack>
            </SignupLayout.Content>
            <SignupLayout.Actions
                rightActions={
                    <Button rightSection={<IoArrowForward />} onClick={nextStep}>
                        Continue
                    </Button>
                }
            />
        </>
    );
};

export { Step0 };
