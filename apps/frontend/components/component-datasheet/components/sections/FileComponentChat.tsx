import { Flex, UnstyledButton } from '@mantine/core';
import { BsRobot } from 'react-icons/bs';

import { useComponentChat } from 'components/component-chat/hooks/useComponentChat';
import { TrackingService } from 'services/TrackingService';

import classes from './ComponentChat.module.css';

const FileComponentChat = () => {
    const { toggleChatOpen, chatOpen } = useComponentChat();

    return (
        <UnstyledButton
            disabled={chatOpen}
            className={classes.fileButton}
            onClick={() => {
                toggleChatOpen();

                TrackingService.trackComponentChat('datasheet_files');
            }}
        >
            <Flex gap={8}>
                <BsRobot size={14} />
                {chatOpen ? 'In progress...' : 'Ask a question...'}
            </Flex>
        </UnstyledButton>
    );
};

export { FileComponentChat };
