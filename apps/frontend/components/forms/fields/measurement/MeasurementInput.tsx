import { NumberInput, NumberInputProps } from '@mantine/core';
import { forwardRef, ReactNode, useState } from 'react';
import { BsChevronBarDown, BsChevronBarExpand, BsChevronBarUp } from 'react-icons/bs';
import { UnitElement } from './types';
import { isNumber } from 'radash';
import { ComponentFieldDisplay, ComponentFieldDisplayLabels } from '@repo/dcide-component-models';
import { useMergedRef } from '@mantine/hooks';
import { useReadOnlyPlaceholder } from '../hooks/use-readonly-placeholder';

export type MeasurementInputProps = Omit<NumberInputProps, 'value' | 'onChange' | 'display'> & {
    value?: string;
    onChange?: (value: string) => void;
    icon?: ReactNode;
    unit: UnitElement;
    hideIcon?: boolean;
    display?: ComponentFieldDisplay;
    hasDifferingValues?: boolean;
    isDirty?: boolean;
};

const MeasurementInput = forwardRef<HTMLInputElement, MeasurementInputProps>(
    (
        {
            value,
            onChange,
            icon,
            unit,
            hideIcon = false,
            display = ComponentFieldDisplay.NORMAL,
            hasDifferingValues,
            isDirty,
            ...props
        },
        forwardedRef,
    ) => {
        const BaseProps: NumberInputProps = {
            hideControls: true,
            leftSection: !hideIcon && icon,
            leftSectionWidth: '32',
            rightSectionWidth: '32',
            style: { flexGrow: 1 },
            //https://developer.1password.com/docs/web/compatible-website-design/
            autoComplete: 'off',
            step: 0,
        };

        const placeholderRef = useReadOnlyPlaceholder('Not specified', props.placeholder === undefined);

        const ref = useMergedRef(forwardedRef, placeholderRef);

        const [isTouched, setTouched] = useState(false);

        if (hasDifferingValues) {
            return <NumberInput {...BaseProps} placeholder="Values differ" disabled />;
        }

        switch (display) {
            case ComponentFieldDisplay.NOT_APPLICABLE:
            case ComponentFieldDisplay.UNKNOWN:
                return <NumberInput {...BaseProps} placeholder={ComponentFieldDisplayLabels[display]} disabled />;

            case ComponentFieldDisplay.HIDDEN:
                return <div style={{ flexGrow: 1 }} />;

            case ComponentFieldDisplay.NORMAL:
                return (
                    <NumberInput
                        {...BaseProps}
                        data-1p-ignore
                        rightSection={unit}
                        value={value}
                        onChange={onChange ? (value) => onChange(value.toString()) : undefined}
                        startValue={value && isNumber(+value) ? +value : undefined}
                        placeholder="Value"
                        {...props}
                        ref={ref}
                        data-dirty={isDirty}
                        data-touched={isTouched}
                        onFocus={() => setTouched(true)}
                        styles={{
                            section: {
                                width: '40px',
                            },
                        }}
                    />
                );
        }
    },
);

const MinimumMeasurementInput = (props: Omit<MeasurementInputProps, 'icon' | 'placeholder'>) => (
    <MeasurementInput placeholder="Min" icon={<BsChevronBarDown size={14} />} {...props} />
);

const NominalMeasurementInput = (props: Omit<MeasurementInputProps, 'icon' | 'placeholder'>) => (
    <MeasurementInput placeholder="Nom" icon={<BsChevronBarExpand size={14} />} {...props} />
);

const MaximumMeasurementInput = (props: Omit<MeasurementInputProps, 'icon' | 'placeholder'>) => (
    <MeasurementInput placeholder="Max" icon={<BsChevronBarUp size={14} />} {...props} />
);

MeasurementInput.displayName = 'MeasurementInput';

export { MeasurementInput, MinimumMeasurementInput, NominalMeasurementInput, MaximumMeasurementInput };
