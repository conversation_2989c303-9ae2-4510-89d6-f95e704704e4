import React, { <PERSON> } from 'react';

import { Box } from '@mantine/core';

import { DiagramComment } from '@repo/dcide-component-models';
import { DiagramCommentTeaser } from 'components/diagram/components/diagram-comments/DiagramCommentTeaser';

import cx from './DiagramComment.module.css';

const DiagramCommentList: FC<{
    comments: DiagramComment[];
}> = ({ comments }) => {
    return (
        <Box className={cx.list}>
            {comments.map((comment) => (
                <DiagramCommentTeaser comment={comment} key={comment.id} />
            ))}
        </Box>
    );
};

export { DiagramCommentList };
