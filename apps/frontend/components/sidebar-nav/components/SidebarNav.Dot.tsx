import { Flex, Tooltip, UnstyledButton } from '@mantine/core';
import { IoEllipse } from 'react-icons/io5';

import cx from './SidebarNav.CreateButton.module.css';
import cxTooltip from './SidebarNav.Tooltip.module.css';

const SidebarNavDot = ({ tooltip, onClick, color }: { tooltip: string; onClick?: () => void; color?: string }) => {
    return (
        <Tooltip label={tooltip} position="top-end" classNames={cxTooltip} offset={0}>
            <UnstyledButton
                className={cx.button}
                onClick={(event) => {
                    if (!onClick) {
                        return;
                    }

                    event.preventDefault();
                    event.stopPropagation();

                    onClick();
                }}
            >
                <Flex c={color}>
                    <IoEllipse size={12} />
                </Flex>
            </UnstyledButton>
        </Tooltip>
    );
};

export { SidebarNavDot };
