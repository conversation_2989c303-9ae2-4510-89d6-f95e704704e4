import React, { FC, useEffect, useState } from 'react';
import Link from 'next/link';

import {
    Box,
    UnstyledButton,
    Breadcrumbs as MantineBreadcrumbs,
    Affix,
    Transition,
    Group,
    Stack,
    Drawer,
    BoxProps,
    Text,
    Tooltip,
    Flex,
} from '@mantine/core';
import { useDisclosure, useWindowScroll } from '@mantine/hooks';
import { BsLayoutSidebar } from 'react-icons/bs';
import { IoChevronForward, IoEllipsisVerticalSharp } from 'react-icons/io5';

import { sidebarNavState } from 'state/sidebar-nav';

import { useRouter } from 'next/router';
import { usePermission } from 'hooks/use-permission';
import { useSidebarNav } from 'hooks/use-sidebar-nav';
import { useLocalUserInfo } from 'hooks/use-local-user-info';

import { SidebarService as DiagramSidebarService } from 'components/diagram/services/SidebarService';
import { TextHelpers } from 'helpers/TextHelpers';
import { ProjectHelpers } from 'helpers/ProjectHelpers';
import { RouterHelpers } from 'helpers/RouterHelpers';

import { SIDEBAR_NAV_WIDTH } from 'components/sidebar-nav/SidebarNav';

import cx from './Breadcrumbs.module.css';
import cxTooltip from 'components/sidebar-nav/components/SidebarNav.Tooltip.module.css';

import { PermissionDiagramElements } from '@repo/dcide-component-models';

import { useCurrentTeam } from 'hooks/use-current-team';
import { useCurrentUser } from 'hooks/use-current-user';

import { Logo } from 'components/logo';

const MAX_BREADCRUMB_LENGTH = 40;
const MAX_BREADCRUMB_LENGTH_MOBILE = 20;

export type BreadcrumbProps = {
    title?: string;
    isSticky?: boolean;
    rightSection?: React.ReactNode;
    showToggle?: boolean;
    showHomeAsLogo?: boolean;
};

const Breadcrumbs: FC<BreadcrumbProps & BoxProps> & {
    Sticky: typeof BreadcrumbsSticky;
    Content: typeof BreadcrumbsContent;
    Floating: typeof BreadcrumbsFloating;
    RightSection: typeof BreadcrumbsRightSection;
    Home: typeof BreadcrumbsHome;
} = ({ title, isSticky = true, rightSection, showToggle, ...props }) => {
    const { isDisabled } = useSidebarNav();

    return !isDisabled ? (
        <>
            <Box className={`${cx.root} ${cx.fullWidth}`} {...props}>
                <Breadcrumbs.Content rightSection={rightSection} title={title} showToggle={showToggle} />
            </Box>
            {isSticky && <Breadcrumbs.Sticky rightSection={rightSection} title={title} {...props} />}
        </>
    ) : null;
};

const BreadcrumbsSticky = ({ title, rightSection, ...props }: BreadcrumbProps & BoxProps) => {
    const [scroll] = useWindowScroll();
    const { isOpen } = useSidebarNav();

    const [hero, setHero] = useState<Element | null>(null);

    useEffect(() => {
        const findHero = document.querySelector('[data-hero]');

        if (findHero) {
            setHero(findHero);
        }
    }, []);

    const scrollOffset = hero ? hero.getBoundingClientRect().height : 200;

    return (
        <Affix position={{ top: 0, left: isOpen ? SIDEBAR_NAV_WIDTH : 0, right: 0 }}>
            <Transition transition="slide-down" mounted={scroll.y > scrollOffset} exitDelay={50}>
                {(transitionStyles) => (
                    <Box className={`${cx.root} ${cx.fullWidth} ${cx.sticky}`} style={transitionStyles} {...props}>
                        <Breadcrumbs.Content rightSection={rightSection} title={title} />
                    </Box>
                )}
            </Transition>
        </Affix>
    );
};

const BreadcrumbsFloating = ({
    title,
    fullWidth,
    rightSection,
    isSticky = true,
    showToggle,
    showHomeAsLogo,
    ...props
}: {
    fullWidth?: boolean;
} & BreadcrumbProps &
    BoxProps) => {
    const { isOpen, isDisabled } = useSidebarNav();

    if (isDisabled) {
        return null;
    }

    if (fullWidth) {
        return (
            <>
                <Box className={`${cx.root} ${cx.floatingFullWidth}`} data-sidebar-open={isOpen} {...props}>
                    <Breadcrumbs.Content
                        rightSection={rightSection}
                        title={title}
                        showToggle={showToggle}
                        showHomeAsLogo={showHomeAsLogo}
                    />
                </Box>
                {isSticky && <Breadcrumbs.Sticky rightSection={rightSection} title={title} {...props} />}
            </>
        );
    }

    return (
        <Box className={cx.floatingWrapper} data-sidebar-open={isOpen} {...props}>
            <Box className={`${cx.root} ${cx.floating}`}>
                <Breadcrumbs.Content showToggle title={title} showHomeAsLogo={showHomeAsLogo} />
            </Box>

            {rightSection && (
                <Box className={cx.floatingRightSection}>
                    <Breadcrumbs.RightSection rightSection={rightSection} />
                </Box>
            )}
        </Box>
    );
};

const BreadcrumbsContent = ({ title, rightSection, showToggle, showHomeAsLogo }: BreadcrumbProps) => {
    const router = useRouter();
    const canEditProject = usePermission(PermissionDiagramElements.EDIT);
    const isProjectEditor = router.pathname.includes('/projects/[projectId]/editor');

    const getIntermediateBreadcrumb = () => {
        if (router.pathname.includes('/projects/')) {
            if (canEditProject) {
                return (
                    <Text inherit component={Link} href={ProjectHelpers.urls.overview()} className={cx.link}>
                        Projects
                    </Text>
                );
            } else {
                return (
                    <Text inherit component={Link} href={RouterHelpers.urls.searchTab('designs')} className={cx.link}>
                        Reference designs
                    </Text>
                );
            }
        }

        if (router.pathname.includes('/products/')) {
            return (
                <Text inherit component={Link} href={RouterHelpers.urls.searchTab('products')} className={cx.link}>
                    Products
                </Text>
            );
        }

        if (router.pathname.includes('/profiles/')) {
            return (
                <Text inherit component={Link} href={RouterHelpers.urls.searchTab('profiles')} className={cx.link}>
                    Profiles
                </Text>
            );
        }

        return null;
    };

    const toggleSidebar = () => {
        sidebarNavState.isOpen = !sidebarNavState.isOpen;
    };

    const toggleSidebarMobile = () => {
        sidebarNavState.isMobileOpen = !sidebarNavState.isMobileOpen;
    };

    const showTitleTooltip = title && title.length > MAX_BREADCRUMB_LENGTH;

    return (
        <>
            {showToggle && (
                <UnstyledButton onClick={toggleSidebar} className={cx.sidebarToggle} visibleFrom="md">
                    <BsLayoutSidebar />
                </UnstyledButton>
            )}

            <UnstyledButton onClick={toggleSidebarMobile} className={cx.sidebarToggle} hiddenFrom="md">
                <BsLayoutSidebar />
            </UnstyledButton>

            <MantineBreadcrumbs separator={<IoChevronForward />} className={cx.breadcrumbs}>
                <Breadcrumbs.Home showHomeAsLogo={showHomeAsLogo} />

                {getIntermediateBreadcrumb()}

                {title && isProjectEditor && (
                    <Tooltip
                        classNames={cxTooltip}
                        label="Click to edit"
                        position="bottom"
                        offset={12}
                        openDelay={1000}
                        withArrow
                    >
                        <Text
                            inherit
                            className={cx.link}
                            onClick={() => {
                                DiagramSidebarService.openProjectSidebar();
                            }}
                        >
                            {TextHelpers.getTextWithEllipsis(title, MAX_BREADCRUMB_LENGTH)}
                        </Text>
                    </Tooltip>
                )}
                {title && !isProjectEditor && (
                    <>
                        <Box visibleFrom="md">
                            <Tooltip
                                classNames={cxTooltip}
                                label={title}
                                disabled={!showTitleTooltip}
                                position="bottom-start"
                                offset={12}
                            >
                                <span>{TextHelpers.getTextWithEllipsis(title, MAX_BREADCRUMB_LENGTH)}</span>
                            </Tooltip>
                        </Box>
                        <Box hiddenFrom="md">
                            {TextHelpers.getTextWithEllipsis(title, MAX_BREADCRUMB_LENGTH_MOBILE)}
                        </Box>
                    </>
                )}
            </MantineBreadcrumbs>

            {rightSection && (
                <Box className={cx.rightSection}>
                    <Breadcrumbs.RightSection rightSection={rightSection} />
                </Box>
            )}
        </>
    );
};

const BreadcrumbsRightSection = ({ rightSection }: BreadcrumbProps) => {
    const [rightSectionOpen, rightSectionHandlers] = useDisclosure();

    return (
        <>
            <Group gap={4} visibleFrom="md">
                {rightSection}
            </Group>
            <UnstyledButton
                ml="auto"
                hiddenFrom="md"
                onClick={rightSectionHandlers.toggle}
                className={cx.sidebarToggle}
            >
                <IoEllipsisVerticalSharp />
            </UnstyledButton>
            <Drawer
                className={cx.rightSectionDrawer}
                withCloseButton={false}
                size={SIDEBAR_NAV_WIDTH}
                opened={rightSectionOpen}
                onClose={rightSectionHandlers.close}
                position="right"
                zIndex={101}
            >
                <Stack gap="xs" align="start">
                    {rightSection}
                </Stack>
            </Drawer>
        </>
    );
};

const BreadcrumbsHome = ({ showHomeAsLogo }: { showHomeAsLogo?: boolean }) => {
    const team = useCurrentTeam();
    const user = useCurrentUser();

    const { referrer } = useLocalUserInfo();

    const url = RouterHelpers.urls.homepage(team, user, referrer);

    if (showHomeAsLogo) {
        return (
            <Flex c="inherit" component={Link} href={url} data-breadcrumbs-logo>
                <Logo width={60} />
            </Flex>
        );
    }

    return (
        <Text inherit component={Link} href={url} className={cx.link}>
            Home
        </Text>
    );
};

Breadcrumbs.Sticky = BreadcrumbsSticky;
Breadcrumbs.Content = BreadcrumbsContent;
Breadcrumbs.Floating = BreadcrumbsFloating;
Breadcrumbs.RightSection = BreadcrumbsRightSection;
Breadcrumbs.Home = BreadcrumbsHome;

export { Breadcrumbs };
