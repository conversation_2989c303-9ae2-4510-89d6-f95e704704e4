// @ts-no-check
import { FC } from 'react';

import { use<PERSON><PERSON><PERSON><PERSON>, useWatch } from 'react-hook-form';

import { Badge, Box, Tooltip, useMantineTheme } from '@mantine/core';

import cx from './LinesFields.module.css';

type LineOption = {
    voltageType: string;
    value: string;
    label: string;
    color: string;
};

const LinesField: FC<{
    voltageType: 'AC' | 'DC' | null;
    name?: string;
    maxNbLines?: number;
    enablePE?: boolean;
}> = ({ voltageType, name = 'lines', maxNbLines = Infinity, enablePE = true }) => {
    const theme = useMantineTheme();

    const lineValues = useWatch({
        name,
        defaultValue: {},
    });

    const checkedNbLines = voltageType
        ? Object.entries(lineValues[voltageType] ?? {}).filter(([key, value]) => value && key !== 'PE').length
        : 0;

    const lines: LineOption[] = [
        { voltageType: 'AC', value: 'AC.L1', label: 'L1', color: theme.colors.orange['9'] },
        { voltageType: 'AC', value: 'AC.L2', label: 'L2', color: theme.colors.orange['9'] },
        { voltageType: 'AC', value: 'AC.L3', label: 'L3', color: theme.colors.orange['9'] },
        { voltageType: 'AC', value: 'AC.N', label: 'N', color: theme.colors.blue['9'] },
        { voltageType: 'AC', value: 'AC.PE', label: 'PE', color: theme.colors.teal['9'] },
        { voltageType: 'DC', value: 'DC.L+', label: 'L+', color: theme.colors.red['7'] },
        { voltageType: 'DC', value: 'DC.M', label: 'M', color: theme.colors.blue['5'] },
        { voltageType: 'DC', value: 'DC.L-', label: 'L-', color: theme.colors.gray['8'] },
        { voltageType: 'DC', value: 'DC.PE', label: 'PE', color: theme.colors.teal['9'] },
    ].filter((line) => {
        return line.voltageType === voltageType;
    });

    return lines.length ? (
        <Box className={cx.connectionLines}>
            {lines.map((line) => {
                const disableConductor = !line.value.includes('PE') && checkedNbLines >= maxNbLines;
                const disablePE = line.value.includes('PE') && !enablePE;

                return (
                    <LinesFieldLine
                        line={line}
                        key={line.value}
                        name={name}
                        disabled={disableConductor || disablePE}
                        disabledTooltip={
                            disableConductor
                                ? 'Max. number of conductors reached'
                                : disablePE
                                  ? 'PE not available'
                                  : undefined
                        }
                    />
                );
            })}
        </Box>
    ) : null;
};

const LinesFieldLine: FC<{
    line: LineOption;
    name?: string;
    disabled?: boolean;
    disabledTooltip?: string;
}> = ({ line, name = 'lines', disabled: _disabled, disabledTooltip }) => {
    const {
        field: { onChange, value: checked },
    } = useController({
        name: `${name}.${line.value}`,
    });

    const disabled = !checked && _disabled;

    return (
        <Tooltip label={disabledTooltip} disabled={!disabled || !disabledTooltip}>
            <Badge
                className={cx.connectionLinesButton}
                size="md"
                radius="xs"
                onClick={() => {
                    if (disabled) {
                        return;
                    }

                    onChange(!checked);
                }}
                style={{
                    'cursor': 'pointer',
                    '--connection-line-color': line.color,
                }}
                data-active={!!checked}
                data-disabled={disabled}
            >
                {line.label}
            </Badge>
        </Tooltip>
    );
};

export { LinesField };
