import type { <PERSON> } from 'react';

import { useController } from 'react-hook-form';

import { Box } from '@mantine/core';

import cx from './DiagramToolbar.module.css';

const DiagramToolbarInput: FC<{
    name: string;
    placeholder: string;
}> = ({ name, placeholder }) => {
    const { field } = useController({
        name,
    });

    return (
        <Box className={cx.text}>
            <Box className={cx.autoInput}>
                <input {...field} autoComplete="off" className={cx.autoInputInput} placeholder={placeholder} />
                <span className={cx.autoInputSizer}>{field.value || placeholder}</span>
            </Box>
        </Box>
    );
};

export { DiagramToolbarInput };
