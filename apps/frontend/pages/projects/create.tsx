import React, { FC, useEffect } from 'react';

import { GetServerSideProps } from 'next';

import {Button, Group, Loader, Paper, Select, Space, Stack, Text} from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { IoAlertCircle } from 'react-icons/io5';

import {FeatureLimit, Team, TeamInfo} from '@repo/dcide-component-models';

import { getSession } from 'helpers/getSession';

import { useCurrentTeam } from 'hooks/use-current-team';
import { useFeatureLimit } from 'hooks/use-feature-limit';
import { useCreateProject } from 'hooks/use-create-project';
import { useCreateProjectTeams } from 'hooks/use-create-project-teams';

import { UserService } from 'services/UserService';
import { RouterService } from 'services/RouterService';
import { ProjectService } from 'services/ProjectService';

import { Page } from 'components/page';

import { FeatureLimitTracker } from 'components/feature-limit-tracker/FeatureLimitTracker';
import { useRouterQuery } from 'hooks/use-router-query';
import {Form} from 'components/forms/Form';
import {SelectField} from 'components/forms/fields/SelectField';
import {TextField} from 'components/forms/fields/TextField';
import {useAction} from 'hooks/use-action';
import {FormSubmit} from 'components/forms/FormSubmit';
import {ModalTitle} from 'components/modals/ModalTitle';
import {useCurrentUser} from 'hooks/use-current-user';

type Props = {
    teamProjectCounts: {
        [key: string]: number;
    };
};

type Values = {
    team: string;
    name: string;
};

const Create: FC<{
    teamProjectCounts: {
        [key: string]: number;
    };
}> = ({ teamProjectCounts }) => {
    const { name, startFromExistingProject } = useRouterQuery<{
        startFromExistingProject?: string;
        name?: string;
    }>();

    const user = useCurrentUser();
    const team = useCurrentTeam();

    const { createTeams, isLoading: isLoadingCreateTeams } = useCreateProjectTeams();

    const projectCount = teamProjectCounts[team?.id ?? ''] || 0;
    const maxNumberOfProjectsCanCreate = useFeatureLimit(FeatureLimit.PROJECTS);

    const reachedMaxProjects = projectCount >= maxNumberOfProjectsCanCreate;

    const hasCreateTeams = createTeams.length > 0;
    const canCreateInCurrentTeam = !!createTeams.find((needle) => needle.id === currentTeam?.id);

    const action = startFromExistingProject ? 'duplicate' : 'create';
    const projectName = ProjectService.generateName({ name, startFromExistingProject });

    const openCreateTeamModal = () => {
        openContextModal({
            modal: 'createTeam',
            innerProps: {},
            withCloseButton: false,
            centered: true,
        });
    };

    const create = (values: Values) => {
        console.log(values);
    };

    if (!team) {
        return (
            <Wrapper action={action}>
                <Stack gap="xs">
                    Create a team to continue.
                    <Button onClick={openCreateTeamModal}>Create team</Button>
                </Stack>
            </Wrapper>
        );
    }

    if (!canCreateInCurrentTeam) {
        return (
            <Stack>
                You do not have permission to create a project in this team.
                <TeamSwitcher team={team} teams={createTeams} />
            </Stack>
        );
    }



    // error: Something went wrong while creating your project. Please try again later.

    return (
        <Page showBackground title="Create project">
            <Page.CenteredContent>
                <Paper w="100%" maw={420} p="xl" shadow="lg">
                    <ModalTitle>
                        {action === 'create' ? 'Create project' : 'Duplicate project'}
                    </ModalTitle>
                    <Space h="lg" />
                    {!hasCreateTeams ? (
                        <Stack gap="xs">
                            Create a team to continue.
                            <Button onClick={openCreateTeamModal}>Create team</Button>
                        </Stack>
                    ) : (
                        <Form<Values>
                            onSubmit={create}
                            defaultValues={{
                                team: team?.id,
                            }}
                        >
                            <Stack gap="xs">

                                {reachedMaxProjects ? (
                                    <FeatureLimitTracker feature={FeatureLimit.PROJECTS} />
                                ) : (
                                    <>
                                        <TextField name="name" />
                                        <FormSubmit>
                                            {startFromExistingProject ? 'Duplicate' : 'Create'}
                                        </FormSubmit>
                                    </>
                                )}
                            </Stack>
                        </Form>
                    )}
                </Paper>
            </Page.CenteredContent>
        </Page>

    );
};

const TeamSwitcher: FC<{
    team: Team;
    teams: TeamInfo[];
}> = ({ team, teams }) => {
    const [switchToTeam, switching] = useAction(async (team: string) => {
        await UserService.switchTeam(team);
        await RouterService.refresh();
    });

    return (
        <Select
            name="team"
            data={teams.map((team) => ({ value: team.id, label: team.name }))}
            value={team.id}
            onChange={async (value) => {
                if (value) {
                    await switchToTeam(value);
                }
            }}
            rightSection={switching && <Loader size="xs" color="blue" />}
        />
    );
};

const Wrapper: FC<{
    action: 'create' | 'duplicate';
    children: React.ReactNode;
}> = ({ action, children }) => (
    <Page showBackground title="Create project">
        <Page.CenteredContent>
            <Paper w="100%" maw={420} p="xl" shadow="lg">
                <ModalTitle>
                    {action === 'create' ? 'Create project' : 'Duplicate project'}
                </ModalTitle>
                <Space h="lg" />
                {children}
            </Paper>
        </Page.CenteredContent>
    </Page>
);

export const getServerSideProps: GetServerSideProps = async ({ req, res }) => {
    const session = await getSession(req, res);

    if (!session.token) {
        return {
            notFound: true,
        };
    }

    const teamProjectCounts = await ProjectService.getTeamProjectCounts();

    return {
        props: {
            teamProjectCounts,
        },
    };
};

export default Create;
