.composer {
    position: sticky;
    bottom: var(--mantine-spacing-md);

    textarea {
        padding-bottom: calc(30px + 2 * var(--mantine-spacing-xs));
    }

    button[type="submit"] {
        background-color: var(--mantine-color-green-7);
    }
}

.beta {
    position: absolute;
    top: var(--mantine-spacing-xs);
    right: var(--mantine-spacing-xs);
}

.actions {
    position: absolute;
    right: var(--mantine-spacing-xs);
    bottom: var(--mantine-spacing-xs);

    flex-direction: row-reverse;
}

.inspiration {
    font-size: var(--mantine-font-size-sm);
    font-weight: 600;

    &:hover {
        text-decoration: underline;
    }
}
