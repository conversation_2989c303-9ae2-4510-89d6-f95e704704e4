import { FC } from 'react';

import Link from 'next/link';
import { UnstyledButton, Box } from '@mantine/core';

import { CompanyProfile, Event } from '@repo/dcide-component-models';

import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';

import { CompanyLogo } from 'components/company-logo';
import { BoothNumber } from 'components/booth-number/BoothNumber';
import { CompanyLogoFallback } from 'components/company-logo/CompanyLogoFallback';

import cx from './ComponentLanding.Company.module.css';

const ComponentLandingCompany: FC<{
    company: CompanyProfile;
    event?: Event;
}> = ({ company, event }) => {
    const { id, logos, name, slug, status } = company;

    // const showInAppSupport = company.services.includes(CompanyService.IN_APP_SUPPORT);

    return (
        <Box className={cx.wrapper}>
            <Box className={cx.booth}>
                <BoothNumber event={event} company={{ id, name }} showEventName={!event} />
            </Box>
            <UnstyledButton
                className={cx.root}
                data-status={status}
                component={Link}
                href={CompanyProfileHelpers.urls.view(slug)}
            >
                <CompanyLogo logos={logos} fallback={<CompanyLogoFallback name={name} />} />
            </UnstyledButton>
        </Box>
    );
};

export { ComponentLandingCompany };
