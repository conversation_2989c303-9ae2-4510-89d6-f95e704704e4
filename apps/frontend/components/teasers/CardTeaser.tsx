import React, { FC, useState } from 'react';

import { Anchor, Box, Button, ButtonProps, Card, Stack, Title, UnstyledButton } from '@mantine/core';

import { RTEContent } from 'components/rte-content/RTEContent';
import { IKImage } from 'components/ik-image/IKImage';
import { SettingsDropdown } from 'elements/dropdowns/SettingsDropdown';

import cx from '../company-profile/components/Stub.module.css';
import cxTeaser from './CardTeaser.module.css';

const CardTeaser: FC<{
    name: string;
    description: any;
    image: any;
    url?: string;
    button: any;
    buttonProps?: ButtonProps;
    handleEdit?: () => void;
    handleDelete?: () => void;
    handleOpen?: () => void;
}> = ({ name, description, image, url, button, handleEdit, handleDelete, handleOpen, buttonProps }) => {
    const internalUrl = url || button?.url;

    const [descriptionLength, setDescriptionLength] = useState(0);
    const showReadMore = descriptionLength > 240;

    return (
        <Card withBorder className={cx.root} radius="sm" h="100%">
            {image && (
                <Box className={cx.imageWrapper}>
                    <IKImage fileOrId={image as any} width={600} height={340} alt={name || ''} />
                </Box>
            )}

            <Stack className={cx.content} gap="sm">
                {name && (
                    <UnstyledButton
                        c="brand"
                        onClick={handleOpen}
                        data-interactive={!!handleOpen}
                        className={cxTeaser.title}
                    >
                        <Title order={3} fz="lg" fw={700} c="brand">
                            {name}
                        </Title>
                    </UnstyledButton>
                )}

                {description && (
                    <Box mah={showReadMore ? 101 : 'none'} style={{ overflow: 'hidden' }}>
                        <RTEContent content={description} updateCharacterCount={setDescriptionLength} />
                    </Box>
                )}

                {showReadMore && handleOpen && (
                    <Button
                        style={{ alignSelf: 'flex-start' }}
                        variant="outline"
                        size="compact-sm"
                        onClick={handleOpen}
                        mt="auto"
                        {...buttonProps}
                    >
                        Read More
                    </Button>
                )}

                {button?.label && internalUrl && !showReadMore && (
                    <Anchor href={internalUrl} target="_blank" style={{ alignSelf: 'flex-start' }} fw={600}>
                        {button.label}
                    </Anchor>
                )}

                {(handleEdit || handleDelete) && (
                    <Box className={cx.actions}>
                        <SettingsDropdown>
                            {handleEdit && <SettingsDropdown.Edit onClick={handleEdit}>Edit</SettingsDropdown.Edit>}
                            {handleDelete && (
                                <SettingsDropdown.Delete onClick={handleDelete}>Delete</SettingsDropdown.Delete>
                            )}
                        </SettingsDropdown>
                    </Box>
                )}
            </Stack>
        </Card>
    );
};

export { CardTeaser };
