import { expect, test } from '@jest/globals';
import { AIConversationMessage, ComponentChatMessage, AIConversationRole } from '@repo/dcide-component-models';
import { AIServiceUtils } from './AIServiceUtils';
import { z } from 'zod';

describe('convertAIMessagesToAIConversationMessages', () => {
    test('should convert component chat messages to AI conversation messages', () => {
        const history: ComponentChatMessage[] = [
            { question: 'Question 1', answer: 'Answer 1' },
            { question: 'Question 2', answer: 'Answer 2' },
        ];

        const expected: AIConversationMessage[] = [
            { role: AIConversationRole.USER, content: 'Question 1' },
            { role: AIConversationRole.ASSISTANT, content: 'Answer 1' },
            { role: AIConversationRole.USER, content: 'Question 2' },
            { role: AIConversationRole.ASSISTANT, content: 'Answer 2' },
        ];

        const result = AIServiceUtils.convertAIMessagesToAIConversationMessages(history);

        expect(result).toEqual(expected);
    });

    test('should return an empty array if history is undefined', () => {
        const history = undefined;
        const result = AIServiceUtils.convertAIMessagesToAIConversationMessages(history);

        expect(result).toEqual([]);
    });
});
describe('cleanSafeParse', () => {
    const schema = z.object({
        name: z.string().optional(),
        age: z.number().optional(),
        features: z.array(z.string()).optional(),
        power: z.object({ value: z.number() }).optional(),
        ports: z.array(z.object({ powerFlowDirection: z.string().optional() })).optional(),
    });

    test('should clean and parse valid data', () => {
        const data = { name: 'John', age: 30 };
        const result = AIServiceUtils.cleanSafeParse(data, schema);

        if (result.success) {
            expect(result.data).toEqual(data);
        } else {
            throw new Error('Parsing failed');
        }
    });

    test('should remove invalid fields and parse data', () => {
        const data = { name: 'John', age: 'invalid', extra: 'extra', features: { value: 'invalid' }, power: [123] };
        const result = AIServiceUtils.cleanSafeParse(data, schema);

        if (result.success) {
            expect(result.data).toEqual({ name: 'John' });
        } else {
            throw new Error('Parsing failed');
        }
    });

    test('should handle ports bidirectional', () => {
        const data = { ports: [{ powerFlowDirection: 'output' }, { powerFlowDirection: ['output'] }] };
        const result = AIServiceUtils.cleanSafeParse(data, schema);
        if (result.success) {
            expect(result.data).toEqual({ ports: [{ powerFlowDirection: 'output' }, {}] });
        } else {
            throw new Error('Parsing failed');
        }
    });

    test('should return an empty object if all fields are invalid', () => {
        const data = { name: 123, age: 'invalid' };
        const result = AIServiceUtils.cleanSafeParse(data, schema);
        expect(result.success).toBe(false);
    });
});
