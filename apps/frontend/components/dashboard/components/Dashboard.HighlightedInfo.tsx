import React from 'react';

import { Box, Card, CardProps, Stack, Text, ThemeIcon } from '@mantine/core';

import { CarouselSection } from 'components/section/CarouselSection';
import { DashboardTitle } from 'components/dashboard/components/Dashboard.Title';

import cx from './Dashboard.HighlightedInfo.module.css';

export type HighlightedInfoItem = {
    value: string;
    title: string;
    rightSection?: React.ReactNode;
    icon: React.ReactNode;
    description: React.ReactNode;
};

const DashboardHighlightedInfo = ({
    items,
    title,
    children,
    nbCols = 5,
}: {
    items: HighlightedInfoItem[];
    title?: string | React.ReactNode;
    children?: React.ReactNode;
    nbCols?: number;
}) => {
    return (
        <Stack
            className={cx.root}
            style={{ flexDirection: 'column', alignItems: 'start' }}
            visibleFrom="md"
            data-highlighted-info
        >
            {typeof title === 'string' ? <DashboardTitle>{title}</DashboardTitle> : title}

            <CarouselSection nbCols={nbCols}>
                {items.map((item) => (
                    <DashboardHighlightedInfoCard key={item.value} {...item} />
                ))}
            </CarouselSection>

            {children}
        </Stack>
    );
};

const DashboardHighlightedInfoCard = ({
    icon,
    title,
    rightSection,
    description,
    ...rest
}: HighlightedInfoItem & CardProps) => {
    return (
        <Card p="xs" h="100%" ta="left" {...rest} data-highlighted-info-item>
            <Stack gap="xs">
                <ThemeIcon color="brand" size="lg" radius="xl">
                    {icon}
                </ThemeIcon>

                {rightSection && (
                    <Box
                        style={{
                            position: 'absolute',
                            top: 'var(--mantine-spacing-xs)',
                            right: 'var(--mantine-spacing-xs)',
                        }}
                    >
                        {rightSection}
                    </Box>
                )}

                <Stack gap="xs">
                    <Text fw={700}>{title}</Text>
                    <Text>{description}</Text>
                </Stack>
            </Stack>
        </Card>
    );
};

export { DashboardHighlightedInfo, DashboardHighlightedInfoCard };
