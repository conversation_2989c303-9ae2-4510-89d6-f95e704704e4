import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

import { getCollection } from '@/helpers';

import { Diagram, DiagramComponentInstance } from '@repo/dcide-component-models';

const name = 'generateGenerationProfile';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: 'Creates a generation profile structure for the given diagram in a csv format',
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {} as const;

const handler = async (message: DiagramAIMessage) => {
    const diagram = (await getCollection('projectDesignDiagrams', message.diagram)) as Diagram;

    const generators: string[] = [];
    const generationTypes = [
        'solar',
        'wind',
        'hydro',
        'generator',
        // 'load',
        // 'charger',
        // 'light',
        // 'hvac',
        // 'motor',
    ];

    Object.values(diagram.componentInstances).forEach((componentInstance: DiagramComponentInstance) => {
        if (generationTypes.includes(componentInstance.componentType)) {
            generators.push(`${componentInstance.designator}.power`);
        }
    });

    return `time,${generators.join(',')}`;
};

const generateGenerationProfile = {
    name,
    definition,
    actions,
    handler,
};

export { generateGenerationProfile };
