.field {
    flex-grow: 1;
    flex-shrink: 1;

    input {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}

.button {
    display: flex;
    align-items: center;
    justify-content: center;

    width: 30px;
    height: 30px;

    background-color: var(--mantine-color-white);

    border: 1px solid var(--mantine-color-gray-2);
    border-left: 0;

    border-bottom-right-radius: var(--mantine-radius-xs);
    border-top-right-radius: var(--mantine-radius-xs);

    color: var(--mantine-color-gray-6);
    font-size: 12px;

    transition: background-color .2s ease, color .2s ease;

    &:hover,
    &:focus {
        background-color: var(--mantine-color-gray-0);
        color: var(--mantine-color-text);
    }
}
