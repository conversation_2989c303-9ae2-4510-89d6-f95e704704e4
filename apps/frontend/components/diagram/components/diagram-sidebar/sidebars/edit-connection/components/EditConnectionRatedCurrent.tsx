import React, { FC } from 'react';

import { Box, Collapse, Divider, Group, Loader, Paper, Stack, Text, Tooltip } from '@mantine/core';
import { TbInfoCircle } from 'react-icons/tb';

import { CurrentField } from 'components/component-fields/CurrentField';

import { useDisclosure } from '@mantine/hooks';
import { useController } from 'react-hook-form';

import { FormatHelpers } from 'helpers/formatters';
import { CalculateWorstCaseLoadButton } from 'components/diagram/components/diagram-sidebar/sidebars/edit-connection/components/CalculateWorstCaseLoadButton';
import { useWireSizings } from 'components/diagram/hooks/use-wire-sizings';

const EditConnectionRatedCurrent: FC = () => {
    const [expanded, handlers] = useDisclosure(false);

    const { running } = useWireSizings();

    const {
        field: { value: requiredCurrent },
    } = useController({
        name: 'requirements.current',
    });

    const title =
        requiredCurrent.value !== null
            ? `Required Current: ${FormatHelpers.formatCurrent(requiredCurrent)}`
            : 'Required Current: not set / calculated';

    return (
        <Paper
            p={8}
            withBorder
            radius="xs"
            style={{
                borderColor: 'var(--mantine-color-gray-2)',
                cursor: expanded ? 'default' : 'pointer',
            }}
            onClick={expanded ? undefined : handlers.open}
        >
            <Group justify="space-between">
                <Text size="sm" fw={600} c="blue" onClick={expanded ? handlers.close : undefined}>
                    {title}
                </Text>
                {running && <Loader size={10} color="blue" />}
                {!running && !expanded && (
                    <Tooltip
                        label={
                            <Text size="sm" ta="center">
                                The maximum current this connection will experience
                                <br />
                                This value is used to calculate the wire size
                            </Text>
                        }
                        position="bottom-end"
                        withArrow
                        disabled={expanded}
                    >
                        <TbInfoCircle size={14} />
                    </Tooltip>
                )}
            </Group>
            <Collapse in={expanded}>
                <Stack gap="xs" mt={8}>
                    <Text size="sm">
                        Automatically calculate the required current based on the worst-case loading conditions for your
                        system.
                    </Text>
                    <Box>
                        <CalculateWorstCaseLoadButton />
                    </Box>
                    <Divider label="Or specify manually" />
                    <CurrentField name="requirements.current" label="Current" fields={['value']} />
                </Stack>
            </Collapse>
        </Paper>
    );
};

export { EditConnectionRatedCurrent };
