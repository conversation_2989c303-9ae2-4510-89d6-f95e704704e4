import { Children, FC } from 'react';

import { useHotkeys } from 'react-hotkeys-hook';

import { Button, ButtonGroupProps, FileButton, Stack, Tooltip } from '@mantine/core';
import { IMAGE_MIME_TYPE } from '@mantine/dropzone';

import { IoChatbubbleOutline, IoImageOutline, IoMoveOutline, IoSquareOutline, IoTextOutline } from 'react-icons/io5';

import { RxCursorArrow } from 'react-icons/rx';

import { DiagramService } from 'components/diagram/services/DiagramService';
import { ImageService } from 'components/diagram/services';

import { useCurrentProject } from 'hooks/use-current-project';
import { useCurrentUser } from 'hooks/use-current-user';

import { useDiagramComments, useDiagramMode } from 'components/diagram/hooks';

import cx from './DiagramToolbar.module.css';
import {
    FeatureLimit,
    PermissionDiagramGroups,
    PermissionDiagramImages,
    PermissionDigramComments,
} from '@repo/dcide-component-models';
import { usePermission } from 'hooks/use-permission';
import { openContextModal } from '@mantine/modals';
import { useCheckFeatureLimit } from 'hooks/use-check-feature-limit';
import { FileSizeLimitExceededError } from 'services/FileService';
import { LocalNotificationService } from 'services/LocalNotificationService';

const DiagramToolbar: FC = () => {
    const comments = useDiagramComments();
    const user = useCurrentUser();
    const project = useCurrentProject();
    const canCreateComment = usePermission(PermissionDigramComments.CREATE);
    const canCreateGroup = usePermission(PermissionDiagramGroups.CREATE);
    const canCreateImages = usePermission(PermissionDiagramImages.CREATE);
    const canImagesTeaser = usePermission(PermissionDiagramImages.TEASER);

    const { mode } = useDiagramMode();
    const diagramId = DiagramService.getId();

    const { status } = useCheckFeatureLimit(FeatureLimit.IMAGES, diagramId);
    const limitReached = !status?.pass;

    useHotkeys('v', () => {
        DiagramService.setMode('edit');
    });
    useHotkeys('h', () => {
        DiagramService.setMode('navigate');
    });
    useHotkeys(
        'c',
        () => {
            DiagramService.setMode('comment');
        },
        {
            enabled: canCreateComment,
        },
    );

    useHotkeys(
        'g',
        () => {
            DiagramService.setMode('add-group');
        },
        {
            enabled: canCreateGroup,
        },
    );

    useHotkeys(
        't',
        () => {
            DiagramService.setMode('add-textarea');
        },
        {
            enabled: canCreateGroup,
        },
    );

    const hasUnseenComments = comments.some((comment) => {
        return !comment.seen;
    });

    return (
        <Stack gap="xs">
            <DiagramToolbarButtonGroup className={cx.toolbar} orientation="vertical">
                <Tooltip label="Select - V" position="bottom" withArrow>
                    <Button
                        onClick={() => {
                            DiagramService.setMode('edit');
                        }}
                        data-active={mode === 'edit'}
                    >
                        <RxCursorArrow size={16} strokeWidth={0.1} />
                    </Button>
                </Tooltip>
                <Tooltip label="Move - H" position="bottom" withArrow>
                    <Button
                        onClick={() => {
                            DiagramService.setMode('navigate');
                        }}
                        data-active={mode === 'navigate'}
                    >
                        <IoMoveOutline size={16} strokeWidth={1.5} />
                    </Button>
                </Tooltip>
            </DiagramToolbarButtonGroup>
            <DiagramToolbarButtonGroup className={cx.toolbar} orientation="vertical">
                {canCreateComment && (
                    <Tooltip label="Comment - C" position="bottom" withArrow>
                        <Button
                            onClick={() => {
                                DiagramService.setMode('comment');
                            }}
                            data-active={mode === 'comment'}
                            data-indicator={hasUnseenComments}
                        >
                            <IoChatbubbleOutline size={16} strokeWidth={1.5} />
                        </Button>
                    </Tooltip>
                )}
                {(canImagesTeaser || canCreateImages) && (
                    <Tooltip
                        label={
                            !user
                                ? 'Log in to add images to project'
                                : !project
                                  ? 'Save the design to add images'
                                  : 'Add image to canvas'
                        }
                        position="bottom"
                        withArrow
                    >
                        <FileButton
                            onChange={(file) => {
                                if (file) {
                                    ImageService.addInCenter(file).catch((error) => {
                                        if (error instanceof FileSizeLimitExceededError) {
                                            LocalNotificationService.showError({
                                                title: error.title,
                                                message: error.message,
                                            });
                                        }
                                    });
                                }
                            }}
                            accept={IMAGE_MIME_TYPE.join(',')}
                        >
                            {({ onClick }) => (
                                <Button
                                    onClick={() => {
                                        if (limitReached) {
                                            openContextModal({
                                                modal: 'featureLimitTracker',
                                                innerProps: {
                                                    feature: FeatureLimit.IMAGES,
                                                    diagramId,
                                                },
                                                withCloseButton: false,
                                            });
                                        } else {
                                            onClick();
                                        }
                                    }}
                                    disabled={!canCreateImages}
                                >
                                    <IoImageOutline size={16} strokeWidth={1.5} />
                                </Button>
                            )}
                        </FileButton>
                    </Tooltip>
                )}
                {canCreateGroup && (
                    <Tooltip label="Draw group - G" position="bottom" withArrow>
                        <Button
                            onClick={() => {
                                DiagramService.setMode('add-group');
                            }}
                            data-active={mode === 'add-group'}
                        >
                            <IoSquareOutline size={16} strokeWidth={1.5} />
                        </Button>
                    </Tooltip>
                )}
                {canCreateGroup && (
                    <Tooltip label="Add text - T" position="bottom" openDelay={1250} withArrow>
                        <Button
                            onClick={() => {
                                DiagramService.setMode('add-textarea');
                            }}
                            data-active={mode === 'add-textarea'}
                        >
                            <IoTextOutline size={16} strokeWidth={1.5} />
                        </Button>
                    </Tooltip>
                )}
            </DiagramToolbarButtonGroup>
        </Stack>
    );
};

const DiagramToolbarButtonGroup = (props: ButtonGroupProps) => {
    const hasChildren = !!Children.toArray(props.children).filter(Boolean).length;

    if (!hasChildren) return null;

    return <Button.Group {...props} />;
};

export { DiagramToolbar };
