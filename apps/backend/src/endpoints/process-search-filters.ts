import type { Endpoint } from 'payload';

import { handleEndpointError } from '../helpers';
import { AiSearchFiltersHelpers } from '@repo/dcide-component-models';

export const processSearchFilters: Endpoint = {
    path: '/process-search-filters',
    method: 'post',
    handler: async (request) => {
        try {
            const aiResult = await request.json!();
            const processedResult = AiSearchFiltersHelpers.parseSearchFiltersResponse(aiResult);

            return Response.json(processedResult);
        } catch (error) {
            return handleEndpointError(error);
        }
    },
};
