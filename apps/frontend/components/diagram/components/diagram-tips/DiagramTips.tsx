import React, { useState, FC, useEffect } from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box, Button, Group, Transition } from '@mantine/core';
import { TbX } from 'react-icons/tb';
import { IoInformationCircleOutline } from 'react-icons/io5';

import { DiagramTip } from './DiagramTip';

import { TipService } from 'components/diagram/services/TipService';
import { state as sidebarState, SidebarType } from 'components/diagram/state/sidebar';
import { useInitialized } from 'components/diagram/hooks';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { useTipsNavigation } from 'components/diagram/components/diagram-tips/hooks/useTipsNavigation';

import cx from './diagram-tips.module.css';
import { useTimeout } from '@mantine/hooks';
import { useDiagramEmpty } from 'components/diagram/hooks/use-diagram-empty';

const DiagramTips: FC = () => {
    const [opened, setOpened] = useState(false);
    const { start, clear } = useTimeout(() => setOpened(true), 2000);

    useEffect(() => {
        start();

        return clear;
    }, [start, clear]);

    const initialized = useInitialized();
    const isEmpty = useDiagramEmpty();
    const sidebar = useSnapshot(sidebarState);

    const tips = initialized.diagram ? TipService.get() : [];

    const { currentTip, TipsNav } = useTipsNavigation(tips.length);

    const tip = tips[currentTip];

    const close = () => {
        setOpened(false);

        tips.forEach((tip) => TipService.dismiss(tip));
    };

    const mounted = !isEmpty && tip && !!tips.length && opened && sidebar.type !== SidebarType.TIPS;

    if (!tip) return null;

    return (
        <Transition mounted={mounted} transition="slide-left" duration={200} timingFunction="ease">
            {(style) => (
                <Box className={cx.tips} style={style}>
                    <Group className={cx.tipsFooter}>
                        {TipsNav}

                        <Group gap={4}>
                            <Button
                                size="compact-xs"
                                variant="outline"
                                leftSection={<IoInformationCircleOutline size={16} />}
                                onClick={() => SidebarService.open({ type: SidebarType.TIPS })}
                            >
                                View all tips
                            </Button>
                            <Button size="compact-xs" variant="outline" onClick={close} leftSection={<TbX size={14} />}>
                                Dismiss all
                            </Button>
                        </Group>
                    </Group>

                    <Box className={cx.tipsContent}>
                        <DiagramTip tip={tip} />
                    </Box>
                </Box>
            )}
        </Transition>
    );
};

export { DiagramTips };
