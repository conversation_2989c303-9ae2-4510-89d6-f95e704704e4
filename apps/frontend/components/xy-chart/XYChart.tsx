import { Card, Flex, Overlay, Text } from '@mantine/core';

import { SimpleButton } from 'elements/buttons';

import { Chart, ChartPoint, ChartProps } from './components/Chart';
import { ChartForm, ChartFormProps } from './components/Form';

import { useDisclosure } from '@mantine/hooks';

import cx from './Chart.module.css';

export type Series = {
    value: number[];
    unit: string;
};

type Props = ChartProps & {
    title: string;
    fields: ChartFormProps['fields'];
    xSeries: Series;
    xUnitConverter?: (value: number) => number;
    ySeries: Series;
    yUnitConverter?: (value: number) => number;
    updateXYSeries: (data: { x: number[]; y: number[] }) => void;
    additionalButtons?: {
        label: string;
        onClick: () => void;
        disabled?: boolean;
    }[];
};

const XYChart = ({
    title,
    fields,
    xSeries,
    ySeries,
    updateXYSeries,
    xUnitConverter,
    yUnitConverter,
    additionalButtons,
    ...props
}: Props) => {
    const data: ChartPoint[] = xSeries.value.map((_, index) => ({
        x: xSeries.value[index],
        y: ySeries.value[index],
    }));

    const [formOpened, formControls] = useDisclosure();

    return (
        <Card pos="relative" withBorder radius="xs" p="xs" bg="gray.0">
            <Flex align="center" gap={4} mb="xs">
                <Text fw={600} fz="xs">
                    {title}
                </Text>
                <SimpleButton className={cx.button} onClick={formControls.open} ml="auto">
                    Edit
                </SimpleButton>
                {additionalButtons?.map(({ label, onClick, disabled }) => (
                    <SimpleButton key={label} className={cx.button} onClick={onClick} disabled={disabled}>
                        {label}
                    </SimpleButton>
                ))}
            </Flex>
            <Chart
                data={data}
                handleDotClick={formControls.open}
                xUnit={xSeries.unit}
                yUnit={ySeries.unit}
                {...props}
            />

            {formOpened && <Overlay color="black" backgroundOpacity={0.1} />}

            <ChartForm
                fields={fields}
                opened={formOpened}
                close={formControls.close}
                xSeries={xSeries}
                ySeries={ySeries}
                handleSubmit={updateXYSeries}
                xUnitConverter={xUnitConverter}
                yUnitConverter={yUnitConverter}
            />
        </Card>
    );
};

export { XYChart };
