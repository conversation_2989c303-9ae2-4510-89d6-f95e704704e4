.root {
    gap: var(--mantine-spacing-sm);

    padding: var(--mantine-spacing-xl);
}

.option {
    cursor: pointer;

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;

    font-weight: 500;

    svg {
        flex-shrink: 0;

        margin-left: auto;
    }

    &:hover {
        box-shadow: var(--mantine-shadow-xs);
    }
}

.checkbox {
    :global(.mantine-Checkbox-label) {
        font-size: var(--mantine-font-size-md);
        font-weight: 500;
    }
}
