import { FC } from 'react';
import { File } from '@repo/dcide-component-models';

import { Image, ImageProps } from '@mantine/core';

import { useFile } from 'hooks/use-file';

import cx from './IKImage.module.css';

import { config } from 'config';

// TODO: add support for sizes?
export type IKImageProps = Omit<ImageProps, 'src'> & {
    width: number;
    height?: number;
    alt?: string;
    disableCrop?: boolean;
    fileOrId: File | File['id'];
};

const IKImage: FC<IKImageProps> = ({ fileOrId, width, height, disableCrop, ...props }) => {
    const { file } = useFile(fileOrId);

    const transform = [width ? `w-${width}` : '', height ? `h-${height}` : '', 'dpr-2', disableCrop && 'cm-pad_resize']
        .filter(Boolean)
        .join(',');

    const dimensions = {
        width: file?.width ?? width,
        height: file?.height ?? height,
    };

    if (width && height) {
        const ratio = width / height;

        dimensions.width = width;
        dimensions.height = height * ratio;
    }

    const src = config.imageKit ? `${config.imageKit}/tr:${transform}/${file.prefix}/${file.filename}` : file.url;

    return file ? (
        <Image
            src={src}
            width={dimensions.width}
            height={dimensions.height}
            className={`${cx.image} ${props.className}`}
            {...props}
        />
    ) : (
        <Image
            src={null}
            width={dimensions.width}
            height={dimensions.height}
            className={`${cx.image} ${props.className}`}
            {...props}
        />
    );
};

export { IKImage };
