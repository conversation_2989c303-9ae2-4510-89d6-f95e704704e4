import { useState } from 'react';
import { useController } from 'react-hook-form';

import {
    Box,
    FileButton,
    Input,
    InputWrapperProps,
    Button,
    Space,
    Loader,
    UnstyledButton,
    ButtonProps,
    Text,
    FileButtonProps,
} from '@mantine/core';
import { IoCloudUploadOutline, IoTrashOutline, IoDocument } from 'react-icons/io5';

import { FileService, FileSizeLimitExceededError } from 'services/FileService';

import { useFile } from 'hooks/use-file';

import cx from './FileField.module.scss';
import { LocalNotificationService } from 'services/LocalNotificationService';

type FileFieldProps = InputWrapperProps & {
    name: string;
    group: string;
    buttonLabel: string;
    buttonProps?: ButtonProps;
    fileButtonProps?: Partial<FileButtonProps>;
};

const FileField = ({ name, group, buttonLabel, buttonProps, fileButtonProps, ...props }: FileFieldProps) => {
    const {
        field: { value, onChange },
        fieldState: { error },
    } = useController({ name });

    const [uploading, setUploading] = useState(false);
    const { file } = useFile(value);

    const handleUpload = async (file: File) => {
        try {
            setUploading(true);

            const serverFile = await FileService.create({
                file,
                group,
            });

            onChange(serverFile.id);
            setUploading(false);
        } catch (error) {
            if (error instanceof FileSizeLimitExceededError) {
                LocalNotificationService.showError({ title: error.title, message: error.message });
            }

            console.log(error);
        }
    };

    const handleDelete = () => {
        onChange('');
    };

    return (
        <Input.Wrapper
            {...props}
            error={
                <Text component="span" inherit mt={4}>
                    {error?.message}
                </Text>
            }
        >
            {uploading || file ? (
                <Box className={cx.preview}>
                    {uploading && <Loader size="xs" color="dimmed" />}
                    {file && (
                        <>
                            <IoDocument />
                            <Box className={cx.previewText}>{file.name}</Box>
                            <UnstyledButton className={cx.previewDelete} onClick={handleDelete}>
                                <IoTrashOutline />
                            </UnstyledButton>
                        </>
                    )}
                </Box>
            ) : (
                <Box>
                    <Space h="4" />
                    <FileButton onChange={(file) => file && handleUpload(file)} {...fileButtonProps}>
                        {(button) => (
                            <Button
                                {...button}
                                variant="default"
                                leftSection={<IoCloudUploadOutline />}
                                {...buttonProps}
                            >
                                {buttonLabel}
                            </Button>
                        )}
                    </FileButton>
                </Box>
            )}
        </Input.Wrapper>
    );
};

export { FileField };
