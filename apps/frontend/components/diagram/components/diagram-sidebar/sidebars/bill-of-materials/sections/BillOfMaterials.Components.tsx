import { useEffect, useState } from 'react';

import { Flex, Loader, Text, Transition } from '@mantine/core';

import { BillOfMaterials, BillOfMaterialsPayload } from '@repo/dcide-component-models';

import { BillOfMaterialsService } from 'services/BillOfMaterialsService';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { LocalNotificationService } from 'services/LocalNotificationService';

import { DiagramSidebar } from 'components/diagram';
import { useBillOfMaterials } from 'components/diagram/hooks';

import { CheckboxButton } from 'components/checkbox-button/CheckboxButton';

import { BillOfMaterialsGrouped } from './BillOfMaterials.Grouped';
import { BillOfMaterialsUngrouped } from './BillOfMaterials.Ungrouped';

import { useFormContext } from 'react-hook-form';
import { Form, FormOnSubmit, useSubmitForm } from 'components/forms/Form';
import { FormSubmit } from 'components/forms/FormSubmit';

import cx from './BillOfMaterials.Components.module.css';

enum BillOfMaterialsView {
    GROUPED = 'grouped',
    UNGROUPED = 'ungrouped',
}

const BillOfMaterialsComponents = () => {
    const submit = useSubmitForm();
    const { formState } = useFormContext();

    const [currentView, setCurrentView] = useState<BillOfMaterialsView>(BillOfMaterialsView.UNGROUPED);

    const { billOfMaterials, isLoading } = useBillOfMaterials();

    const components = billOfMaterials?.components ?? [];
    const componentInstances = billOfMaterials?.componentInstances ?? [];

    const allComponents = [...components, ...componentInstances];

    const hasChanges = Object.keys(formState.dirtyFields).length > 0;

    useEffect(() => {
        if (hasChanges) {
            SidebarService.setConfirmModal({
                title: 'Update Bill of Materials?',
                children: 'You have unsaved changes. Do you want to save them?',
                labels: {
                    confirm: 'Save changes',
                    cancel: 'Discard changes',
                },
                onConfirm: async () => {
                    await submit();
                },
            });
        } else {
            SidebarService.setConfirmModal(null);
        }
    }, [hasChanges]);

    return (
        <DiagramSidebar.Section padding={0} className={cx.list}>
            {isLoading ? (
                <Loader size="sm" p="md" />
            ) : allComponents?.length ? (
                <>
                    <CheckboxButton
                        m="xs"
                        display="inline-flex"
                        label="Show per group"
                        checked={currentView === BillOfMaterialsView.GROUPED}
                        setChecked={(checked) =>
                            setCurrentView(checked ? BillOfMaterialsView.GROUPED : BillOfMaterialsView.UNGROUPED)
                        }
                    />
                    {currentView === BillOfMaterialsView.UNGROUPED ? (
                        <BillOfMaterialsUngrouped />
                    ) : (
                        <BillOfMaterialsGrouped />
                    )}
                </>
            ) : (
                <Text c="dimmed" p="md">
                    Bill of materials is not available yet.
                </Text>
            )}
            <Transition mounted={hasChanges} transition="fade-up" duration={200}>
                {(styles) => (
                    <Flex pb="sm" justify="center" style={{ ...styles, position: 'sticky', bottom: 0, zIndex: 1 }}>
                        <FormSubmit variant="gradient">Save changes</FormSubmit>
                    </Flex>
                )}
            </Transition>
        </DiagramSidebar.Section>
    );
};

const WrappedBillOfMaterialsComponents = () => {
    const { billOfMaterials, mutate } = useBillOfMaterials();

    const onSubmit: FormOnSubmit<BillOfMaterials> = async (values) => {
        try {
            if (!billOfMaterials || !billOfMaterials.id) return;

            const cleanValues: BillOfMaterialsPayload = {
                ...values,
                components: values.components?.map((component) => ({
                    ...component,
                    component: component.component.id,
                })),
            };

            const result = await BillOfMaterialsService.update(billOfMaterials.id, cleanValues);

            if (!result.doc) {
                throw new Error();
            }

            await mutate(result, { revalidate: false });
        } catch {
            LocalNotificationService.showError({ message: 'Could not update bill of materials' });
        }
    };

    return (
        <Form<BillOfMaterials> onSubmit={onSubmit} defaultValues={billOfMaterials}>
            <BillOfMaterialsComponents />
        </Form>
    );
};

export { WrappedBillOfMaterialsComponents as BillOfMaterialsComponents };
