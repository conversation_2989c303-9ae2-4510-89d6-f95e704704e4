import React, { FC } from 'react';

import { Form } from 'components/forms/Form';

import { InlineFiltersSectionRemove } from 'components/inline-filters/InlineFilters.SectionRemove';
import { InlineFiltersWrapper, InlineFiltersWrapperProps } from 'components/inline-filters/InlineFilters.Wrapper';
import { InlineFiltersSection, InlineFiltersSectionWithPopover } from 'components/inline-filters/InlineFilters.Section';
import { InlineFiltersMeasurementWrapper } from 'components/inline-filters/InlineFilters.MeasurementWrapper';
import { InlineFiltersSectionWithIcon } from 'components/inline-filters/InlineFilters.SectionWithIcon';

import { InlineFiltersEvent } from 'components/component-overview/inline-filters/filters/InlineFilters.Event';

import { InlineFiltersSearch } from './InlineFilters.Search';
import { InlineFiltersServices } from './InlineFilters.Services';
import { InlineFiltersManufacturer } from './InlineFilters.Manufacturer';

import { ProfileOverviewService } from 'components/profile-overview/services/ProfileOverviewService';
import { InlineFiltersMoreButton } from 'components/profile-overview/inline-filters/InlineFilters.MoreButton';
import { InlineFiltersCompliances } from 'components/profile-overview/inline-filters/InlineFilters.Compliances';
import { InlineFiltersSystemSize } from 'components/profile-overview/inline-filters/InlineFilters.SystemSize';
import { InlineFiltersInAppSupport } from 'components/profile-overview/inline-filters/InlineFilters.InAppSupport';
import { InlineFiltersApplication } from 'components/profile-overview/inline-filters/InlineFilters.Application';
import { InlineFiltersLocation } from 'components/profile-overview/inline-filters/InlineFilters.Location';
import { InlineFiltersProjectBudget } from 'components/profile-overview/inline-filters/InlineFilters.ProjectBudget';

const InlineFilters: FC<InlineFiltersWrapperProps> & {
    Wrapper: typeof InlineFiltersWrapper;
    MeasurementWrapper: typeof InlineFiltersMeasurementWrapper;
    Section: typeof InlineFiltersSection;
    SectionWithIcon: typeof InlineFiltersSectionWithIcon;
    SectionWithPopover: typeof InlineFiltersSectionWithPopover;
    RemoveButton: typeof InlineFiltersSectionRemove;
    Search: typeof InlineFiltersSearch;
    Services: typeof InlineFiltersServices;
    Application: typeof InlineFiltersApplication;
    More: typeof InlineFiltersMoreButton;
    Compliances: typeof InlineFiltersCompliances;
    Manufacturer: typeof InlineFiltersManufacturer;
    SystemSize: typeof InlineFiltersSystemSize;
    ProjectBudget: typeof InlineFiltersProjectBudget;
    InAppSupport: typeof InlineFiltersInAppSupport;
    Location: typeof InlineFiltersLocation;
} = (props) => {
    return (
        <Form onSubmit={() => {}}>
            <InlineFilters.Wrapper variant="dark" {...props}>
                <InlineFiltersEvent search={ProfileOverviewService.search} />
                <InlineFilters.Search />
                <InlineFilters.Services />
                <InlineFilters.Application />
                <InlineFilters.SystemSize />
                <InlineFilters.ProjectBudget />
                <InlineFilters.Location />
                <InlineFilters.Manufacturer />
                <InlineFilters.Compliances />
                <InlineFilters.More />
            </InlineFilters.Wrapper>
        </Form>
    );
};

InlineFilters.Wrapper = InlineFiltersWrapper;
InlineFilters.MeasurementWrapper = InlineFiltersMeasurementWrapper;
InlineFilters.Section = InlineFiltersSection;
InlineFilters.SectionWithIcon = InlineFiltersSectionWithIcon;
InlineFilters.SectionWithPopover = InlineFiltersSectionWithPopover;
InlineFilters.RemoveButton = InlineFiltersSectionRemove;
InlineFilters.Search = InlineFiltersSearch;
InlineFilters.Services = InlineFiltersServices;
InlineFilters.Application = InlineFiltersApplication;
InlineFilters.More = InlineFiltersMoreButton;
InlineFilters.Compliances = InlineFiltersCompliances;
InlineFilters.Manufacturer = InlineFiltersManufacturer;
InlineFilters.SystemSize = InlineFiltersSystemSize;
InlineFilters.ProjectBudget = InlineFiltersProjectBudget;
InlineFilters.InAppSupport = InlineFiltersInAppSupport;
InlineFilters.Location = InlineFiltersLocation;

export { InlineFilters };
