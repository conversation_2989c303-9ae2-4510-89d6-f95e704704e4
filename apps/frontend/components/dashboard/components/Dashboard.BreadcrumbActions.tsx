import React, { FC } from 'react';

import { Button, ButtonGroup, Menu, MenuItem } from '@mantine/core';
import { IoAddSharp, IoChevronDown } from 'react-icons/io5';

import { CompanyProfileService } from 'services/CompanyProfileService';
import { ComponentService } from 'services/ComponentService';
import { ProjectService } from 'services/ProjectService';

import cx from './Dashboard.BreadcrumbActions.module.css';

const DashboardBreadcrumbActions: FC<{
    isManufacturer?: boolean;
    hasCompany?: boolean;
}> = ({ isManufacturer = false, hasCompany = false }) => {
    const createCompany = () => {
        CompanyProfileService.navigate.create().then();
    };

    const createComponent = () => {
        ComponentService.navigate.create().then();
    };

    const createProject = () => {
        ProjectService.navigate.create().then();
    };

    const actions: {
        label: string;
        onClick: () => void;
    }[] = [];

    if (!hasCompany) {
        actions.push({ label: 'Create profile', onClick: createCompany });
    }

    if (isManufacturer || hasCompany) {
        actions.push({ label: 'Add product', onClick: createComponent });
    }

    actions.push({ label: 'Create project', onClick: createProject });

    const mainAction = actions[0];

    return (
        <ButtonGroup>
            <Button size="xs" onClick={mainAction.onClick} className={cx.button} leftSection={<IoAddSharp size={12} />}>
                {mainAction.label}
            </Button>

            {actions.length > 1 && (
                <Menu trigger="click-hover" offset={3}>
                    <Menu.Target>
                        <Button size="xs" className={cx.trigger}>
                            <IoChevronDown size={12} />
                        </Button>
                    </Menu.Target>

                    <Menu.Dropdown>
                        {actions.map(({ label, onClick }, index) => (
                            <MenuItem key={index} onClick={onClick} leftSection={<IoAddSharp size={12} />}>
                                {label}
                            </MenuItem>
                        ))}
                    </Menu.Dropdown>
                </Menu>
            )}
        </ButtonGroup>
    );
};

export { DashboardBreadcrumbActions };
