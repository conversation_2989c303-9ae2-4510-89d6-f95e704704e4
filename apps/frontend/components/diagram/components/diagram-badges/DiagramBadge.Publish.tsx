import React from 'react';

import { Tooltip, UnstyledButton } from '@mantine/core';
import { openContextModal } from '@mantine/modals';
import { IoEyeOffSharp } from 'react-icons/io5';

import { PermissionDiagramElements } from '@repo/dcide-component-models';

import { usePermission } from 'hooks/use-permission';
import { useCurrentUser } from 'hooks/use-current-user';
import { useCurrentProject } from 'hooks/use-current-project';
import { useDiagramEmpty } from 'components/diagram/hooks/use-diagram-empty';

import cx from './DiagramBadges.module.css';

const DiagramBadgePublish = () => {
    const currentUser = useCurrentUser();
    const project = useCurrentProject();
    const canEdit = usePermission(PermissionDiagramElements.EDIT);

    const isReferenceDesign = project?.isReferenceDesign;
    const isPublished = project?.visibility === 'marketplace';
    const isDiagramEmpty = useDiagramEmpty();

    const handle = () => {
        openContextModal({
            modal: 'publishDesign',
            innerProps: {},
            size: 'lg',
            withCloseButton: false,
        });
    };

    if (!isReferenceDesign) return null;
    if (!currentUser) return null;
    if (isPublished) return null;
    if (!canEdit) return null;

    const tooltip = isDiagramEmpty ? 'Diagram is empty' : !isPublished ? 'Design is not yet published' : '';

    return (
        <Tooltip label={tooltip} disabled={!tooltip}>
            <UnstyledButton onClick={handle} disabled={isDiagramEmpty} className={cx.badge}>
                <IoEyeOffSharp fill="var(--mantine-color-orange-6)" />
                <span>Publish design</span>
            </UnstyledButton>
        </Tooltip>
    );
};

export { DiagramBadgePublish };
