import { OpenAI } from 'openai';
import { DiagramAIMessage } from '@/payload-types';

import { generateToolPrompt } from '../helpers/generate-tool-prompt';
import {getCollection, getId} from '@/helpers';
import {PayloadService} from '@/services/PayloadService';
import {config} from '@/config';
import fs from 'fs';
import { Readable } from 'stream';
import * as http from 'node:http';

const name = 'searchDiagramFiles';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: 'Searches the file attachments for this diagram',
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
} as const;

const actions = {} as const;

const handler = async (message: DiagramAIMessage) => {
    const payload = await PayloadService.getPayload();
    const openai = new OpenAI({
        apiKey: config.openai.apiKey,
    });

    const { docs: [design] } = await payload.find({
        collection: 'projectDesigns',
        where: {
            diagrams: {
                in: getId(message.diagram),
            },
        },
        depth: 0,
        limit: 1,
    });

    const { docs: files } = await payload.find({
        collection: 'files',
        where: {
            id: {
                in: design.files?.map((file: any) => file.file),
            },
        },
        depth: 0,
        limit: 100,
    });

    const vectorStore = await openai.vectorStores.create({
        name: `Diagram Vector Store: ${message.diagram}`,
        expires_after: {
            anchor: 'last_active_at',
            days: 1,
        },
    });

    const fileStreams = [];

    for (const file of files) {
        const response = await fetch(file.url!);

        fileStreams.push(response.body!);
    }

    const file = files[0]!;
    const xxx = await fetch(files[0]!.url!);

    if (xxx.body) {
        await openai.vectorStores.files.uploadAndPoll(vectorStore.id, {
            files: [
                {
                    name: file.id,
                    content: xxx.body!,
                }
            ]
        });
    }


    const response = await openai.responses.create({
        model: "gpt-4o",
        tools: [
            {
                type: "file_search",
                vector_store_ids: [vectorStore.id],
            }
        ],
        input: message.question!,
    });

    return response.output_text;
};

const prompt = generateToolPrompt({
    name: 'Diagram File Search',
    objective: `
        To address user questions or requests related to searching the file attachments for this diagram.
    `,
    activationCondition: `
        Only activate this section if the user explicitly asks a question or makes a request
        directly related to searching the file attachments for this diagram.
    `,
    exampleQuestions: [
        'Can you search the files for ...?',
        'Searching the files for this diagram, can you find ...?',
    ],
    mandatoryToolCalling: `You must call the ${name} function to search the diagram files.`,
    handler: `

    `,
});

const searchDiagramFiles = {
    name,
    definition,
    actions,
    handler,
    prompt,
};

export { searchDiagramFiles };
