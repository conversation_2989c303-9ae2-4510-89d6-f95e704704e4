import React, { <PERSON> } from 'react';

import { Badge, Box } from '@mantine/core';
import { IoChatbubblesOutline, IoChatbubbleOutline } from 'react-icons/io5';

import { DiagramCommentThread } from 'components/diagram/components/diagram-comments/DiagramComment.Thread';
import { DiagramCommentActions } from 'components/diagram/components/diagram-comments/DiagramCommentActions';
import { DiagramCommentDisplay } from 'components/diagram';

import { CommentService } from 'components/diagram/services/CommentService';

import { useActiveCanvasComment } from 'components/diagram/hooks/use-active-canvas-comment';
import { useUser } from 'hooks/use-user';

import { DiagramComment } from '@repo/dcide-component-models';

import cx from './DiagramComment.module.css';

const DiagramCommentTeaser: FC<{
    comment: DiagramComment;
}> = ({ comment }) => {
    const messages = comment.thread.filter((item) => item.blockType === 'message');
    const numberOfReplies = messages.length - 1; // -1 because we don't want the initial message

    const activeCanvasComment = useActiveCanvasComment();

    let numberOfMessageTeasers = 0;
    numberOfMessageTeasers = messages.length > 1 ? 1 : numberOfMessageTeasers;
    numberOfMessageTeasers = messages.length > 2 ? 2 : numberOfMessageTeasers;

    const messageTeasers = messages.slice(messages.length - numberOfMessageTeasers);

    return (
        <Box
            className={cx.teaser}
            onClick={() => {
                CommentService.activateCanvasComment(comment.id);
                CommentService.deactivateCanvasPreview();

                CommentService.moveIntoView(comment);
            }}
            onMouseEnter={() => {
                CommentService.activateCanvasPreview(comment.id);
            }}
            onMouseLeave={() => {
                CommentService.deactivateCanvasPreview();
            }}
            data-indicator={!comment.seen}
            data-active={comment.id === activeCanvasComment}
        >
            <DiagramCommentThread comment={comment} teaser />
            {messageTeasers.length > 0 && (
                <Box className={cx.teaserMessagePreviews}>
                    {messageTeasers.map((message) => (
                        <DiagramCommentMessagePreview message={message} key={message.id} />
                    ))}
                </Box>
            )}
            <Box className={cx.teaserFooter}>
                {(numberOfReplies === 0 || numberOfReplies > 2) && (
                    <Box className={cx.teaserFooterReplies}>
                        <IoChatbubblesOutline />
                        {numberOfReplies === 0 && 'No replies'}
                        {numberOfReplies === 3 && '1 other reply'}
                        {numberOfReplies > 3 && `${numberOfReplies - 2} other replies`}
                    </Box>
                )}
                <DiagramCommentActions.LinkComponentInstance comment={comment}>
                    {({ linkedComponentInstance, linkedComponent }) =>
                        linkedComponentInstance ? (
                            <Badge variant="light" size="xs" radius="xs" color="blue">
                                {linkedComponent ? linkedComponent.name : `#${linkedComponentInstance.designator}`}
                            </Badge>
                        ) : null
                    }
                </DiagramCommentActions.LinkComponentInstance>
                <DiagramCommentActions.Resolve comment={comment}>
                    {({ resolved }) =>
                        resolved ? (
                            <Badge variant="light" size="xs" radius="xs" color="green">
                                Resolved
                            </Badge>
                        ) : (
                            <div />
                        )
                    }
                </DiagramCommentActions.Resolve>
                <DiagramCommentActions.Delete comment={comment}>
                    {({ deleted }) =>
                        deleted ? (
                            <Badge variant="light" size="xs" radius="xs" color="red">
                                Deleted
                            </Badge>
                        ) : (
                            <div />
                        )
                    }
                </DiagramCommentActions.Delete>
            </Box>
        </Box>
    );
};

const DiagramCommentMessagePreview: FC<{
    message: DiagramComment['thread'][number];
}> = ({ message }) => {
    const { user } = useUser(message.user);

    return message.blockType === 'message' ? (
        <Box className={cx.teaserMessagePreview}>
            <IoChatbubbleOutline />
            <Box className={cx.teaserMessagePreviewUser}>{user?.name}</Box>
            <DiagramCommentDisplay content={message.content} />
        </Box>
    ) : null;
};

export { DiagramCommentTeaser };
