import { ApiService } from './ApiService';

import { config } from 'config';
import {
    CompanyProfile,
    CompanyProfileStubFields,
    CompanyService,
    PublishedStatus,
    StubWithLocalFiles,
} from '@repo/dcide-component-models';

import { currentProfileState } from 'components/company-profile/state/current-profile';

import { UserService } from 'services/UserService';

import { ServiceHelpers } from 'helpers/ServiceHelpers';
import { CompanyProfileHelpers } from 'helpers/CompanyProfileHelpers';
import { LocalStorageService } from 'services/LocalStorageService';
import { mutate } from 'swr';

export type CompanyProfileListProps = {
    page?: number;
    depth?: number;
    query?: string;
    services?: CompanyService[];
    status?: CompanyProfile['status'];
    compliance?: CompanyProfile['compliance'];
    subscription?: CompanyProfile['subscription'];
    sort?: string;
    ids?: string[];
    internal?: boolean;
    limit?: number;
};

// safe to use depth=1 b/c we set maxDepth on relationship fields
const CompanyProfileService = {
    navigate: ServiceHelpers.createNavigator(CompanyProfileHelpers.urls),

    getSearchParams: (props?: CompanyProfileListProps) => {
        const {
            page,
            depth = 1,
            query,
            services = [],
            compliance = {},
            status,
            ids = [],
            sort = 'name',
            subscription,
            internal,
            limit,
        } = props ?? {};

        const searchParams = new URLSearchParams({
            depth: depth.toString(),
            page: page ? page.toString() : '1',
            limit: page ? '24' : '9999',
            sort,
        });

        if (query) {
            searchParams.set('where[name][like]', query);
        }

        if (status) {
            searchParams.set('where[status][equals]', status);
        }

        Object.entries(compliance).forEach(([key, value], index) => {
            searchParams.set(`where[or][${index}][compliance.${key}][equals]`, (value as boolean).toString());
        });

        if (services.length) {
            searchParams.set('where[services][in]', services.join(','));
        }

        if (subscription) {
            searchParams.set('where[subscription][equals]', subscription);
        }

        if (ids.length) {
            searchParams.set('where[id][in]', ids.join(','));
        }

        if (internal) {
            searchParams.set('where[team.createdBy.internal][equals]', 'true');
        }

        if (internal === false) {
            searchParams.set('where[team.createdBy.internal][not_equals]', 'true');
        }

        if (limit) {
            searchParams.set('limit', limit.toString());
        }

        return searchParams;
    },

    list: async (props?: CompanyProfileListProps) => {
        const searchParams = CompanyProfileService.getSearchParams(props);

        return await ApiService.get(`${config.api.backend}/manufacturers?${searchParams.toString()}`);
    },

    getRecentlyJoined: async (limit = 8) =>
        await CompanyProfileService.list({ sort: '-createdAt', limit, status: PublishedStatus.PUBLISHED }),

    refresh: async (profileId: string, data?: CompanyProfile): Promise<CompanyProfile> => {
        const updatedProfile =
            data ?? (await ApiService.get(`${config.api.backend}/manufacturers/${profileId}?depth=0`))!;

        mutate(CompanyProfileHelpers.swr.profile(profileId), updatedProfile, { revalidate: false });

        if (profileId === currentProfileState.profile?.id) {
            currentProfileState.profile = updatedProfile;
        }

        return updatedProfile;
    },

    create: async (payload: any, depth: number = 1) => {
        const result = await ApiService.post<{
            doc: CompanyProfile;
        }>(`${config.api.backend}/manufacturers?depth=${depth}`, payload, {
            throwError: true,
        });

        await UserService.refreshProgress();

        return result;
    },

    publish: async (id: string) => {
        return await ApiService.post(`${config.api.backend}/manufacturers/${id}/publish`);
    },

    get: async (manufacturerId: string, depth: number = 1) => {
        const result = await ApiService.get(`${config.api.backend}/manufacturers/${manufacturerId}?depth=${depth}`);

        if (result) {
            CompanyProfileService.refresh(result.id, result);
        }

        return result;
    },

    getByTeam: async (teamId: string, depth: number = 1) => {
        return ApiService.get(
            `${config.api.backend}/manufacturers?limit=99&where[team][equals]=${teamId}&depth=${depth}`,
        );
    },

    getBySlug: async (slug: string, depth: number = 1) => {
        const matchId = `where[or][0][id][equals]=${slug}`;
        const matchSlug = `where[or][1][slug][equals]=${slug}`;

        return ApiService.get(`${config.api.backend}/manufacturers?${matchId}&${matchSlug}&depth=${depth}`);
    },

    getMultiple: async (manufacturerIds: string[]) => {
        const params = manufacturerIds.map((id) => `where[id][in]=${id}`).join('&');

        if (!manufacturerIds.length) {
            return { docs: [] };
        }

        return await ApiService.get(`${config.api.backend}/manufacturers?limit=99&${params}`);
    },

    getServiceTags: () => {
        return ApiService.get(`${config.api.backend}/manufacturers/service-tags`);
    },

    getApplicationTags: () => {
        return ApiService.get(`${config.api.backend}/manufacturers/application-tags`);
    },

    update: async (manufacturerId: string, payload: any, depth: number = 1) => {
        const result = await ApiService.patch(
            `${config.api.backend}/manufacturers/${manufacturerId}?depth=${depth}`,
            payload,
        );

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    addSupportUser: async (manufacturerId: string, payload: any) => {
        return await ApiService.post(
            `${config.api.backend}/manufacturers/${manufacturerId}/add-support-user`,
            payload,
            { throwError: true },
        );
    },

    requestAccess: async (manufacturerId: string) => {
        return await ApiService.post(`${config.api.backend}/manufacturers/${manufacturerId}/request-access`);
    },

    moveTeam: async (manufacturerId: string, payload: { teamId: string; userId: string; companyId: string }) => {
        return await ApiService.post(`${config.api.backend}/manufacturers/${manufacturerId}/move-team`, payload);
    },

    denyAccess: async (manufacturerId: string, payload: { message: string; userId: string; userTeamId: string }) => {
        return await ApiService.post(`${config.api.backend}/manufacturers/${manufacturerId}/deny-access`, payload);
    },

    addStub: async ({
        companyId,
        data,
        field,
    }: {
        companyId: string;
        data: StubWithLocalFiles;
        field: CompanyProfileStubFields;
    }) => {
        const result = await ApiService.post(`${config.api.backend}/manufacturers/${companyId}/stub/${field}`, data);

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    editStub: async ({
        companyId,
        data,
        stubId,
        field,
    }: {
        companyId: string;
        stubId: string;
        data: Partial<StubWithLocalFiles>;
        field: CompanyProfileStubFields;
    }) => {
        const result = await ApiService.patch(
            `${config.api.backend}/manufacturers/${companyId}/stub/${field}/${stubId}`,
            data,
        );

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    deleteStub: async ({
        companyId,
        stubId,
        field,
    }: {
        companyId: string;
        stubId: string;
        field: CompanyProfileStubFields;
    }) => {
        const result = await ApiService.delete(
            `${config.api.backend}/manufacturers/${companyId}/stub/${field}/${stubId}`,
        );

        if (result?.doc) {
            CompanyProfileService.refresh(result.doc.id, result.doc);
        }

        return result;
    },

    search: async (query: string) => {
        const params = JSON.stringify({
            search: query,
        });
        return ApiService.get<{ docs: CompanyProfile[] }>(
            `${config.api.backend}/manufacturers/search?query=${params}&sort=relevance&page=0`,
        );
    },

    getLocalSignupUrl: (teamId: string) => {
        return LocalStorageService.get(`${teamId}.localCompanySignup`);
    },

    setLocalSignup: (teamId: string, companyId?: CompanyProfile['id'], step?: number) => {
        LocalStorageService.store(
            `${teamId}.localCompanySignup`,
            CompanyProfileHelpers.urls.create({ id: companyId, step }),
        );
    },

    finishLocalSignup: (teamId: string) => {
        LocalStorageService.clear(`${teamId}.localCompanySignup`);
    },
};

export { CompanyProfileService };
