import React, { FC, useEffect, useState } from 'react';

import { <PERSON>I<PERSON>, But<PERSON>, Card } from '@mantine/core';
import { IoClose } from 'react-icons/io5';

import { digestString } from 'helpers/digestString';
import { useGeneralGlobals } from 'hooks/use-general-globals';
import { LocalStorageService } from 'services/LocalStorageService';

import cx from './SidebarNav.UpdateMessage.module.css';
import { useShowDesignEditor } from 'hooks/use-show-design-editor';

const UPDATE_MESSAGE_HASH_KEY = 'update_message_hash';

const SidebarNavUpdateMessage: FC = () => {
    const {
        data: { updateMessage } = { updateMessage: undefined },
    } = useGeneralGlobals();
    const [showMessage, setShowMessage] = useState(false);
    const storedHash = LocalStorageService.get(UPDATE_MESSAGE_HASH_KEY);
    const showDesignEditor = useShowDesignEditor();

    const messageAsString = `${updateMessage?.title ?? ''}${updateMessage?.description ?? ''}${updateMessage?.label ?? ''}${updateMessage?.url ?? ''}`;

    useEffect(() => {
        if (messageAsString.length) {
            digestString(messageAsString).then((hash) => {
                if (hash !== storedHash) {
                    setShowMessage(true);
                } else {
                    setShowMessage(false);
                }
            });
        }
    }, [messageAsString, storedHash]);

    const setMessageHidden = () => {
        setShowMessage(false);

        digestString(messageAsString).then((hash) => {
            LocalStorageService.store(UPDATE_MESSAGE_HASH_KEY, hash);
        });
    };

    if (!showMessage || !showDesignEditor) return null;

    return (
        <Card className={cx.root}>
            {updateMessage?.title && <Card.Section>{updateMessage.title}</Card.Section>}

            <ActionIcon variant="subtle" size="xs" className={cx.close} onClick={setMessageHidden}>
                <IoClose />
            </ActionIcon>

            {updateMessage?.description && <span>{updateMessage.description}</span>}

            {updateMessage?.label && updateMessage?.url && (
                <Card.Section>
                    <Button
                        fullWidth
                        size="xs"
                        variant="light"
                        color="primary"
                        radius={0}
                        component="a"
                        href={updateMessage.url}
                        onClick={setMessageHidden}
                    >
                        {updateMessage.label}
                    </Button>
                </Card.Section>
            )}
        </Card>
    );
};

export { SidebarNavUpdateMessage };
