import React, { FC } from 'react';

import { Affix, Box, ScrollArea, Tabs, Transition } from '@mantine/core';
import { useHash, useWindowScroll } from '@mantine/hooks';

import { useSidebarNav } from 'hooks/use-sidebar-nav';

import { HEADER_HEIGHT } from 'components/diagram';
import { SIDEBAR_NAV_WIDTH } from 'components/sidebar-nav/SidebarNav';

import { HorizontalTabType } from 'components/horizontal-tabs/HorizontalTabs';

import cx from './HorizontalTabs.module.css';

type Props = {
    tabs: HorizontalTabType[];
    value?: string;
    defaultTab?: string;
    isSticky?: boolean;
    keepMounted?: boolean;
    onTabClick?: (tab: string) => void;
    children?: React.ReactNode;
};

const HorizontalTabsTabs: FC<Props> = ({ tabs, value, defaultTab, isSticky, keepMounted, onTabClick, children }) => {
    return (
        <Tabs
            orientation="horizontal"
            classNames={cx}
            defaultValue={defaultTab}
            value={value}
            keepMounted={keepMounted}
        >
            <HorizontalTabsTabList tabs={tabs} handleClick={onTabClick} />

            {isSticky && <HorizontalTabsStickyTabs tabs={tabs} handleClick={onTabClick} />}

            {children}
        </Tabs>
    );
};

const HorizontalTabsTabList = ({
    tabs,
    handleClick,
}: {
    tabs: HorizontalTabType[];
    handleClick?: (tab: string) => void;
}) => {
    const [, setHash] = useHash();

    return (
        <ScrollArea w="100%" type="never">
            <Tabs.List>
                {tabs.map((tab) => (
                    <Tabs.Tab
                        key={tab.value}
                        value={tab.value}
                        leftSection={tab.icon}
                        disabled={tab.disabled}
                        onClick={() => {
                            setHash(tab.value);
                            handleClick && handleClick(tab.value);
                        }}
                    >
                        {tab.label}
                    </Tabs.Tab>
                ))}
            </Tabs.List>
        </ScrollArea>
    );
};

const HorizontalTabsStickyTabs = ({
    tabs,
    handleClick,
}: {
    tabs: HorizontalTabType[];
    handleClick?: (tab: string) => void;
}) => {
    const [scroll] = useWindowScroll();
    const { isOpen } = useSidebarNav();

    return (
        <Affix position={{ top: HEADER_HEIGHT, left: isOpen ? SIDEBAR_NAV_WIDTH : 0, right: 0 }} zIndex={199}>
            <Transition transition="slide-down" mounted={scroll.y > 200} enterDelay={50}>
                {(transitionStyles) => (
                    <Box className={cx.sticky} style={transitionStyles}>
                        <HorizontalTabsTabList tabs={tabs} handleClick={handleClick} />
                    </Box>
                )}
            </Transition>
        </Affix>
    );
};

export { HorizontalTabsTabs };
