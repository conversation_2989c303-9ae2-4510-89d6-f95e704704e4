import React from 'react';

import {
    MeasurementInputWrapperProps,
    useMultiUnitMeasurementController,
    UnitWithTooltip,
    useRenderVariableMultiInput,
    MeasurementInputWrapper,
} from 'components/forms/fields/measurement';
import { MeasurementFieldType, frequencyConverter } from '@repo/dcide-component-models';

export type FrequencyFieldProps = {
    name: string;
    fallbackBaseUnit?: string;
    fields?: MeasurementFieldType[];
    hideIcons?: boolean;
} & MeasurementInputWrapperProps;

const FrequencyField = ({ name, fields = ['min', 'nom', 'max'], hideIcons, ...wrapperProps }: FrequencyFieldProps) => {
    const {
        unit,
        cycleUnit,
        fields: fieldsRecord,
    } = useMultiUnitMeasurementController({ name, converter: frequencyConverter });

    const Unit = <UnitWithTooltip onClick={cycleUnit}>{unit}</UnitWithTooltip>;
    const Fields = useRenderVariableMultiInput({
        fields,
        fieldsRecord,
        Unit,
        extraProps: {
            hideIcon: hideIcons,
            size: wrapperProps.size,
        },
    });

    return <MeasurementInputWrapper {...wrapperProps}>{Fields}</MeasurementInputWrapper>;
};

export { FrequencyField };
