import React, { FC } from 'react';

import { Box, Stack, Tooltip, UnstyledButton } from '@mantine/core';
import { TbHash, TbPlus, TbPointFilled } from 'react-icons/tb';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';

import { ChatService } from 'components/diagram/services/ChatService';

import { TeamHelpers } from 'helpers/TeamHelpers';

import { useCurrentUser } from 'hooks/use-current-user';
import { useProjectTeam } from 'hooks/use-project-team';
import { useChatChannels } from 'components/diagram/hooks/use-chat-channels';

import cx from './Chat.module.css';

const Channels: FC = () => {
    const { channels } = useChatChannels();

    const user = useCurrentUser();
    const { team } = useProjectTeam();
    const userIsTeamMember = TeamHelpers.isTeamMember(team, user);

    const activateChannel = (channelId: string) => {
        ChatService.activateChannel(channelId);
    };

    const openCreateChannelModal = () => {
        if (userIsTeamMember) {
            ChatService.openCreateChannelModal();
        }
    };

    return (
        <React.Fragment>
            <DiagramSidebar.SimpleHeader
                rightSection={
                    <Tooltip
                        label="Only team members can create channels"
                        position="bottom-end"
                        withArrow
                        arrowOffset={13}
                        disabled={userIsTeamMember}
                    >
                        <DiagramSidebar.Button onClick={openCreateChannelModal}>
                            <TbPlus />
                        </DiagramSidebar.Button>
                    </Tooltip>
                }
            >
                Channels
            </DiagramSidebar.SimpleHeader>
            <DiagramSidebar.Section>
                <Stack gap="4">
                    {channels.map((channel) => (
                        <UnstyledButton
                            className={cx.channelTeaser}
                            onClick={() => {
                                activateChannel(channel.id);
                            }}
                            key={channel.id}
                        >
                            <Box className={cx.channelTeaserName}>
                                <TbHash size={14} />
                                {channel.name}
                            </Box>
                            {!channel.viewed && <TbPointFilled size={14} style={{ color: 'red' }} />}
                        </UnstyledButton>
                    ))}
                </Stack>
                {userIsTeamMember && (
                    <Box className={cx.channelListActions}>
                        <DiagramSidebar.Button onClick={openCreateChannelModal}>
                            <TbPlus />
                            Create Channel
                        </DiagramSidebar.Button>
                    </Box>
                )}
            </DiagramSidebar.Section>
        </React.Fragment>
    );
};

export { Channels };
