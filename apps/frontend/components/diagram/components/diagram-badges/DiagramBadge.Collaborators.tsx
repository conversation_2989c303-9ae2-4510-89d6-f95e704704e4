import React, { FC } from 'react';
import { PermissionDiagramGeneral, User } from '@repo/dcide-component-models';
import { CursorPosition } from '@ably/spaces';

import { useRouter } from 'next/router';

import { AvatarGroup, UnstyledButton, Box } from '@mantine/core';
import { IoPersonAddOutline } from 'react-icons/io5';
import { Avatar } from 'components/avatar/Avatar';

import { SidebarService } from 'components/diagram/services/SidebarService';
import { SidebarType } from 'components/diagram/state/sidebar';

import { ViewportService } from 'components/diagram/services/ViewportService';
import { useCurrentUser } from 'hooks/use-current-user';
import { usePermission } from 'hooks/use-permission';
import { useCursors, useMembers } from '@ably/spaces/react';

import cx from './DiagramBadges.module.css';
import { LocalStorageService } from 'services/LocalStorageService';
import { InternalTrackingService } from 'services/InternalTrackingService';

export const TRACKING_REAL_TIME_COLLABORATION = 'tracking.real-time-collaboration';

const DiagramBadgeCollaborators: FC = () => {
    const router = useRouter();
    const { query } = router;

    const animateBadge = query.action === 'create-actions';

    const user = useCurrentUser();

    const { others } = useMembers();
    const { cursors } = useCursors({ returnCursors: true });

    const allCursors = Object.values(cursors);
    const othersCursors = allCursors.filter((cursor) => {
        return others.some((member) => member.clientId === cursor.member.clientId);
    });

    const canShare = usePermission(PermissionDiagramGeneral.SHARE);

    const onClick = (cursor: CursorPosition) => {
        ViewportService.moveIntoView(cursor);
    };

    const hasTrackedDate = () => {
        return LocalStorageService.get(TRACKING_REAL_TIME_COLLABORATION);
    };
    const currentDate = () => {
        return new Date().toLocaleDateString('en-US');
    };

    if (user && allCursors.length > 0 && (!hasTrackedDate() || hasTrackedDate() !== currentDate())) {
        LocalStorageService.store(TRACKING_REAL_TIME_COLLABORATION, currentDate());
        InternalTrackingService.track('diagram.realTimeCollaboration');
    }

    const text =
        othersCursors.length === 0
            ? 'Invite to Collaborate'
            : allCursors.length === 1
              ? '1 user online'
              : `${allCursors.length} users online`;

    return user && canShare ? (
        <Box className={animateBadge ? `animation-border-spin ${cx.animationWrapper}` : cx.animationWrapper}>
            <UnstyledButton
                className={cx.badge}
                onClick={() => {
                    SidebarService.open({ type: SidebarType.SHARE });
                }}
            >
                {othersCursors.length > 0 && (
                    <AvatarGroup>
                        {allCursors.slice(0, 3).map(({ member, cursorUpdate }) => {
                            const name = (member?.profileData as User)?.name ?? 'Anonymous';

                            return (
                                <Avatar
                                    name={name}
                                    onClick={() => {
                                        onClick(cursorUpdate.position);
                                    }}
                                    size={20}
                                    tooltip
                                    key={cursorUpdate?.connectionId}
                                />
                            );
                        })}
                    </AvatarGroup>
                )}
                {othersCursors.length === 0 && <IoPersonAddOutline />}
                {text}
            </UnstyledButton>
        </Box>
    ) : null;
};

export { DiagramBadgeCollaborators };
