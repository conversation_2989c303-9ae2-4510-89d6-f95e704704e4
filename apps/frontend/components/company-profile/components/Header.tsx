import { FC } from 'react';

import { <PERSON><PERSON>, Flex, Group, Stack, Title } from '@mantine/core';

import { CompanyProfile, CompanySubscription, PermissionCompany, SavedItemType } from '@repo/dcide-component-models';

import { useProfilePermission } from 'components/company-profile/hooks/use-profile-permission';

import { Socials } from 'components/company-profile/components/Socials';
import { HeaderLogo } from 'components/company-profile/components/HeaderLogo';
import { PublishedBadge } from 'components/published-badge/PublishedBadge';
import { HeaderMeta } from 'components/company-profile/components/HeaderMeta';
import { VerifiedBadge } from 'components/verified-badge/VerifiedBadge';
import { IoChatbubbleOutline } from 'react-icons/io5';
import { ConnectProfileButton } from './ConnectProfileButton';
import { SaveButton } from 'components/save-button/SaveButton';
import { ShareButton } from 'components/company-profile/components/ShareButton';

import { IntercomService } from 'services/IntercomService';

import classes from './Header.module.css';

const Header: FC<{ company: CompanyProfile }> = ({ company }) => {
    const canEdit = useProfilePermission(PermissionCompany.EDIT);

    const isPremiumCompany = company.subscription === CompanySubscription.PREMIUM && !company.internal;

    return (
        <Flex className={classes.header}>
            <HeaderLogo company={company} />

            <Flex className={classes.info}>
                <Stack gap={0} className={classes.infoLeft}>
                    <Group gap={8}>
                        <Title fz={40} fw={800} mr="xs">
                            {company.name}
                        </Title>

                        {isPremiumCompany && <VerifiedBadge withTooltip companyName={company.name} />}

                        {canEdit && <PublishedBadge size="md" status={company.status} />}
                    </Group>

                    <HeaderMeta company={company} />
                </Stack>

                <Stack className={classes.infoRight}>
                    <Group gap="xs">
                        <SaveButton
                            iconOnly
                            id={company.id}
                            name={company.name}
                            type={SavedItemType.PROFILE}
                            actionIconProps={{
                                variant: 'transparent',
                                size: 'lg',
                            }}
                        />
                        <ShareButton company={company} />
                        {!canEdit && <ConnectProfileButton company={company} />}
                        {!canEdit && (
                            <Button
                                variant="outline"
                                leftSection={<IoChatbubbleOutline />}
                                onClick={() => {
                                    IntercomService.open();
                                }}
                            >
                                Message
                            </Button>
                        )}
                    </Group>
                    {/* <ClaimProfileButton company={company} /> */}
                    <Socials socials={{ website: company.website, ...company.socials }} />
                </Stack>
            </Flex>
        </Flex>
    );
};

export { Header };
