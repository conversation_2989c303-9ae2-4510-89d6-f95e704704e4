import React from 'react';

import { DiagramSidebar } from 'components/diagram';
import {useAIConversation} from 'components/diagram/hooks/use-ai-conversation';
import {useDiagram} from 'components/diagram/hooks';

import { Composer } from './components/Composer';

const AI = () => {
    const { id } = useDiagram();
    const { conversation } = useAIConversation(id);

    console.log(conversation);

    return (
        <DiagramSidebar.Section>
            <Composer />
        </DiagramSidebar.Section>
    );
};

export { AI };
