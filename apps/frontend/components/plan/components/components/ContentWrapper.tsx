import React, { FC, useEffect, useRef, useState } from 'react';

import { Button, Flex, Transition } from '@mantine/core';
import { IoArrowDown } from 'react-icons/io5';

import cx from '../Body.module.css';

const ContentWrapper: FC<{
    children?: React.ReactNode;
    isSteaming?: boolean;
}> = ({ children, isSteaming }) => {
    const [showScrollDown, setShowScrollDown] = useState(false);
    const viewport = useRef<HTMLDivElement>(null);

    const handleShowScrollDown = () => {
        setShowScrollDown(false);

        if (viewport.current) {
            const current = viewport.current;

            setShowScrollDown(current.scrollTop < current.scrollHeight - current.clientHeight - 100);
        }
    };

    useEffect(() => {
        if (viewport.current) {
            const current = viewport.current;

            current.scrollTo({
                top: current.scrollHeight,
                behavior: 'smooth',
            });
        }
    }, [viewport, children]);

    return (
        <>
            <Flex id="content" className={cx.content} ref={viewport} onScroll={handleShowScrollDown}>
                {children}
            </Flex>

            <Transition transition="slide-up" mounted={showScrollDown && !isSteaming} duration={200}>
                {(style) => (
                    <Button
                        size="xs"
                        color="brand.7"
                        className={cx.scrollDown}
                        onClick={() => {
                            viewport.current?.scrollTo({
                                top: viewport.current.scrollHeight,
                                behavior: 'smooth',
                            });
                        }}
                        leftSection={<IoArrowDown size={12} />}
                        style={style}
                    >
                        Scroll down
                    </Button>
                )}
            </Transition>
        </>
    );
};

export { ContentWrapper };
