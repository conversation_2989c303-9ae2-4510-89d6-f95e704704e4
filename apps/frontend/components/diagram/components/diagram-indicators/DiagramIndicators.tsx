import React, { FC } from 'react';

import { Box, Tooltip } from '@mantine/core';

import cx from './DiagramIndicators.module.css';
import { useDiagramIndicators } from 'components/diagram/hooks/use-diagram-indicators';

import { FormatHelpers } from 'helpers/formatters';
import { NumberHelpers } from 'helpers/NumberHelpers';
import { SidebarService } from 'components/diagram/services/SidebarService';
import { SidebarType } from 'components/diagram/state/sidebar';
import { energyConverter, powerConverter } from '@repo/dcide-component-models';

const DiagramIndicators: FC = () => {
    const totals = useDiagramIndicators();

    const indicators = [
        {
            label: 'Storage',
            description: 'Total energy storage capacity',
            total: FormatHelpers.formatValue(totals.storage, energyConverter),
        },
        {
            label: 'Grid',
            description: 'Total power of all grid connections',
            total: FormatHelpers.formatValue(totals.grid, powerConverter),
        },
        {
            label: 'Generation',
            description: 'Total power of all power sources',
            total: FormatHelpers.formatValue(totals.generation, powerConverter),
        },
        {
            label: 'Load',
            description: 'Total power of all loads',
            total: FormatHelpers.formatValue(totals.load, powerConverter),
        },
        {
            label: 'Cost',
            description: 'Bill of Materials',
            total: totals.billOfMaterials ? NumberHelpers.formatPrice(totals.billOfMaterials) : '-',
            onClick: () => {
                SidebarService.open({ type: SidebarType.BILL_OF_MATERIALS });
            },
        },
    ];

    return (
        <Box className={cx.indicators} visibleFrom="md">
            {indicators.map((indicator) => (
                <Indicator
                    label={indicator.label}
                    description={indicator.description}
                    total={indicator.total}
                    onClick={indicator.onClick}
                    key={indicator.label}
                />
            ))}
        </Box>
    );
};

const Indicator: FC<{
    label: string;
    description: string;
    total: string;
    onClick?: () => void;
}> = ({ label, description, total, onClick }) => (
    <Tooltip label={description} withArrow>
        <Box className={cx.indicator} onClick={onClick} data-clickable={!!onClick}>
            {label}
            <strong>{total || '-'}</strong>
        </Box>
    </Tooltip>
);

export { DiagramIndicators };
