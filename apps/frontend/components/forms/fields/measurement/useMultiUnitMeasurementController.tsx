import { useController, useWatch } from 'react-hook-form';
import { useCallback, useEffect, useState } from 'react';
import { Converter } from 'convert-units';
import { notFalsey } from 'helpers';
import { useComponentBulkFields } from 'components/component-bulk-editor/ComponentBulkFieldsContext';
import { ComponentFieldDisplay, MeasurementFieldType, MeasurementFieldTypes } from '@repo/dcide-component-models';

type Props = {
    name: string;
    units: string[];
    fallbackBaseUnit: string;
    defaultUnit?: string;
    converter: (value?: number) => Converter<string, string, string>;
};

export type FieldsRecord = Record<
    MeasurementFieldType,
    {
        value: string;
        onChange: (value: string) => void;
        hasDifferingValues: boolean;
        display: ComponentFieldDisplay;
        error: string | undefined;
        isDirty?: boolean;
    }
>;

type MultiUnitMeasurementController = {
    unit: string;
    cycleUnit: () => void;
    fields: FieldsRecord;
};

const useMultiUnitMeasurementController = ({
    name,
    units,
    fallbackBaseUnit,
    defaultUnit = units[0],
    converter,
}: Props): MultiUnitMeasurementController => {
    const {
        field: { value: fieldValues, onChange },
        fieldState: { error, isDirty },
    } = useController({ name });

    const baseUnit = fieldValues?.unit ?? fallbackBaseUnit;

    const getInitialUnit = useCallback((): string => {
        const fieldQuantities = MeasurementFieldTypes.map((fieldType) => fieldValues?.[fieldType]).filter(notFalsey);

        const smallestQuantity =
            fieldQuantities.length > 0 ? fieldQuantities.reduce((minimum, next) => Math.min(minimum, next)) : null;

        return (smallestQuantity && converter(smallestQuantity).from(baseUnit).toBest()?.unit) || defaultUnit;
    }, [fieldValues, baseUnit, defaultUnit, converter]);

    const [unit, setUnit] = useState<string>(getInitialUnit);

    const cycleUnit = useCallback(() => {
        setUnit((currentUnit) => {
            const currentUnitIndex = units.findIndex((unit) => unit === currentUnit);
            return units?.[currentUnitIndex + 1] ?? units[0];
        });
    }, [units]);

    useEffect(() => {
        if (!units.includes(unit)) {
            setUnit(getInitialUnit());
        }
    }, [unit, units, getInitialUnit]);

    const convertValue = useCallback(
        (value: string | number | null) => {
            if (value === '' || value === null) {
                return '';
            }

            const convertValue = converter(+value).from(baseUnit).to(unit);

            if (convertValue === 0) {
                return 0;
            }

            return convertValue.toFixed(6).replace(/\.?0*$/, '');
        },
        [converter, baseUnit, unit],
    );

    const parseValue = useCallback(
        (value: string) => {
            if (value === '' || isNaN(+value)) {
                return null;
            }

            return converter(+value).from(unit).to(baseUnit);
        },
        [converter, baseUnit, unit],
    );

    const { overrides, diff } = useComponentBulkFields();

    const componentMeta = useWatch({ name: `metadata.${name}` });

    const fields: FieldsRecord = Object.fromEntries(
        MeasurementFieldTypes.map((field) => {
            const fieldPath = `${name}.${field}`;
            const value = fieldValues?.[field] ?? null;

            return [
                field,
                {
                    value: convertValue(value as string),
                    onChange: (value: string) => {
                        onChange({ ...fieldValues, [field]: parseValue(value) });
                    },
                    hasDifferingValues: diff.includes(fieldPath) && !overrides.includes(fieldPath),
                    display: componentMeta?.[field]?.display ?? ComponentFieldDisplay.NORMAL,
                    error: error?.message,
                    isDirty,
                },
            ];
        }),
    ) as FieldsRecord;

    return { unit, cycleUnit, fields };
};

export { useMultiUnitMeasurementController };
