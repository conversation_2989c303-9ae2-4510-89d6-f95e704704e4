import { CollectionConfig, Field } from 'payload';

import { CreatedBy, Name, PermissionsField } from '../fields';
import { getRequestUser } from '../helpers/get-request-user';

import { ComponentPermissions } from './components/permissions';
import { OrderPermissions } from './orders/permissions';
import { ProjectPermissions } from './projects/permissions';
import { TeamPermissions } from './teams/permissions';

import { activateUser, deleteUser, editUser, inviteUser } from './teams/index';
import { subscriptionSignUp } from './teams/endpoints/subscription-sign-up';
import { subscriptionRequestDowngrade } from './teams/endpoints/subscription-request-downgrade';
import { current } from './teams/endpoints/current';
import { invoices } from './teams/endpoints/invoices';
import { portalUrl } from './teams/endpoints/portal-url';
import { subscriptionFeedback } from './teams/endpoints/subscription-feedback';
import { subscriptionRefresh } from './teams/endpoints/subscription-refresh';
import { postCreateSubscription } from './teams/endpoints/post-create-subscription';
import { requestAccess } from './teams/endpoints/request-access';

import { syncSubscriptionToCompanies } from './teams/hooks/sync-subscription-to-companies';

import { StripeHelpers } from '../helpers/StripeHelpers';
import { RequestAccess } from '../access/RequestAccess';

import { AllSubscriptions, defaultVoltageClasses } from '@repo/dcide-component-models';

const Teams: CollectionConfig = {
    slug: 'teams',
    admin: {
        useAsTitle: 'name',
    },
    access: {
        create: RequestAccess.userIsLoggedIn,
        read: RequestAccess.userIsLoggedIn,
        update: RequestAccess.userIsLoggedIn,
        delete: RequestAccess.blocked,
    },
    fields: [
        Name,
        {
            name: 'users',
            type: 'array',
            fields: [
                {
                    name: 'user',
                    type: 'relationship',
                    relationTo: 'users',
                    required: true,
                    maxDepth: 0,
                    index: true,
                },
                {
                    ...PermissionsField,
                    name: 'newPermissions',
                    options: [...ComponentPermissions, ...OrderPermissions, ...ProjectPermissions, ...TeamPermissions],
                } as Field,
            ],
            hooks: {
                beforeChange: [
                    async ({ req, operation }) => {
                        if (operation === 'create') {
                            return [
                                {
                                    user: req.user?.id,
                                    newPermissions: ['team.owner', 'project.all'],
                                },
                            ];
                        }
                    },
                ],
            },
            required: true,
        },
        {
            name: 'inactiveUsers',
            type: 'array',
            fields: [
                {
                    name: 'user',
                    type: 'relationship',
                    relationTo: 'users',
                    required: true,
                    maxDepth: 0,
                    index: true,
                },
                {
                    ...PermissionsField,
                    name: 'newPermissions',
                    options: [...ComponentPermissions, ...OrderPermissions, ...ProjectPermissions, ...TeamPermissions],
                } as Field,
            ],
        },
        CreatedBy,
        {
            name: 'stripeCustomerId',
            label: 'Stripe Customer ID',
            type: 'text',
            access: {
                create: RequestAccess.userIsDeveloper,
                update: RequestAccess.userIsDeveloper,
            },
            index: true,
        },
        {
            name: 'stripeSubscription',
            label: 'Stripe Subscription',
            type: 'json',
            defaultValue: {},
            hooks: {
                afterChange: [syncSubscriptionToCompanies],
            },
        },
        {
            name: 'stripeSubscriptionSchedule',
            label: 'Stripe Subscription Schedule',
            type: 'json',
            defaultValue: {},
        },
        {
            name: 'subscriptions',
            label: 'Subscriptions',
            type: 'array',
            admin: {
                readOnly: true,
            },
            fields: [
                {
                    name: 'subscription',
                    type: 'select',
                    options: AllSubscriptions,
                },
                {
                    name: 'quantity',
                    type: 'number',
                },
            ],
            hooks: {
                beforeChange: [
                    ({ siblingData }) => {
                        // ensures data is not stored in DB
                        delete siblingData['subscriptions'];
                    },
                ],
                afterRead: [
                    ({ data }) => {
                        if (!data || !data.stripeSubscription || !Object.keys(data.stripeSubscription).length)
                            return [];

                        return StripeHelpers.getTeamSubscriptions(data.stripeSubscription);
                    },
                ],
            },
            virtual: true,
        },
        {
            label: 'Subscription Billing Cycle',
            name: 'subscriptionBillingCycle',
            type: 'text',
            admin: {
                readOnly: true,
            },
            hooks: {
                beforeChange: [
                    ({ siblingData }) => {
                        // ensures data is not stored in DB
                        delete siblingData['subscriptionBillingCycle'];
                    },
                ],
                afterRead: [
                    ({ data }) => {
                        if (!data || !data.stripeSubscription || !Object.keys(data.stripeSubscription).length) return;

                        return (
                            StripeHelpers.getDesignerSubscriptionDetails(data.stripeSubscription)?.billingCycle ??
                            StripeHelpers.getCompanySubscriptionDetails(data.stripeSubscription)?.billingCycle
                        );
                    },
                ],
            },
        },
        {
            label: 'Subscription Update Notice',
            name: 'subscriptionUpdateNotice',
            type: 'json',
            admin: {
                readOnly: true,
            },
            hooks: {
                beforeChange: [
                    ({ siblingData }) => {
                        // ensures data is not stored in DB
                        delete siblingData['subscriptionUpdateNotice'];
                    },
                ],
                afterRead: [
                    async ({ data }) => {
                        if (
                            !data ||
                            !data.stripeSubscriptionSchedule ||
                            !Object.keys(data.stripeSubscriptionSchedule).length
                        ) {
                            return null;
                        }

                        const { phase: nextPhase } = StripeHelpers.getNextSubscriptionSchedulePhase(
                            data.stripeSubscriptionSchedule,
                        );

                        if (nextPhase) {
                            const { phase: currentPhase } = StripeHelpers.getActivePhase(
                                data.stripeSubscriptionSchedule,
                            );

                            const currentSuscriptions = StripeHelpers.getSubscriptions(currentPhase.items);
                            const nextSubscriptions = StripeHelpers.getSubscriptions(nextPhase.items);

                            const subscriptionChanges = StripeHelpers.getSubscriptionChanges(
                                currentSuscriptions,
                                nextSubscriptions,
                            );

                            if (subscriptionChanges.length === 0) {
                                return null;
                            }

                            return {
                                date: nextPhase.start_date,
                                changes: subscriptionChanges,
                            };
                        }

                        return null;
                    },
                ],
            },
        },
        {
            label: 'Subscription Cancels At',
            name: 'subscriptionCancelsAt',
            type: 'number',
            admin: {
                readOnly: true,
            },
            hooks: {
                beforeChange: [
                    ({ siblingData }) => {
                        // ensures data is not stored in DB
                        delete siblingData['subscriptionCancelsAt'];
                    },
                ],
                afterRead: [
                    ({ data }) => {
                        if (!data || !data.stripeSubscription || !Object.keys(data.stripeSubscription).length) return;

                        return data.stripeSubscription.cancel_at;
                    },
                ],
            },
        },
        {
            name: 'voltageClasses',
            type: 'json',
            defaultValue: defaultVoltageClasses,
        },
        {
            name: 'interestedIn',
            type: 'text',
        },
        {
            name: 'showDesignEditor',
            type: 'checkbox',
            defaultValue: true,
        },
    ],
    endpoints: [
        current,
        inviteUser,
        deleteUser,
        editUser,
        subscriptionFeedback,
        subscriptionSignUp,
        subscriptionRefresh,
        subscriptionRequestDowngrade,
        invoices,
        portalUrl,
        activateUser,
        postCreateSubscription,
        requestAccess,
    ],
    hooks: {
        afterChange: [
            // Adjust user permissions in team
            async ({ req, operation, doc }) => {
                const user = await getRequestUser(req);

                if (user && operation === 'create') {
                    await req.payload.update({
                        collection: 'users',
                        id: user.id,
                        data: {
                            team: doc.id,
                        },
                    });
                }
            },
            // Attach reference projects to new team
            async ({ req, operation, doc }) => {
                const user = await getRequestUser(req);
                const { payload } = req;

                // An existing user with an existing team doesn't need new reference projects
                if (user && user.team) return;

                if (operation === 'create') {
                    const general = await payload.findGlobal({
                        slug: 'general',
                        depth: 1,
                        req,
                    });

                    general.reference?.projects?.forEach((project) => {
                        if (typeof project === 'string') {
                            return;
                        }

                        payload
                            .create({
                                collection: 'projects',
                                data: {
                                    name: project.name,
                                    team: doc.id,
                                    designs: [],
                                },
                                context: {
                                    startFromExistingDesign: project.designs?.[0],
                                },
                                req,
                            })
                            .then();
                    });
                }
            },
        ],
    },
};

export default Teams;
