import React, { FC } from 'react';
import { Container } from '@/components/container/Container'
import { Spacer } from '@/components/spacer/Spacer'
import {
    IconApple,
    IconLemon,
    IconGlassChampagne,
    IconGlassCocktail,
    IconGlassGin,
    IconLeaf, IconConfetti, IconBalloon,
} from '@tabler/icons-react'
import Link from 'next/link'

import { PayloadService } from '@/services/PayloadService';

const Footer: FC = async () => {
    const payload = await PayloadService.get();

    const list = async (collection: 'flavours' | 'occasions' | 'styles') => {
        const { docs } = await payload.find({
            collection,
            select: {
                title: true,
                slug: true,
            },
            pagination: false,
        });

        return docs;
    };

    const [flavours, occasions, styles] = await Promise.all([
        list('flavours'),
        list('occasions'),
        list('styles'),
    ]);

    return (
        <Container>
            <div className="max-w-6xl mx-auto">
                <Spacer size="lg" />
                <Spacer size="lg" />
                <div className="flex justify-center items-center gap-4">
                    {[
                        IconApple,
                        IconGlassChampagne,
                        IconGlassGin,
                        IconGlassCocktail,
                        IconLeaf,
                    ].map((Icon, index) => (
                        <Icon className="w-10 h-10 opacity-15" strokeWidth={1.25} key={index} />
                    ))}
                </div>
                <Spacer size="lg" />
                <Spacer size="lg" />
                <div className="grid gap-8 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
                    <div className="sm:col-span-2 md:col-span-3 xl:col-span-1">
                        <Link className="flex items-center gap-1 font-bold text-xl" href="/">
                            <IconLemon className="w-7 h-7 text-yellow-600" strokeWidth={1.5} />
                            Bittrs.
                        </Link>
                        <div className="h-4" />
                        <div>
                            Discover the best cocktail recipes for every occasion.<br />
                            Learn fun facts about ingredients and accessories.
                        </div>
                        <div className="h-2" />
                        <a href="https://www.webosaurus.be">
                            A website by Webosaurus
                        </a>
                    </div>
                    {[
                        { title: 'Flavours', items: flavours },
                        { title: 'Occasions', items: occasions },
                        { title: 'Styles', items: styles },
                    ].map((group) => (
                        <div key={group.title}>
                            <div className="flex items-center gap-1 font-bold text-xl">
                                <IconBalloon className="w-7 h-7 text-yellow-600" strokeWidth={1.5} />
                                {group.title}
                            </div>
                            <div className="h-4" />
                            <ul className="grid gap-1">
                                {group.items.map((item) => (
                                    <li key={item.id}>
                                        <Link href={`/${group.title.toLowerCase()}/${item.slug}`}>{item.title}</Link>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>

                <Spacer size="lg" />
            </div>
        </Container>
    );
}

export { Footer };
