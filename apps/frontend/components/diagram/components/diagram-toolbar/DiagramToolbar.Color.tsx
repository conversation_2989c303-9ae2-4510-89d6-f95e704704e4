import { type FC } from 'react';

import { useController, useFormContext } from 'react-hook-form';

import { useDisclosure } from '@mantine/hooks';
import { ColorPicker, ColorSwatch, Combobox, Tooltip, UnstyledButton, useCombobox } from '@mantine/core';

import { SWATCHES } from 'components/forms/fields/ColorField';

import cx from 'components/diagram/components/diagram-toolbar/DiagramToolbar.module.css';

const DiagramToolbarColor: FC<{
    name: string;
    label: string;
    transparentSwatches?: boolean;
}> = ({ name, label, transparentSwatches = false }) => {
    const { setValue } = useFormContext();
    const { field } = useController({
        name,
    });

    const [opened, handlers] = useDisclosure();
    const combobox = useCombobox({
        opened,
    });

    const currentColor = field.value ?? 'transparent';

    const handleChange = (value: string) => {
        setValue(name, value, { shouldDirty: true });
    };

    return (
        <Combobox
            store={combobox}
            classNames={{
                dropdown: `${cx.dropdown} ${cx.colorPicker}`,
            }}
            position="top"
            withinPortal={false}
            onClose={handlers.close}
        >
            <Combobox.Target>
                <Tooltip
                    label={label}
                    classNames={{
                        tooltip: cx.tooltip,
                    }}
                    withArrow
                    disabled={opened}
                >
                    <UnstyledButton className={cx.colorSwatch} onClick={handlers.toggle}>
                        <ColorSwatch size={20} color={currentColor} />
                        <input {...field} type="hidden" />
                    </UnstyledButton>
                </Tooltip>
            </Combobox.Target>
            <Combobox.Dropdown>
                <ColorPicker
                    size="sm"
                    format="rgba"
                    value={currentColor}
                    onChange={handleChange}
                    onColorSwatchClick={(color) => {
                        if (transparentSwatches) {
                            const [, red, green, blue] = color.match(
                                /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)/,
                            )!;

                            handleChange(RGBToHex(red, green, blue));
                        }
                    }}
                    swatches={SWATCHES}
                />
            </Combobox.Dropdown>
        </Combobox>
    );
};

function RGBToHex(red: string, green: string, blue: string) {
    const convert = (value: string) => {
        value = (+value).toString(16);

        if (value.length === 1) {
            value = '0' + value;
        }

        return value;
    };

    return '#' + convert(red) + convert(green) + convert(blue) + '1A';
}

export { DiagramToolbarColor };
