import React from 'react';

import { Box } from '@mantine/core';
import { useWatch } from 'react-hook-form';

import { CompanyProfile, CompanyService } from '@repo/dcide-component-models';

import { useCompanyServiceTags } from 'hooks/use-company-service-tags';

import { TagsField } from 'components/forms/fields/TagsField';

import cx from 'components/checkbox-button/CheckboxButton.module.css';

const CompanyFormServiceTags = () => {
    const services = useWatch({ name: 'services' }) as CompanyProfile['services'];

    const { serviceTags: serviceTagsSuggestions } = useCompanyServiceTags();

    if (services?.includes(CompanyService.OTHER)) {
        return (
            <Box className={cx.root} data-interactive data-checked data-border-top={false} pl={36} pt={0} pb="xs">
                <TagsField
                    name="serviceTags"
                    label="Other services"
                    placeholder="Enter any other services you provide"
                    data={serviceTagsSuggestions}
                />
            </Box>
        );
    }

    return null;
};

export { CompanyFormServiceTags };
