// @ts-nocheck

import type { MetadataRoute } from 'next';

import { blogPosts as allBlogPosts } from '@/data/blog-posts';

const base = 'https://about.dcide.app';
const blogPosts = allBlogPosts.filter(post => post.slug);

export default function sitemap(): MetadataRoute.Sitemap {
    return [
        {
            url: base,
            lastModified: new Date(),
            priority: 1,
        },
        ...[
            'about',
            'blog',
            'features',
        ].map((path) => ({
            url: `${base}/${path}`,
            lastModified: new Date(),
            changeFrequency: 'monthly' as any,
            priority: 0.8,
        })),
        ...[
            'features/annotations',
            'features/battery-charging',
            'features/bill-of-materials',
            'features/cloud-based',
            'features/in-app-communication',
            'features/in-app-support',
            'features/prototyping',
            'features/product-catalog',
            'features/realtime-collaboration',
            'features/validations',
            'features/wire-sizing',
        ].map((path) => ({
            url: `${base}/${path}`,
            lastModified: new Date(),
            changeFrequency: 'monthly' as any,
            priority: 0.6,
        })),
        ...blogPosts.map((post) => ({
            url: `${base}/blog/${post.slug}`,
            lastModified: new Date(),
            changeFrequency: 'monthly' as any,
            priority: 0.6,
        })),
    ]
}
