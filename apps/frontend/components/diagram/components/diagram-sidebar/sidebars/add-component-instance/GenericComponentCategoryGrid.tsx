import React, { FC, useEffect, useState } from 'react';
import { useSnapshot } from 'hooks/use-safe-snapshot';

import { Box, Divider, SimpleGrid, Stack } from '@mantine/core';

import { ComponentDefinition, all } from '@repo/dcide-component-models';
import { DraggingType } from 'types/DraggingType';

import { LastUsedComponentInstanceService } from 'components/diagram/services/LastUsedComponentInstanceService';

import { CategoryGrid } from './CategoryGrid';
import { DraggableCategoryGridItem } from './CategoryGridItem';

import { genericComponentSearchState } from 'components/diagram/state/generic-component-search';
import { SearchBar } from 'components/diagram/components/diagram-sidebar/sidebars/add-component-instance/SearchBar';

import classes from './AddComponentInstance.module.css';

type Category = {
    key: ComponentDefinition['type'];
    label: ComponentDefinition['name'];
};

type CategorySection = {
    title: string;
    keys: Category['key'][];
};

const GenericComponentCategoryGrid: FC<unknown> = () => {
    const { query } = useSnapshot(genericComponentSearchState);
    const [allowRender, setAllowRender] = useState(false);

    useEffect(() => {
        setAllowRender(true);
    }, []);

    useEffect(() => {
        return () => {
            genericComponentSearchState.query = null;
        };
    }, []);

    if (!allowRender) {
        return null;
    }

    const categories: CategorySection[] = [
        {
            title: 'Recently used',
            keys: LastUsedComponentInstanceService.getRecentlyUsed(),
        },
        {
            title: 'Frequently used',
            keys: LastUsedComponentInstanceService.getMostUsed(),
        },
        {
            title: 'Load',
            keys: ['charger', 'light', 'hvac', 'motor', 'load'],
        },
        {
            title: 'Source',
            keys: ['solar', 'hydro', 'wind', 'utility', 'generator'],
        },
        {
            title: 'Store',
            keys: ['battery'],
        },
        {
            title: 'Convert',
            keys: ['converter', 'transformer'],
        },
        {
            title: 'Connect',
            keys: ['panel', 'bus', 'transferSwitch'],
        },
        {
            title: 'Protect',
            keys: ['breaker', 'contactor', 'disconnect', 'fuse'],
        },
        {
            title: 'Other',
            keys: ['meter', 'grounding', 'custom', 'solution'],
        },
    ];

    const filteredCategories: CategorySection[] = query
        ? categories
              .map(({ keys, ...rest }) => ({
                  keys: query ? keys.filter((key) => key.includes(query)) : keys,
                  ...rest,
              }))
              .filter(({ keys }) => keys.length)
        : categories;

    return (
        <Stack>
            <SearchBar />
            <SimpleGrid cols={1} spacing="sm">
                {filteredCategories.map(({ title, keys }) => (
                    <Box className={classes.components} aria-hidden={!keys.length} key={title}>
                        <Divider label={title} labelPosition="left" mb="sm" />
                        <CategoryGrid>
                            {keys.map((key) => (
                                <DraggableCategoryGridItem
                                    key={key}
                                    type={key}
                                    label={all[key].name}
                                    dragOptions={{
                                        id: `draggable-new-${key}-${title}`,
                                        data: {
                                            type: DraggingType.NEW_COMPONENT_INSTANCE,
                                            componentType: key,
                                        },
                                    }}
                                />
                            ))}
                        </CategoryGrid>
                    </Box>
                ))}
            </SimpleGrid>
        </Stack>
    );
};

export { GenericComponentCategoryGrid };
