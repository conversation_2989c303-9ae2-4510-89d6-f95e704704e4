import { Button, <PERSON>dal, SimpleGrid, Text } from '@mantine/core';

import { Component } from '@repo/dcide-component-models';

import { ComponentOverviewHit } from 'components/component-overview';
import { useGenerateDatasheet } from 'components/generate-datasheet/GenerateDatasheet.Context';

const GenerateDatasheetChooseModal = () => {
    const { chooseModalOpened, showChooseModal, aiResult, fillInDatasheet } = useGenerateDatasheet();

    const handleResultClick = (result: Partial<Component>) => {
        showChooseModal(false);
        fillInDatasheet(result);
    };

    return (
        <Modal
            opened={chooseModalOpened && !!aiResult?.length}
            onClose={() => showChooseModal(false)}
            title={`We found ${aiResult?.length} possible matches`}
            size="xl"
        >
            <Text>
                Choose the product you want to generate the datasheet for. If you do not see the product you are looking
                for, you can fill in the details manually.
            </Text>

            <SimpleGrid cols={{ base: 1, sm: 2 }} mt="md" spacing="xs">
                {aiResult?.map((result) => (
                    <ComponentOverviewHit key={result.id} component={result as any} withBorder>
                        <Button
                            key={result.id}
                            mt="md"
                            size="xs"
                            variant="outline"
                            onClick={() => handleResultClick(result)}
                        >
                            Choose this product
                        </Button>
                    </ComponentOverviewHit>
                ))}
            </SimpleGrid>
        </Modal>
    );
};

export { GenerateDatasheetChooseModal };
