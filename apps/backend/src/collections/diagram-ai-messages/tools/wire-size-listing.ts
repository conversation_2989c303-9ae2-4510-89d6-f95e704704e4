import { OpenAI } from 'openai';

import { generateToolPrompt } from '../helpers/generate-tool-prompt';

const name = 'wireSizeListing';

const definition: OpenAI.Responses.FunctionTool = {
    type: 'function',
    name,
    description: 'Retrieves the wire size listing',
    parameters: {
        type: 'object',
        properties: {},
        additionalProperties: false,
    },
    strict: true,
};

const handler = async () => {
    return '';
};

const prompt = generateToolPrompt({
    name: 'Wire Size Listing Questions',
    objective: 'Address user questions or requests related to wire size listings, schedules and schemas.',
    exampleQuestions: [
        'Can you list all wire sizes in my diagram',
        'List all the wire sizes in this diagram and organize them by size',
        'What are the wire sizes used in my diagram',
        'Can you give me a wiring schema for my diagram',
        'Give me a wiring schedule for my diagram',
    ],
    handler: `
        **Formatting:**
        * 6x 12 AWG
        * 2x 10 AWG (Number of Cables: 2)
        * 1x 8 mm²
        * 4x 1 mm² (Number of Cables: 10)
        * 2x 800 MCM (Number of Cables: 1)

        If the user asks for a wiring schema **always** return the result in a markdown table

        **Ordering:**
        Unless the user asks for a specific order, return them from smallest to largest.

        **Measurement System:**
        All the wire sizes in the diagram are in mm².

        Based on the user's measurement system preference:
        *   Show the values in mm² if the user's measurement system is metric
        *   Show the values in AWG if the user's measurement system is imperial

        When the user's measurement system is imperial, you **must** use the following conversion list to convert the mm² values to AWG:

        0.05 mm² = 30 AWG
        0.08 mm² = 28 AWG
        0.14 mm² = 26 AWG
        0.25 mm² = 24 AWG
        0.34 mm² = 22 AWG
        0.38 mm² = 21 AWG
        0.5 mm² = 20 AWG
        0.75 mm² = 18 AWG
        1 mm² = 17 AWG
        1.5 mm² = 16 AWG
        2.5 mm² = 14 AWG
        4 mm² = 12 AWG
        6 mm² = 10 AWG
        10 mm² = 8 AWG
        16 mm² = 6 AWG
        25 mm² = 4 AWG
        26.67 mm² = 3 AWG
        35 mm² = 2 AWG
        50 mm² = 1 AWG
        55 mm² = 1/0 AWG
        70 mm² = 2/0 AWG
        95 mm² = 3/0 AWG
        120 mm² = 4/0 AWG
        127 mm² = 250MCM AWG
        150 mm² = 300MCM AWG
        185 mm² = 350MCM AWG
        240 mm² = 500MCM AWG
        300 mm² = 600MCM AWG
        355 mm² = 700MCM AWG
        400 mm² = 750MCM AWG
        407 mm² = 800MCM AWG
        500 mm² = 1000MCM AWG
        635 mm² = 1250MCM AWG
        762 mm² = 1500MCM AWG
        889 mm² = 1750MCM AWG
        1016 mm² = 2000MCM AWG

        **Note:** Never mention the fact that you are converting units, the user already knows this.
    `,
});

const wireSizeListing = {
    name,
    definition,
    handler,
    prompt,
};

export { wireSizeListing };
