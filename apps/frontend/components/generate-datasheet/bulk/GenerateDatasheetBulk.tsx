import React, { useState } from 'react';

import { useRouter } from 'next/router';
import { <PERSON><PERSON>, But<PERSON> } from '@mantine/core';

import {
    Component,
    ComponentFileType,
    ComponentType,
    FileVisibility,
    getComponentPayloadValidator,
} from '@repo/dcide-component-models';

import { AIService } from 'services/AIService';
import { ComponentHelpers } from 'helpers/ComponentHelpers';
import { ComponentService } from 'services/ComponentService';
import { LocalNotificationService } from 'services/LocalNotificationService';
import { InternalTrackingService } from 'services/InternalTrackingService';

import { Page } from 'components/page';

import { GenerateDatasheetState } from 'components/generate-datasheet/GenerateDatasheet.Context';
import { GenerateDatasheetBulkForm } from 'components/generate-datasheet/bulk/GenerateDatasheetBulk.Form';
import { GenerateDatasheetBulkContext } from 'components/generate-datasheet/bulk/GenerateDatasheetBulk.Context';
import { GenerateDatasheetBulkSpecifications } from 'components/generate-datasheet/bulk/GenerateDatasheetBulk.Specifications';
import { z } from 'zod';

export const GenerateDatasheetBulkSchema = z.object({
    type: ComponentType.validator,
    productSeries: z.string(),
    manufacturer: z.string(),
    file: z.string(),
});

export type GenerateDatasheetBulkValues = z.infer<typeof GenerateDatasheetBulkSchema>;

const GenerateDatasheetBulk = () => {
    const router = useRouter();

    const [createComponents, setCreateComponents] = useState<Component[]>([]);

    const [formValues, setFormValues] = useState<GenerateDatasheetBulkValues | null>(null);

    const [state, setState] = useState<GenerateDatasheetState>(GenerateDatasheetState.INITIAL);

    const [creating, setCreating] = useState(false);

    const generateDatasheet = async (values: GenerateDatasheetBulkValues) => {
        setCreateComponents([]);
        setFormValues(values);

        try {
            setState(GenerateDatasheetState.LOADING);

            const validator = getComponentPayloadValidator(values.type);

            // TODO refactor AI endpoint so we don't need a dummy component
            const dummyComponent = validator.parse({
                name: 'TODO DELETE ME',
                files: [
                    {
                        file: values.file,
                        type: ComponentFileType.DATASHEET,
                        visibility: FileVisibility.PRIVATE,
                    },
                ],
            });

            const { id: createdComponentId } = await ComponentService.create(dummyComponent);

            InternalTrackingService.track('product.aiDatasheet.populate', {
                componentId: createdComponentId,
                componentType: values.type,
            });

            const result = await AIService.populateDatasheet({
                componentId: createdComponentId,
                componentType: values.type,
                fileId: values.file,
            });

            await ComponentService.delete(createdComponentId);

            if (!result?.length) {
                setState(GenerateDatasheetState.ERROR);
                return;
            }

            const cleanComponents = result.map((component) =>
                getComponentPayloadValidator(values.type).parse({
                    ...component,
                    manufacturer: values.manufacturer,
                    productSeries: values.productSeries,
                    name: component.name ?? component.productIdentifier ?? '',
                }),
            );

            setCreateComponents(cleanComponents as Component[]);

            setState(GenerateDatasheetState.SUCCESS);
        } catch (error: any) {
            if (error.name !== 'AbortError') {
                setState(GenerateDatasheetState.ERROR);
                console.error(error);
            }
        }
    };

    const handleCreateComponents = async () => {
        setCreating(true);

        try {
            await Promise.all(createComponents.map((component) => ComponentService.create(component)));

            if (formValues?.manufacturer && formValues?.productSeries) {
                router.push(ComponentHelpers.urls.bulk(formValues.manufacturer, formValues.productSeries)).then();
            }
        } catch (error) {
            setCreating(false);
            LocalNotificationService.showError({
                message: 'Something went wrong while creating the products, please try again.',
            });
        }
    };

    const isError = state === GenerateDatasheetState.ERROR;
    const isLoading = state === GenerateDatasheetState.LOADING;

    const showSpecifications = !!createComponents?.length;

    return (
        <GenerateDatasheetBulkContext.Provider
            value={{
                createComponents,
                setCreateComponent: (component, index) => {
                    setCreateComponents((components) => {
                        components[index] = component;
                        return [...components];
                    });
                },
            }}
        >
            <Page>
                <Page.WideContent>
                    <GenerateDatasheetBulkForm onSubmit={generateDatasheet} disabled={isLoading} />

                    {isError && <Alert color="red">Something went wrong</Alert>}

                    {showSpecifications && <GenerateDatasheetBulkSpecifications />}

                    {showSpecifications && (
                        <Button
                            fullWidth
                            variant="gradient"
                            size="xl"
                            onClick={handleCreateComponents}
                            loading={creating}
                        >
                            Create {createComponents.length} products
                        </Button>
                    )}
                </Page.WideContent>
            </Page>
        </GenerateDatasheetBulkContext.Provider>
    );
};

export { GenerateDatasheetBulk };
