import { User, Team, OrderStatus } from '../types';

type BaseNotification = {
    id: string;
    from?: User['id'];
    to: User['id'];
    createdAt: string;
    updatedAt: string;
    deletedAt?: string;
    readAt?: string;
    handledAt?: string;
    team: Team['id'];
    cache?: {
        app?: object;
    };
};

type DiagramCommentNotificationBaseData = {
    commentId: string;
    content: string;
    attachment?: boolean;
};

export enum NotificationKey {
    DIAGRAM_COMMENT_CREATE = 'diagram.comment.create',
    DIAGRAM_COMMENT_REPLY_USER = 'diagram.comment.reply.user',
    DIAGRAM_COMMENT_REPLY_AI = 'diagram.comment.reply.ai',
    DIAGRAM_COMMENT_MENTION_USER = 'diagram.comment.mention.user',
    DIAGRAM_COMMENT_MENTION_MANUFACTURER = 'diagram.comment.mention.manufacturer',
    DIAGRAM_INVITE = 'diagram.invite',
    DIAGRAM_REQUESTACCESS = 'diagram.requestAccess',
    DESIGN_DUPLICATE = 'design.duplicate',
    ORDER_EVENT = 'order.event',
    ORDER_MESSAGE = 'order.message',
    DIAGRAM_CHAT_MESSAGE = 'diagram.chat.message',
    COMPANY_REQUEST_ACCESS = 'company.requestAccess',
    COMPANY_GRANT_ACCESS = 'company.grantAccess',
    COMPANY_DENY_ACCESS = 'company.denyAccess',
    USER_TEAM_INVITE = 'user.teamInvite',
}

type NotificationDiagramCommentCreate = BaseNotification & {
    type: NotificationKey.DIAGRAM_COMMENT_CREATE;
    data: DiagramCommentNotificationBaseData;
};

type NotificationDiagramCommentReplyUser = BaseNotification & {
    type: NotificationKey.DIAGRAM_COMMENT_REPLY_USER;
    data: DiagramCommentNotificationBaseData;
};

type NotificationDiagramCommentReplyAI = BaseNotification & {
    type: NotificationKey.DIAGRAM_COMMENT_REPLY_AI;
    data: DiagramCommentNotificationBaseData;
};

type NotificationDiagramCommentMentionUser = BaseNotification & {
    type: NotificationKey.DIAGRAM_COMMENT_MENTION_USER;
    data: DiagramCommentNotificationBaseData & {
        mention: string;
    };
};

export type NotificationDiagramCommentMentionManufacturer = BaseNotification & {
    type: NotificationKey.DIAGRAM_COMMENT_MENTION_MANUFACTURER;
    data: DiagramCommentNotificationBaseData & {
        mention: string;
        manufacturerName: string;
    };
};

export type NotificationDiagramInvite = BaseNotification & {
    type: NotificationKey.DIAGRAM_INVITE;
    data: {
        projectId: string;
        login: {
            url: string;
            code: string;
        };
        isNewUser: boolean;
    };
};

export type NotificationDiagramRequestAccess = BaseNotification & {
    type: NotificationKey.DIAGRAM_REQUESTACCESS;
    data: {
        projectId: string;
        designId: string;
        diagramId: string;
        userId: string;
    };
};

export type NotificationDesignDuplicate = BaseNotification & {
    type: NotificationKey.DESIGN_DUPLICATE;
    data: {
        referenceDesignId: string;
        designId: string;
    };
};

export type NotificationDiagramComment =
    | NotificationDiagramCommentCreate
    | NotificationDiagramCommentReplyUser
    | NotificationDiagramCommentReplyAI
    | NotificationDiagramCommentMentionUser
    | NotificationDiagramCommentMentionManufacturer;

type NotificationOrderEvent = BaseNotification & {
    type: NotificationKey.ORDER_EVENT;
    data: {
        orderId: string;
        status: OrderStatus;
    };
};

type NotificationOrderMessage = BaseNotification & {
    type: NotificationKey.ORDER_MESSAGE;
    data: {
        orderId: string;
    };
};

type NotificationDiagramChatChannel = BaseNotification & {
    type: 'diagram.chat.channel';
    data: {
        channelId: string;
    };
};

type NotificationDiagramChatMessage = BaseNotification & {
    type: NotificationKey.DIAGRAM_CHAT_MESSAGE;
    data: {
        channelId: string;
        messageId: string;
    };
};

type NotificationIntercomMessage = BaseNotification & {
    type: 'intercom.message';
    data: {
        channelId: string;
        messageId: string;
    };
};

export type NotificationCompanyRequestAccess = BaseNotification & {
    type: NotificationKey.COMPANY_REQUEST_ACCESS;
    data: {
        companyId: string;
        teamId: string;
        userId: string;
        userTeamId: string;
    };
};

export type NotificationCompanyGrantAccess = BaseNotification & {
    type: NotificationKey.COMPANY_GRANT_ACCESS;
    data: {
        companyId: string;
        teamId: string;
        userId: string;
    };
};

export type NotificationCompanyDenyAccess = BaseNotification & {
    type: NotificationKey.COMPANY_DENY_ACCESS;
    data: {
        companyId: string;
        userId: string;
        message: string;
    };
};

export type NotificationUserTeamInvite = BaseNotification & {
    type: NotificationKey.USER_TEAM_INVITE;
    data: {
        teamId: string;
        userId: string;
        login: {
            url: string;
            code: string;
        };
        isNewUser: boolean;
    };
};

export type NotificationOrder = NotificationOrderEvent | NotificationOrderMessage;

export type Notification =
    | NotificationDiagramComment
    | NotificationDiagramInvite
    | NotificationDiagramRequestAccess
    | NotificationDesignDuplicate
    | NotificationDiagramChatChannel
    | NotificationDiagramChatMessage
    | NotificationOrder
    | NotificationIntercomMessage
    | NotificationCompanyRequestAccess
    | NotificationCompanyGrantAccess
    | NotificationCompanyDenyAccess
    | NotificationUserTeamInvite;
