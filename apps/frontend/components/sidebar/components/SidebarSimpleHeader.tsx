import React, { FC } from 'react';

import { Box, Group } from '@mantine/core';
import { TbChevronLeft } from 'react-icons/tb';

import { DiagramSidebar } from 'components/diagram/components/diagram-sidebar/DiagramSidebar';

import cx from './SidebarComponents.module.css';

export const SidebarSimpleHeader: FC<{
    leftSection?: React.ReactNode;
    rightSection?: React.ReactNode;
    handleBackClick?: () => void;
    children?: React.ReactNode;
}> = ({ leftSection, rightSection, handleBackClick, children }) => {
    return (
        <Group className={cx.simpleHeader} justify="space-between" align="center">
            <Group gap="xs">
                {leftSection}
                {handleBackClick && (
                    <DiagramSidebar.Button onClick={handleBackClick}>
                        <TbChevronLeft />
                    </DiagramSidebar.Button>
                )}
                <Box>{children}</Box>
            </Group>
            <Group>{rightSection}</Group>
        </Group>
    );
};
